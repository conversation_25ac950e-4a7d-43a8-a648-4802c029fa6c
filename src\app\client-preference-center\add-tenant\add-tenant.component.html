<div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
    </div>
</div>
<div class="fixed-nav bg-gray">
    <div class="content-wrapper">
        <div class="container-fluid">
            <div>
                <div class="pd-30" style="margin-top: 1%; margin-bottom: 1%;">
                    <span class="dashbord-title"><a (click)="backToListPage()"><i
                                class="fa fa-chevron-circle-left"></i></a> Add
                        Tenant</span>
                </div>
                <div class="row form-pad">
                    <marketplace-dynamic-form *ngIf="enableForm" (onValueChange)="valuechange($event)"
                        (onValueChanges)="getPreviousCurrentValues($event)" [formJSON]="ViewTenantJson"
                        [isSubmitNeeded]="false">
                    </marketplace-dynamic-form>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="footer-btns" style="display: flex; justify-content: flex-end; align-items: flex-end; ">
    <marketplace-button [label]="'Cancel'" [type]="'secondary'" [name]="'secondary'" (onclick)="backToListPage()">
    </marketplace-button>

    <marketplace-button [enabled]="isSubmitEnabled" [label]="'Submit'" [type]="'primary'" [name]="'primary'"
        (onclick)="savePreference()">
    </marketplace-button>


</div>

<div class="modal" tabindex="-1" role="dialog" [ngStyle]="{'display':popupDisplayStyle}">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-header-custom">
                    <h4 class="modal-title custom-title">Attention !</h4>
                </div>
                <span class="btn-span"><a (click)="closePopup()"><i
                            class="fa fa-times-circle-o fa-2x close-icon-color"></i></a></span>
            </div>
            <div class="modal-body custom-message">
                <p class="pad-30">Please fill all the fields</p>
            </div>
            <div class="modal-footer">
                <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closePopup()">
                </marketplace-button>

            </div>
        </div>
    </div>
</div>