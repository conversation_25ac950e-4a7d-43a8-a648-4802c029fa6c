import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ProductBundleFeeAddComponent } from './product-bundle-fee-add.component';

describe('ProductBundleFeeAddComponent', () => {
  let component: ProductBundleFeeAddComponent;
  let fixture: ComponentFixture<ProductBundleFeeAddComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ProductBundleFeeAddComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ProductBundleFeeAddComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
