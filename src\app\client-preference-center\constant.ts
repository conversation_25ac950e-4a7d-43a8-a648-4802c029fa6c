export const list = {
  ADD: "Add",
  EDIT: "Edit",
  VIEW: "View",
  DE: "Data Exchange",
  DEADD: "Add Data Exchange",
  DEEDIT: "Edit Data Exchange",
  DEVIEW: "View Data Exchange",
  FEVIEW: "View File Exchange",
  TEEDIT: "Edit Tenant",
  TEVIEW: "View Tenant",
  TEADD: "Add Tenant",
  DROPDOWN: "DRODOWN",
  VARCHAR: "VARCHAR",
  DATE_COLUMN: "DATE",
  DATE: "date",
  NUMBER: 'NUMBER',
  DECIMAL: "decimal",
  DATE_FORMAT: "MM-DD-YYYY",
  DATE__FORMAT: "MM-dd-YYYY",
  DECIMAL_REG: "^\\d+(\\.\\d)?\\d*$",
  SINGLE_SELECT: "singleselect",
  INFO: "Info",
  NOTIFICATION_MESSAGE: "Criteria has been changed since the template name is changed!!",
  PRODUCT_NAME: "productName",
  BUSINESS_DIVISION: "businessDivision",
  PRODUCT_ID: "productId",
  DBG_UNIT: "dbgUnit",
  TEMPLATE_NAME: "templateName",
  SYSTEM: "system",
  FILETEMP_NAME: "fileTmplName",
  PROD_UAT: "PROD AND UAT",
  PROD: "PROD",
  UAT: "UAT",
  CONCEPT_STATE: "conceptState",
  PREFERENCE: "preferenceName",
  FILE_DESTINATION: "fileDestination",
  FILE_NAME: "fileName",
  FREQUENCY: "frequency",
  TIME: "time",
  START_DATE: "startDate",
  END_DATE: "endDate",
  INVENTORY_TYPE: "inventoryType",
  WARNING: "Warning",
  WARNING_MESG: "Please fill all the fields",
  FAIL: "Fail",
  DATAEXCHANGE_ALREADY_EXISTS: "Data exchange already exist, adjust the dates",
  SUCCESS: "Success",
  SUCCESS_MESG: "Data Exchange Sucessfully added to the list!!",
  SUCCESS_EDIT_MESG: "Data Exchange Sucessfully Edited in the list !!",
  STATUS: "status",
  VALID: "VALID",
  INVALID: "INVALID",
  TRANSFER_TIME: "transferTime",
  CLIENT_ID: "clientId",
  CLIENT_NAME: "clientName",
  ACTIVE_INDICATOR: "activeInd",
  PREFERENCE_ID: "preferenceId",
  SYSTEM_ID: "systemId",
  TEMPLATE_ID: "templateId",
  INVENTORY_TYPE_ID: "invTypeId",
  CONDITIONS: "conditions",
  CLASS_RIGHT_ICON: "float-right add-icon",
  CLASS_LEFT_ICON: "float-right delete-icon",
  ADD_NEW_ROW: "Add New Row",
  DELETE_ROW: "Delete This Row",
  DAILY: "Daily",
  INPUT_ELEMENT_CLASS: "color_mode",
  LABEL_ELEMENT_CLASS: ".btn-color-mode-switch .btn-color-mode-switch-inner",
  FOR_ATTRIBUTE: "for",
  ANTHEM_CLIENT_ID: 59,
  CLAIM_ANOMALY_DETECTION: "Data Mining Solution",
  BUS_DIV_PLACEHOLDER: "Choose Business Division",
  TENANT: "View Tenant",
  DMS: "Data Mining Solution",
  CAD: "Claim Anomaly Detection"
}

export const QBOperators = {
  text: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }, { name: 'contains', id: 'contains' }, { name: 'Does Not Contain(s)', id: 'Does Not Contain(s)' }, { name: 'Begins With', id: 'Begins With' }, { name: 'Ends With', id: 'Ends With' }, { name: 'Does Not Begins With', id: 'Does Not Begins With' }, { name: 'Does Not End With', id: 'Does Not End With' }, { name: 'Is Null', id: 'Is Null' }, { name: 'Is Not Null', id: 'Is Not Null' }, { name: 'In', id: 'in' }],
  numeric: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }, { name: 'Greater Than', id: 'Greater Than' }, { name: 'Greater Than or Equal', id: 'Greater Than Or Equal' }, { name: 'Less Than', id: 'Less Than' }, { name: 'Less Than Or Equal', id: 'Less Than Or Equal' }, { name: 'Between', id: 'Between' }, { name: 'Not Between', id: 'Not Between' }, { name: 'Is Null', id: 'Is Null' }, { name: 'Is Not Null', id: 'Is Not Null' }],
  textarea: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }, { name: 'contains', id: 'contains' }, { name: 'Does Not Contain(s)', id: 'Does Not Contain(s)' }, { name: 'Begins With', id: 'Begins With' }, { name: 'Ends With', id: 'Ends With' }, { name: 'Does Not Begins With', id: 'Does Not Begins With' }, { name: 'Does Not End With', id: 'Does Not End With' }, { name: 'Is Null', id: 'Is Null' }, { name: 'Is Not Null', id: 'Is Not Null' }],
  time: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }, { name: 'Greater Than', id: 'Greater Than' }, { name: 'Greater Than or Equal', id: 'Greater Than Or Equal' }, { name: 'Less Than', id: 'Less Than' }, { name: 'Less Than Or Equal', id: 'Less Than Or Equal' }],
  calendar: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }, { name: 'Greater Than', id: 'Greater Than' }, { name: 'Greater Than or Equal', id: 'Greater Than Or Equal' }, { name: 'Less Than', id: 'Less Than' }, { name: 'Less Than Or Equal', id: 'Less Than Or Equal' }, { name: 'Between', id: 'Between' }, { name: 'Not Between', id: 'Not Between' }, { name: 'Is Null', id: 'Is Null' }, { name: 'Is Not Null', id: 'Is Not Null' }],
  singleselect: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }],
  checkbox: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }],
  multipleselect: [{ name: 'Equal', id: 'Equal' }]
};