{"dataExchange": {"productName": "Cardiology", "preferenceName": "preference", "preferenceId": 21, "dbgUnit": "<PERSON><PERSON>", "system": "ANM12", "inventoryType": "Provider <PERSON>", "templateName": "File Template1 Edit", "fileDestination": "Daily", "frequency": "Daily", "startDate": "2022-03-28T00:00:00.000+00:00", "endDate": "2022-03-29T00:00:00.000+00:00", "transferTime": "30mins"}, "condition": {"condition": "and", "rules": [{"field": "lob", "operator": "Equal", "value": "anthem", "stat": false, "active": true}, {"field": "lob", "operator": "Equal", "value": "12", "stat": false, "active": true}]}}