{"BasicRoleDetails": [{"type": "text", "name": "<PERSON><PERSON><PERSON>", "label": "Role Name", "column": 1, "id": "<PERSON><PERSON><PERSON>", "disabled": false, "required": true, "value": "", "placeholder": "Enter Role Name"}, {"type": "textarea", "name": "description", "label": "Role Description", "column": 1, "id": "description", "disabled": false, "required": true, "value": "", "placeholder": "Enter description"}], "clientNames": [{"label": "Client", "type": "text", "name": "clientId", "column": "4", "disabled": true, "hidden": false, "required": true, "id": "client", "value": "", "placeholder": "Select Client Here.."}], "productNames": [{"label": "Assign Product", "type": "text", "closeOnSelect": true, "name": "productId", "required": true, "column": "4", "disabled": true, "hidden": false, "id": "product", "value": "", "placeholder": "Select a Product Here..."}], "businessDivision": [{"label": "Business Division", "type": "text", "closeOnSelect": true, "name": "businessDivision", "required": true, "column": "4", "disabled": true, "hidden": false, "id": "businessDivision", "value": "", "placeholder": "Select Business Division"}], "inventoryTypeDetails": [{"label": "Inventory Type", "type": "text", "name": "invType", "id": "invType", "column": "4", "disabled": true, "required": false, "value": ""}], "clientSiteJson": [{"id": "reminderDate", "label": "Reminder Date", "type": "date", "name": "reminderDate", "column": "4", "disabled": false, "required": true, "value": "", "placeholder": "Please select Reminder Date", "pickerType": "single"}, {"options": [{"name": "Active", "value": true}, {"name": "Inactive", "value": false}], "id": "status", "optionName": "name", "optionValue": "value", "label": "Status", "type": "radio", "name": "status", "column": "4", "disabled": true, "required": true, "value": "", "customClass": "form-radio-button"}, {"label": "Client Site", "id": "clientSiteRow", "text": "Offshore", "type": "switch", "name": "clientSite", "column": "4", "preLabel": "Onshore", "disabled": false, "required": true, "selectedVal": "Onshore", "value": ""}, {"options": [{"name": "Internal", "value": "internal"}, {"name": "External", "value": "external"}], "id": "teamSelect", "optionName": "name", "optionValue": "value", "label": "Select Team", "type": "radio", "name": "teamType", "column": "4", "disabled": false, "required": true, "value": "", "customClass": "form-radio-button"}]}