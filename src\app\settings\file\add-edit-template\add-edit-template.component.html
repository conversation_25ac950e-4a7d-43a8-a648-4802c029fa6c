<div class="card" style="margin: 13px;border: none;">
    <div class="row">
        <app-filebreadcum [schedulerBreadCrumbData]='schedulerBreadCrumbData'></app-filebreadcum>
    </div>
    <div class="row ml-0">
        <div class="col-6 row ml-0">
            <span class="dashboard-title">
                <a (click)="backToPreviousPage()">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="currentColor"  class="icon icon-tabler icons-tabler-filled icon-tabler-circle-chevron-left"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M17 3.34a10 10 0 0 1 5 8.66c0 5.523 -4.477 10 -10 10s-10 -4.477 -10 -10a10 10 0 0 1 15 -8.66m-3.293 4.953a1 1 0 0 0 -1.414 0l-3 3a1 1 0 0 0 0 1.414l3 3a1 1 0 0 0 1.414 0l.083 -.094a1 1 0 0 0 -.083 -1.32l-2.292 -2.293l2.292 -2.293a1 1 0 0 0 0 -1.414" /></svg>
                </a>&nbsp;&nbsp;&nbsp;{{pageType}} template
            </span>
            
        </div>
        <div class="autoSaveInCorner col-3" *ngIf="pageType == 'Create new'">
            <h5>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" aria-hidden="true" focusable="false" style="width: 1em; height: 1em; vertical-align: middle; fill: currentColor;">
                    <path d="M433.941 129.941l-83.882-83.882A48 48 0 0 0 315.118 32H48C21.49 32 0 53.49 0 80v352c0 26.51 21.49 48 48 48h352c26.51 0 48-21.49 48-48V140.882a48 48 0 0 0-14.059-33.941zM224 416c-44.112 0-80-35.888-80-80 0-44.112 35.888-80 80-80s80 35.888 80 80c0 44.112-35.888 80-80 80zm96-304c0 8.837-7.163 16-16 16H80c-8.837 0-16-7.163-16-16V80c0-8.837 7.163-16 16-16h224c8.837 0 16 7.163 16 16v32z"/>
                </svg>
                Auto save {{timeLeft | formatTime}}
            </h5>
        </div>
        
    </div>
    <br />
    <hr />




    <marketplace-stepper [headerConfig]="addEditHeaderConfig" [showFooterButtons]="true"
        [submitBtnLabel]="pageType == 'Edit' ? 'Update Template' :pageType == 'Create new'?'Create Template' : ''"
        (onStepChange)="stepchange($event)" (onSubmit)="submit()">

        <marketplace-step>
            <div class="card-body specificationBroder">

                <marketplace-dynamic-form #templateName *ngIf="showFormItem"
                    (onValueChanges)="formValue($event,'details')" [isSubmitNeeded]="false"
                    [formJSON]="simpleFormValidationJson">
                </marketplace-dynamic-form>
                <form>
                    <div class="row right">
                        <div class="col-4">
                            <div class="card">
                                <div class="card-body">
                                    <div class="card-title">
                                        <h6>
                                            <div class="italic-blue">File Type Details</div>
                                        </h6>
                                    </div>
                                    <div class="row form-group">
                                        <div class="col-6 delimiter-box">
                                            <div class="delimiter">
                                                <marketplace-dynamic-form *ngIf="showDelimiter" [id]="'delimiter'"
                                                    [isSubmitNeeded]="false" [formJSON]="delimiterRadio"
                                                    (onValueChanges)="delimiterChanges($event)">
                                                </marketplace-dynamic-form>
                                            </div>
                                            <br />
                                            <div class="row col-12 checkbox-group">
                                                <marketplace-dynamic-form
                                                    [ngClass]="{'npdelimiter':currentTemplate.delimiter.type==1}"
                                                    *ngIf="radioSelection"
                                                    (onValueChanges)="onCheckBoxSelection($event)"
                                                    [isSubmitNeeded]="false" [formJSON]="simpleRadio">
                                                </marketplace-dynamic-form>
                                                <div class="col-10" *ngIf="checkboxSelected=='other'">
                                                    <input type="text" name="otherValue"
                                                        class="form-control delimiterInput"
                                                        [(ngModel)]="currentTemplate.delimiter.value"
                                                        [disabled]="disableDelimiterOthrInputBox">
                                                    <br />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-5">
                                            <div class="fixedColumn">
                                                <marketplace-dynamic-form *ngIf="showFixedWidth" [id]="'fixedWidth'"
                                                    [isSubmitNeeded]="false" [formJSON]="fixedWidthRadio"
                                                    (onValueChanges)="fixedWidthChanges($event)">
                                                </marketplace-dynamic-form>
                                            </div>
                                            <br />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="card nc">
                                <marketplace-dynamic-form #templateNc (onValueChanges)="formNCValue($event,'nc')"
                                    [isSubmitNeeded]="false" *ngIf="showFormncItem"
                                    [formJSON]="simpleFormDYnamicValidationJson">
                                </marketplace-dynamic-form>
                            </div>
                        </div>
                    </div>
                </form>



            </div>
        </marketplace-step>

        <marketplace-step>
            <div class="card-body">
                <div class="col-12">
                    <marketplace-table *ngIf="isTableReady" [id]="'example-static-table'" [dataset]="mappingDataJSONs"
                        [dataRoot]="'src'" [columnDefinitions]="columnConfigInlineEdit" [isDraggableRowsNeeded]="true"
                        [isStaticDelete]="'false'" [draggableRowIconPosition]="1" [(tableModel)]="mappingRowdata"
                        [recreate]="tableRecreate" [updateExistingRow]="updateExistingRow"
                        (onAddRowClick)="onAddRowClick($event)" (beforeCellClick)="beforeCellClick($event)"
                        (onSave)="onSaveClick()" (onRowDelete)="deleteRowFromTable($event)"
                        (onCellValueChange)="onCellValueChange($event)">
                    </marketplace-table>

                </div>
            </div>
        </marketplace-step>

        <marketplace-step>
            <div class="card-body">
                <h5>Configuration Summary</h5>
                <h4>{{this.currentTemplateData.prefix+'_'+this.currentTemplateData.fileTmplName+'_'+this.currentTemplateData.sufix}}
                </h4>
                <br />
                <div class="row ml-0">
                    <div class="col-7 pl-0">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="row sub-header first-elem"> {{mappingDataJSON.length }} Fields choose to
                                    import and export</h5>
                                <div class="row">
                                    <div class="col-4 mb-3">
                                        <div>
                                            <h5>System</h5>
                                        </div>
                                        <div>
                                            {{sysNamePublish}}
                                        </div>
                                    </div>
                                    <div class="col-4 mb-3">
                                        <div>
                                            <h5>Export format</h5>
                                        </div>
                                        <div>
                                            {{currentTemplateData.fileFrmtTxt}}
                                        </div>
                                    </div>
                                    <div class="col-4 mb-3">
                                        <div>
                                            <h5>Template Type</h5>
                                        </div>
                                        <div *ngIf="currentTemplateData.tmpltType!= 'BOTH'">
                                            {{currentTemplateData.tmpltType}}
                                        </div>
                                        <div *ngIf="currentTemplateData.tmpltType == 'BOTH'">
                                            PROD and UAT

                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div>
                                            <h5>Inventory Type</h5>
                                        </div>
                                        <div>
                                            {{this.selectedInvType}}
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div>
                                            <h5>Product Name</h5>
                                        </div>
                                        <div>
                                            {{this.selectedProdName}}
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="row">
                                            <h5 class="sub-header">Naming convention</h5>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                Configured to
                                            </div>
                                            <div class="col-6">
                                                <h5>{{importExportStatus}}</h5>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                Prefix
                                            </div>
                                            <div class="col-6">
                                                <h5>{{currentTemplateData.prefix}}</h5>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                Sufix
                                            </div>
                                            <div class="col-6">
                                                <h5>{{currentTemplateData.sufix}}</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="row">
                                            <h5 class="sub-header">File Type Details</h5>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                Delimiter
                                            </div>
                                            <div class="col-6">
                                                <h5>{{currentTemplateData.delimiter}}</h5>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                Fixed Width
                                            </div>
                                            <div class="col-6">
                                                <h5>{{currentTemplateData.fixedWidthFlag}}</h5>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                Header
                                            </div>
                                            <div class="col-6">
                                                <h5>{{headerStatus}}</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6"><label>Created</label></div>
                                    <div class="col-6">
                                        <b>{{currentTemplate.Created}}</b>
                                    </div>
                                    <div class="col-6"><label>Created by</label></div>
                                    <div class="col-6">
                                        <b>{{currentTemplate.CreatedBy}}</b>
                                    </div>
                                    <div class="col-6"><label>Modified</label></div>
                                    <div class="col-6">
                                        <b>{{modifiedDate}}</b>
                                    </div>
                                    <div class="col-6"><label>Used</label></div>
                                    <div class="col-6">
                                        <b>Not used yet</b>
                                    </div>
                                    <div class="col-6"><label>Last Used</label></div>
                                    <div class="col-6">
                                        <b>Not used yet</b>
                                    </div>
                                    <div class="col-6"><label>Status</label></div>
                                    <div class="col-6">
                                        <b>{{status}}</b>
                                    </div>
                                    <div class="col-6"><label>Modified by</label></div>
                                    <div class="col-6">
                                        <b>{{currentTemplate.MOdifiedBy}}</b>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="publishbBtnGroup">


                </div>
            </div>
        </marketplace-step>

    </marketplace-stepper>

</div>
<marketplace-notification *ngIf="notificationOpen=='true'" [open]="notificationOpen" [header]="notificationHeader"
    [bodyText]="notificationBody" [type]="notificationType" [duration]="notificationDuration"
    [position]="notificationPosition">
</marketplace-notification>

<marketplace-popup [open]="cffLinkPopup" [size]="'small'" (onClose)="closePopup($event)">
    <div mpui-modal-header>CFF Link Constant Value</div>
    <div mpui-modal-body>
        <p class="cffLinkPopupTxtColor" *ngIf="cffLinkPopupTxtColor">Please Enter the Constant Value</p>
        <marketplace-input [label]="'Enter Value'" [id]="'custom-val'" [(ngModel)]="hcValue"></marketplace-input>

    </div>

    <div mpui-modal-footer>
        <marketplace-button [label]="'Close'" [type]="'secondary'" (onclick)="closePopup($event)">
        </marketplace-button>
        <marketplace-button [label]="'Save'" [type]="'primary'" (onclick)="saveValue($event)">
        </marketplace-button>
    </div>
</marketplace-popup>