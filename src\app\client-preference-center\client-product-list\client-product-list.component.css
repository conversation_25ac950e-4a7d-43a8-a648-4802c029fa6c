app-client-product-list marketplace-button #addProductBut{
    float: right;
}
app-client-product-list .modal-footer{
    margin-left: auto;
}
app-client-product-list .card {
    border: 3px solid rgba(5,5,6,0.125);
    border-radius: 0.95rem;
    padding-bottom: 30px;
}
app-client-product-list .table-title {
    
    font-style: normal;
    font-weight: 900 !important;
    font-size: 24px;
    line-height: 34px;
    color: #000000;
    padding-left: 14px;
}
app-client-product-list .pad-top
{
    padding-top: 22px;
}
app-client-product-list .btn-ruleadd{
    background: #5009B5;
    
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    color: #FFFFFF;
}
app-client-product-list .add-new-rules-link{
    color: #FFFFFF;
    text-decoration: none;
}
app-client-product-list .quickaction-title {
    float: left;
    width: 200px;
    height: 24px;
    left: 195px;
    top: 148px;
    
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 24px;
    color: #2453A6;
    padding: 25px 0px 20px 30px;
}
app-client-product-list .mb-3{
    margin-top: 1rem;
    margin-left: 1rem;
}
app-client-product-list .tp-bt-rem-1{
    margin-top: 1rem;
    margin-bottom: 1rem;
}
app-client-product-list .dashbord-card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 2rem 1rem 2rem 1rem;
}
app-client-product-list .fa-caret-right{
    font-size: 31px;
    color: #5009B5;
    float: right;
    padding-right: 20px;
}
app-client-product-list .card-title{
    
    font-style: normal;
    font-weight: normal;
    font-size: 18px;
    line-height: 17px;
    color: #161616;
}
app-client-product-list .setup-rule{
    margin-top: 5px;
}

app-client-product-list .fa-list, app-client-product-list .fa-chevron-circle-left{
    font-size: 30px;
    color: #5009B5;
}
app-client-product-list .fa-plus:before{
    color: #FFFFFF;
}
app-client-product-list .pd-left-30{
    padding-left: 30px;
}
app-client-product-list .pd-righ-10{
    padding-right: 10px;
}
app-client-product-list .pd-righ-20{
    padding-right: 20px;
}

/*Rules dashboard Table action menu dropdown*/
app-client-product-list .dropdown {
    position: absolute;
    background-color: gray;
    padding: 5px;
    outline: none;
    opacity: 0;
    /* min-width: 160px; */
    min-width: 100%;
    overflow: auto;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    background: #ffffff;
    box-shadow: 0px 0px 20px rgb(0 0 0 / 40%);
    border-radius: 4px;
    right: 10px;

}
app-client-product-list .dropdown a {
    color: black;
    padding: 5px 10px;
    text-decoration: none;
    display: block;
}
app-client-product-list input:not(:checked) ~ .dropdown {
    display: none;
}
app-client-product-list input:checked ~ .dropdown {
    opacity: 1;
    z-index: 100;
    transition: opacity 0.2s;
    z-index: 1;
}
app-client-product-list .three-dots:after {
    cursor: pointer;
    color: #444;
    content: "\2807";
    font-size: 20px;
    z-index: 999;
}
app-client-product-list .table-action-menu .fa-eye, app-client-product-list .table-action-menu .fa-edit, app-client-product-list .table-action-menu .fa-trash, app-client-product-list .table-action-menu .fa-plus {
    font-size: 20px;
    color: #5009B5 !important;
    padding-right: 15px;
}
app-client-product-list .table-action-menu .fa-plus:before {
    color: #5009B5 !important;
}
app-client-product-list .table-action-menu {
    border: 8.5px solid #ffffff;
}
app-client-product-list .fa-plus {
    width: 35px;
}



/* table overflow css overwrite*/
app-client-product-list .btn.product-dashboard {
    /* padding: 0.075rem 0.45rem !important; */
    color: white;
    font-weight:350;
}
app-client-product-list .btn.focus.product-dashboard, app-client-product-list .btn:focus.product-dashboard {
    outline: 0;
    box-shadow: 0 0 0 0rem;
}
app-client-product-list .btn.product-dashboard-big {
    padding: 0.075rem 0.1rem !important;
    color: white;
    font-weight:200;
}
app-client-product-list .btn.focus.product-dashboard-big, app-client-product-list .btn:focus.product-dashboard-big {
    outline: 0;
    box-shadow: 0 0 0 0rem;
}
app-client-product-list .dropdown-container{
    padding-top: 5px;
}
app-client-product-list .search-filter .operator.input-group-addon{
    display: none !important;
}

app-client-product-list .btn-active{
    background:#D9F5F5;
    width: 100%;
    border: 1px solid #00BBBA;
}
app-client-product-list .btn-pending {
    background:#E1EDFF;
    width: 100%;
    border: 1px solid #44B8F3;
}
app-client-product-list .btn-inactive {
    background: #F5F5F5;
    width: 100%;
    border: 1px solid #231E33;
}
app-client-product-list .btn.product-list {
    color: #000;
    margin-top: -5px
}
app-client-product-list .bundles-text-field{
    color: #2D6FE1;
    margin-right: 50px;
}
/* dashboard css end */

/* table overflow css overwrite*/

app-client-product-list .mar-10 {
    margin-top: 10px;
}

app-client-product-list .card-body-padding {
    padding: 0px 0px 0px 8px !important;
}