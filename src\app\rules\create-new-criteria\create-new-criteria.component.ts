import { Component, OnInit, ViewEncapsulation, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-create-new-criteria',
  templateUrl: './create-new-criteria.component.html',
  styleUrls: ['./create-new-criteria.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class CreateNewCriteriaComponent implements AfterViewInit{
  public headerText = "Add New Criteria";
  public isPriviousRedirectPage = true;
  levelIndicator: string = "Client Level"; //make it dynamic
  examplequery: any;
  showMessage: boolean;
  displayDuplicateMessage: boolean;
  displayMessage: string;
  displayStyle: string;
  breadcrumbDataset = [{  label: 'Home',  url: '/dashboard/'},{  label: 'Rules engine', url: '/rules'}, {  label: 'Frequently used criteria setup', url: '/rules/frequently-used-criteria'}, {  label: 'Add new criteria'}];
  tableRedraw: number;
      constructor(private router : Router) {}
      generalDetailsJson: any[] = [
        {
          type: "group",
          name: "General 1",
          label: "",
          column: "2",
          groupControls: [
            {
              label: "Criteria Group Name",
              type: "text",
              name: "criteriaGroupName",
              column: "1",
              disabled: false,
              value: "",
            },
            {
              options:[
                {
                  name:"Expiration/Lookback Rule",
                  value: "exp"
                },
                {
                  name:"On Hold",
                  value: "on_hold"
                },
                {
                  name:"No Recovery",
                  value: "no_recovery"
                },
                {
                  name:"Exclusion",
                  value: "exclusion"
                }
                
        
              ],
              optionName: "name",
              optionValue: "code",
              label: "Rule Type",
              group: "",
              type: "select",
              multiple: false,
              closeOnSelect: true,
              name: "ruleType",
              column: "1",
              groupColumn: "1",
              disabled: false,
            },
            
           
          ],
        },
        
       
        {
          type: "group",
          name: "General 2",
          label: "",
          column: "2",
          groupControls: [
            {
              label: "Criteria Group Description",
              type: "textarea",
              name: "ruledesc",
              column: "1",
              disabled: false,
              value: "",
              placeholder: "Enter details here..."
            },
          ],
        },
    
      
      ];
      
    
     _onListSelection(event){
     }
     
     _onItemAddition(event){
     }
     
     _onItemDeletion(event){
     }
     
     public dropquery = {
      "condition": "and",
      "rules": [
        {
          "field": "market",
          "operator": "Not Equal",
          "value": ["CA"],
          "static": true,
          "active": true
        }  
      ]
    };
     public dragdropconfig: any = {
      fields: {
    
        client: { name: 'Client', type: 'numeric' },
        conceptID: { name: 'ConceptID', type: 'text' }, //textarea
        memberID: { name: 'MemberID', type: 'text' },
        DOB: { name: 'DOB', type: 'calendar' }, //calendar function in the works as of 2/22/2022
    
        market: {
          name: 'Market',
          type: 'text',
          dataURL: '',
          dataRoot: 'ddl3',    
          key:'name',    
          id:'value',    
        },
        country: {
          name: 'Country',
          type: 'text',
          dataURL: '',
          dataRoot: 'ddl3',
          key:'name',
          id:'value', 
        },
    
        age: { name: 'age', type: 'text' }
      }
    }
    recentQueryList=[
      {
          "Name": "Criteria One",
          "Rule Type": "Global",
          "Rule Sub Type": "Global",
          "Created By": "Lakki Reddy",
          "Created Date": "02/22/2022",
          "ruleSet": {
              "condition": "and",
              "rules": [
                  {
                      "field": "DOB",
                      "operator": "Equal",
                      "value": "22",
                      "static": true,
                      "active": true
                  },
                  {
                      "field": "client",
                      "operator": "Less Than",
                      "value": "100",
                      "static": true,
                      "active": false
                  }
              ]
          }
      },
      {
          "Name": "Criteria Two",
          "Rule Type": "Global",
          "Rule Sub Type": "Global",
          "Created By": "Ajan Srinivas",
          "Created Date": "02/22/2022",
          "ruleSet": {
              "condition": "and",
              "rules": [
                  {
                      "field": "conceptID",
                      "operator": "contains",
                      "value": "55",
                      "static": true,
                      "active": true
    
                  },
                  {
                      "field": "memberID",
                      "operator": "like",
                      "value": "400",
                      "static": true,
                      "active": false
                  }
              ]
          }
      },
      {
        "Name": "Criteria Three",
        "Rule Type": "Regional",
        "Rule Sub Type": "Local",
        "Created By": "User 3",
        "Created Date": "02/22/2022",
        "ruleSet": {
            "condition": "or",
            "rules": [
                {
                    "field": "DOB",
                    "operator": "Less Than",
                    "value": "40",
                    "static": true,
                    "active": true
                },
                {
                    "field": "client",
                    "operator": "Greater Than",
                    "value": "12",
                    "static": true,
                    "active": false
                }
            ]
        }
    },
    {
      "Name": "Criteria Four",
      "Rule Type": "Regional",
      "Rule Sub Type": "Local",
      "Created By": "User 4",
      "Created Date": "02/22/2022",
      "ruleSet": {
          "condition": "or",
          "rules": [
              {
                  "field": "DOB",
                  "operator": "Greater Than",
                  "value": "40",
                  "static": true,
                  "active": true
              },
              {
                  "field": "client",
                  "operator": "like",
                  "value": "Smith",
                  "static": true,
                  "active": false
              }
          ]
      }
  },
    ];
    cancelCreate(){

    }
    validateCreate(){

      let isRuleLevel = false;
      this.examplequery.rules.forEach(element => {
       
        if(element.field == 'client' || element.field == 'conceptID') {
          isRuleLevel = true;
        }
      });
      if(!isRuleLevel){
        this.showMessage = true;
        this.displayDuplicateMessage = false;
        this.displayMessage = "You need to choose Client/Concept criteria before moving forward. \n Please click continue if you wish to proceed"
        this.displayStyle = "block";
      }
      else {
        this.showMessage = false;
        this.displayDuplicateMessage = true;
        this.displayStyle = "block";
        setTimeout(()=> this.tableRedraw = Date.now(), 100);
      }
    }
    dropRecentList(event){
    }
    returnHomeClick(): void {
      this.router.navigate(['product-catalog/rules'])
    }

    ngAfterViewInit(): void {
      const collection = document.querySelectorAll(
        'marketplace-dynamic-form button'
      );
      for (let i = 0; i < collection.length; i++) {
        collection[i].remove();
      }
    }
    
  }
  
