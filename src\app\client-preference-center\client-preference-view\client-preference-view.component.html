<div class="fixed-nav bg-gray">
  <div class="content-wrapper">
    <div class="container-fluid">
      <div class="card card-no-border">
        <div class="pd-30">
          <span class="dashbord-title"><a (click)="backToListPage()">
              <i class="fa fa-chevron-circle-left"></i></a> View Data Exchange</span>
        </div>
        <div class="row">
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">Product Name : </div>
            <div class="label-value">{{editData['productName']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">Business Division : </div>
            <div class="label-value">{{editData['businessDivision']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">Preference Name : </div>
            <div class="label-value">{{editData['preferenceName']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">Template Name : </div>
            <div class="label-value">{{editData['templateName']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">Concept State Name : </div>
            <div class="label-value">{{editData['conceptState']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">DBG Unit : </div>
            <div class="label-value">{{editData['dbgUnit']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">System : </div>
            <div class="label-value">{{editData['system']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">Inventory Type : </div>
            <div class="label-value">{{editData['inventoryType']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">File Destination : </div>
            <div class="label-value">{{editData['fileDestination']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">Frequency : </div>
            <div class="label-value">{{editData['frequency']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">File Name : </div>
            <div class="label-value">{{editData['fileName']}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">Start Date : </div>
            <div class="label-value">{{editData['startDate'] | date: 'MM/dd/yyyy'}}</div>
          </div>
          <div class="col-md-4 mar-0 uimp-key-values">
            <div for="name" class="label-title">End Date : </div>
            <div class="label-value">{{editData['endDate'] | date: 'MM/dd/yyyy'}}</div>
          </div>
          <div class="col-md mar-0 uimp-key-values">
            <div for="name" class="label-title">Time: </div>
            <div class="label-value">{{editData['transferTime']}}</div>
          </div>
        </div>
        <div [ngClass]="['cardqbdisabled']">
          <marketplace-query-builder [query]="dataExchangequery" [headerText]="'Criteria'" *ngIf="showLoader"
            [qbConfig]="dataExchangeconfig" [operators]="operators">
          </marketplace-query-builder>
        </div>
      </div>
    </div>
  </div>
</div>