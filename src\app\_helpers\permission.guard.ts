import { Injectable } from '@angular/core';
import { Router, CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { AuthService } from '../_services/authentication.services';
//C2P
// import { UserManagementApiService } from '../product-catalog/security/_services/user-management-api.service';
// import * as file from '../../assets/json/navigation.json';
// import { AuthService } from '../_services';

@Injectable({ providedIn: 'root' })
export class PermissionGuard implements CanActivate {
    constructor(
        private router: Router,
        private authService: AuthService,
        private cookieService: CookieService,
        //C2P public rolesListService: UserManagementApiService
    ) { }

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
        if (this.authService.checkHasWritePermission(state)) {
            return true;
        }
        return false;
    }
}