import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { forkJoin } from 'rxjs';
import { constants, tableColumnConfig } from '../users-constants';
import { cloneDeep } from 'lodash';
import { UserManagementApiService } from '../_services/user-management-api.service';
import { AuthService } from '../../_services/authentication.services';
import { UtilitiesService } from '../../_services/utilities.service';
import { LoaderService } from '../../_services/loader.service';


@Component({
  selector: 'app-view-user',
  templateUrl: './view-user.component.html',
  styleUrls: ['./view-user.component.sass'],
  encapsulation: ViewEncapsulation.None
})
export class ViewUserComponent implements OnInit {

  userFormJSON: any;
  newSkillJSON: any;
  isNewSkillsReady: boolean
  isTableGroupReady: boolean
  groupDataset: any = [];
  groupConfig: any = {};
  clientMasterSkills = [];

  breadcrumbDataset: any = [{
    label: 'Users Management',
    //C2P url: '/product-catalog/security/users'
    url: 'users'
  }, {
    label: 'View User'
  }]

  viewUsersDataset: any = [];
  viewUsersConfig: any = {};
  isCarelonAdmin: boolean = false;
  _formData: any;
  preSelectedRows: any;
  categoryData: any = [];
  masterData: any;
  userConfigDataset: any = [];
  userProfileSelection: { clientName: string, clientId: string, roleName: string, roleId: number };
  tableDataJSON: any;
  tableColumnConfig: any = tableColumnConfig;
  clientRoleMasterList: any;
  filteredRecords: any;
  distinctClients: any;
  distinctProducts: any;
  initialSkillJSON: any;
  constructor(private router: Router,
    private userManagementSvc: UserManagementApiService, private authService: AuthService,
    private dateService: UtilitiesService, private loaderService: LoaderService) { }

  /**
   * Navigate to the home page
   * @param event 
   */
  selectedLink(event): void {
    this.router.navigate([event.selected.url]);
  }



  ngOnInit() {
    let userRecord = JSON.parse(localStorage.getItem('user-row-selected')),
      userId = userRecord['userId'],
      userRegStatus = userRecord['registrationStatus'],
      _fetchUserDetails = this.userManagementSvc.getUserDetails(userId),
      _fetchUser = this.userManagementSvc.getAssetsJson('./assets/json/user-management/users-management/user-form.json'),
      _fetchSkills = this.userManagementSvc.getAssetsJson('./assets/json/user-management/users-management/skill-history.json'),
      //_fetchMasterData = this.userManagementSvc.getUserMasterData(),
      _fetchClientRoleList = this.userManagementSvc.getRolesList();

    //C2P this.userProfileSelection = {
    //   clientId: sessionStorage.getItem(constants.CLIENT_ID), clientName: sessionStorage.getItem(constants.CLIENT_NAME),
    //   roleId: this.authService.getSelectedUserRoleDetails().roleId, roleName: this.authService.getSelectedUserRoleDetails().roleName
    // };
    let roleDetails = JSON.parse(sessionStorage.getItem('roleDetails'));
    let clientDetails = JSON.parse(sessionStorage.getItem('clientDetails'));
    this.userProfileSelection = {
      clientId: clientDetails.clientId, clientName: clientDetails.clientName,
      roleId: <number>roleDetails.roleId, roleName: <string>roleDetails.roleName
    };

    this.isCarelonAdmin = (
      this.userProfileSelection.roleName == constants.CARELON_ADMINISTRATOR ||
      this.userProfileSelection.roleName == constants.PORTAL_ADMINISTRATOR ||
      this.userProfileSelection.roleName == constants.SIU_ADMIN);

    this.loaderService.show();
    forkJoin([_fetchUser, _fetchSkills, _fetchUserDetails, _fetchClientRoleList]).subscribe(
      ([userForm, skill, userDetails, clientRoleMasterList]) => {
        this.userFormJSON = userForm;
        this._formData = userDetails;
        this.initialSkillJSON = skill;
        //this.masterData = masterData;
        this.clientRoleMasterList = clientRoleMasterList;
        //this.getFinalObjectForSkills(masterData);
        this.newSkillJSON = cloneDeep(this.initialSkillJSON);

        if (this.isCarelonAdmin) {
          this.userFormJSON.find((x) => x.name == constants.INTERNAL_FLAG)[constants.DISABLED] = false;
        } else {
          this.userFormJSON = this.userFormJSON.filter(x => x.name !== constants.INTERNAL_FLAG);
        }
        if (!this._formData.status && userRegStatus === constants.INACTIVE) {
          this.userFormJSON.find(x => x.label == 'Deactivation Status').visible = true;
          this.userFormJSON.find(x => x.label == 'Deactivation Status').required = true;
          this.userFormJSON.find(x => x.label == 'Deactivation Reason').visible = true;
          this.userFormJSON.find(x => x.label == 'Deactivation Reason').required = true;
        }
        //below lines should be removed once API is ready with the discussed items
        this._formData[constants.MANAGER_NAME] = [this._formData[constants.MANAGER_NAME]];
        this.newSkillJSON.groupControls.forEach(e => e['disabled'] = true);
        //this.loadClientAndProductForSkills();
        if (userDetails.clientRole.length > 0) {
          this.populateUserRoleForm(userDetails.clientRole, clientRoleMasterList[constants.RESPONSE_DATA]);
        }
        else {
          this.populateNewUserRoleForm(clientRoleMasterList[constants.RESPONSE_DATA]);
        }
        let clientData: Array<{ clientName: any, roleName: string }> = [];
        const uniqueClient = [...new Set(userDetails.clientRole.map(obj => obj.clientName))];
        uniqueClient.forEach(data => {
          let roleName = [];
          let role = userDetails.clientRole.filter(c => c.clientName != null && c.clientName == data);
          role.forEach(row => {
            roleName.push(row.roleName);
          })
          let name = roleName.join(', ');
          let client: any = data;
          clientData.push({ clientName: client, roleName: name });

        })

        this.tableDataJSON = clientData;
        this.populateMasterDataOnForm();
        this.loaderService.hide();
      }
    )
  }

  /**
   * Method to set the selected Form Values
   */
  setFormData(): void {
    this._formData[constants.REMINDER_DATE] = this.dateService.getDbgDateFormat(this._formData[constants.REMINDER_DATE]);
    this.userFormJSON.forEach(e => {
      e['value'] = this._formData[e.name];
      e['selectedVal'] = this._formData[e.name];
      e['options']?.forEach(element => {
        element['enabled'] = false;
      });
      e['disabled'] = true;
      if (e.name == constants.CLIENT_SITE) {
        e[constants.OPTION_VALUE] = this._formData[constants.CLIENT_SITE] == constants.CLIENT_OFFSHORE ? true : false;
      }
      if (e.name == constants.MANAGER_NAME) {
        e[constants.OPTION_VALUE] = this._formData[e.name][0] != null ? this._formData[e.name][0] : '';
        e[constants.SELECTED_VALUE] = this._formData[e.name][0] != null ? this._formData[e.name][0] : '';
      }
      e.name == constants.STATUS ? (this._formData[e.name] == true ? e[constants.OPTION_VALUE] = constants.ACTIVE : e[constants.OPTION_VALUE] = constants.DEACTIVATE) : '';
    });


    this.filteredRecords?.map((data, index) => {
      data['skillJSON'] = cloneDeep(this.initialSkillJSON);
      let containsMaxClaimCount = false;
      let containsConceptState = false;
      let containsMemberBrand = false;
      let containsServiceProvider = false;

      data.skills.forEach(skill => {
        if (skill.skillType === constants.MAX_CLAIM_COUNT) {
          data['skillJSON'].groupControls.find(e => e.name === constants.MAX_CLAIM_COUNT).value = skill.skillValue;
          containsMaxClaimCount = true;
        }
        if (skill.skillType === constants.CONCEPT_STATE) {
          containsConceptState = true;
        }
        if (skill.skillType === constants.MEMBER_BRAND_Label) {
          containsMemberBrand = true;
        }
        if (skill.skillType === constants.SERVICE_PROVIDER_REGION_Label) {
          containsServiceProvider = true;
        }
      });
      if (containsMaxClaimCount) {
        data['skillJSON'].groupControls.find(e => e.name === constants.MAX_CLAIM_COUNT).visible = true;
      } else {
        data['skillJSON'].groupControls.find(e => e.name === constants.MAX_CLAIM_COUNT).visible = false;
      }
      if (containsConceptState) {
        data['skillJSON'].groupControls.find(e => e.name === constants.CONCEPT_STATE).visible = true;
      } else {
        data['skillJSON'].groupControls.find(e => e.name === constants.CONCEPT_STATE).visible = false;
      }
      if (containsMemberBrand) {
        data['skillJSON'].groupControls.find(e => e.name === constants.MEMBER_BRAND_Label).visible = true;
      } else {
        data['skillJSON'].groupControls.find(e => e.name === constants.MEMBER_BRAND_Label).visible = false;
      }
      if (containsServiceProvider) {
        data['skillJSON'].groupControls.find(e => e.name === constants.SERVICE_PROVIDER_REGION_Label).visible = true;
      } else {
        data['skillJSON'].groupControls.find(e => e.name === constants.SERVICE_PROVIDER_REGION_Label).visible = false;
      }

      if (data.prodId === constants.COORDINATION_OF_BENEFITS_ID) {
        data['skillJSON'].groupControls.forEach(skill => {
          (skill.id === constants.COB_SKILL_ID || skill.id === constants.COB_SUB_SKILL_ID) ? skill['visible'] = true : null;
          (skill.id === constants.CLAIM_SYSTEM_1 || skill.id === constants.CONCEPT_CATEGORY_SKILL_2 || skill.id === constants.BLUE_CARD_ID) ? skill['visible'] = false : null;
          skill['disabled'] = true;
        });
        data.skills.forEach(skill => {
          data['skillJSON'].groupControls.forEach(skill => {
            skill.options = this.updateSkillOpts(skill, data);
            if (data?.skills.find(element => skill.name?.toLowerCase() === element.skillType?.toLowerCase())) {
              skill['selectedVal'] = data?.skills.find(element => skill.name?.toLowerCase() === element.skillType?.toLowerCase())['values'];
              skill['disabled'] = true;
            }
          });

        });
      } else {
        data['skillJSON'].groupControls.forEach(skill => {
          (skill.id === constants.COB_SKILL_ID || skill.id === constants.COB_SUB_SKILL_ID) ? skill['visible'] = false : null;
          (skill.id === constants.CLAIM_SYSTEM_1 || skill.id === constants.CONCEPT_CATEGORY_SKILL_2 || skill.id === constants.BLUE_CARD_ID) ? skill['visible'] = true : null;
          skill['disabled'] = true;
        });
        data.skills.forEach(skill => {
          data['skillJSON'].groupControls.forEach(skill => {
            skill.options = this.updateSkillOpts(skill, data);
            if (data?.skills.find(element => skill.name?.toLowerCase() === element.skillType?.toLowerCase())) {
              skill['selectedVal'] = data?.skills.find(element => skill.name?.toLowerCase() === element.skillType?.toLowerCase())['values'];
              skill['disabled'] = true;
            }
          });
        });
      }
    });
    this.filteredRecords?.map(item => item.skillJSON = [item.skillJSON]);
    this.isNewSkillsReady = true;
    this.updateSkillsOptions();
  }

  /**
   * 
   * Update the skills based on client
   */
  updateSkillsOptions() {
    this.filteredRecords?.forEach((data, index) => {
      const skillJSON = this.newSkillJSON.length > 0 ? this.newSkillJSON[0]['groupControls'] : this.newSkillJSON['groupControls'];
      data['skillJSON'][0].groupControls.forEach(skill => {
        skill.options = [];
        skill.options = this.clientMasterSkills[index].skills.find(element => skill.name?.toLowerCase() === element.skillName?.toLowerCase() && element.prodId === data.prodId && element.clientId === data.clientId)?.values?.map(skillOptions => {
          return {
            label: skillOptions.value,
            value: skillOptions.id
          }
        }) ?? [];
      });
    });
  }


  /**
   * Method to update skill dropdown options
   */
  updateSkillOpts(skill, data): void {
    const clientId = this.distinctClients.find(item => item.id === data.clientId)?.id ? this.distinctClients.find(item => item.id === data.clientId)?.id : null,
      prodId = this.distinctProducts.find(item => item.id === data.prodId)?.id ? this.distinctProducts.find(item => item.id === data.prodId)?.id : null;

    skill.options = [];

    if (clientId && prodId && this.masterData.skills.find(item => item.prodId === prodId && item.clientId === clientId)) {
      skill.options = this.updateSkillsBasedOnClientProdGeneric(skill.name, prodId, clientId);
    } else {
      skill.options = [];
    }

    return skill.options;

  }
  /**
   * Method to update skill dropdown options based on any productId or any client id from the dropdown list of client and product respectively.
   */
  updateSkillsBasedOnClientProdGeneric(skillName, prodId, clientId) {
    let resultOpts = [];

    resultOpts = this.masterData.skills.find(element => skillName?.toLowerCase() === element.skillName?.toLowerCase() && element.prodId === prodId && element.clientId === clientId)?.values?.map(skillOptions => {
      return {
        label: skillOptions.value,
        value: skillOptions.id
      }
    }) ?? [];

    return resultOpts;
  }


  /**
  * @function loadClientAndProductForSkills Get unique clients and products to repopulate skills based on client and product selections
  */
  loadClientAndProductForSkills() {
    this.distinctClients = this.getDistinctClientsData(this.clientRoleMasterList[constants.RESPONSE_DATA]);
    this.distinctProducts = this.getDistinctProductsData(this.clientRoleMasterList[constants.RESPONSE_DATA]);
  }

  /**
   * @function getDistinctClientsData Gets distinct client data based on master data
   * @param clientRoleMasterData
   */
  getDistinctClientsData(clientRoleMasterData) {
    return clientRoleMasterData.filter(c => c.clientName != null).filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null)).map(({ clientName, clientId }) => ({ name: clientName, id: clientId }));
  }

  /**
   * @function getDistinctProductsData Gets distinct product data based on master data
   * @param clientRoleMasterData
   */
  getDistinctProductsData(clientRoleMasterData) {
    return clientRoleMasterData.filter(c => c.prodName != null).filter(function (a) {
      var key = a.prodId + '|' + a.prodName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null)).map(({ prodName, prodId }) => ({ name: prodName, id: prodId }));
  }

  getFinalObjectForSkills(masterData) {
    const result = this._formData.skills.reduce((acc, curr) => {
      const foundIndex = acc.findIndex(
        (item) =>
          item.clientId === curr.clientId && item.prodId === curr.prodId
      );
      if (foundIndex === -1) {
        acc.push({
          clientId: curr.clientId,
          clientName: curr.clientName,
          prodId: curr.prodId,
          prodName: curr.prodName,
          skills: [{
            skillType: curr.skillType,
            values: curr.values,
            skillValue: curr.skillValue
          }
          ]
        });
      } else {
        acc[foundIndex].skills.push({
          skillType: curr.skillType,
          values: curr.values,
          skillValue: curr.skillValue
        })
      }
      return acc;

    }, [])
    const filteredData = result.filter(
      (item, index, self) =>
        index === self.findIndex(
          (temp) =>
            temp.clientId === item.clientId && temp.prodId === item.prodId
        )
    )
    let clientMasterDataCalls = [];
    let responseMasterData = [];
    if (filteredData.length > 0) {
      filteredData.forEach((data, index) => {
        clientMasterDataCalls.push(this.userManagementSvc.getClientMasterData(data.clientId));
        responseMasterData.push("responseData" + data.clientId);
      });
      forkJoin(clientMasterDataCalls).subscribe(
        (responseMasterData) => {
          this.clientMasterSkills = responseMasterData;
          this.filteredRecords = filteredData;
          this.populateMasterDataOnForm(masterData);
        });
    }
    else {
      this.filteredRecords = filteredData;
      this.populateMasterDataOnForm(masterData);
    }
  }

  /**
 * method to populate master data on all dropdowns
 * @param masterData 
 */
  populateMasterDataOnForm(masterData?) {
     let _skillexperience = this.userManagementSvc.getAssetsJson(constants.USER_FORM_EXPERIENCE_JSON);
    _skillexperience.subscribe(data => {
      this.userFormJSON.find((x) => x.id == constants.EXPERIENCE_LVL_ID).options = data["skillexperience"];
    });
    this.loadSkillsDD(masterData);
  }

  createFinalRolePrepopulationData(userClientRoleList: any[]): any[] {
    return Object.values(userClientRoleList.reduce((acc, { clientId, prodId, clientName, prodName, roleId, roleName, businessDivision }) => {
      const key = `${clientId}: ${prodId}: ${businessDivision}`;
      if (!acc[key]) acc[key] = { clientId, prodId, clientName, prodName, role: [], businessDivision };
      acc[key].role.push({ roleId, roleName });
      return acc;
    }, {}));
  }

  /**
   * method to populate clients and roles form details   
   * @param userClientRoleList 
   * @param clientRoleMasterList
   */
  populateUserRoleForm(userClientRoleList: any[], clientRoleMasterList: any[]) {
    this.userConfigDataset = [];
    let userConfigDatasetJson: UsersClientRoleForm[] = [];

    let distinctUserClnt = userClientRoleList.filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    let distinctMasterClnt = clientRoleMasterList.filter(c => c.clientName != null).filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    let distinctProducts = clientRoleMasterList.filter(c => c.prodName != null).filter(function (a) {
      var key = a.prodId + '|' + a.prodName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    const rolePrepopulationData = this.createFinalRolePrepopulationData(userClientRoleList);


    rolePrepopulationData?.forEach(clnt => {
      let roleProdConfig = new UsersClientRoleForm();
      roleProdConfig.formJSON.push({
        optionName: constants.OPTION_NAME,
        optionValue: constants.OPTION_VALUE,
        label: "Client",
        type: constants.OPTION_TYPE_SELECT,
        multiple: false,
        closeOnSelect: true,
        name: "clientName",
        id: "clientId",
        column: "4",
        disabled: true,
        hidden: false,
        options: [].concat(distinctMasterClnt.map(mstClnt => {
          return {
            label: mstClnt.clientName,
            value: "" + mstClnt.clientId
          }
        })),
        selectedVal: "" + clnt.clientId,
      });

      roleProdConfig.formJSON.push({
        optionName: constants.OPTION_NAME,
        optionValue: constants.OPTION_VALUE,
        label: "Product",
        type: constants.OPTION_TYPE_SELECT,
        multiple: false,
        closeOnSelect: true,
        name: "prodName",
        id: "prodId",
        column: "4",
        disabled: true,
        hidden: false,
        options: [].concat(distinctProducts.map(mstClnt => {
          return {
            label: mstClnt.prodName,
            value: "" + mstClnt.prodId
          }
        })),
        selectedVal: "" + clnt.prodId
      });

      roleProdConfig.formJSON.push({
        optionName: constants.OPTION_NAME,
        optionValue: constants.OPTION_VALUE,
        label: constants.BUSINESS_DIVISION,
        type: constants.OPTION_TYPE_SELECT,
        multiple: false,
        closeOnSelect: true,
        name: constants.BUS_DIV_NAME,
        id: constants.BUS_DIV_ID,
        column: "4",
        disabled: true,
        hidden: false,
        visible: true,
        options: [
          {
            "label": constants.CSBD,
            "value": constants.CSBD
          },
          {
            "label": constants.GBD,
            "value": constants.GBD
          }
        ],
        selectedVal: "" + clnt.businessDivision
      });

      roleProdConfig.formJSON.push({
        optionName: 'label',
        optionValue: 'value',
        label: "Role",
        type: constants.OPTION_TYPE_SELECT,
        multiple: true,
        closeOnSelect: false,
        name: "roleName",
        id: "roleId",
        column: "4",
        disabled: true,
        hidden: false,
        options: [].concat(clientRoleMasterList.filter(c => c.clientId == clnt.clientId && c.prodId === clnt.prodId).map(c => {
          return {
            label: c.roleName,
            value: "" + c.roleId
          }
        })),
        selectedVal: [].concat(clnt.role.map(c => "" + c.roleId))
      });
      userConfigDatasetJson.push(roleProdConfig);
    });
    this.userConfigDataset = userConfigDatasetJson;
  }

  /**
   * method to populate clients and roles form details when no role is assigned to user  
   * @param clientRoleMasterList 
   */
  populateNewUserRoleForm(clientRoleMasterList: any[]) {
    this.userConfigDataset = [];
    let userConfigDatasetJson: UsersClientRoleForm[] = [];
    let roleProdConfig = new UsersClientRoleForm();
    let distinctMasterClnt = this.clientRoleMasterList.filter(c => c.clientName != null).filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    let distinctProducts = this.clientRoleMasterList.filter(c => c.prodName != null).filter(function (a) {
      var key = a.prodId + '|' + a.prodName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: "Client",
      type: constants.OPTION_TYPE_SELECT,
      multiple: false,
      closeOnSelect: true,
      name: "clientName",
      column: "4",
      disabled: true,
      hidden: false,
      id: "clientId",
      options: [].concat(distinctMasterClnt.map(mstClnt => {
        return {
          label: mstClnt.clientName,
          value: "" + mstClnt.clientId
        }
      }))
    });

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: "Product",
      type: constants.OPTION_TYPE_SELECT,
      multiple: false,
      closeOnSelect: true,
      name: "prodName",
      column: "4",
      disabled: true,
      hidden: false,
      id: "prodId",
      options: [].concat(distinctProducts.map(mstClnt => {
        return {
          label: mstClnt.prodName,
          value: "" + mstClnt.prodId
        }
      }))
    });

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: constants.BUSINESS_DIVISION,
      type: constants.OPTION_TYPE_SELECT,
      multiple: false,
      closeOnSelect: true,
      name: constants.BUS_DIV_NAME,
      id: constants.BUS_DIV_ID,
      column: "4",
      disabled: true,
      hidden: false,
      options: [
        {
          "label": constants.CSBD,
          "value": constants.CSBD
        },
        {
          "label": constants.GBD,
          "value": constants.GBD
        }
      ],
    });

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: "Role",
      type: constants.OPTION_TYPE_SELECT,
      multiple: true,
      closeOnSelect: false,
      name: "roleName",
      id: "roleId",
      column: "4",
      disabled: true,
      hidden: false,
      options: [],
      selectedVal: []
    });
    userConfigDatasetJson.push(roleProdConfig);
    this.userConfigDataset = userConfigDatasetJson;
  }

  /**
   * Common method to load Add skills drop downs
   * @param skill
   */
  loadSkillsDD(masterData) {

    this.setFormData();
  }

  /**
  * Going Back to previous page
  */
  backToPreviousPage() {
    this.router.navigate(['/product-catalog/security/users']);
  }
}

class UsersClientRoleForm {
  name: string;
  formJSON: FormJsonClass[] = [];
}

class FormJsonClass {
  options?: any[] = [];
  optionName?: string;
  optionValue?: string;
  label?: string;
  type?: string;
  multiple?: boolean;
  closeOnSelect?: boolean;
  name?: string;
  column?: string;
  disabled?: boolean;
  hidden?: boolean;
  value?: string;
  key?: string;
  id?: string;
  selectedVal?: any;
  visible?: boolean;
}