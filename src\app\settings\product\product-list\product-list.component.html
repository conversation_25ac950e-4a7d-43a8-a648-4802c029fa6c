<div class="fixed-nav bg-gray">   
      
        <marketplace-breadcrumb [dataset]="breadcrumbDataset" (onSelection)="breadcrumSelection($event)">
        </marketplace-breadcrumb>
        <div>
          <span class="table-title">Product List</span>
          <span class='btn-span'>
            <marketplace-button [label]="'Add New Bundle'" [type]="'primary'" [name]="'primary'" [enabled]="!isReadOnly"
              (onclick)="AddNewBundlesfun()">
            </marketplace-button>

          </span>
        </div>
        <div class="pd-left-30 table-container">
          <div class="table-note">
            <lable>Total Entries: {{totalEntries}}</lable>
            <p><b>Note:</b> Filter to find a specific product</p>
          </div>
            <marketplace-table [id]="'productlist-table'" [dataset]="dataURL" [rowHeight]="ruleDashbordTableRowhg"
              [headerRowHeight]="ruleDashbordTableHeaderhg" [columnDefinitions]="productColumnConfig"
              (onCellValueChange)="cellValueChanged($event)" (onCellClick)="cellClicked($event)" *ngIf="showTable"
              [isRowSelectable]="false" (onTableReady)="tableReady($event)" [dropdownOptions]="kebabOptions"
              (onDropdownOptionsClick)="onDropdownOptionsClick($event)">
            </marketplace-table>
        </div>
            
  
</div>
<app-ng-dynamic-breadcrumb></app-ng-dynamic-breadcrumb>
<div class="inventory-overlay-wrapper" *ngIf="showInventoryOverlay">
  <div class="inventory-overlay">
      <div class="inventory-overlay-header">
          <button class="inventory-overlay-close-icon" (click)="onCloseInventory(false)">
              <i class="fa fa-solid fa-chevron-right"></i>
          </button>
          <h4 class="inventory-overlay-header-title">Inventory Type Mapping</h4>
      </div>
      <div class="inventory-options-wrapper card">
        <marketplace-dynamic-form  *ngIf="isFormReady"
          [isSubmitNeeded]="false" 
          [formJSON]="inventoryFormData"
          
          (onValueChanges)="onFormChange($event)">
        </marketplace-dynamic-form>
        <span class="inventory-options-footer">
          <marketplace-button [label]="'Reset'" [type]="'secondary'" [name]="'secondary'" (onclick)="resetInventoryForm()">
          </marketplace-button>
          <marketplace-button [label]="'Submit'" [type]="'primary'" [enabled]="isFormValid && !isReadOnly" [name]="'primary'" (onclick)="onSubmitForm()">
          </marketplace-button>
      
        </span>
      </div>
      
  </div>
</div>