import { Component, OnInit, Input } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from 'src/app/_services/authentication.services';

@Component({
  selector: 'app-breadcrumbs-nav',
  templateUrl: './breadcrumbs-nav.component.html',
  styleUrls: ['./breadcrumbs-nav.component.css']
})
export class BreadcrumbsNavComponent implements OnInit {
  @Input() public headerText;
  @Input() public isPriviousRedirectPage=false;
  @Input() public breadcrumbDataset:any = [];
  
  
  constructor(private router: Router,
    public authService: AuthService) { }

  ngOnInit(): void {
  }

  breadcrumSelection(event){ 
    this.router.navigate([`${event.selected.url}`]);
  }

  backToPreviousPage(){
    window.history.back()
  }
}
