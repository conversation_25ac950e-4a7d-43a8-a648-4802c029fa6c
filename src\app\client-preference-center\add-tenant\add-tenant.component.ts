import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastService } from 'src/app/_services/toast.service';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { AuthService } from 'src/app/_services/authentication.services';
import { NOTIFICATION_CONSTANT } from 'src/app/_constants/notification_constants';

@Component({
  selector: 'app-add-tenant',
  templateUrl: './add-tenant.component.html',
  styleUrls: ['./add-tenant.component.css']
})
export class AddTenantComponent implements OnInit {

  public ViewTenantJson: any;
  TENANTS_JSON: any = "./assets/json/client-preference/add-edit-tenant-form.json";
  showLoader: boolean = false;
  enableForm: boolean = false;
  tenantId: number;
  clientId: Number;
  tenantDataById: any;
  @Output() DataEvent = new EventEmitter<string>();
  public popupDisplayStyle: any = 'none';
  isSubmitEnabled: boolean = false;
  selectedValuesForAllFieldsOfTenant: any;
  clientName: string;
  userProfile: any;
  clientCode: any;
  constructor(private clientApiService: ClientApiService, private route: ActivatedRoute, public authService: AuthService, private notificationService: ToastService,) {
    this.clientId = Number(this.route.snapshot.paramMap.get('clientId'));
    this.clientName = this.route.snapshot.paramMap.get('clientName');
  }
  ngOnInit(): void {
    this.userProfile = sessionStorage.getItem('userId');
    this.clientApiService.getTenantsAssetsJson(this.TENANTS_JSON).subscribe((jsonData) => {
      this.ViewTenantJson = jsonData;
      this.ViewTenantJson.forEach((x) => {
        switch (x.name) {
          case 'tenantId':
            x.disabled = false;
            x.required = true
            break;
          case 'offshoreAccess':
            x.value = 'Y';
            x.selectedVal = 'Y'
            break;
          case 'active':
            x.value = true;
            x.selectedVal = true
            break;
        }
      })
      this.enableForm = true;
    })
    this.clientApiService.getAllClientsMasterData().subscribe((data) => {
      this.clientCode = data.find((item) => item.clientId === this.clientId)?.clientCode;
    })
  }

  valuechange(event): void {

  }

  /**
   * triggers on change of any field inside form
   */
  getPreviousCurrentValues(event: any): void {
    if (!event.current.tenantId || !event.current.tenantCode || !event.current.tenantName || (event.current.tenantDesc && event.current.tenantDesc.length > 250) || (event.current.SecuLvlCd && event.current.SecuLvlCd.length > 10) || (event.current.tenantCode && event.current.tenantCode.length > 50) || (event.current.tenantName && event.current.tenantName.length > 100)) {
      this.isSubmitEnabled = false;
    } else {
      this.isSubmitEnabled = true;
      this.selectedValuesForAllFieldsOfTenant = event.current;
    }
  }

  /**
   * method to save tenant data
   */
  savePreference(): void {

    let payloadForApi = {
      "tenantId": this.selectedValuesForAllFieldsOfTenant.tenantId,
      "tenantCode": this.selectedValuesForAllFieldsOfTenant.tenantCode,
      "clientId": this.clientId,
      "clientCode": this.clientCode,
      "offshoreAccessibleIndicator": this.selectedValuesForAllFieldsOfTenant.offshoreAccess,
      "offshoreAccessibleIndicatorLogicText": this.selectedValuesForAllFieldsOfTenant.offshoreAccLogic,
      "securityLevelCode": this.selectedValuesForAllFieldsOfTenant.SecuLvlCd,
      "tenantName": this.selectedValuesForAllFieldsOfTenant.tenantName,
      "tenantDesc": this.selectedValuesForAllFieldsOfTenant.tenantDesc,
      "active": this.selectedValuesForAllFieldsOfTenant.active,
      "creatUserId": this.userProfile.responseData.userId,
      "lastUpdtUserId": this.userProfile.responseData.userId,
      "actionType": "Add"
    }

    this.clientApiService.addEditTenantData(payloadForApi).subscribe((data) => {
      if (data['responseCode'] == 200) {
        this.notificationPopUpMsg(NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER, `Tenant Added Successfully`);
        this.backToListPage();
      }
      else {
        this.notificationPopUpMsg(NOTIFICATION_CONSTANT.ERROR_NOTIFICATION_HEADER, data['responseData']);
      }
    },
      error => {
        this.notificationPopUpMsg(NOTIFICATION_CONSTANT.WARNING_NOTIFICATION_HEADER, error.responseData);
      });

  }

  /**
   * Generic method for showing notification message as per params
   * @param header - string, to show notification pop-up header
   * @param body - string, to show notification pop-up body
   */
  notificationPopUpMsg(header: string, body: string) {
    if (header === NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER.toString()) {
      this.notificationService.setSuccessNotification({
        notificationHeader: header,
        notificationBody: body,
      });
    }
    else {
      this.notificationService.setErrorNotification({
        notificationHeader: header,
        notificationBody: body,
      });
    }
  }

  /**
   *  Method closes the validation popup
   */
  closePopup(): void {
    this.popupDisplayStyle = 'none';
  }

  /**
   *  method takes back to preference list
  */
  backToListPage(): void {
    this.DataEvent.emit('back to list');
  }

}
