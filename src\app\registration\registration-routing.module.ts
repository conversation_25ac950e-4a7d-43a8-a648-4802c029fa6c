import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from 'src/app/_helpers/auth.guard';
import { RegistrationComponent } from './registration.component';


const routes: Routes = [
  {
    path: '',
    component: RegistrationComponent,
    canActivate:[AuthGuard]
  },
 
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RegistraionRoutingModule { }
