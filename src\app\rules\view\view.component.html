<div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>
<div class="breadcrumb-viewheading">
  <app-breadcrumbs-nav [headerText]="headerText" [isPriviousRedirectPage]="isPriviousRedirectPage"
    [breadcrumbDataset]="breadcrumbDataset">
  </app-breadcrumbs-nav>

  <div class="pd-5">
    <span class="level-indicator float-right">{{levelIndicator}}</span>
  </div>
</div>
<!-- <hr /> -->
<marketplace-tabs class="container col-12" [selectedTabIndex]="0" (onTabSelection)="onTabSelection($event)">
  <marketplace-tab [header]="'View Rule'">

    <div class="fixed-nav bg-gray mar-20" *ngIf="showForms">
      <div class="content-wrapper">
        <div class="container-fluid">
          <div>
            <div class="card-no-border mb-3">
              <div class="row">
                <div class="col-md-9">
                  <span class="card-title">Enter Below Details</span>
                  <marketplace-dynamic-form [formJSON]="relationSHJSON" [isSubmitNeeded]="false"
                    (onValueChange)="mapValuesFromMainToJson($event)">
                  </marketplace-dynamic-form>
                </div>
                <div class="col-md-3 wrapper" *ngIf="enableInventoryStatus">
                  <div class="search-input">
                    <marketplace-input [label]="labelName" [name]="inputname" [groupText]="groupIcon"
                      [(ngModel)]="selectedValue" (input)="giveDescriptionForStatus($event)" [enabled]="false"
                      ngDefaultControl>
                    </marketplace-input>
                    <div class="DescriptionProvider" *ngIf="openAccordion">
                      <marketplace-accordion [openPanel]="openPanelIndex">
                        <marketplace-panel [header]="'Status Description'">
                          <div class="label info">
                            <span class="btn-span statusHeader"><i
                                class="fa-thin fa-circle-info close-icon-color"></i></span> <br>
                            {{statusDescription}}
                          </div>
                        </marketplace-panel>
                      </marketplace-accordion>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <hr />
            <div class="card-no-border mb-3 tabs-padding">
              <marketplace-tabs class="container col-12" [selectedTabIndex]="0"
                (onTabSelection)="onTabSelection($event)">
                <marketplace-tab [header]="'General Details'">
                  <div class="mb-3 tabs-padding">
                    <marketplace-dynamic-form [formJSON]="generalDetailsJson" [isSubmitNeeded]="false">
                    </marketplace-dynamic-form>
                  </div>
                  <div class="mb-3 tabs-padding">
                    <span class="notification-title">Attention: <span class="attention-note">The following change will
                        effect the entire inventory. Please be mindful ! </span></span>
                    <hr />

                    <div class="row">
                      <span class="notification-title notification-font-wt">
                        Retro apply the rules to inventory
                      </span>
                      <div class="custom-control custom-switch">
                        <marketplace-switch class="switch floating" [enabled]="false" [value]="retroApply"
                          (onChange)="setRetro($event)">
                        </marketplace-switch>
                      </div>
                    </div>
                    <div class="row">
                      <span class="notification-title notification-font-wt">
                        Bypass Enhancement
                      </span>
                      <div class="custom-control custom-switch">
                        <marketplace-switch class="switch floating" [enabled]="false" [value]="bypassApply">
                        </marketplace-switch>
                      </div>
                    </div>
                    <div class="row">
                      <span class="notification-title notification-font-wt">
                        Apply the rules to :&nbsp;&nbsp; Line level
                      </span>
                      <div class="custom-switch ">
                        <marketplace-switch class="switch floating" [enabled]="false" [value]="headerLevel"
                          (onChange)="setLevel($event)">
                        </marketplace-switch>
                      </div>
                      <span class="notification-title notification-font-wt">
                        Header level
                      </span>
                    </div>
                  </div>
                </marketplace-tab>
                <marketplace-tab [header]="'Additional Details'">
                  <div class="mb-3 pd-25">
                    <marketplace-dynamic-form [formJSON]="additionalDetailsJson" [isSubmitNeeded]="false">
                    </marketplace-dynamic-form>
                  </div>
                  <div class="mb-3 pd-25">
                    <h5>File details</h5>
                    <hr />
                    <marketplace-table [id]="'file-upload-details-table-view'" [dataset]="dataJSON"
                      [isRowSelectable]="false" [columnDefinitions]="columnConfigforFileUploadtable"
                      [rowHeight]="ruleDashbordTableRowhg" [headerRowHeight]="ruleDashbordTableHeaderhg"
                      [dropdownOptions]="kebabOptions" (onCellValueChange)="cellValueChanged($event)"
                      (onCellClick)="cellClicked($event)" (onTableReady)="tableReady($event)"
                      (onDropdownOptionsClick)="moveToOptionSelected($event)" *ngIf="isFileUploadTabledata">
                    </marketplace-table>
                  </div>
                </marketplace-tab>
              </marketplace-tabs>
            </div>
          </div>

        </div>
      </div>
    </div>
    <div class="fixed-nav bg-gray mar-10" *ngIf="showForms">
      <div class="content-wrapper">
        <div class="container-fluid">
          <div class="row">
            <div class="ruleDefinition">
              <span class="card-title">Rule Definition</span>
            </div>
            <div class="col-md-9 mar-20" style="pointer-events: none;">
              <div class="chip-Container">
                <ng-container *ngIf="compatibleJsonForConcepts.length > 0">
                  <label *ngFor="let chip of compatibleJsonForConcepts" class="chips">
                    <span class="chips-text" title="{{ chip }}">Concept: {{ chip }}</span>
                    <span class="close-button" (click)="closeStateChip(chip)">&times;</span>
                  </label>
                </ng-container>
              </div>
              <marketplace-dynamic-form *ngIf="isConceptDataReady" [formJSON]="querySpecificationJson"
                [isSubmitNeeded]="false" (onValueChanges)="ruleLevelSelectionJsonJson($event)">
              </marketplace-dynamic-form>
            </div>
          </div>
          <marketplace-dynamic-form *ngIf="showCustomSqlJson" [formJSON]="customSqlJson" [isSubmitNeeded]="false">
          </marketplace-dynamic-form>
          <div class="multi-criteria-div-align">
            <marketplace-button *ngIf="multipleCriteriaRule" [label]="'Download File'" [type]="'primary'"
              [name]="'primary'" (onclick)="DownloadMultiCriteriaFile()">
            </marketplace-button>
          </div>
          <div class="pad-1rem">
            <ng-container *ngIf="!showQueryBuilderComponents">
              <div class="mar-30">
                <marketplace-textarea [id]="'customSql'" [readonly]="true" [label]="'Custom Query'"
                  [placeholder]="'Enter SQL here...'" (modelChange)="_onSqlChange($event)" [model]="customSql"
                  ngDefaultControl>
                </marketplace-textarea>
              </div>
            </ng-container>
          </div>
          <div class="pad-1rem-pointer">
            <ng-container *ngIf="showQueryBuilderComponents">
              <marketplace-query-builder [isReadableQueryRequired]=true [switchToggleNames]="switchToggleNames"
                [query]="dropquery" [qbConfig]="dragdropconfig" [operators]="operators"
                (onDropquery)="dropRecentList($event)">
              </marketplace-query-builder>
            </ng-container>
          </div>

        </div>
      </div>
    </div>
  </marketplace-tab>
  <marketplace-tab [header]="'Rule History'">
    <app-rule-history *ngIf="showHistory" [ruleId]="ruleId" [screenName]="'view'" [ruleLevel]="rule.rule_level"></app-rule-history>
  </marketplace-tab>
</marketplace-tabs>