import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewEncapsulation,
  ViewChild,
  HostListener,
} from '@angular/core';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { forkJoin } from 'rxjs';
import { UtilitiesService } from '../../_services/utilities.service';
import { ActivatedRoute, Router } from '@angular/router';
import { default as dataExchange } from './../../../assets/json/client-preference/dataExchange.json';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { QueryBuildFormatterService } from '../../_services/query-build-formatter.service';
import { list, QBOperators } from '../constant';
import { emptyProduct, nullValueProduct, typeMapping, operatorMap, rules, exchangeObj } from '../client-preference';
import { ToastService } from 'src/app/_services/toast.service';

const CLAIM_ANOMALY_DETECTION = 'Claim Anomaly Detection';
@Component({
  selector: 'app-client-preference-edit',
  templateUrl: './client-preference-edit.component.html',
  styleUrls: ['./client-preference-edit.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class ClientPreferenceEditComponent implements OnInit {
  public dataExchangeJson: any;
  startDateValue: string | any = '';
  endDateValue: string | any = '';
  datePickerFormat: string = 'DD-MM-YYYY';
  datePickerName: string = 'test';
  templateName: any;
  fileDestin: any;
  freqency: any;
  isdisabled: boolean = false;
  time: any;
  productId: any;
  productName: any;
  preferenceName: any;
  dbgUnit: any;
  businessDivision: any;
  system: any;
  inventoryType: any;
  fileName: string = "";
  notificationReady: boolean;
  notificationOpen: any = false;
  notificationHeader: string;
  notificationBody: string;
  notificationPosition: any;
  notificationDuration: any;
  notificationType: any;
  enableFormButtton: boolean = true;
  public popupDisplayStyle: any = 'none';
  showLoader: boolean = false;
  templateType: any = [];
  products: any = [];
  templates: any = [];
  systems: any = [];
  dbgUnits: any = [];
  systemDependentFieldsData: any = [];
  dataExchangeconfig = {
    fields: {}
  };
  dataExchangequery: any;
  enableForm = false;
  clientObject: any;
  formValidation: any;
  clientId: any;
  clientName: any;
  inventoryTypes: any = [];
  enableSystemValidation = true;
  selectedTemplate: any;
  @Input() editDataFromList: any;
  @Output() DataEvent = new EventEmitter<string>();
  editData: any;
  @ViewChild('formRef') formRef: any;
  isTemplateReady: boolean = false;
  showCriteriaButton: boolean = false;
  productMappingFileTemplatesList: any;
  timeFrameDataset: any;
  timeFrequency: string = "";
  dataExchangeTimeFormJson: any = [{
    label: "Time",
    type: "time",
    name: "time",
    required: true,
    column: "1",
    disabled: false
  }];
  maxTargetSelection: number = 5;
  isFrequencyDaily: boolean = false;
  multiTimeFrameValidation: boolean = false;
  operators: any = QBOperators;

  constructor(public el: ElementRef, private clientApiService: ClientApiService, private alertService: ToastService, private dateService: UtilitiesService,
    private route: ActivatedRoute, private queryBuildServer: QueryBuildFormatterService,
    private router: Router) { }

  ngOnInit(): void {
    this.clientName = this.route.snapshot.paramMap.get('clientName');
    this.clientId = Number(this.route.snapshot.paramMap.get('clientId'));
    this.getMasterData();
  }


  /**
   * Calls the service method to get master data from API
   */
  getMasterData(): void {
    this.showLoader = true;
    const obv1 = this.clientApiService.getMasterData(this.clientId);
    const obv2 = this.clientApiService.getClientPreferencesViewDataExchange(this.editDataFromList.preferenceId);
    forkJoin([obv1, obv2]).subscribe(
      ([[products, templates, DBGUnits], DEData]) => {
        this.products = products;
        this.templates = templates.filter((elem) => {
          return elem.actvInd === true;
        });
        this.dbgUnits = DBGUnits;
        if (DEData["condition"]["rules"]?.length) {
          for (const obj of DEData["condition"]["rules"]) {
            obj['static'] = obj['stat'];
            delete obj['stat'];
          }
        }
        this.constructQbConfigFields(this.editDataFromList.inventoryType, this.editDataFromList.productName, DEData["condition"])
        this.editData = DEData['dataExchange'];
        this.clientObject = this.editData;
        this.clientObject['conditions'] = this.dataExchangequery;
        this.populateMasterDateOnForm();
        this.showLoader = false;
      },
      (error) => {
        this.populateMasterDateOnForm();
        this.showLoader = false;
      }
    );
  }

  /**
   * Changing the query builder structure to be sync with API
  */
  modifyQBuilderStructure(qbQuery) {
    let parsed = JSON.parse(JSON.stringify(qbQuery), function (k, v) {
      switch (k) {
        case "operator":
          return operatorMap[v] ? operatorMap[v] : v;
          break;
        case "static":
          this.stat = v;
          break;
        case "config":
        case "operatorList":
        case "delete":
        case "fieldsList":
        case "fieldsMapList":
        case "customfieldsList":
        case "tabsList":
          delete qbQuery[k];
          break;
        default:
          return v;
      }
    });
    parsed = this.queryBuildServer.getQueryOutput(parsed, this.dataExchangeconfig?.fields)
    return parsed;
  }

  /**
   * Methods populates the data on form 
   */
  populateMasterDateOnForm(): void {
    this.dataExchangeJson = cloneDeep(dataExchange);
    this.bindDropdown();
    this.bindData(this.editData);
  }

  /* 
  * method is used to bind dropdown for product,template,dbgunit,system columns 
  * And relationship of system with inventory  
  */
  bindDropdown(): void {
    if (this.clientId != list.ANTHEM_CLIENT_ID) {
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).disabled = true;
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).required = false;
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).placeholder = "";
    }
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.PRODUCT_NAME).options = this.products;
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.PRODUCT_NAME)['disabled'] = true;
    this.constructRelationForProductFileTemplate();
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.PRODUCT_NAME).relationship = this.productMappingFileTemplatesList;
    let productId = this.products.find((x) => x.productName == this.editData.productName)?.productId;
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.TEMPLATE_NAME).options = this.templates.filter(x => x.prodId == productId).map(template => ({ "fileTmplName": template.fileTmplName }));
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.DBG_UNIT).options = this.dbgUnits;

  }

  /**
   * Method to construct related dropdown file templates for product
   */
  constructRelationForProductFileTemplate(): void {
    this.productMappingFileTemplatesList = [];
    let relationshipNullObj = nullValueProduct;
    let relationshipSelectedValObj = emptyProduct;
    this.productMappingFileTemplatesList.push(relationshipSelectedValObj);
    this.productMappingFileTemplatesList.push(relationshipNullObj);
    this.products.forEach(product => {
      let relationshipObj = {
        updateDataset: [
          {
            id: list.TEMPLATE_NAME, dataset: this.templates.filter(x => x.prodId == product.productId).map(template => ({ "fileTmplName": template.fileTmplName }))
          }],
        when: product.productName
      }
      this.productMappingFileTemplatesList.push(relationshipObj);
    });
  }

  /**
   * Method populates form data
   * @param data 
   */
  bindData(data): void {
    if (data.frequency == "Daily") {
      let userConfigDatasetJson: timeFrameForm[] = [];
      const timeStr = data.transferTime.split(';')
      timeStr?.forEach(timeFrame => {
        let roleProdConfig = new timeFrameForm();
        roleProdConfig.formJSON.push({
          label: "Time",
          type: "time",
          name: "time",
          required: true,
          column: "1",
          disabled: false,
          value: timeFrame
        });
        userConfigDatasetJson.push(roleProdConfig);
      });
      this.timeFrameDataset = userConfigDatasetJson;
      this.isFrequencyDaily = true;
    }
    else {
      this.dataExchangeTimeFormJson[0].value = data.transferTime;
      this.isFrequencyDaily = false;
    }
    this.selectedTemplate = this.templates.find((elem) => {
      return data.templateName == elem.fileTmplName
    });
    if (this.selectedTemplate) {
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.TEMPLATE_NAME).selectedVal = this.templateName = data.templateName;
    }

    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.PRODUCT_NAME).selectedVal = this.productName = data.productName;
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.PREFERENCE).value = this.preferenceName = data.preferenceName;
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.DBG_UNIT).selectedVal = this.dbgUnit = data.dbgUnit;
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.FILE_NAME).value = this.fileName = data.fileName;
    if (this.clientId == list.ANTHEM_CLIENT_ID) {
      if (this.productName === list.CLAIM_ANOMALY_DETECTION || this.productName === CLAIM_ANOMALY_DETECTION) {
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).selectedVal = this.businessDivision = data.businessDivision;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).disabled = false;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).required = true;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).placeholder = list.BUS_DIV_PLACEHOLDER;
      } else {
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).disabled = true;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).required = false;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).placeholder = null;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).selectedVal = undefined;
      }
    }
    if (data?.conceptState) {
      data.conceptState == list.UAT || data.conceptState == list.PROD ? this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).disabled = true : this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).disabled = false;
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).selectedVal = data.conceptState;
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).disabled = true;
      this.templateType.push(data.conceptState);
    }
    if (data.templateName) {
      this.selectedTemplate = this.templates.find((elem) => { return data.templateName == elem.fileTmplName })
      if (this.selectedTemplate?.tmpltType == list.PROD_UAT) { this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).disabled = false }
    }
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.FILE_DESTINATION).value = data.fileDestination;
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.FREQUENCY).selectedVal = data.frequency;

    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.START_DATE).value = this.dateService.getDbgDateFormat(data.startDate);
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.END_DATE).value = this.dateService.getDbgDateFormat(data.endDate);
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.END_DATE).minDate = this.dateService.getFutureDate(this.dataExchangeJson[0].groupControls.filter((x) => x.name == list.START_DATE)[0].value, 1, list.DATE__FORMAT);
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.INVENTORY_TYPE).value = data.inventoryType;
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.SYSTEM).value = data.system;
    this.enableForm = true;

  }
  /**
  * Method will be triggered on Query Builder Rule Set Addition 
  * @param event 
  * @param status 
  */
  onQueryBuilderRuleSetAddition(event) {
    this.enableFormButtton = false;
  }

  /**
  * Method will be triggered on Query Builder value change
  * @param event 
  * @param status 
  */
  onQueryBuilderFieldChange(event) {
    this.enableFormButtton = false;
  }

  /**
 * Method will be triggered on timepicker form change
 * @param event 
 */
  onTimepickerChange(event) {
    this.timeFrequency = event.value.time;
    this.enableFormButtton = this.formValidation[list.STATUS] == list.VALID ? false : true;
  }

  /**
   * Method will be triggered on form repeater form change
   * @param event 
   */
  formRepeaterValueChange() {
    let count = 0;
    this.timeFrameDataset.forEach(element => {
      if (element.ngModel.value.time)
        count++;
    });
    if (this.timeFrameDataset.length == count) {
      this.multiTimeFrameValidation = true;
      this.enableFormButtton = this.formValidation[list.STATUS] == list.VALID ? false : true
    }
    else {
      this.multiTimeFrameValidation = false;
      this.enableFormButtton = true
    }
  }

  /**
   * Method will be triggered on form value change
   * @param event 
   * @param status 
   */
  valuechange(event, status): void {
    if (status && (!this.formValidation || this.formValidation.value.exchange.templateName != event.value.exchange.templateName)) {
      this.selectedTemplate = this.templates.find((elem) => {
        return event.value.exchange.templateName == elem.fileTmplName
      })
      this.formValidation = cloneDeep(event);
      if (this.selectedTemplate) {
        event.controls.exchange.patchValue({
          inventoryType: this.selectedTemplate.invTypeName,
          system: this.selectedTemplate.systemName
        });
        if (this.selectedTemplate?.tmpltType != list.PROD_UAT) {
          event.controls.exchange.patchValue({ conceptState: this.selectedTemplate.tmpltType })
        }

        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.INVENTORY_TYPE).value = this.selectedTemplate.invTypeName;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.SYSTEM).value = this.selectedTemplate.systemName;
      } else {
        event.controls.exchange.patchValue(exchangeObj);
      }
    } else {

      if (event.value.exchange.startDate >= this.formValidation.value.exchange.endDate && this.formValidation.value.exchange.startDate != event.value.exchange.startDate) {
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.END_DATE).value = null;
        event.value.exchange.endDate = null;
        event[list.STATUS] = list.INVALID;
      }
      else {
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.END_DATE).value = event.value.exchange.endDate;
      }
      this.formValidation = cloneDeep(event);
    }
    if (this.selectedTemplate?.tmpltType == list.UAT || this.selectedTemplate?.tmpltType == list.PROD) {
      this.templateType.push(this.selectedTemplate?.tmpltType);
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).disabled = true;
    }
    else {
      this.templateType = [];
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).disabled = false;
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).options = [{ name: list.UAT, id: 1 }, { name: list.PROD, id: 2 }];
    }
    this.templateType.push(event.value.exchange.conceptState);
  }

  /**
   *  Method to get Product Id for sending to Data platform
  */
  getProductId(): any {
    this.products.forEach(e => {
      if (e.productName == this.clientObject['productName']) {
        return this.productId = e['productId'];
      }
    });
  }

  /**
   *  method does service call to update the preference
  */
  UpdatePreference(): void {
    /**The following lines will be needed in future, 
     * so commenting them down */
    //let timeList = [];
    if (this.isFrequencyDaily == true) {
      this.timeFrequency = "";
      this.timeFrameDataset.forEach(data => {
        this.timeFrequency += data.ngModel.value.time + ";";
        //timeList.push(data.ngModel.value.time);
      });
      this.timeFrequency = this.timeFrequency.slice(0, -1);
    }
    // else if (this.timeFrequency) {
    //   timeList.push(this.timeFrequency);
    //}
    this.notificationReady = false;
    this.getProductId();
    let clientObject;
    if (this.formValidation) {
      if (this.formValidation[list.STATUS] == list.VALID) {
        clientObject = this.formValidation.value.exchange;
      } else {
        this.displayNotification(list.WARNING, list.WARNING_MESG);
        return;
      }
    }
    else {
      clientObject = this.editData;
      clientObject[list.TIME] = this.editData[list.TRANSFER_TIME];
      delete clientObject.dbName;
      delete clientObject.dbSchemaName;
      delete clientObject.transferTime;
    }

    const startIsBeforeEnd = this.dateService.checkDateLatest(clientObject[list.START_DATE], clientObject[list.END_DATE]);
    if (startIsBeforeEnd) {
      clientObject[list.CLIENT_ID] = this.clientId;
      clientObject[list.CLIENT_NAME] = this.clientName;
      clientObject[list.ACTIVE_INDICATOR] = 1;
      clientObject[list.PREFERENCE_ID] = this.editData.preferenceId;
      clientObject[list.CONCEPT_STATE] = this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).selectedVal;
      clientObject[list.SYSTEM_ID] = this.selectedTemplate.systemId;
      clientObject[list.START_DATE] = moment(clientObject[list.START_DATE]).format('YYYY-MM-DD');
      clientObject[list.END_DATE] = moment(clientObject[list.END_DATE]).format('YYYY-MM-DD');
      clientObject[list.PRODUCT_ID] = this.productId;
      clientObject[list.TEMPLATE_ID] = this.selectedTemplate.fileTmplId;
      clientObject[list.INVENTORY_TYPE_ID] = this.selectedTemplate.invTypeId;
      clientObject[list.PRODUCT_NAME] = clientObject[list.PRODUCT_NAME] == list.DMS ? list.CAD : clientObject[list.PRODUCT_NAME];
      var timeValuepair = { time: this.timeFrequency };
      clientObject = { ...clientObject, ...timeValuepair };
      const rules = this.dataExchangequery?.rules?.map(elem => {
        const item = {
          ...elem,
          stat: elem.static
        }
        return item;
      });
      if (rules != undefined) {
        clientObject[list.CONDITIONS] = {
          condition: this.dataExchangequery.condition,
          rules: rules
        };
        clientObject[list.CONDITIONS] = this.modifyQBuilderStructure(clientObject[list.CONDITIONS]);
      }
      this.clientApiService.createEditAddDE(clientObject).subscribe((data: any) => {
        if (data.statusCodeValue == 200) {
          this.alertService.setSuccessNotification({
            notificationHeader: list.SUCCESS,
            notificationBody: list.SUCCESS_EDIT_MESG,
          });
          this.showLoader = false;
          this.backToListPage();
        }
        else if (data.statusCodeValue == 500) {
          this.displayNotification(list.FAIL, data.body)
        }

      }, (err: any) => {
        this.displayNotification(list.FAIL, err)
      })
    } else { this.displayNotification(list.WARNING, list.WARNING_MESG) }

  }

  /**
   * Method to display notification on success or failure
   * @param header 
   * @param body 
   */
  displayNotification(header: string, body: string): void {
    this.alertService.setErrorNotification({
      notificationHeader: header,
      notificationBody: body,
    });
    this.showLoader = false;
  }
  /**
   *  method takes back to preference list
  */
  backToListPage(): void {
    this.DataEvent.emit('back to list');
  }

  /**
   *  method closes the validation popup
  */
  closePopup(): void {
    this.popupDisplayStyle = 'none';
  }

  /**
   * Method to get the previous and current values to get cff fields
   * @param event 
   */
  getPreviousCurrentValues(event: any): void {
    if (event.current.exchange.frequency == list.DAILY)
      this.multiTimeFrameValidation = true;
    else
      this.timeFrequency = this.dataExchangeTimeFormJson[0].value;
    if (event.current.exchange.frequency != list.DAILY && this.timeFrequency)
      this.enableFormButtton = this.formValidation[list.STATUS] == list.VALID ? false : true;
    else if (event.current.exchange.frequency == list.DAILY && this.multiTimeFrameValidation == true)
      this.enableFormButtton = this.formValidation[list.STATUS] == list.VALID ? false : true;
    else
      this.enableFormButtton = true;
    if (event.current.exchange.frequency == list.DAILY && event.previous.exchange.frequency != event.current.exchange.frequency) {
      this.timeFrameDataset = [{
        formJSON: [{
          label: "Time",
          type: "time",
          name: "time",
          required: true,
          column: "1",
          disabled: false
        }]
      }];
      this.isFrequencyDaily = true;
      this.enableFormButtton = true;
    }
    else if (event.current.exchange.frequency != list.DAILY) {
      this.isFrequencyDaily = false;
    }
    if (event.previous.exchange.templateName != event.current.exchange.templateName) {
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).selectedVal = this.selectedTemplate?.tmpltType == list.PROD_UAT ? undefined : this.selectedTemplate?.tmpltType;
      this.constructQbConfigFields(this.selectedTemplate.invTypeName, this.productName, null, "templateChange");
    }
  }

  /**
   * Method to construct the query builder config
   * @param invType 
   * @param prodName 
   */

  constructQbConfigFields(invType: string, prodName: string, query: any, mode?: string): void {
    try {
      this.isTemplateReady = false;
      this.clientApiService.getCffFieldsByInventoryTypeProduct(invType, prodName).subscribe(data => {
        if (Array.isArray(data) && data.length) {
          this.dataExchangeconfig.fields = {};
          this.dataExchangeconfig.fields = this.modifyQBConfig(data);
          if (!query) {
            let rulesetObj =
            {
              "condition": "or",
              "rules": rules
            }
            rulesetObj.rules[0].field = data[0].clmnNm;
            if (mode) {
              this.dataExchangequery = rulesetObj;
              this.isTemplateReady = true;
              this.showCriteriaButton = false;
            }
            else {
              this.dataExchangequery = {};
              this.isTemplateReady = false;
              this.showCriteriaButton = true;
            }
            this.alertService.setInfoNotification({
              notificationHeader: list.INFO,
              notificationBody: list.NOTIFICATION_MESSAGE,
            });
          }
          else {
            this.dataExchangequery = this.queryBuildServer.modifyStructureToShowQB(query);
            this.isTemplateReady = true;
            this.showCriteriaButton = false;
          }
        }
      })
    }
    catch (err) {
      console.log("Error while constructing qbConfig", err)
    }

  }

  /**
   * Modify master data fields into the format query builder understands
  */
  modifyQBConfig(masterDataQBConfig): any {
    let QBfields = {};
    masterDataQBConfig.forEach(field => {
      switch (field.clmnType.toUpperCase()) {
        case list.DROPDOWN:
          QBfields[field.clmnNm] = { name: field.dsplyNm, type: list.SINGLE_SELECT, dataset: field.options, key: 'name', id: 'id', table: field.tableName };
          break;
        case list.VARCHAR, list.NUMBER, list.DATE_COLUMN:
          QBfields[field.clmnNm] = { name: field.dsplyNm, type: typeMapping[field.clmnType.toUpperCase()], table: field.tableName };
          if (field.clmnType == list.DATE_COLUMN) {
            QBfields[field.clmnNm].dateFormat = list.DATE_FORMAT;
          }
          if (field.clmnType == list.DECIMAL) {
            QBfields[field.clmnNm].regPattern = list.DECIMAL_REG;
            QBfields[field.clmnNm].forceRegex = true;
          }
          break;
        default:
          QBfields[field.clmnNm] = { name: field.dsplyNm, type: typeMapping[field.clmnType.toUpperCase()], table: field.tableName };
          break;
      }
    });
    return QBfields;
  }

  /**
* event to disable submit button when adding a row on form repeater
*/
  @HostListener('document:click', ['$event'])
  documentClick(event) {

    /** Adjusting marketplace Timepicker element Ids in the DOM to make AM,PM selection works properly 
    * when we use Timepicker with Marketplace Formrepeater.
    */
    let InputIds = ['inputId0', 'inputId1', 'inputId2', 'inputId3', 'inputId4']
    let outerElementcount = 0;
    document.getElementsByName(list.INPUT_ELEMENT_CLASS).forEach(outerElement => {
      outerElement.id = InputIds[outerElementcount];
      let innerElementCount = 0;
      document.querySelectorAll(list.LABEL_ELEMENT_CLASS).forEach(innerElement => {
        if (outerElementcount == innerElementCount)
          innerElement.setAttribute(list.FOR_ATTRIBUTE, outerElement.id)
        innerElementCount++;
      })
      outerElementcount++;
    })

    if (event.srcElement?.parentElement?.parentElement?.className == list.CLASS_RIGHT_ICON ||
      event.srcElement?.parentElement?.title == list.ADD_NEW_ROW
    ) {
      this.enableFormButtton = true;
      this.formRepeaterValueChange()
    }
    else if (event.srcElement?.parentElement?.parentElement?.className == list.CLASS_LEFT_ICON ||
      event.srcElement?.parentElement?.title == list.DELETE_ROW) {
      this.formRepeaterValueChange()
    }
  }

}
class timeFrameForm {
  name: string;
  formJSON: FormJsonClass[] = [];
}
class FormJsonClass {
  label?: string;
  type?: string;
  name?: string;
  required?: boolean;
  column?: string;
  disabled?: boolean;
  value?: string;
}

