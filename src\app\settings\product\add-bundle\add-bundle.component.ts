import {
  Component,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { ProductApiService } from 'src/app/_services/product-api.service';
import { Router, ActivatedRoute } from '@angular/router';
import { sample } from 'rxjs/operators';
import { ToastService } from 'src/app/_services/toast.service';
import { forkJoin } from 'rxjs';
import { CookieService } from 'ngx-cookie-service';
import { ROUTING_LABELS } from 'src/app/_constants/menu.constant';
import { environment } from 'src/environments/environment';
import { AuthService } from 'src/app/_services/authentication.services';
@Component({
  selector: 'app-add-bundle',
  templateUrl: './add-bundle.component.html',
  styleUrls: ['./add-bundle.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class AddBundleComponent implements OnInit {
  public generalDetailsJson: any[];
  public isFormready: boolean = false;
  public postDataJson: any;
  public postDataJsonForVaildation: any;
  public product: any[];
  public conceptsName: any[];
  public prodId: any;
  public isEnabled: any = false;
  public showLoader: boolean = true;
  public isLoading: any = false;
  public breadcrumbDataset: any;
  public productName: any;
  public userId: String = "";
  constructor(
    private router: Router,
    private productApiService: ProductApiService,
    private route: ActivatedRoute,
    private alertService: ToastService,
    private cookieService: CookieService,
    private authService: AuthService
  ) {
    let addBundleUrl = this.router.url.slice(this.router.url.lastIndexOf('/') + 1);
    if (addBundleUrl != "add-bundle") {
      this.prodId = Number(this.router.url.slice(this.router.url.lastIndexOf('/') + 1));
    }
    this.userId = this.cookieService.get(ROUTING_LABELS.USER_ID).toUpperCase();
  }
  /**
   * calling both API's altogether to set data in dynamic form
   */
  getProductandConceptdetails() {
    let tokenVal = localStorage.getItem("token")
    return forkJoin([
      this.productApiService.getProductConceptsId(tokenVal),

      this.productApiService.getProductDetails()]);

  }

  ngOnInit(): void {
    this.getProductandConceptdetails().subscribe(([conceptdata, data]) => {
      if (data && conceptdata) {
        this.product = data.map(val => ({ name: val.productName, code: val.prodId }));
        this.conceptsName = conceptdata['executionConceptAnalyticResponse'].map(e => ({ name: e.exConceptReferenceNumber, description: e.busConceptDesc }));

        this.generalDetailsJson = [
          {
            "type": "group",
            "name": "General_1",
            "label": "",
            "column": "2",
            "groupControls": [
              {
                "label": "Bundle Name",
                "type": "text",
                "name": "bundle_name",
                "column": "1",
                "disabled": false,
                "value": "",
                "placeholder": "Type Bundle Name"
              },
              {
                "label": "Description",
                "type": "textarea",
                "name": "bundle_desc",
                "column": "1",
                "disabled": false,
                "value": "",
                "maxLimit": "200",
                "placeholder": "Type Description"
              }
            ]
          },
          {
            "type": "group",
            "name": "General_2",
            "label": "",
            "column": "2",
            "groupControls": [
              {
                "label": "Created By",
                "type": "text",
                "name": "create_by",
                "column": "2",
                "disabled": true,
                "value": this.userId
              },
              {
                options: this.product,
                optionName: 'name',
                optionValue: 'code',
                label: 'Add to a Product',
                group: '',
                type: 'select',
                multiple: false,
                closeOnSelect: true,
                name: 'product_id',
                column: '2',
                groupColumn: '2',
                disabled: (this.prodId != undefined) ? true : false,
                selectedVal: (this.prodId != undefined) ? this.prodId : "",
                placeholder: "Select Product"
              },
              {
                "label": "Effective Start Date",
                "type": "date",
                "name": "start_date",
                "column": "2",
                "disabled": false,
                "pickerType": "single",
                "value": "",
                "placeholder": "Select Effective Start Date",
                "relatedDateControls": [{
                  "target": 'effend_date'
                }]
              },
              {
                "id": "effend_date",
                "label": "Effective End Date",
                "type": "date",
                "name": "end_date",
                "column": "2",
                "disabled": false,
                "pickerType": "single",
                "value": "",
                "placeholder": "Select Effective End Date"
              },
              {
                options: this.conceptsName,
                optionName: 'name',
                optionValue: 'name',
                label: 'Add a Concept',
                group: '',
                type: 'select',
                multiple: true,
                closeOnSelect: true,
                name: 'concept_name',
                column: '2',
                groupColumn: '2',
                disabled: false,
                selectedVal: [],
                placeholder: "Select Concept Name"
              }
            ]
          }
        ];
        setTimeout(() => (this.isFormready = true, 1000));
        this.showLoader = false;
      }
    })
    this.changeBreadCrumb();
  }

  /**
   * 
   * @param event function to change breadcrum according to location from which the user is coming to add bundle screen
   */
  changeBreadCrumb() {
    if (this.prodId != undefined) {
      this.productApiService.getProductDetails().subscribe((data) => {
        let matchedProduct = data.find((x) => x.productId == this.prodId)
        this.productName = matchedProduct?.productName;
        this.breadcrumbDataset = [{ label: 'Home', url: '/' }, { label: `${this.productName}`, url: 'settings/product' }, { label: 'Bundle list', url: `settings/product/bundle/${this.prodId}` }, { label: `Add Bundle` }];
      })
    } else {
      this.breadcrumbDataset = [{ label: 'Home', url: '/' }, { label: 'Product list', url: 'settings/product' }, { label: `Add Bundle` }];
    }
  }

  /**
  * breadcrumSelection Funtion
  */
  breadcrumSelection(event) {
    if (event.selected.label == 'Home') {
      this.authService.onHomeClick();
    }
    this.router.navigate([`${event.selected.url}`]);
  }

  backToPreviousPage() {
    window.history.back()
  }

  ngAfterViewInit(): void {
    const collection = document.querySelectorAll(
      'marketplace-dynamic-form button'
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].remove();
    }
  }

  mapValuesToJson(event: any) {
    let conceptObj = [];
    event.value['General_2'].concept_name.forEach(element => {
      conceptObj.push({
        "conceptId": element,
        "conceptDescription": this.conceptsName.find(e => e.name == element)?.description ? this.conceptsName.find(e => e.name == element).description : ""
      })
    });
    this.isEnabled = true;
    let createdDate = event.value['General_2'].start_date.split("-");
    let lastUpdatedDate = event.value['General_2'].end_date.split("-");
    this.postDataJson =
    {
      "bundleName": event.value['General_1'].bundle_name,
      "bundleDesc": event.value['General_1'].bundle_desc,
      "prodId": event.value['General_2'].product_id,
      "activeFlag": createdDate > lastUpdatedDate ? true : false,
      "createdByUserId": event.value['General_2'].create_by,
      "effStartDate": createdDate[2] + '-' + createdDate[0] + '-' + createdDate[1],
      "lastUpdatedByUserId": "string",
      "effEndDate": lastUpdatedDate[2] + '-' + lastUpdatedDate[0] + '-' + lastUpdatedDate[1],
      "concepts": conceptObj
    };
    this.postDataJsonForVaildation =
    {
      "bundleName": event.value['General_1'].bundle_name,
      "bundleDesc": event.value['General_1'].bundle_desc,
      "prodId": event.value['General_2'].product_id,
      "activeFlag": createdDate > lastUpdatedDate ? true : false,
      "createdByUserId": event.value['General_2'].create_by,
      "effStartDate": event.value['General_2'].start_date,
      "lastUpdatedByUserId": "string",
      "effEndDate": event.value['General_2'].end_date,
      "concepts": conceptObj
    }
    this.checkValidation();
  }

  checkValidation() {
    if (this.postDataJson.bundleName == undefined || this.postDataJson.bundleName == "" ||
      this.postDataJson.bundleDesc == undefined || this.postDataJson.bundleDesc == "" ||
      this.postDataJson.prodId == undefined || this.postDataJson.prodId == "" ||
      this.postDataJson.concepts == undefined || this.postDataJson.concepts == "" ||
      this.postDataJsonForVaildation.effStartDate == undefined || this.postDataJsonForVaildation.effStartDate == "" ||
      this.postDataJsonForVaildation.effEndDate == undefined || this.postDataJsonForVaildation.effEndDate == "" ||
      this.postDataJsonForVaildation.concepts == undefined || !this.postDataJsonForVaildation.concepts) {
      this.isEnabled = false;
    }

  }
  /**
   *  Method to cancelling the process for adding a bundle
   */
  cancelAddBundle() {
    if (this.prodId) {
      window.history.back();
    }
    else {
      this.router.navigate([`settings/product`]);
    }
  }

  postApiSubmit() {
    this.isLoading = true;
    this.checkValidation();
    this.productApiService.addandEditProductBundle(this.postDataJson).subscribe(
      data => {
        if (data) {
          this.cancelAddBundle();
          this.alertService.setSuccessNotification({
            notificationHeader: "Success",
            notificationBody: "Bundle Created Successfully",
          });
        }
      },
      error => {
        this.alertService.setErrorNotification({
          notificationHeader: "Warning",
          notificationBody: error,
        });
        this.isLoading = false;
      });

  }

}
