import { Component, OnInit, ViewChild, ViewEncapsulation, AfterViewInit, AfterViewChecked, On<PERSON><PERSON><PERSON> } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MAPPING_COLUMNS, MAPPING_COLUMNS_MAPPING_TBL, FILE_TEMPLATE_CONSTANTS } from '../file.constants';
import { has, get } from 'lodash';
import { FileTemplatemappings } from '../models/fileTemplatemappingmodel';
import { ADD_EDIT_CONSTANTS } from "../constants/add-edit-constants"
import { ToastService } from 'src/app/_services/toast.service';


import { FileService } from '../services/file.service';
import { forkJoin, Subscription } from 'rxjs';
import moment from 'moment';

@Component({
  selector: 'app-add-edit-template',
  templateUrl: './add-edit-template.component.html',
  styleUrls: ['./add-edit-template.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class AddEditTemplateComponent implements OnInit, AfterViewChecked, OnDestroy {
  templateDateScreen = [];
  editScreen = false;
  @ViewChild('f') templateData: any;
  @ViewChild('templateName') templateName: any;
  @ViewChild('templateNc') templateNc: any;
  inventoryTableRedraw: number;
  selectedRows: any;
  isTableJsonReady: boolean;
  cfflinkCount: any;
  addDeletedDropDownOption: any;
  cffValueOnEditPage: any;
  valuesForSubmit: any;
  updateValuesForSubmit: any;
  initialFieldValues: any;
  fixedWidthRadioSelected: any;
  delimiterRadioSelected: any;
  isSave: any;
  fixedWidthSelected: boolean = false;
  constructor(private router: Router, private alertService: ToastService, private route: ActivatedRoute, private fileService: FileService) { }
  currentStep: any = 1;
  delimiter = '0';
  checkboxSelected: any;
  delimiterDataset: any = [
    { name: 'Tab', value: 'tab' },
    { name: 'Semicolon', value: 'semicolon' },
    { name: 'Comma', value: 'comma' },
    { name: 'Space', value: 'space' },
    { name: 'Other', value: 'other' }
  ];;
  enableFormButtton = false;
  disabledButton = true;
  isTableReady: boolean = false;
  enabletwoFormButtton = false;
  showFormItem: boolean = false;
  showFormncItem: boolean = false;
  radioSelection: boolean = false;
  mappingRowdata: any = [];
  open = true
  notificationHeader: string = "";
  notificationBody: string;
  notificationPosition: any = 'top-right';
  notificationDuration: number = 3000;
  notificationType: any = 'success';
  notificationOpen: string = "false";
  simpleFormValidationJson: any;
  simpleFormDYnamicValidationJson: any;
  mappingDataJSONs: any;
  tableRecreate: any;
  simpleRadio: any;
  systemInventory: any = [];
  templateMappingDetails: any = [];
  cffdetails: any = [];
  mappingDataJSONData = {};
  showOfDelimiter: boolean = true;
  isDraggableRowsNeeded: boolean = true;
  seqNo: number = 1;
  sysNamePublish: any
  headerStatus: any
  importExportStatus: any
  modifiedDate: any = new Date().toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  });
  allTemplateDataListJSON: any
  sameTemplateName: any
  isView: boolean = false
  isSubmitBtnHide: boolean = false
  disableDelimiter: boolean = false;
  disablefixed: boolean = false;
  disableCreateEditBtn: boolean = true;
  status: any;
  isMandatoryValuesFilled = false;
  updatedDropdownOptions: any = [];
  valuesOnAddRow: any;
  valuesOnDeleteRow: any;
  initialCount = 1;
  updatedMappingData: any
  autoSaveFileDetails: any
  disableDelimiterOthrInputBox: boolean = false;
  cffLinkPopup: any;
  currentRowToUpdate: any;
  hcValue: any;
  updateExistingRow: any;
  cffLinkPopupTxtColor: boolean = false;
  currentRowClicked: any;
  clickedCellSecond: boolean = false;
  showDelimiter: boolean = true;
  showFixedWidth: boolean = true;
  delimiterRadio: any;
  fixedWidthRadio: any;
  allProdNames: any = [];
  filteredInventoryType: any;
  filteredProdNames: any;
  selectedInvType: string;
  selectedProdName: string;
  selectedProdId: any;
  selectedDelimiterBool: boolean = true;
  callCffFldLinkApi: boolean;

  templateDynamicFormJson: string = "./assets/json/settings/file/add-edit-template/template-details-dynamic-form.json";

  editPageFileDetails: any;
  loggedInUser: any;

  allInventoryType: any;
  inventoryTypeSelected: any;

  addEditHeaderConfig = [
    { name: "Specifications", description: "Specifications (01)", enabled: true },
    { name: "Mapping", description: "Mapping (02)", enabled: false },
    { name: "Publish", description: "Publish (03)", enabled: false }
  ];

  currentTemplate: any = {
    "details": {
      "memberDetails": {
        "templatename": "",
        "description": ""
      },
      "details": {
        "fileType": "",
        "system": null,
        "inventoryType": "",
        "productName": "",
        "status": "",
        "header": {
          "headers": false
        }
      }
    },
    "delimiter": {
      "type": 0,
      "checkboxSelected": [],
      "value": ""
    },
    "fixedWidth": {
      "value": ""
    },
    "nc": {
      "NamingConventions": {
        "cg": {
          "Import": false,
          "Export": false
        },
        "prefix": "",
        "sufix": ""
      }
    },
    "CreatedBy": "",
    "Created": "",
    "modified": ""
  };
  apiForm: any;
  autoSavefileTmplId: any = "";
  currentTemplateData: any = {
    "fileTmplId": null,
    "fileTmplName": "",
    "descText": "",
    "fileFrmtTxt": "",
    "tmpltType": "",
    "sysId": 0,
    "inventoryType": "",
    "productName": "",
    "status": false,
    "headerFlag": false,
    "delimiter": "",
    "fixedWidthFlag": 0,
    "importFlag": false,
    "exportFlag": false,
    "prefix": "",
    "sufix": "",
    "createdUserId": "",
    "lastModifiedUserId": "",
    "lastModifiedDate": "",
    "createdDate": "",
    "fileDetails": [
    ]

  }
  mappingDataJSON = [{
    "id": 1647260958031,
    "fieldSeqNo": "1",
    "fieldName": "",
    "fieldDataType": "",
    "fieldSize": "",
    "recordType": "",
    "fieldOption": "",
    "invClmTypInd": "",
    "cffFldLink": "",
    "cffLength": "",
    "cffConstValue": "",
    "action": ""
  }];

  showDelimiterOptions: boolean = true;
  pageType: any;
  id: any;
  simpleConfig: any = [
    { name: "1", description: "Specifications" },
    { name: "2", description: "Mapping" },
    { name: "3", description: "Publish" }
  ];

  public schedulerBreadCrumbData: any = [{ label: 'Home', url: '/' }, { label: 'Files', url: '/settings/files' }];

  columnConfigInlineEdit: any = MAPPING_COLUMNS_MAPPING_TBL;
  autoSavetime: number = 180;
  timeLeft: number = this.autoSavetime;
  interval;
  subscriptions: Subscription[] = [];

  ngOnInit(): void {
    let _getProdNames = this.fileService.getAllProdNames();
    let _formData = this.fileService.getAllReuiredFormDropDowns()

    this.subscriptions.push(forkJoin([_getProdNames, _formData]).subscribe(([apiData, data]) => {
      Object.values(apiData).forEach(x => {
        this.allProdNames.push({ name: x.prodName, invTypId: x.invTypId, prodId: x.productId, id: x.productId })
      })
      this.systemInventory = data.invSysList;
      this.allInventoryType = data?.invTypeList?.map(val => ({ name: val.name, id: val.id, other: val.refId, code: val.id }));

      this.fileService.getJsonFromUrl(this.templateDynamicFormJson).subscribe((data) => {
        this.simpleFormValidationJson = data;
      }, (err) => {
        console.log(err);
      });

      let fetchCookie: any = document.cookie?.split(";");
      fetchCookie?.map(e => {
        if (e?.includes("userId")) {
          this.loggedInUser = e?.trim()?.slice(7);
          this.currentTemplate.CreatedBy = this.loggedInUser
        }
      })



      this.isView = this.route.snapshot.routeConfig.path.includes(ADD_EDIT_CONSTANTS.VIEW)
      this.pageType = this.isView ? FILE_TEMPLATE_CONSTANTS.VIEW : FILE_TEMPLATE_CONSTANTS.CREATE_NEW;
      this.getAllTemplatesApi()
      this.isMandatoryValuesFilled = this.pageType === FILE_TEMPLATE_CONSTANTS.VIEW || this.pageType === FILE_TEMPLATE_CONSTANTS.EDIT ? true : false;
      this.addEditHeaderConfig[2].enabled = this.isMandatoryValuesFilled

      if (this.route.snapshot.params['id']) {
        this.pageType = this.isView ? FILE_TEMPLATE_CONSTANTS.VIEW : FILE_TEMPLATE_CONSTANTS.EDIT;
        this.isMandatoryValuesFilled = this.pageType === FILE_TEMPLATE_CONSTANTS.VIEW || this.pageType === FILE_TEMPLATE_CONSTANTS.EDIT ? true : false;
        this.addEditHeaderConfig[2].enabled = this.isMandatoryValuesFilled
        if (this.isView) {
          this.columnConfigInlineEdit.switches.editable = false
          this.disableDelimiter = true
          this.disablefixed = true
          this.disabledButton = false
          this.isSubmitBtnHide = true
          this.addEditHeaderConfig[1].enabled = true
          this.addEditHeaderConfig[2].enabled = true
          this.disableDelimiterOthrInputBox = true
        } else {
          this.columnConfigInlineEdit.switches.editable = true
          this.disableDelimiter = false
          this.disablefixed = false
          this.disableDelimiterOthrInputBox = false
        }

        this.showOfDelimiter = false;
        this.editScreen = true;
        this.id = Number(this.route.snapshot.params['id']);
        const valueDelimiter = this.delimiterDataset.map(elem => {
          return elem.value
        })

        this.fileService.getSingleTemplateDetails(this.id).subscribe((data) => {
          data.fileDetails?.forEach((e) => {
            e.id = e.fileDetailId;
            if (e.fieldOption == ADD_EDIT_CONSTANTS.NULL) {
              e.cffFldLink = ADD_EDIT_CONSTANTS.NULL;
              e.cffLength = ADD_EDIT_CONSTANTS.NULL;
            }
            e.invClmTypInd == "N" ? e.invClmTypInd = FILE_TEMPLATE_CONSTANTS.XREF : e.invClmTypInd == "Y" ? e.invClmTypInd = FILE_TEMPLATE_CONSTANTS.TARGET : e.invClmTypInd;
          });

          !data.fixedWidthFlag && !data.delimiter ? this.showDelimiterOptions = true : data.fixedWidthFlag ? this.showDelimiterOptions = true : this.showDelimiterOptions = false;

          this.apiForm = data;
          this.editPageFileDetails = JSON.parse(JSON.stringify(data.fileDetails));
          const itemValue = has(data, FILE_TEMPLATE_CONSTANTS.DELIMETER) ? get(data, FILE_TEMPLATE_CONSTANTS.DELIMETER)?.toLowerCase() : "";
          const indexValue = valueDelimiter.indexOf(get(data, FILE_TEMPLATE_CONSTANTS.DELIMETER)?.toLowerCase());
          this.checkboxSelected = get(data, FILE_TEMPLATE_CONSTANTS.FIXED_WIDTH_FLAG) === false ? (indexValue > -1 ? itemValue : (itemValue == "" || itemValue == undefined) ? "" : ADD_EDIT_CONSTANTS.OTHER) : true;
          this.fixedWidthRadioSelected = has(data, FILE_TEMPLATE_CONSTANTS.FIXED_WIDTH_FLAG) ? get(data, FILE_TEMPLATE_CONSTANTS.FIXED_WIDTH_FLAG) : "";
          this.delimiterRadioSelected = has(data, FILE_TEMPLATE_CONSTANTS.DELIMETER) ? get(data, FILE_TEMPLATE_CONSTANTS.DELIMETER) : "";

          this.filteredInventoryType = this.allInventoryType?.filter(e => e.other == this.systemInventory?.filter(res => res.id == data.sysId)[0].id);
          this.filteredProdNames = this.allProdNames?.filter(res => res.invTypId == data.invTypId);
          this.inventoryTypeSelected = this.allInventoryType?.filter(val => val.other == data?.sysId).filter(res => res.id == data.invTypId);
          this.selectedInvType = this.inventoryTypeSelected?.length ? this.inventoryTypeSelected[0]?.name : '';
          this.selectedProdName = data?.prodNm;
          this.selectedProdId = this.filteredProdNames[0]?.id;
          this.getAllCffLinks(this.inventoryTypeSelected[0]?.name, data?.prodNm);
          this.currentTemplate = {
            "details": {
              "memberDetails": {
                "templatename": has(data, FILE_TEMPLATE_CONSTANTS.FILE_TEMPLATE_NAME) ? get(data, FILE_TEMPLATE_CONSTANTS.FILE_TEMPLATE_NAME) : "",
                "description": has(data, FILE_TEMPLATE_CONSTANTS.DESCRIPTION_TEXT) ? get(data, FILE_TEMPLATE_CONSTANTS.DESCRIPTION_TEXT) : "",
              },
              "details": {
                "status": has(data, FILE_TEMPLATE_CONSTANTS.ACTIVE_IND) ? get(data, FILE_TEMPLATE_CONSTANTS.ACTIVE_IND) : "",
                "fileType": has(data, FILE_TEMPLATE_CONSTANTS.FILE_FORMAT_TXT) ? get(data, FILE_TEMPLATE_CONSTANTS.FILE_FORMAT_TXT) : "",
                "system": has(data, FILE_TEMPLATE_CONSTANTS.SYS_ID) ? get(data, FILE_TEMPLATE_CONSTANTS.SYS_ID) : "",
                "templateType": has(data, FILE_TEMPLATE_CONSTANTS.TEMPLATE_TYPE) ? get(data, FILE_TEMPLATE_CONSTANTS.TEMPLATE_TYPE) : "",
                "inventoryType": this.inventoryTypeSelected[0].id,
                "productName": this.selectedProdId,
                "header": {
                  "headers": has(data, FILE_TEMPLATE_CONSTANTS.HEADER_FLAG) ? get(data, FILE_TEMPLATE_CONSTANTS.HEADER_FLAG) : false,
                }
              }
            },
            "delimiter": {
              "type": get(data, FILE_TEMPLATE_CONSTANTS.FIXED_WIDTH_FLAG) === true ? 1 : 0,
              "checkboxSelected": [],
              "value": get(data, FILE_TEMPLATE_CONSTANTS.FIXED_WIDTH_FLAG) === false ? (itemValue ? ((indexValue > -1 && indexValue != 4) ? "" : itemValue) : "") : ""
            },
            "fixedWidth": {
              "value": has(data, FILE_TEMPLATE_CONSTANTS.FIXED_WIDTH_FLAG) ? get(data, FILE_TEMPLATE_CONSTANTS.FIXED_WIDTH_FLAG) : false,
            },
            "nc": {
              "NamingConventions": {
                "cg": {
                  "Import": has(data, FILE_TEMPLATE_CONSTANTS.IMPORT_FLAG) ? get(data, FILE_TEMPLATE_CONSTANTS.IMPORT_FLAG) : false,
                  "Export": has(data, FILE_TEMPLATE_CONSTANTS.EXPORT_FLAG) ? get(data, FILE_TEMPLATE_CONSTANTS.EXPORT_FLAG) : false,
                },
                "prefix": has(data, FILE_TEMPLATE_CONSTANTS.PREFIX) ? get(data, FILE_TEMPLATE_CONSTANTS.PREFIX) : "",
                "sufix": has(data, FILE_TEMPLATE_CONSTANTS.SUFIX) ? get(data, FILE_TEMPLATE_CONSTANTS.SUFIX) : ""
              }
            },
            "CreatedBy": has(data, FILE_TEMPLATE_CONSTANTS.CREATED_USER_ID) ? get(data, FILE_TEMPLATE_CONSTANTS.CREATED_USER_ID) : "",
            "Created": has(data, FILE_TEMPLATE_CONSTANTS.CREATED_USER_ID) ? moment(get(data, FILE_TEMPLATE_CONSTANTS.CREATED_DATE)).format(FILE_TEMPLATE_CONSTANTS.DATE_TIME_FORMAT).toString() : "",
            "modified": has(data, FILE_TEMPLATE_CONSTANTS.FILE_TEMPLATE_NAME) ? get(data, FILE_TEMPLATE_CONSTANTS.LAST_MODIFIED_DATE) : "",
            "MOdifiedBy": has(data, FILE_TEMPLATE_CONSTANTS.LAST_MODIFIED_USER_ID) ? get(data, FILE_TEMPLATE_CONSTANTS.LAST_MODIFIED_USER_ID) : "",
          };

          setTimeout(() => {
            this.showOfDelimiter = true;
            this.getFormDrop();
          }, 100);


        })
      } else {
        this.getFormDrop();
      }

      if (this.pageType == ADD_EDIT_CONSTANTS.CREATE_NEW) {
        this.startTimer()
      }
    }))

  }

  /**
  * This function will trigger at the initialization of the component
  * @function getAllInventoryTypes will call system inventory and inventory type dropdowns
  */
  getAllInventoryTypes() {
    this.fileService.getAllReuiredFormDropDowns().subscribe((data: any) => {
      this.systemInventory = data.invSysList;
      this.allInventoryType = data?.invTypeList?.map(val => ({ name: val.name, id: val.id, other: val.refId, code: val.id }));
    }, err => {

    });
  }

  /**
 * API call to fetch all Templates JSON
 * @function getAllTemplatesApi will trigger at the initialization of the component
 */
  getAllTemplatesApi() {
    this.fileService.getAllTemplates().subscribe((data: any) => {
      if (data.length) {
        this.allTemplateDataListJSON = data
      }
    });
  }


  /**
   * Invokes when the user clicks on second tab
   * API call to fetch Prod Names Drop down Data
   * @function getAllProdNames will collect all the Product Names and will map with dynamic form JSON
   */
  getAllProdNames = () => {
    this.fileService.getAllProdNames().subscribe((prodNames: any) => {
      this.allProdNames = prodNames?.map(val => ({ name: val.prodName, invTypId: val.invTypId, prodId: val.productId, id: val.productId }));
    }, err => {

    })
  }

  /**
   * This method will trigger after click the next button in first stepper
   * @function getAllCffLinks method will collects all the CFFLinks
   * @param selectedInvType
   * @param selectedProdName
   */
  getAllCffLinks(selectedInvType: string, selectedProdName: string) {
    this.fileService.getAllCFFLinks(selectedInvType, selectedProdName).subscribe((cffLinks: any) => {
      this.cffdetails = cffLinks?.map(val => ({ label: val.name, id: val.id, other: val.other }));
      this.cffValueOnEditPage = this.cffdetails;
      if (this.editScreen) {
        if (this.apiForm.fileDetails) {
          this.apiForm.fileDetails?.sort((a, b) => parseInt(b.fieldSeqNo) - parseInt(a.fieldSeqNo))
        }
        this.apiForm.fileDetails?.forEach((elem) => {
          let field = cffLinks?.find(x => x.id == elem.cffFldLink)
          if (field) {
            elem.cffFldLink = field.name
            elem.cffLength = field.other
            elem.cffId = field.id
          }
        })

        this.cffdetails = this.cffdetails?.filter(x => {
          return !this.apiForm.fileDetails?.some(element => {
            return element[ADD_EDIT_CONSTANTS.CFF_FLD_LINK] === x[ADD_EDIT_CONSTANTS.LABEL]
          });
        })
      }

      this.getTableData();
      this.callCffFldLinkApi = false;
    });
  }
  /**
   * get DropDrown For system and inventory list
   * @function getFormDrop will trigger at the initialization of the component
   */
  getFormDrop() {
    this.fileService.getAllReuiredFormDropDowns().subscribe((data: any) => {
      this.systemInventory = data.invSysList;
      this.allInventoryType = data?.invTypeList?.map(val => ({ name: val.name, id: val.id, other: val.refId, code: val.id }));
      this.getFormData();
      this.getTableData();
      this.setFileTypeDetails();
    }, (err) => {
      this.getFormData();
      this.getTableData();
      this.setFileTypeDetails();
    });
  }

  /**
   * This function will trigger at the initialization of the component
   * @function setFileTypeDetails  Set or , to assign JSON to delimiter & fixed width Dynamic form
   */
  setFileTypeDetails = () => {
    this.delimiterRadio = [{
      "options": [{ name: "Delimiter", value: "Delimiter" }],
      "optionName": "name",
      "optionValue": "value",
      "label": "",
      "type": "radio",
      "multiple": false,
      "closeOnSelect": true,
      "name": "delimiter",
      "column": "1",
      "value": this.delimiterRadioSelected ? "Delimiter" : "",
    }]

    this.fixedWidthRadio = [{
      "options": [{ name: "Fixed Width", value: "fixedWidth" }],
      "optionName": "name",
      "optionValue": "value",
      "label": "",
      "type": "radio",
      "multiple": false,
      "closeOnSelect": true,
      "name": "fixedWidth",
      "column": "1",
      value: this.fixedWidthRadioSelected ? "fixedWidth" : ""
    }]
    if (this.isView) {
      this.delimiterRadio[0]['disabled'] = true
      this.fixedWidthRadio[0]['disabled'] = true
    }
  }

  /**
   * Invokes when the component initialization and step change in the stepper
   * @function getFormData holds the breadcrumb data
   */
  getFormData() {
    if (this.isView) {
      this.schedulerBreadCrumbData.push({
        label: FILE_TEMPLATE_CONSTANTS.VIEW_TEMPLATE,
        url: ""
      })
    }
    else if (this.editScreen) {
      this.schedulerBreadCrumbData.push({
        label: FILE_TEMPLATE_CONSTANTS.EDIT_TEMPLATE,
        url: ""
      })
    } else {
      this.schedulerBreadCrumbData.push({
        label: FILE_TEMPLATE_CONSTANTS.CREATE_NEW_TEMPLATE,
        url: ""
      })
    }
    this.setTheFormValue();
  }

  /**
   * Invokes when the user clicks on stepper to change the step
   * @function stepChange will set the form value and change the button names dynamically
   * @param event
   */
  stepchange(event) {

    switch (event) {
      case 0:
        break;
      case 1:
        this.next(event + 1)
        this.setTheFormValue();
        break;
      case 2:
        let submitBtnTextChange = document.getElementsByClassName('btn-text',) as HTMLCollectionOf<HTMLElement>;
        let submitBtnHide = document.getElementsByClassName('btn-cta',) as HTMLCollectionOf<HTMLElement>;
        setTimeout(() => {
          switch (this.pageType) {
            case FILE_TEMPLATE_CONSTANTS.CREATE_NEW:
              submitBtnTextChange[1].innerText = FILE_TEMPLATE_CONSTANTS.CREATE_TEMPLATE;
              break;
            case FILE_TEMPLATE_CONSTANTS.EDIT:
              submitBtnTextChange[1].innerText = FILE_TEMPLATE_CONSTANTS.UPDATE_TEMPLATE;
              break;
            case FILE_TEMPLATE_CONSTANTS.VIEW:
              submitBtnHide[0].style.display = FILE_TEMPLATE_CONSTANTS.NONE;
              break;
            default:
              break;
          }
        }, 0);
        this.next(event + 1)
        this.setTheFormValue();
        break;
      default:
        break;
    }

  }

  /**
   * Invokes when the user clicks on stepper to change the step
   * @function next collects currentTemplate JSON and mappingDataJSON and to navigate to next tabs in the stepper
   * @param value
   * @param isAutoSave
   */
  next(value: number, isAutoSave = false) {
    let mappingDetails = this.mappingDataJSON;
    if (!isAutoSave) {
      this.currentStep = value;
    }
    this.systemInventory.map((e) => {
      if (this.currentTemplate.details.details.system == e.id) {
        this.sysNamePublish = e.name
      }
    });
    if (value === 2) {
      if (isAutoSave) {
        this.mappingDataJSONs.src = this.mappingRowdata.dataset == undefined ? this.mappingDataJSONs.src : this.mappingRowdata.dataset
      } else {
        if (this.callCffFldLinkApi) {
          this.getAllCffLinks(this.selectedInvType, this.selectedProdName);
        }
        this.systemInventory.map((e) => {
          if (this.currentTemplate.details.details.system == e.id) {
            this.sysNamePublish = e.name
          }
        });
        if (this.editScreen) {
          this.currentTemplate['modified'] = new Date().getDate() + '/' + (new Date().getMonth() + 1) + '/' + new Date().getFullYear();
          this.currentTemplate["MOdified By"] = this.loggedInUser;
        } else {
          this.currentTemplate["CreatedBy"] = this.loggedInUser;
          this.currentTemplate['modified'] = new Date().getDate() + '/' + (new Date().getMonth() + 1) + '/' + new Date().getFullYear();
        }
        this.tableRecreate = Date.now();
      }
    }
    if (value === 3) {
      this.mappingDataJSONs.src = this.mappingRowdata.dataset == undefined || this.pageType === FILE_TEMPLATE_CONSTANTS.EDIT ? this.mappingDataJSONs.src : this.mappingRowdata.dataset
    }
    this.isTableReady = true;
    this.nextSetup();
  }

  /**
   * Invokes on click of stepper
   * @function fileTempId return the file template ID on condition
   * @returns file template ID
   */
  fileTempId = () => {
    if (this.pageType === FILE_TEMPLATE_CONSTANTS.EDIT) {
      return this.apiForm.fileTmplId;
    } else if (this.pageType == FILE_TEMPLATE_CONSTANTS.CREATE_NEW) {
      return this.currentTemplateData.fileTmplId == null ? null : this.currentTemplateData.fileTmplId;
    }
  }

  /**
   * Invokes on change of the stepper
   * @function nextSetup setting the dynamic form as Per API Model
   */

  nextSetup() {
    this.currentTemplateData = {
      "fileTmplId": this.fileTempId(),
      "fileTmplName": has(this.currentTemplate, 'details.memberDetails.templatename') ? get(this.currentTemplate, 'details.memberDetails.templatename') : "",
      "descText": has(this.currentTemplate, 'details.memberDetails.description') ? get(this.currentTemplate, 'details.memberDetails.description') : "",
      "fileFrmtTxt": has(this.currentTemplate, 'details.details.fileType') ? get(this.currentTemplate, 'details.details.fileType') : "",
      "tmpltType": has(this.currentTemplate, 'details.details.templateType') ? get(this.currentTemplate, 'details.details.templateType') : "",
      "sysId": has(this.currentTemplate, 'details.details.system') ? get(this.currentTemplate, 'details.details.system') : "",
      "invTypId": has(this.currentTemplate, 'details.details.inventoryType') ? get(this.currentTemplate, 'details.details.inventoryType') : "",
      "prodNm": this.selectedProdName,
      "prodId": this.selectedProdId,
      "actvInd": has(this.currentTemplate, 'details.details.status') ? get(this.currentTemplate, 'details.details.status') : "",
      "headerFlag": this.currentTemplate.details.details.header.headers ? true : false,
      "delimiter": this.currentTemplate.delimiter.type == 0 ? this.checkboxSelected == "other" ? this.currentTemplate.delimiter.value : this.checkboxSelected : null,
      "fixedWidthFlag": this.currentTemplate.delimiter.type == 1 ? true : false,
      "importFlag": has(this.currentTemplate, 'nc.NamingConventions.cg.Import') ? get(this.currentTemplate, 'nc.NamingConventions.cg.Import') : "",
      "exportFlag": has(this.currentTemplate, 'nc.NamingConventions.cg.Export') ? get(this.currentTemplate, 'nc.NamingConventions.cg.Export') : "",
      "prefix": has(this.currentTemplate, 'nc.NamingConventions.prefix') ? get(this.currentTemplate, 'nc.NamingConventions.prefix') : "",
      "sufix": has(this.currentTemplate, 'nc.NamingConventions.sufix') ? get(this.currentTemplate, 'nc.NamingConventions.sufix') : "",
      "publishInd": true,
      "CreatedByTemp": "System",
      "createdUserId": has(this.currentTemplate, "CreatedBy") ? get(this.currentTemplate, "CreatedBy") : "System",
      "fileDetails": [
      ]
    }
    // Future Purpose
    // if(this.editScreen){
    //   this.currentTemplateData.lastModifiedUserId=this.apiForm.lastModifiedUserId
    //   this.currentTemplateData.createdDate= has(this.currentTemplate, "Created") && get(this.currentTemplate, "Created");
    //   }

    this.importExportStatus = this.currentTemplateData.importFlag && this.currentTemplateData.exportFlag ? "Import & Export" : this.currentTemplateData.importFlag ? "Import" : this.currentTemplateData.exportFlag ? "Export" : "";
    this.headerStatus = this.currentTemplateData.headerFlag ? "Yes" : "No";
    this.status = this.currentTemplate.details.details.status;
  }

  /**
   * Invokes on click of previous button and set form state
   * @function previous will set the form value
   * @param value
   */
  previous(value: number) {
    this.currentStep = value;
    this.setTheFormValue();
  }

  /**
   * Invokes on radio selection of delimiter
   * @function onCheckBoxSelection selects the radiobox
   */

  onCheckBoxSelection(event) {
    this.checkboxSelected = event.current.fileDetails.type;
    this.selectedDelimiterBool = true;
    this.enableDisableNextBtns();
  }

  /**
   * Invokes on click of save button
   * @function tableData Mapping tab table data
   */

  tableData(data: any) {
    this.mappingDataJSONs.src = data;
  }

  /**
   * triggers when the user Delete selected rows button is clicked In Mapping Tab's Table
   *  @function deleteRowFromTable deletes the selected row
   * @param event
   */
  deleteRowFromTable = (event) => {
    let count = 0;
    if (this.pageType === FILE_TEMPLATE_CONSTANTS.EDIT) {
      this.updatedDropdownOptions = this.cffdetails;
    }
    event.forEach(x => {
      if (this.pageType == FILE_TEMPLATE_CONSTANTS.CREATE_NEW) {
        let fromAPI = this.autoSaveFileDetails ? this.autoSaveFileDetails?.find(y => y.fileDetailId == x.fileDetailId) : '';
        if (fromAPI) {
          let valuesFound = this.valuesForSubmit?.find(y => y.fileDetailId == x.fileDetailId);
          valuesFound['action'] = valuesFound ? valuesFound['action'] = 'delete' : '';
        } else {
          this.valuesForSubmit = this.valuesForSubmit.filter(z => z.id != x.id);
        }
      } else {
        let fromAPI = this.initialFieldValues ? this.initialFieldValues?.find(y => y.id == x.id) : '';
        if (fromAPI) {
          let valuesFound = this.valuesForSubmit?.find(y => y.id == x.id);
          valuesFound['action'] = valuesFound ? valuesFound['action'] = 'delete' : '';
        } else {
          this.valuesForSubmit = this.valuesForSubmit.filter(z => z.id != x.id);
        }
      }
    });
    if (this.valuesOnAddRow) {
      this.valuesOnDeleteRow = this.modifyDataset(event, this.valuesOnAddRow);
      this.valuesOnAddRow = this.valuesOnDeleteRow;
    }
    else if (this.apiForm.fileDetails) {
      this.valuesOnDeleteRow = this.modifyDataset(event, this.apiForm.fileDetails)
      this.apiForm.fileDetails = this.valuesOnDeleteRow
    }
    this.valuesOnDeleteRow.reverse().forEach((x, i) => {
      x.fieldSeqNo = ++i;
      x.fieldSeqNo.toString();
    });
    this.isMandatoryValuesFilled = false;
    this.addEditHeaderConfig[2].enabled = this.isMandatoryValuesFilled
    this.valuesOnDeleteRow.forEach(x => {
      if (x.fieldName == "" || x.fieldSize === "" || x.fieldDataType == "" || (x.cffFldLink == "" && x.fieldOption != FILE_TEMPLATE_CONSTANTS.CONST_VALUE) || (x.fieldOption == FILE_TEMPLATE_CONSTANTS.CONST_VALUE && x.cffConstValue == "")) {
        count++;
      }
    });
    if (count == 0 && this.valuesOnDeleteRow.length > 0) {
      this.isMandatoryValuesFilled = true;
      this.addEditHeaderConfig[2].enabled = this.isMandatoryValuesFilled
    }
    this.addDeletedDropDownOption = this.cffValueOnEditPage.filter(cffdetailsValue => {
      return event.some(eventValue => {
        return eventValue['cffFldLink'] === cffdetailsValue['label']
      })
    });
    this.addDeletedDropDownOption.forEach(x => {
      this.updatedDropdownOptions.push(x);
    });
    if (this.updatedDropdownOptions.length > 0) {
      this.mappingDataJSONs.CFF = this.updatedDropdownOptions;
    }
    this.mappingDataJSONs.src = this.valuesOnDeleteRow.reverse();
    this.tableRecreate = Date.now();
  }

  /**
   * Going back to previos page on click of Back Button
   */
  backToPreviousPage() {
    this.router.navigate([`settings/files`]).then(() => { window.location.reload() });
  }

  /**
   * Invoke when we delete a row from table
   * @function modifyDataset to modify table dataset
   * @param event from delete function
   * @param dataset from delete function
   * @returns updated dataset
   */
  modifyDataset(event: any, dataset: any) {
    let modifiedDataset = dataset.filter(x => {
      return !event.some(eventValue => {
        return x.id === eventValue.id
      })
    })
    return modifiedDataset;
  }

  /**
   * Method Invoked on before cell click of table's in Mapping tab
   * @param e
   */
  beforeCellClick(e: any) {
    this.updatedDropdownOptions = [...this.cffValueOnEditPage];
    if (e._newArgs?.item.invClmTypInd == FILE_TEMPLATE_CONSTANTS.TARGET || e._newArgs?.item.invClmTypInd == FILE_TEMPLATE_CONSTANTS.XREF) {
      e.dataset.forEach(datasetelement => {
        if (e._newArgs?.item.invClmTypInd == FILE_TEMPLATE_CONSTANTS.TARGET) {
          if ((datasetelement.invClmTypInd == FILE_TEMPLATE_CONSTANTS.TARGET || datasetelement.invClmTypInd == "" || datasetelement.invClmTypInd == null) && datasetelement.cffFldLink != "") {
            let obj = this.updatedDropdownOptions.find(ele => ele.label == datasetelement.cffFldLink);
            this.updatedDropdownOptions = this.updatedDropdownOptions.filter(ele => {
              return obj[FILE_TEMPLATE_CONSTANTS.LABEL] != ele[FILE_TEMPLATE_CONSTANTS.LABEL];
            });
          }
        }
        else if (e._newArgs?.item.invClmTypInd == FILE_TEMPLATE_CONSTANTS.XREF) {
          if ((datasetelement.invClmTypInd == FILE_TEMPLATE_CONSTANTS.XREF || datasetelement.invClmTypInd == "" || datasetelement.invClmTypInd == null) && datasetelement.cffFldLink != "") {
            let obj = this.updatedDropdownOptions.find(ele => ele.label == datasetelement.cffFldLink);
            this.updatedDropdownOptions = this.updatedDropdownOptions.filter(ele => {
              return obj[FILE_TEMPLATE_CONSTANTS.LABEL] != ele[FILE_TEMPLATE_CONSTANTS.LABEL];
            });
          }
        }
      });
    } else {
      this.updatedDropdownOptions = this.cffValueOnEditPage.filter(cffdetailsValue => {
        return !e.dataset.some(eventValue => {
          return eventValue['cffFldLink'] === cffdetailsValue[FILE_TEMPLATE_CONSTANTS.LABEL]
        })
      });
    }
    this.mappingDataJSONs.CFF = this.updatedDropdownOptions
    e?._newArgs?.item != undefined && e?._newArgs?.item?.cffConstValue == null ? e._newArgs.item.cffConstValue = "" : ""
    this.currentRowClicked = e._newArgs.item;

    if (e?._newArgs?.column?.field == 'cffConstValue') {
      if (e?._newArgs?.item != undefined && e?._newArgs?.item?.fieldOption == FILE_TEMPLATE_CONSTANTS.CONST_VALUE) {
        this.hcValue = e?._newArgs?.item?.cffConstValue
        this.clickedCellSecond = true;
        this.columnConfigInlineEdit.colDefs.forEach(x => {
          if (x.field == "cffConstValue") {
            x.editorType = "";
          }
        })
        // this.tableRecreate = Date.now();
        this.cffLinkPopup = Date.now()
        setTimeout(() => {
          document.getElementById('custom-val')['value'] = e?._newArgs?.item?.cffConstValue;
        }, 0);
      } else {
        return
      }
    }

    if (e?._newArgs?.column?.field == 'cffFldLink' && e?._newArgs?.item != undefined && (e?._newArgs?.item?.fieldOption == 'Null' || e?._newArgs?.item?.fieldOption == FILE_TEMPLATE_CONSTANTS.CONST_VALUE)) {
      return
    }

  }

  /**
   * Invokes on autosave
   * @function assignEmptyValuesHelper is a helper method to check if the array element is null and assign empty string
   * @param fileDetailsObj
   * @param key
   */
  assignEmptyValuesHelper(fileDetailsObj: any, key: string) {
    if (fileDetailsObj[key] == null) {
      fileDetailsObj[key] = "";
    }
  }

  /**
   * Invokes on autosave
   * @function assignEmptyValues assigning empty values to array elements
   * @param fileDetailsObj
   * @param keys
   */
  assignEmptyValues(fileDetailsObj: any, keys: any) {
    keys?.forEach(key => fileDetailsObj[key] = this.assignEmptyValuesHelper(fileDetailsObj, key));
  }

  /**
   * Invokes on auto save
   * @function getSingleFileTemplateOnAutoSave Updated File Template's Mapping Data to get the latest fileDetailId of every Mapping data
   * @param templateID
   * @param isDeleteFilter
   */
  getSingleFileTemplateOnAutoSave = (templateID, isDeleteFilter) => {
    this.fileService.getSingleTemplateDetails(templateID).subscribe((data) => {
      this.mappingDataJSONs.src = data.fileDetails?.map((e) => {
        e.id = e.fileDetailId
        e.cffId = e.cffFldLink
        e.cffFldLink = e.cffFldName
      });
      data.fileDetails?.forEach((e) => {
        e.id = e.fileDetailId
        e.cffId = e.cffFldLink
        e.cffFldLink = e.cffFldName
        let keysToBeChecked = ['recordType', 'fieldOption', 'cffConstValue'];
        this.assignEmptyValues(e, keysToBeChecked);
        if (e.fieldOption?.toLowerCase() == FILE_TEMPLATE_CONSTANTS.CONSTANT_VALUE && e.cffFldLink == null) {
          e.cffFldLink = "";
        }
        if (e.fieldOption?.toLowerCase() == "null") {
          e.cffFldLink = "Null";
        }
        let updatefileDetailId = this.currentTemplateData.fileDetails.find(x => x.fieldName == e.fieldName)
        if (updatefileDetailId) {
          e.action = updatefileDetailId.action;
        }
      });
      this.autoSaveFileDetails = JSON.parse(JSON.stringify(data.fileDetails))
      if (isDeleteFilter) {
        this.valuesForSubmit = this.valuesForSubmit.filter(d => d.action != "delete")
      }
      this.valuesForSubmit.forEach(e => {
        data.fileDetails.map((x) => {
          if (e.fieldName == x.fieldName) {
            e.fileDetailId = x.fileDetailId
          }
        });
      });
    })
  }

  /**
   * Invokes on save button in 2nd strp
   */
  onSaveClick() {
    this.isSave = true;
    this.submit(true)
  }

  /**
   * Invokes on Submit button
   * @function submit submits the Template Details to DB
   * @param isAutoSave
   */
  submit(isAutoSave = false) {
    if (this.isView) {
      return
    }
    if (!isAutoSave) {
      let submitBtnDisable = document.getElementsByClassName('btn-cta',) as HTMLCollectionOf<HTMLElement>;
      submitBtnDisable[3]?.classList.add("disabled");
    }
    isAutoSave ? this.disableCreateEditBtn = false : this.disableCreateEditBtn = true
    this.notificationOpen = "false";
    this.templateMappingDetails = [];
    this.templateMappingDetails = [];
    this.templateDateScreen = [];
    let action;
    this.mappingDataJSONs.src = this.valuesForSubmit
    this.mappingDataJSONs.src?.forEach(element => {
      if (this.autoSaveFileDetails) {
        let actionUpdateForSameRow = this.autoSaveFileDetails?.find(x => x.fileDetailId == element.fileDetailId);
        if (actionUpdateForSameRow && actionUpdateForSameRow?.fieldName != element?.fieldName || actionUpdateForSameRow?.fieldDataType != element?.fieldDataType || actionUpdateForSameRow?.fieldSize != element?.fieldSize || actionUpdateForSameRow?.recordType != element?.recordType || actionUpdateForSameRow?.fieldOption != element?.fieldOption || actionUpdateForSameRow?.cffFldLink != element?.cffFldLink || actionUpdateForSameRow?.cffConstValue != element?.cffConstValue) {
          action = FILE_TEMPLATE_CONSTANTS.UPDATE;
        } else {
          action = FILE_TEMPLATE_CONSTANTS.READ;
        }
      } else if (this.pageType == FILE_TEMPLATE_CONSTANTS.EDIT) {
        let actionUpdateForSameRowEdit = this.editPageFileDetails?.find(x => x.fileDetailId == element.fileDetailId)
        if (actionUpdateForSameRowEdit) {
          if (actionUpdateForSameRowEdit?.fieldName != element?.fieldName || actionUpdateForSameRowEdit?.fieldDataType != element?.fieldDataType || actionUpdateForSameRowEdit?.fieldSize != element?.fieldSize || actionUpdateForSameRowEdit?.recordType != element?.recordType || actionUpdateForSameRowEdit?.fieldOption != element?.fieldOption || actionUpdateForSameRowEdit?.cffFldName != element?.cffFldLink || actionUpdateForSameRowEdit?.cffConstValue != element?.cffConstValue) {
            action = FILE_TEMPLATE_CONSTANTS.UPDATE;
          } else {
            action = FILE_TEMPLATE_CONSTANTS.READ;
          }
        }
      }
      const mapDetails = {
        "fileDetailId": element.fileDetailId == undefined ? null : element.fileDetailId,
        "fieldSeqNo": has(element, FILE_TEMPLATE_CONSTANTS.FIELD_SEQ_NO) ? get(element, FILE_TEMPLATE_CONSTANTS.FIELD_SEQ_NO) : null,
        "fieldName": has(element, FILE_TEMPLATE_CONSTANTS.FIELD_NAME) ? get(element, FILE_TEMPLATE_CONSTANTS.FIELD_NAME) : null,
        "fieldDataType": has(element, FILE_TEMPLATE_CONSTANTS.FIELD_DATA_TYPE) ? get(element, FILE_TEMPLATE_CONSTANTS.FIELD_DATA_TYPE) : null,
        "fieldSize": has(element, FILE_TEMPLATE_CONSTANTS.FIELD_SIZE) ? get(element, FILE_TEMPLATE_CONSTANTS.FIELD_SIZE) : null,
        "recordType": element.recordType == "" ? null : element.recordType,
        "cffFldLink": (element.fieldOption == FILE_TEMPLATE_CONSTANTS.CONST_VALUE || element.fieldOption == "Null") ? null : element.cffId,
        "cffFldName": (element.fieldOption == FILE_TEMPLATE_CONSTANTS.CONST_VALUE || element.fieldOption == "Null") ? null : element.cffFldLink,
        "invClmTypInd": has(element, FILE_TEMPLATE_CONSTANTS.FIELD_TARGET_XREF) ? get(element, FILE_TEMPLATE_CONSTANTS.FIELD_TARGET_XREF) : null,
        "cffConstValue": element.cffConstValue == "" ? null : element.cffConstValue,
        "fieldOption": element.fieldOption == "" ? null : element.fieldOption,
        "action": element.action == "delete" ? "delete" : element.fileDetailId == undefined || element.fileDetailId == null ? "create" : action == undefined ? "update" : action,
      };
      if (mapDetails.fieldName != null && mapDetails.fieldName != '' && mapDetails.cffFldLink != '' && mapDetails.fieldSize != null && mapDetails.fieldSize != ''
        && mapDetails.fieldDataType != null && mapDetails.fieldDataType != '') {
        this.templateMappingDetails.push(mapDetails);
      }
    });
    this.currentTemplateData.fileDetails = this.templateMappingDetails;
    this.currentTemplateData.fileDetails.forEach(element => {
      element.invClmTypInd == "" ? element.invClmTypInd = "" : element.invClmTypInd == FILE_TEMPLATE_CONSTANTS.TARGET ? element.invClmTypInd = "Y" : element.invClmTypInd = "N";
    });
    if (this.editScreen || (this.autoSavefileTmplId != "" && isAutoSave)) {
      this.currentTemplate['modified'] = new Date().getDate() + '/' + (Number(new Date().getMonth()) + 1) + '/' + new Date().getFullYear();
      this.currentTemplate["Modified By"] = this.loggedInUser;
    } else {
      this.currentTemplate["CreatedBy"] = this.loggedInUser;
      this.currentTemplate["id"] = new Date().getTime();
      this.currentTemplate['Created'] = new Date().getDate() + '/' + (Number(new Date().getMonth()) + 1) + '/' + new Date().getFullYear();
      this.currentTemplate['modified'] = new Date().getDate() + '/' + (Number(new Date().getMonth()) + 1) + '/' + new Date().getFullYear();
    }
    if (this.editScreen) {
      this.currentTemplateData.lastModifiedUserId = this.loggedInUser;
      const indexItem = this.templateDateScreen.findIndex((elem) => {
        return elem.id === this.id
      })
      if (indexItem > -1) {
        this.templateDateScreen[indexItem]['currentTemplate'] = this.currentTemplate;
        this.templateDateScreen[indexItem]['tableData'] = this.mappingDataJSONs.src;
      }
    } else {
      let obj = {
        currentTemplate: this.currentTemplate,
        tableData: this.mappingDataJSONs.src,
        id: this.currentTemplate["id"],
        published: true
      }
      this.templateDateScreen.push(obj);
    }
    if (isAutoSave || this.isSave) {
      this.currentTemplateData.fileTmplId = (this.autoSavefileTmplId != "") ? this.autoSavefileTmplId : null;
      this.currentTemplateData.publishInd = true;
    }
    localStorage.setItem('templateJson', JSON.stringify(this.templateDateScreen));
    if (this.pageType == FILE_TEMPLATE_CONSTANTS.EDIT || this.currentTemplateData.fileTmplId != null) {
      // UPDATE TEMPLATE API CALL
      this.fileService.updateTemplateDetails(this.currentTemplateData).subscribe(data => {
        this.settingNotificationVariables(isAutoSave, data, 'update');
      });

    } else {
      // The SAVE TEMPLATE CODE API CALL IS SHIFTED HERE FOR DIFFRENTIATING THE UPDATE & CREATE TEMPLATE API CALL DIFFRENCES
      this.fileService.saveTemplateDetails(this.currentTemplateData).subscribe(data => {
        this.settingNotificationVariables(isAutoSave, data, 'create');
      });
    }
  }

  /**
   * Invokes on submit button click or on autosave
   * @function settingNotificationVariables will set all the notification data
   * @param isAutoSave
   * @param data
   * @param type
   */
  settingNotificationVariables(isAutoSave, data: any, type: string) {
    if (data?.message?.includes("Successfully")) {
      if (!isAutoSave) {
        if (type == FILE_TEMPLATE_CONSTANTS.UPDATE) {
          this.notificationBody = FILE_TEMPLATE_CONSTANTS.TEMPLATE_UPDATED_SUCCESSFULLY
        } else {
          this.notificationBody = FILE_TEMPLATE_CONSTANTS.TEMPLATE_CREATED_SUCCESSFULLY;
        }
        this.notificationType = "success";
        this.notificationDuration = 10000;
        this.notificationOpen = "true";
        setTimeout(() => {
          this.router.navigate(['/settings/files']).then(() => { window.location.reload() });
        }, 3000);
      } else {
        this.notificationType = "success"
        this.notificationDuration = 15000;
        if (!this.isSave) {
          this.notificationBody = FILE_TEMPLATE_CONSTANTS.NOTIFICATION_BODY_AUTO_SAVE;
        }
        else {
          this.notificationBody = FILE_TEMPLATE_CONSTANTS.NOTIFICATION_BODY_SAVE;
        }
        this.notificationOpen = "true";
        this.autoSavefileTmplId = data.data;
        this.currentTemplateData.fileTmplId = data.data;
        this.getSingleFileTemplateOnAutoSave(data.data, true)
      }
    } else {
      if (data?.includes(FILE_TEMPLATE_CONSTANTS.TEMPLATE_NAME_EXIST)) {
        this.notificationOpen = "true";
        this.notificationType = "warning";
        this.notificationBody = FILE_TEMPLATE_CONSTANTS.FILE_TEMPLATE_ALREADY_EXIST;
        this.notificationDuration = 15000;
      }
    }
  }

  /**
   * reuse able funtion to minimise the code
   * Method invoked from dynamic forms onValueChanges funcitons
   * will enable or disable the next buttons Conditionally of MKP Stepper
   */
  enableDisableNextBtns = () => {
    this.disabledButton = this.isView ? false : !(this.enableFormButtton && this.enabletwoFormButtton && !this.sameTemplateName);
    this.disableCreateEditBtn = this.disabledButton;
    this.addEditHeaderConfig[1].enabled = !this.disabledButton && this.selectedDelimiterBool;
    this.addEditHeaderConfig[2].enabled = !this.disabledButton && this.isMandatoryValuesFilled;
  }


  /**
   * detecting FOrm Value while each value change
   * use of html template reference->for dynamically Enabling disabled button , onValueChanges - return previous and current value, to get valid status i am using the templateref
   * @param event
   * @param item
   */
  formValue(event: any, item: string) {

    let prevSys = event?.previous?.details?.system;
    let currSys = event?.current?.details?.system

    let prevInvenType = event?.previous?.details?.inventoryType
    let prevProdName = event?.previous?.details?.productName

    let currInvenType = event?.current?.details?.inventoryType
    let currProdName = event?.current?.details?.productName

    if (prevInvenType != currInvenType || prevProdName != currProdName) {
      this.callCffFldLinkApi = true;
    }

    this.notificationOpen = "false";
    this.filteredInventoryType = this.allInventoryType?.filter(e => e.other == this.systemInventory?.filter(res => res.id == event.current.details.system)[0]?.id);
    this.filteredProdNames = this.allProdNames?.filter(res => res.invTypId == event.current.details.inventoryType);
    this.simpleFormValidationJson.forEach(element => {
      if (element.name == FILE_TEMPLATE_CONSTANTS.DETAILS) {
        element.groupControls.forEach(x => {
          if (x.id == FILE_TEMPLATE_CONSTANTS.INVENTORY_TYPE) {
            x.options = this.filteredInventoryType;
            if (prevSys != currSys) {
              x.selectedVal = null
            }
          }
          if (x.id == FILE_TEMPLATE_CONSTANTS.PRODUCT_NAME) {
            x.options = this.filteredProdNames;
            if (prevSys != currSys || prevInvenType != currInvenType) {
              x.selectedVal = null
            }
          }
        });
      }
    });
    this.filteredInventoryType?.forEach(e => {
      if (e.id == event.current.details.inventoryType) {
        this.selectedInvType = e.name;
      }
    });
    this.filteredProdNames?.forEach(e => {
      if (e.id == event.current.details.productName) {
        this.selectedProdName = e.name;
        this.selectedProdId = e.prodId;
      }
    });
    if (item === FILE_TEMPLATE_CONSTANTS.DETAILS) {
      this.enableFormButtton = this.templateName.form.status === FILE_TEMPLATE_CONSTANTS.VALID ? true : false;
    }
    this.currentTemplate[item] = event.current;
    let value = event.current.memberDetails.templatename;
    if (this.pageType == FILE_TEMPLATE_CONSTANTS.EDIT) {
      if (event.current.memberDetails.templatename.toLowerCase() != event.previous.memberDetails.templatename.toLowerCase()) {
        if (value.toLowerCase() != this.apiForm.fileTmplName.toLowerCase()) {
          this.sameTemplateName = this.allTemplateDataListJSON.some((e) => e.fileTmplName.toLowerCase() == value.toLowerCase())
        }
      }
    } else {
      this.sameTemplateName = this.allTemplateDataListJSON.some((e) => e.fileTmplName.toLowerCase() == value.toLowerCase())
    }
    if (this.sameTemplateName) {

      this.alertService.setWarningNotification({
        notificationBody: ADD_EDIT_CONSTANTS.DUPLICATE_NAME_ERROR,
        notificationHeader: ADD_EDIT_CONSTANTS.WARNING_HEADER,
        notificationDuration: 4000,
      });

      this.disableCreateEditBtn = true;
    } else {
      this.disableCreateEditBtn = false;
    }
    this.enableDisableNextBtns();
  }

  /**
   * detecting FOrm Value while each value change
    use of html template reference->for dynamically Enabling disabled button , onValueChanges - return previous and current value, to get valid status i am using the templateref
   * @param event
   * @param item
   */
  formNCValue(event: any, item: string) {
    if (item === 'nc') {
      this.enabletwoFormButtton = this.templateNc.form.status === FILE_TEMPLATE_CONSTANTS.VALID ? true : false;
    }
    this.currentTemplate[item] = event.current;
    this.enableDisableNextBtns();
  }

  /*
  setting form values
  1.setting of radio selection in delimiter
  2.enabling of all dynamic forms
  */
  setTheFormValue() {
    this.setDynamicFOrm();
    this.delimiterDataset = [
      { name: 'Tab', value: 'tab' },
      { name: 'Semicolon', value: 'semicolon' },
      { name: 'Comma', value: 'comma' },
      { name: 'Space', value: 'space' },
      { name: 'Other', value: 'other' }
    ];
    this.simpleRadio = [
      {
        "type": "group",
        "name": "fileDetails",
        "label": "",
        "column": "1",
        "groupControls": [{
          "options": this.delimiterDataset,
          "optionName": "name",
          "optionValue": "value",
          "label": "",
          "type": "radio",
          "multiple": false,
          "closeOnSelect": true,
          "name": "type",
          "column": "4",
          "orientation": "vertical",
          "disabled": this.isView || this.showDelimiterOptions,
          "border": true,
          "value": this.fixedWidthSelected ? "" : this.checkboxSelected,
        }]
      }
    ];
    this.showFormItem = true;
    this.showFormncItem = true;
    this.radioSelection = true;
    this.isTableJsonReady = false;
    this.nextSetup();
  }
  /** 
   * Invoked when navigated to file template table details 
   * @function getTableData To get the file template table data 
  */
  getTableData() {
    this.mappingDataJSONs = {
      "fieldDataType": [
        { label: "String", id: "1" },
        { label: "Integer", id: "2" },
        { label: "Date", id: "3" },
        { label: "Date Time", id: "4" },
        { label: "Boolean", id: "5" },
      ],
      "invClmTypInd": [
        { label: "Target", id: "1" },
        { label: "Xref", id: "2" }
      ],
      "CFF": this.cffdetails,
      "Req": [
        { label: "Required", id: "1" },
        { label: "Optional", id: "2" },
        { label: "Null", id: "3" },
        { label: "Constant Value", id: "4" }
      ],
      "RecordType": [
        { label: "Header1", id: "1" },
        { label: "Header2", id: "2" },
        { label: "Header3", id: "3" }
      ],
      "src": this.editScreen && this.apiForm.fileDetails != undefined ? this.apiForm.fileDetails : this.mappingDataJSON
    };
    this.initialFieldValues = this.editScreen && this.apiForm.fileDetails != undefined ? JSON.parse(JSON.stringify(this.apiForm.fileDetails)) : ''
    this.valuesForSubmit = this.editScreen && this.apiForm.fileDetails != undefined ? JSON.parse(JSON.stringify(this.apiForm.fileDetails)) : this.mappingDataJSON
    setTimeout(() => {
      this.isTableJsonReady = true;
    }, 0);

  }

  /**
    * Dynamic form setting form values
    * @function setDynamicFOrm binding of Template details in specification tab
   */
  setDynamicFOrm() {
    let toggleStatus = this.currentTemplate.details.details.status;
    this.simpleFormValidationJson.forEach(elem => {
      elem.groupControls.forEach(x => {
        x.disabled = this.isView;
        switch (x.id) {
          case FILE_TEMPLATE_CONSTANTS.TEMPLATE_NAME:
            x.value = has(this.currentTemplate, 'details.memberDetails.templatename') ? get(this.currentTemplate, 'details.memberDetails.templatename') : "";
            break;

          case FILE_TEMPLATE_CONSTANTS.SPEC_DESC_TXT_AREA:
            x.value = has(this.currentTemplate, 'details.memberDetails.description') ? get(this.currentTemplate, 'details.memberDetails.description') : "";
            break;

          case FILE_TEMPLATE_CONSTANTS.FILE_TYPE:
            x.selectedVal = has(this.currentTemplate, 'details.details.fileType') ? get(this.currentTemplate, 'details.details.fileType') : "";
            break;

          case FILE_TEMPLATE_CONSTANTS.SYSTEM_INVENTORY:
            x.options = this.systemInventory;
            x.selectedVal = has(this.currentTemplate, 'details.details.system') ? get(this.currentTemplate, 'details.details.system') : "";
            break;

          case FILE_TEMPLATE_CONSTANTS.INVENTORY_TYPE:
            x.options = this.pageType == 'Create new' ? [] : this.filteredInventoryType;
            x.selectedVal = has(this.currentTemplate, 'details.details.inventoryType') ? get(this.currentTemplate, 'details.details.inventoryType') : "";
            break;

          case FILE_TEMPLATE_CONSTANTS.TEMPLATE_TYPE:
            x.selectedVal = has(this.currentTemplate, 'details.details.templateType') ? get(this.currentTemplate, 'details.details.templateType') : "PROD";
            break;

          case FILE_TEMPLATE_CONSTANTS.PRODUCT_NAME:
            x.options = this.pageType == 'Create new' ? [] : this.filteredProdNames;
            x.selectedVal = has(this.currentTemplate, 'details.details.productName') ? get(this.currentTemplate, 'details.details.productName') : "";
            break;

          case FILE_TEMPLATE_CONSTANTS.ACTIVE_STATUS:
            x.value = toggleStatus;
            break;

          case FILE_TEMPLATE_CONSTANTS.HEADER_CHECKBOX:
            x.options.forEach(z => {
              z.value = has(this.currentTemplate, 'details.details.header.headers') ? get(this.currentTemplate, 'details.details.header.headers') : false;
              z.disabled = this.isView;
            });
            break;

          default:
            break;
        }
      });
    });
    this.simpleFormDYnamicValidationJson = [
      {
        "type": "group",
        "name": "NamingConventions",
        "label": "Naming Convention",
        "column": "1",
        "groupControls": [
          {
            type: "checkboxgroup",
            name: "cg",
            label: "",
            column: 1,
            checkboxgroupcolumn: 2,
            required: true,
            options: [
              {
                name: "Import", label: "Import",
                value: has(this.currentTemplate, 'nc.NamingConventions.cg.Import') ? get(this.currentTemplate, 'nc.NamingConventions.cg.Import') : "",
                disabled: this.isView,
              },
              {
                name: "Export",
                label: "Export",
                value: has(this.currentTemplate, 'nc.NamingConventions.cg.Export') ? get(this.currentTemplate, 'nc.NamingConventions.cg.Export') : "",
                disabled: this.isView,
              },
            ]
          },
          {
            "label": "Prefix",
            "type": "text",
            "name": "prefix",
            "column": "1",
            "disabled": this.isView,
            "required": true,
            "placeholder": "Enter text",
            "value": has(this.currentTemplate, 'nc.NamingConventions.prefix') ? get(this.currentTemplate, 'nc.NamingConventions.prefix') : ""
          },
          {
            "options": [
              {
                "name": "DDMMYYYY",
                "code": "DDMMYYYY"
              },
              {
                "name": "MMYYYY",
                "code": "MMYYYY"
              },
              {
                "name": "DDMM",
                "code": "DDMM"
              },
              {
                "name": "YYYYMMDDHH24MM",
                "code": "YYYYMMDDHH24MM"
              },
              {
                "name": "YYYYMMDDHH24MISS",
                "code": "YYYYMMDDHH24MISS"
              },
              {
                "name": "YYYYMMDDHH24MISSFF",
                "code": "YYYYMMDDHH24MISSFF"
              }
            ],
            "optionName": "name",
            "optionValue": "code",
            "label": "Sufix",
            "type": "select",
            "multiple": false,
            "closeOnSelect": true,
            "name": "sufix",
            "id": "sufix",
            "column": "1",
            "disabled": this.isView,
            "required": true,
            "placeholder": "Choose the date time picker",
            "selectedVal": has(this.currentTemplate, 'nc.NamingConventions.sufix') ? get(this.currentTemplate, 'nc.NamingConventions.sufix') : ""
          }
        ],
      }
    ];
  }

  /*
  radio form Value changes from delimiter or fixed width
  */
  changeEvent(event, element) {

    this.radioSelection = false;
    this.simpleRadio[0].groupControls[0].value = "";
    this.checkboxSelected = "";
    this.currentTemplate.delimiter.value = ""
    this.currentTemplate.delimiter.type = event;
    setTimeout(() => {
      this.currentTemplate.delimiter.type = event;
      this.radioSelection = true;
      document.getElementById(element).click()
    }, 0);
  }

  /**
   * Method Invoked when popup is closed in Mapping Tab's Table's field Option is Selected as Constant Value
   * @param event
   */
  closePopup(event: any) {
    if (this.currentRowClicked?.fieldOption == FILE_TEMPLATE_CONSTANTS.CONST_VALUE && (this.currentRowClicked?.cffConstValue == "" || this.currentRowClicked?.cffConstValue == null)) {
      this.isMandatoryValuesFilled = false;
      this.addEditHeaderConfig[2].enabled = this.isMandatoryValuesFilled
    }
    this.columnConfigInlineEdit.colDefs.forEach(x => {
      if (x.field == "cffConstValue") {
        x.editorType = "Text";
      }
    })
    this.tableRecreate = Date.now();

    this.cffLinkPopupTxtColor = false;
    this.cffLinkPopup = false;
  }

  /**
   * To Check all mandatory values are filled in Mapping Tab's Table
   * @param value
   */
  checkMandatoryFieldsFilled = (value: any) => {
    let count = 0;
    this.isMandatoryValuesFilled = false;
    this.addEditHeaderConfig[2].enabled = this.isMandatoryValuesFilled

    this.mappingDataJSONs.src.forEach((e) => {
      if (e.id == this.currentRowClicked.id) {
        e.cffConstValue = value
      }

      if (e.fieldName == "" || e.fieldSize === "" || e.fieldDataType == "" || (e.cffFldLink == "" && e.fieldOption != FILE_TEMPLATE_CONSTANTS.CONST_VALUE) || (e.fieldOption == FILE_TEMPLATE_CONSTANTS.CONST_VALUE && e.cffConstValue == "")) {
        count++;
      }
    })

    if (count == 0) {
      this.isMandatoryValuesFilled = true;
      this.addEditHeaderConfig[2].enabled = this.isMandatoryValuesFilled
    }
  }

  /**
   * Method Invoked when Save Button is clicked in PopUp of Mapping Tab's Table's field Option is Selected as Constant Value
   * @param event
   */
  saveValue(event: any) {

    this.valuesForSubmit = this.valuesForSubmit.map(obj => this.mappingDataJSONs.src.find(o => o.id === obj.id) || obj);
    let value: any = document.getElementById('custom-val')['value'];

    if (value == undefined || value?.trim().length == 0) {
      this.cffLinkPopupTxtColor = true;

    } else {

      this.columnConfigInlineEdit.colDefs.forEach(x => {
        if (x.field == "cffConstValue") {
          x.editorType = "Text";
        }
      })

      this.checkMandatoryFieldsFilled(value);
      this.cffLinkPopupTxtColor = false;
      this.cffLinkPopup = false;
      this.clickedCellSecond = false;
      if (this.clickedCellSecond) {
        this.tableRecreate = Date.now();
      } else {
        this.currentRowToUpdate['cffConstValue'] = value;
        this.updateExistingRow = [this.currentRowToUpdate];

      }
    }
  }
  /*
  When click on fixedwidth radio button
  */
  fixedWidthChanges(event: any) {
    if (event.current.fixedWidth == 'fixedWidth') {
      this.fixedWidthSelected = true;
      this.showDelimiter = false;
      this.radioSelection = false;
      this.delimiterRadio[0]['value'] = '';

      this.showDelimiterOptions = true;
      this.simpleRadio[0].groupControls[0].disabled = true;
      this.checkboxSelected = "Hide";
      this.currentTemplate.delimiter.value = ""

      this.simpleRadio[0].groupControls[0].value = "";
      this.currentTemplate.delimiter.type = 1;
      this.selectedDelimiterBool = true;
      setTimeout(() => {
        this.radioSelection = true;
        this.showDelimiter = true;
        this.enableDisableNextBtns();
      }, 0);
    }
  }

  /*
  When click on delimeter radio button
  */
  delimiterChanges(event: any) {
    if (event.current.delimiter == ADD_EDIT_CONSTANTS.DELIMITER) {
      this.fixedWidthSelected = false;
      this.showFixedWidth = false;
      this.fixedWidthRadio[0][ADD_EDIT_CONSTANTS.VALUE] = '';
      this.showDelimiterOptions = false;
      this.simpleRadio[0].groupControls[0].disabled = false;
      this.currentTemplate.delimiter.type = 0;
      this.selectedDelimiterBool = false;
      setTimeout(() => {
        this.showFixedWidth = true;
        this.enableDisableNextBtns();
      }, 0);
    }
  }

  /*
  Table selection of a cell in each row in table
  */
  onCellValueChange(event) {
    if (event.args.column.field == 'fieldOption') {
      if (event.args.item.fieldOption == 'Required' || event.args.item.fieldOption == 'Optional') {
        event.args.item.cffFldLink = '';
        event.args.item.cffLength = '';
      }
      if (event.args.item.fieldOption == FILE_TEMPLATE_CONSTANTS.CONST_VALUE) {
        this.currentRowToUpdate = event.args.item;
        event.args.item.cffFldLink = '';
        event.args.item.cffLength = '';
        this.hcValue = '';
        this.cffLinkPopup = Date.now();
      } else {
        this.currentRowToUpdate = event.args.item;
        this.currentRowToUpdate['cffConstValue'] = '';
        this.updateExistingRow = [this.currentRowToUpdate];
      }

      if (event.args.item.fieldOption == "" && (event.args.item.cffFldLink == "" || event.args.item.cffFldLink == "Null")) {
        event.args.item.cffFldLink = '';
        event.args.item.cffLength = '';
      }
    }

    if (event.args.column.field == 'fieldOption' && event.args.item.fieldOption == 'Null') {
      event.args.item.cffFldLink = 'Null'
      event.args.item.cffLength = 'Null'
    }

    this.valuesForSubmit = this.valuesForSubmit.map(obj => event.dataset.find(o => o.id === obj.id) || obj);
    if (event.args.column.name == "Data Type") {
      event.args.item.size = 16;

      event.args.grid.updateCell(event.args.row, event.args.cell + 1);
    }

    let count = 0;
    this.isMandatoryValuesFilled = false;
    this.addEditHeaderConfig[2].enabled = this.isMandatoryValuesFilled

    event.dataset.forEach(x => {
      if (x.fieldName == "" || x.fieldSize === "" || x.fieldDataType == "" || (x.cffFldLink == "" && x.fieldOption != FILE_TEMPLATE_CONSTANTS.CONST_VALUE) || (x.fieldOption == FILE_TEMPLATE_CONSTANTS.CONST_VALUE && x.cffConstValue == "")) {
        count++;
      }
    })
    if (count == 0) {
      this.isMandatoryValuesFilled = true;
      this.addEditHeaderConfig[2].enabled = this.isMandatoryValuesFilled
    }
    this.updatedDropdownOptions = [...this.cffValueOnEditPage];
    if (event.args.item.invClmTypInd == FILE_TEMPLATE_CONSTANTS.TARGET || event.args.item.invClmTypInd == FILE_TEMPLATE_CONSTANTS.XREF) {
      event.dataset.forEach(datasetelement => {
        if (event.args.item.invClmTypInd == FILE_TEMPLATE_CONSTANTS.TARGET) {
          if ((datasetelement.invClmTypInd == FILE_TEMPLATE_CONSTANTS.TARGET || datasetelement.invClmTypInd == "" || datasetelement.invClmTypInd == null) && datasetelement.cffFldLink != "") {
            let obj = this.updatedDropdownOptions.find(ele => ele.label == datasetelement.cffFldLink);
            this.updatedDropdownOptions = this.updatedDropdownOptions.filter(ele => {

              return obj[FILE_TEMPLATE_CONSTANTS.LABEL] != ele[FILE_TEMPLATE_CONSTANTS.LABEL];
            });
          }
        }

        else if (event.args.item.invClmTypInd == FILE_TEMPLATE_CONSTANTS.XREF) {
          if ((datasetelement.invClmTypInd == FILE_TEMPLATE_CONSTANTS.XREF || datasetelement.invClmTypInd == "" || datasetelement.invClmTypInd == null) && datasetelement.cffFldLink != "") {
            let obj = this.updatedDropdownOptions.find(ele => ele.label == datasetelement.cffFldLink);
            this.updatedDropdownOptions = this.updatedDropdownOptions.filter(ele => {
              return obj[FILE_TEMPLATE_CONSTANTS.LABEL] != ele[FILE_TEMPLATE_CONSTANTS.LABEL];
            });
          }
        }

      });

    } else {
      this.updatedDropdownOptions = this.cffValueOnEditPage.filter(cffdetailsValue => {
        return !event.dataset.some(eventValue => {
          return eventValue['cffFldLink'] === cffdetailsValue['label']
        })
      });
    }
    event.args.item.cffFldLink == "" ? event.args.item.cffLength = "" : ""

    event.args.grid.updateCell(event.args.row, event.args.cell + 1);
    if (this.mappingRowdata?.dataset) {
      this.cfflinkCount =
        this.mappingRowdata?.dataset?.filter(x =>
          x.fieldToLink != ''
        ).length
    }
    else if (event.args.column.name == "CFF Field To Link *") {
      this.cfflinkCount = 1
    }
    this.cffdetails.map((e) => {
      if (e.label == event.args.item.cffFldLink) {
        event.args.item.cffLength = e.other
        event.args.item.cffId = e.id
      }
    })
    this.mappingDataJSONs.CFF = this.updatedDropdownOptions

    if (event.args.column.name == "CFF Field To Link *" || event.args.column.name == "Field Option") {
      this.tableRecreate = Date.now();
    }
    this.apiForm.fileDetails = this.mappingDataJSONs?.src
  }

  /*
  When fetching the data from APi to form
  Need to check validation of form
  */
  ngAfterViewChecked() {
    if (this.pageType == FILE_TEMPLATE_CONSTANTS.EDIT && this.templateName && this.templateNc) {
      this.enableFormButtton = this.templateName.form.status === FILE_TEMPLATE_CONSTANTS.VALID ? true : false;
      this.enabletwoFormButtton = this.templateNc.form.status === FILE_TEMPLATE_CONSTANTS.VALID ? true : false;
      this.enableDisableNextBtns();
    }
  }

  /**
   * create function for Auto save timer
   */
  startTimer() {
    this.interval = setInterval(() => {
      if (this.timeLeft > 0) {
        this.timeLeft--;
      } else {
        this.next(2, true);
        setTimeout(() => {
          if (!this.disabledButton) {
            this.nextSetup();
            this.submit(true);
          }
        }, 0);
        this.timeLeft = this.autoSavetime;
      }
    }, 1000);
  }
  /**
   *  clear interval
   */
  pauseTimer() {
    clearInterval(this.interval);
  }

  /**
   *
   * @function ngOnDestroy - Prevents memory leaks for any observables
   */
  ngOnDestroy() {
    this.autoSavefileTmplId = "";
    this.pauseTimer();
  }
  /**
   * This method will trigger on click of the add row button in the table in second tab
   * This method will add a new row to the existing table
   * @param event
   */
  onAddRowClick(event: any) {
    this.updateValuesForSubmit = event.dataset.filter(x => {
      return !this.valuesForSubmit?.some(eventValue => {
        return eventValue.id === x.id
      })
    })
    this.updateValuesForSubmit.forEach(x => {
      this.valuesForSubmit?.push(x);
    })
    this.mappingDataJSONs.src = event.dataset
    this.updatedDropdownOptions = this.cffValueOnEditPage.filter(cffdetailsValue => {
      return !event.dataset.some(eventValue => {
        return eventValue['cffFldLink'] === cffdetailsValue['label']
      })
    })
    this.mappingDataJSONs.CFF = this.updatedDropdownOptions
    let l = event.dataset.length;
    event.dataset.forEach(x => {
      if (x.fieldSeqNo == "") {
        x.fieldSeqNo = l++;
        x.fieldSeqNo.toString();
      }
    })
    this.tableRecreate = Date.now();
    this.valuesOnAddRow = event.dataset
    this.isMandatoryValuesFilled = false;
    this.addEditHeaderConfig[2].enabled = this.isMandatoryValuesFilled
  }
}
