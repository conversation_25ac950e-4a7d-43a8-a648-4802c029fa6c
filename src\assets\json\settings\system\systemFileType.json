{"columnConfig": {"switches": {"enableSorting": true, "enablePagination": true, "enableFiltering": true}, "colDefs": [{"name": "System Code", "field": "systemCode", "filterType": "Text", "visible": "True", "editorType": "Long Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 60}, {"name": "System Name", "field": "systemName", "filterType": "Text", "visible": "True", "editorType": "Long Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 180}, {"name": "Inventory Type", "field": "inventoryType", "filterType": "Text", "visible": "True", "editorType": "Long Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 160}, {"name": "Created Date", "field": "createdDate", "filterType": "Calendar", "visible": "True", "editorType": "Long Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 110}, {"name": "Created By", "field": "created<PERSON>y", "filterType": "Text", "visible": "True", "editorType": "Long Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 55}, {"name": "Last Modified Date", "field": "lastModifiedDate", "filterType": "Calendar", "visible": "True", "editorType": "Long Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 110}, {"name": "Last Modified By", "field": "lastModifiedBy", "filterType": "Calendar", "visible": "True", "editorType": "Long Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}]}, "columnConfigSystemFiletype": {"switches": {}, "colDefs": [{"name": "Template Name", "field": "fileTmplName", "filterType": "Text", "visible": "True", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 165}, {"name": "Format", "field": "fileFrmtTxt", "filterType": "Text", "visible": "True", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 50}, {"name": "Inventory Type", "field": "invTypeName", "filterType": "Text", "visible": "True", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 70}, {"name": "Enable Upload", "field": "enabledUpld", "visible": "True", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 50}, {"name": "File Settings", "field": "finish", "visible": "True", "width": 180}]}}