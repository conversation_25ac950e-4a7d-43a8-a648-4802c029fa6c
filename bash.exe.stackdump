Stack trace:
Frame         Function      Args
0007FFFFAC00  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFAC00, 0007FFFF9B00) msys-2.0.dll+0x1FEBA
0007FFFFAC00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAED8) msys-2.0.dll+0x67F9
0007FFFFAC00  000210046832 (000210285FF9, 0007FFFFAAB8, 0007FFFFAC00, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC00  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFAC00  0002100690B4 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAEE0  00021006A49D (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8753F0000 ntdll.dll
7FF874210000 KERNEL32.DLL
7FF872850000 KERNELBASE.dll
7FF873080000 USER32.dll
7FF872510000 win32u.dll
7FF873390000 GDI32.dll
7FF872720000 gdi32full.dll
000210040000 msys-2.0.dll
7FF872680000 msvcp_win.dll
7FF872F60000 ucrtbase.dll
7FF8752F0000 advapi32.dll
7FF873640000 msvcrt.dll
7FF873430000 sechost.dll
7FF8724E0000 bcrypt.dll
7FF873520000 RPCRT4.dll
7FF871D60000 CRYPTBASE.DLL
7FF872DA0000 bcryptPrimitives.dll
7FF8734E0000 IMM32.DLL
000000C60000 umppc19508.dll
7FF870B30000 PGHook.dll
