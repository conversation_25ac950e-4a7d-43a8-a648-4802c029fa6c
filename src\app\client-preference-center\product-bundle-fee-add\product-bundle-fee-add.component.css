app-product-bundle-fee-add .card {
  border: 3px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
}
app-product-bundle-fee-add .title {
  
  font-style: normal;
  font-weight: 900;
  font-size: 24px;
  line-height: 34px;
  color: #000000;
  padding: 0px;
}
app-product-bundle-fee-add .fa-chevron-circle-left {
  font-size: 25px;
  color: #5009B5;
  margin-right: 5px;
}

app-product-bundle-fee-add .custom-btn {
  padding: 10px 24px !important;
  margin-right: 20px;
  /* background: #5009B5 !important; */
  border-radius: 4px !important;
}

app-product-bundle-fee-add hr {
  width: 100%;
  height: 0px;
  left: 254px;
  top: 324px;
  border: 1px solid #dbdbdb;
}

app-product-bundle-fee-add .label-title {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #666666;
  margin-left: 20px;
}

app-product-bundle-fee-add .label-title {
  color: #666666 !important;
}

app-product-bundle-fee-add .label-value {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #000000;
  padding-left: 5px;
}

app-product-bundle-fee-add .pad-05 {
  padding: 5px 0px;
}

app-product-bundle-fee-add .mar-0 {
  margin-left: 0px;
}

app-product-bundle-fee-add .mar-top-20 {
  margin-top: 20px;
  position: relative;
}

app-product-bundle-fee-add .pad-lt-35 {
  padding-left: 35px;
}

app-product-bundle-fee-add .pad-btm-30 {
  position: absolute;
  right: 0;
  bottom: 0;
}

app-product-bundle-fee-add .modal-footer {
  padding: 0px !important;
  margin-top: -20px !important;
}

app-product-bundle-fee-add .pad-20 {
  padding-left: 20%
}

app-product-bundle-fee-add .pad-30 {
  margin-left: 35%;
}

app-product-bundle-fee-add .close-icon-color {
  color: #5009B5;
}

app-product-bundle-fee-add .modal-content {
  width: 140% !important;
  margin-top: 30% !important;
}

app-product-bundle-fee-add .custom-title {
  
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 29px;
  color: #5009B5;
}

app-product-bundle-fee-add .custom-message {
  
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 19px;
  color: #4e4e4e;
}
app-product-bundle-fee-add .modal-header-custom {
  width: 100%;
  display: flex;
  justify-content: center;
}

app-product-bundle-fee-add.spinner-border {
  display: block;
  position: fixed;
  top: calc(50% - (58px / 2));
  right: calc(40% - (58px / 2));
  width: 5rem;
  height: 5rem;
}

app-product-bundle-fee-add .backdrop {
  position: absolute;
  right: 13px;
}
