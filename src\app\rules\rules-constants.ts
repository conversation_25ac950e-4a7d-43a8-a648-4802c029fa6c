export const constants = {
    START_DATE: "start_date",
    END_DATE: "end_date",
    UPDATED_BY: "updated_by",
    UPDATED_DATE: "updated date",
    ANTM: "ANTM",
    RULE_QUERY_SPEC_JSON: "./assets/json/rules/rules-sql-structure.json",
    QUERY_BUILDER: "qb",
    GLOBAL: "Global",
    ANTHEM: "Anthem",
    DATE: "date",
    GLOBAL_LEVEL: "Global Level",
    RULE_ID: "rule_id",
    RULE_LEVEL: "rule_level",
    NUMBERSEL: "numberSelect",
    RULE_TYPE: "Rule Type",
    RULES: "rules",
    SELECT: "Select",
    ATTENTION: "ATTENTION",
    FILL_QB_STATUS_MESSAGE: "Please fill query builder crieteria before uploading the file",
    CONCEPT_LEVEL: "Concept Level",
    CLIENT_LEVEL: "Client Level",
    CLIENTID: "clientId",
    CLNT_ID: "CLNT_ID",
    CONCEPT_ID: "conceptId",
    VALID: "VALID",
    QUERY_FIELDS: "query_fields",
    TOKEN: "token",
    RULES_LEVEL: "rulesLevel",
    GROUP_CONTROLS: "groupControls",
    GRACE_PERIOD: "Grace Period(in days)",
    INVALID: "INVALID",
    RULE_EDITED_MESSAGE: "Rule Edited Successfully",
    RULE_SUBMISSION_MESSAGE: "Rule Submitted Successfully",
    RULE_SAVED_MESSAGE: "Rule Saved Successfully",
    LAG: "lag",
    BUSINESS_DIVISION: "businessDivision",
    PRODUCT: "product",
    DATA_MINING_SOLUTION: "Data Mining Solution",
    KS_CLIENT_ID: 87,
    MEDICA_CLIENT_ID: 86,
    BCBSKC_CLIENT_ID: 65,
    RULE_LEVEL_GLOBAL: 'global',
    RULE_LEVEL_CONCEPT: 'concept',
    RULE_LEVEL_CLIENT: 'client'
}