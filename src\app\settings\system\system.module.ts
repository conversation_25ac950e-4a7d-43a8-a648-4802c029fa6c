import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SystemRoutingModule } from './sytem-routing.module';
import { SystemComponent } from './system-list/system.component';
import {MPUITableModule} from 'marketplace-table';
import {MPUIPanelGroup} from 'marketplace-accordion';
import {MPUINotificationModule} from 'marketplace-notification';
import {MPUIPaginationModule} from 'marketplace-pagination';
import { MPUIBreadcrumbModule } from 'marketplace-breadcrumb';
import {MPUITabsModule} from 'marketplace-tabs';
import { MPUIButtonModule } from 'marketplace-button';
import { MPUIDatePickerModule } from 'marketplace-date-picker';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { MPUITextareaModule } from 'marketplace-textarea';
import { MPUISwitchModule } from 'marketplace-switch';
import { MPUIInputModule } from 'marketplace-input';


@NgModule({
  declarations: [
      SystemComponent
  ],
  imports: [
    CommonModule,
    SystemRoutingModule,
    MPUITextareaModule,
    MPUISwitchModule,
    MPUIDatePickerModule,
    MPUIButtonModule,

    MPUIPanelGroup,
    MPUINotificationModule,
    MPUIPaginationModule,
    MPUIBreadcrumbModule,
    MPUITableModule,
    MPUITabsModule,
    MPUIDynamicFormModule,
    MPUIInputModule
   
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class SystemModule { }