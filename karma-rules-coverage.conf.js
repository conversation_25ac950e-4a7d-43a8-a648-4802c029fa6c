// Karma configuration for rules folder coverage demonstration
module.exports = function (config) {
  config.set({
    basePath: '',
    frameworks: ['jasmine'],
    plugins: [
      require('karma-jasmine'),
      require('karma-chrome-launcher'),
      require('karma-jasmine-html-reporter'),
      require('karma-coverage')
    ],
    client: {
      jasmine: {
        random: false
      },
      clearContext: false
    },
    jasmineHtmlReporter: {
      suppressAll: true
    },
    coverageReporter: {
      dir: require('path').join(__dirname, './coverage/rules-coverage'),
      subdir: '.',
      reporters: [
        { type: 'html' },
        { type: 'text-summary' },
        { type: 'lcov' },
        { type: 'text' }
      ],
      check: {
        global: {
          statements: 85,
          branches: 75,
          functions: 85,
          lines: 85
        }
      },
      // Include only rules folder in coverage calculation
      include: [
        'src/app/rules/**/*.ts'
      ],
      // Exclude specific folders and files from coverage
      exclude: [
        'src/app/users/**/*',
        'src/app/users/_services/**/*',
        'src/app/_services/**/*',
        'src/app/_constants/**/*',
        'src/app/rules/**/*.spec.ts',
        'src/app/rules/**/test-*.ts'
      ]
    },
    reporters: ['progress', 'kjhtml', 'coverage'],
    port: 9876,
    colors: true,
    logLevel: config.LOG_INFO,
    autoWatch: false,
    browsers: ['ChromeHeadless'],
    singleRun: true,
    restartOnFileChange: false,
    // Include ALL rules test files for maximum coverage
    files: [
      'src/app/rules/**/*.spec.ts'
    ],
    exclude: [
      // Exclude folders from coverage calculation
      'src/app/users/**/*',
      'src/app/users/_services/**/*',
      'src/app/_services/**/*',
      'src/app/_constants/**/*'
    ]
  });
};
