import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { forkJoin } from 'rxjs';
import { QueryBuildFormatterService } from '../../_services/query-build-formatter.service';
import { list, QBOperators } from '../constant';
import { typeMapping } from '../client-preference';

@Component({
  selector: 'app-client-preference-view',
  templateUrl: './client-preference-view.component.html',
  styleUrls: ['./client-preference-view.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class ClientPreferenceViewComponent implements OnInit {
  isdisabled: boolean = false;
  showQueryBuilder: boolean = false;
  editData: any = {};
  @Output() DataEvent = new EventEmitter<string>();
  @Input() editDataFromList: any;
  public dataExchangequery = {};
  dataExchangeconfig = {
    fields: {}
  };
  showLoader: boolean = false;
  isTemplateReady: boolean = false;
  operators: any = QBOperators;

  constructor(public el: ElementRef, private clientApiService: ClientApiService, private queryBuildServer: QueryBuildFormatterService) { }

  ngOnInit(): void {
    this.showLoader = false;
    const obv1 = this.clientApiService.getClientPreferencesViewDataExchange(this.editDataFromList.preferenceId);
    forkJoin([obv1]).subscribe(([data]) => {
      if (!data) return;
      this.editData = data['dataExchange']
      if (data["condition"]["rules"].length) {
        for (const obj of data["condition"]["rules"]) {
          obj['static'] = obj['stat'];
          delete obj['stat'];
        }
      }
      this.constructQbConfigFields(this.editDataFromList.inventoryType, this.editDataFromList.productName, data["condition"])
    },
      err => {
        this.showLoader = true;
      });
  }


  /**
   * Method to construct the query builder config
   * @param invType 
   * @param prodName 
   */

  constructQbConfigFields(invType: string, prodName: string, query: any): void {
    try {
      this.isTemplateReady = false;
      this.clientApiService.getCffFieldsByInventoryTypeProduct(invType, prodName).subscribe(data => {
        if (Array.isArray(data) && data.length) {
          this.dataExchangeconfig.fields = {};
          this.dataExchangeconfig.fields = this.modifyQBConfig(data);
          this.dataExchangequery = this.queryBuildServer.modifyStructureToShowQB(query);
          this.showLoader = true;
        }
      })
    }
    catch (err) {
      console.log("Error while constructing qbConfig", err)
    }
  }

  /**
   * Modify master data fields into the format query builder understands
  */
  modifyQBConfig(masterDataQBConfig): any {
    let QBfields = {};
    masterDataQBConfig.forEach(field => {
      switch (field.clmnType.toUpperCase()) {
        case list.DROPDOWN:
          QBfields[field.clmnNm] = { name: field.dsplyNm, type: list.SINGLE_SELECT, dataset: field.options, key: 'name', id: 'id', table: field.tableName };
          break;
        case list.VARCHAR, list.NUMBER, list.DATE_COLUMN:
          QBfields[field.clmnNm] = { name: field.dsplyNm, type: typeMapping[field.clmnType.toUpperCase()], table: field.tableName };
          if (field.clmnType == list.DATE_COLUMN) {
            QBfields[field.clmnNm].dateFormat = list.DATE_FORMAT;
          }
          if (field.clmnType == list.DECIMAL) {
            QBfields[field.clmnNm].regPattern = list.DECIMAL_REG;
            QBfields[field.clmnNm].forceRegex = true;
          }
          break;
        default:
          QBfields[field.clmnNm] = { name: field.dsplyNm, type: typeMapping[field.clmnType.toUpperCase()], table: field.tableName };
          break; 
      }
    });
    return QBfields;
  }

  /**
   * Back to list Data exchange view
   */
  backToListPage(): void {
    this.DataEvent.emit('back to list');
  }
}
