/* You can add global styles to this file, and also import other style files */
@import "@ng-select/ng-select/themes/default.theme.css"

@font-face
  font-family: elevance
  src: url(assets/fonts/elevancesan_v1_105/ElevanceSans-Regular.otf) format("opentype")

@font-face
  font-family: elevance-medium
  src: url(assets/fonts/elevancesan_v1_105/ElevanceSans-Medium.otf) format("opentype")

@font-face
  font-family: elevance-semi-bold
  src: url(assets/fonts/elevancesan_v1_105/ElevanceSans-Semibold.otf) format("opentype")

@font-face
  font-family: Lato-Bold
  src: url(assets/fonts/lato/Lato-Bold.ttf) format("opentype")

@font-face
  font-family: Lato
  src: url(assets/fonts/lato/Lato-Regular.ttf) format("opentype")

@font-face
  font-family: Lato-Thin
  src: url(assets/fonts/lato/Lato-Thin.ttf) format("opentype")

*
  font-family: 'Lato', "Helvetica Neue", sans-serif

body
  height: 100%
  margin: 0

.carelon-theme *
  font-family: 'Elevance', "Helvetica Neue", sans-serif

marketplace-slide-panel marketplace-target-cards .target-cards__container .target-cards_holder
  min-width: 300px

marketplace-slide-panel marketplace-target-cards .target-cards__container
  justify-content: center
  margin: 1.2rem 0

marketplace-slide-panel marketplace-quick-navs .quick-navs__holder 
  flex-direction: column
  align-items: start
  margin: 0rem 6px
  gap: 1rem

marketplace-slide-panel marketplace-quick-navs .quick-nav-links
  white-space: nowrap



   
