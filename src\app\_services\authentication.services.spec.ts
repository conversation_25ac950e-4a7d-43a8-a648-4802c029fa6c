import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';
import { MPUIJwtVerifierService } from "marketplace-jwt-verifier";
import { AuthService } from './authentication.services';
import { environment } from '../../environments/environment';
import { Subject, BehaviorSubject } from 'rxjs';

describe('AuthService', () => {
  let authService: AuthService;
  let httpTestingController: HttpTestingController;
  let mockCookieService: jasmine.SpyObj<CookieService>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(() => {
    // Create spy objects
    mockCookieService = jasmine.createSpyObj('CookieService', ['get', 'set', 'deleteAll']);
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AuthService,
        { provide: CookieService, useValue: mockCookieService },
        { provide: Router, useValue: mockRouter },
        MPUIJwtVerifierService
      ],
    });

    authService = TestBed.inject(AuthService);
    httpTestingController = TestBed.inject(HttpTestingController);

    // Initialize service properties properly
    authService.currentUserProfile = new Subject();
    (authService as any).currentUserNameStore = new BehaviorSubject<string>("");
    authService.currentUserName$ = (authService as any).currentUserNameStore.asObservable();
    (authService as any).userSubject = new BehaviorSubject(null);

    // Reset all boolean flags
    authService.isLogin = false;
    authService.isLogout = false;
    authService.isUnAuthorized = false;
    authService.isDeactivated = false;
    authService.isTimedout = false;
    authService.isWriteOnly = true;
    authService.isInternalUser = false;
  });

  afterEach(() => {
    localStorage.clear();
    sessionStorage.clear();
    httpTestingController.verify();
  });

  describe('Component Initialization', () => {
    it('should be created', () => {
      expect(authService).toBeTruthy();
    });

    it('should initialize with default boolean values', () => {
      expect(authService.isLogin).toBeFalse();
      expect(authService.isLogout).toBeFalse();
      expect(authService.isUnAuthorized).toBeFalse();
      expect(authService.isDeactivated).toBeFalse();
      expect(authService.isTimedout).toBeFalse();
      expect(authService.isWriteOnly).toBeTrue();
      expect(authService.isInternalUser).toBeFalse();
    });

    it('should have currentUserProfile Subject initialized', () => {
      expect(authService.currentUserProfile).toBeInstanceOf(Subject);
    });

    it('should have currentUserName$ observable initialized', () => {
      expect(authService.currentUserName$).toBeDefined();
    });
  });

  describe('navigateTo Method', () => {
    it('should have navigateTo method defined', () => {
      expect(authService.navigateTo).toBeDefined();
      expect(typeof authService.navigateTo).toBe('function');
    });
  });

  describe('singleSignOn Method - Comprehensive Coverage', () => {

    it('should test SSO method exists', () => {
      expect(authService.singleSignOn).toBeDefined();
      expect(typeof authService.singleSignOn).toBe('function');
    });

    it('should test SSO method behavior with mocked navigation', () => {
      mockCookieService.get.and.returnValue('usertoken');
      spyOn(sessionStorage, 'getItem').and.returnValue('apptoken');
      const navigateToSpy = spyOn(authService, 'navigateTo').and.stub();

      authService.singleSignOn();

      expect(navigateToSpy).not.toHaveBeenCalled();
      expect(authService.isLogin).toBeTrue();
    });
  });

  describe('refreshToken method - Enhanced Coverage', () => {
    it('should refresh token with valid tokens', () => {
      const mockResponse = { newToken: 'renewedToken', expiresIn: 3600 };
      mockCookieService.get.and.returnValue('userToken123');
      spyOn(sessionStorage, 'getItem').and.returnValue('appToken456');

      authService.refreshToken().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpTestingController.expectOne(`${environment.iamManagerUser}/refresh`);
      expect(req.request.method).toBe('GET');
      expect(req.request.headers.get('z-generate-cad-token')).toBe('Y');
      expect(req.request.headers.get('x-appid')).toBe('PORTAL');
      expect(req.request.headers.get('userToken')).toBe('userToken123');
      expect(req.request.headers.get('appToken')).toBe('appToken456');
      expect(req.request.withCredentials).toBeTrue();
      req.flush(mockResponse);
    });

    it('should refresh token with empty tokens', () => {
      const mockResponse = { error: 'Invalid tokens' };
      mockCookieService.get.and.returnValue('');
      spyOn(sessionStorage, 'getItem').and.returnValue('');

      authService.refreshToken().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpTestingController.expectOne(`${environment.iamManagerUser}/refresh`);
      expect(req.request.headers.get('userToken')).toBe('');
      expect(req.request.headers.get('appToken')).toBe('');
      req.flush(mockResponse);
    });

    it('should handle refresh token error', () => {
      mockCookieService.get.and.returnValue('invalidToken');
      spyOn(sessionStorage, 'getItem').and.returnValue('invalidAppToken');

      authService.refreshToken().subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(401);
        }
      });

      const req = httpTestingController.expectOne(`${environment.iamManagerUser}/refresh`);
      req.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
    });
  });

  describe('logoutPortal method - Enhanced Coverage', () => {
    it('should logout successfully with valid tokens', () => {
      const mockResponse = { message: 'Successfully logged out', status: 'success' };
      mockCookieService.get.and.returnValue('userToken789');
      spyOn(sessionStorage, 'getItem').and.returnValue('appToken101');

      authService.logoutPortal().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpTestingController.expectOne(`${environment.iamManagerUser}/logout`);
      expect(req.request.method).toBe('POST');
      expect(req.request.headers.get('x-appid')).toBe('PORTAL');
      expect(req.request.headers.get('userToken')).toBe('userToken789');
      expect(req.request.headers.get('appToken')).toBe('appToken101');
      expect(req.request.withCredentials).toBeTrue();
      expect(req.request.body).toEqual({});
      req.flush(mockResponse);
    });

    it('should logout with Okta source header when enabled', () => {
      const originalEnableOkta = environment.enableOkta;
      (environment as any).enableOkta = true;

      mockCookieService.get.and.returnValue('userToken');
      spyOn(sessionStorage, 'getItem').and.returnValue('appToken');

      authService.logoutPortal().subscribe();

      const req = httpTestingController.expectOne(`${environment.iamManagerUser}/logout`);
      expect(req.request.headers.get('source')).toBe('internal');
      req.flush({ message: 'Logged out' });

      // Restore original value
      (environment as any).enableOkta = originalEnableOkta;
    });

    it('should logout without Okta source header when disabled', () => {
      const originalEnableOkta = environment.enableOkta;
      (environment as any).enableOkta = false;

      mockCookieService.get.and.returnValue('userToken');
      spyOn(sessionStorage, 'getItem').and.returnValue('appToken');

      authService.logoutPortal().subscribe();

      const req = httpTestingController.expectOne(`${environment.iamManagerUser}/logout`);
      expect(req.request.headers.get('source')).toBeNull();
      req.flush({ message: 'Logged out' });

      // Restore original value
      (environment as any).enableOkta = originalEnableOkta;
    });

    it('should handle logout error', () => {
      mockCookieService.get.and.returnValue('userToken');
      spyOn(sessionStorage, 'getItem').and.returnValue('appToken');

      authService.logoutPortal().subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(500);
        }
      });

      const req = httpTestingController.expectOne(`${environment.iamManagerUser}/logout`);
      req.flush('Server Error', { status: 500, statusText: 'Internal Server Error' });
    });

    it('should logout with null tokens', () => {
      mockCookieService.get.and.returnValue(null);
      spyOn(sessionStorage, 'getItem').and.returnValue(null);

      authService.logoutPortal().subscribe();

      const req = httpTestingController.expectOne(`${environment.iamManagerUser}/logout`);
      expect(req.request.headers.get('userToken')).toBe('null');
      expect(req.request.headers.get('appToken')).toBe('null');
      req.flush({ message: 'Logged out' });
    });
  });

  describe('Enhanced Authentication Tests', () => {
    describe('login method - Enhanced Coverage', () => {
      it('should authenticate user with valid credentials and store user data', () => {
        const mockUser = {
          id: '1',
          username: 'testuser',
          password: 'password123',
          firstName: 'Test',
          lastName: 'User',
          token: 'jwt-token',
          userId: '1'
        };
        const username = 'testuser';
        const password = 'password123';
        spyOn(localStorage, 'setItem');

        authService.login(username, password).subscribe(user => {
          expect(user.username).toBe(mockUser.username);
          expect(user.token).toBe(mockUser.token);
          expect(localStorage.setItem).toHaveBeenCalledWith('user', JSON.stringify(mockUser));
        });

        const req = httpTestingController.expectOne(`${environment.identityUrl}/api/v1/webusers/authenticate`);
        expect(req.request.method).toBe('POST');
        expect(req.request.body).toEqual({ username, password });
        req.flush(mockUser);
      });

      it('should handle login with empty credentials', () => {
        authService.login('', '').subscribe(user => {
          expect(user).toBeDefined();
        });

        const req = httpTestingController.expectOne(`${environment.identityUrl}/api/v1/webusers/authenticate`);
        expect(req.request.body).toEqual({ username: '', password: '' });
        req.flush({ id: 'empty', username: '', token: 'empty-token' });
      });

      it('should handle login error with 401 status', () => {
        authService.login('invalid', 'invalid').subscribe({
          next: () => fail('should have failed'),
          error: (error) => {
            expect(error.status).toBe(401);
          }
        });

        const req = httpTestingController.expectOne(`${environment.identityUrl}/api/v1/webusers/authenticate`);
        req.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
      });

      it('should handle login error with 500 status', () => {
        authService.login('user', 'pass').subscribe({
          next: () => fail('should have failed'),
          error: (error) => {
            expect(error.status).toBe(500);
          }
        });

        const req = httpTestingController.expectOne(`${environment.identityUrl}/api/v1/webusers/authenticate`);
        req.flush('Server Error', { status: 500, statusText: 'Internal Server Error' });
      });

      it('should update userSubject when login succeeds', () => {
        const mockUser = { id: '1', username: 'test', token: 'token' };
        let userSubjectValue: any;

        (authService as any).userSubject.subscribe((user: any) => {
          userSubjectValue = user;
        });

        authService.login('test', 'pass').subscribe();

        const req = httpTestingController.expectOne(`${environment.identityUrl}/api/v1/webusers/authenticate`);
        req.flush(mockUser);

        expect(userSubjectValue).toEqual(mockUser);
      });

      it('should handle special characters in credentials', () => {
        const specialUsername = '<EMAIL>';
        const specialPassword = 'p@ssw0rd!#$';

        authService.login(specialUsername, specialPassword).subscribe();

        const req = httpTestingController.expectOne(`${environment.identityUrl}/api/v1/webusers/authenticate`);
        expect(req.request.body).toEqual({ username: specialUsername, password: specialPassword });
        req.flush({ id: '1', username: specialUsername, token: 'token' });
      });
    });

    describe('getToken method', () => {
      it('should get token with auth code and verifier', () => {
        const mockTokenResponse = { access_token: 'token123', expires_in: 3600 };
        const authCode = 'auth-code-123';
        const codeVerifier = 'code-verifier-456';

        authService.getToken(authCode, codeVerifier).subscribe(response => {
          expect(response).toEqual(mockTokenResponse);
        });

        const req = httpTestingController.expectOne(`${environment.iamManagerAuth}/token`);
        expect(req.request.method).toBe('POST');
        expect(req.request.body).toEqual({ auth_code: authCode, code_verifier: codeVerifier });
        expect(req.request.headers.get('x-appid')).toBe('PORTAL');
        expect(req.request.withCredentials).toBeTrue();
        req.flush(mockTokenResponse);
      });

      it('should include source header when Okta is enabled', () => {
        // Temporarily set environment.enableOkta to true
        const originalEnableOkta = environment.enableOkta;
        (environment as any).enableOkta = true;

        authService.getToken('auth-code', 'verifier').subscribe();

        const req = httpTestingController.expectOne(`${environment.iamManagerAuth}/token`);
        expect(req.request.headers.get('source')).toBe('internal');
        req.flush({});

        // Restore original value
        (environment as any).enableOkta = originalEnableOkta;
      });
    });

    describe('piAuthorize method', () => {
      it('should authorize PI portal access', () => {
        const mockResponse = { authorized: true, permissions: ['read', 'write'] };
        mockCookieService.get.and.returnValue('user-token-123');

        authService.piAuthorize().subscribe(response => {
          expect(response).toEqual(mockResponse);
        });

        const req = httpTestingController.expectOne(`${environment.iamManagerAuth}/pi-authorize`);
        expect(req.request.method).toBe('GET');
        expect(req.request.headers.get('z-generate-cad-token')).toBe('Y');
        expect(req.request.headers.get('x-appid')).toBe('PORTAL');
        expect(req.request.headers.get('userToken')).toBe('user-token-123');
        req.flush(mockResponse);
      });

      it('should include source header for Okta when enabled', () => {
        // Temporarily set environment.enableOkta to true
        const originalEnableOkta = environment.enableOkta;
        (environment as any).enableOkta = true;
        mockCookieService.get.and.returnValue('token');

        authService.piAuthorize().subscribe();

        const req = httpTestingController.expectOne(`${environment.iamManagerAuth}/pi-authorize`);
        expect(req.request.headers.get('source')).toBe('internal');
        req.flush({});

        // Restore original value
        (environment as any).enableOkta = originalEnableOkta;
      });
    });

    describe('clearSessions method', () => {
      it('should clear all sessions and navigate to logout', () => {
        spyOn(sessionStorage, 'clear');

        authService.clearSessions();

        expect(mockCookieService.deleteAll).toHaveBeenCalledWith('/');
        expect(sessionStorage.clear).toHaveBeenCalled();
        expect(mockRouter.navigate).toHaveBeenCalledWith(['logout']);
      });
    });

    describe('getStoredUserProfile method', () => {
      it('should return stored user profile', () => {
        const mockProfile = {
          responseCode: 200,
          responseName: 'Success',
          responseData: { id: 1, name: 'Test User' }
        };
        (authService as any).userProfile = mockProfile;

        const result = authService.getStoredUserProfile();

        expect(result.responseCode).toBe(mockProfile.responseCode);
        expect(result.responseName).toBe(mockProfile.responseName);
      });

      it('should return undefined when no profile stored', () => {
        (authService as any).userProfile = undefined;

        const result = authService.getStoredUserProfile();

        expect(result).toBeUndefined();
      });
    });

    describe('strRandom method', () => {
      it('should generate random string of specified length', () => {
        const length = 10;
        const result = authService.strRandom(length);

        expect(result).toBeDefined();
        expect(result.length).toBe(length);
        expect(typeof result).toBe('string');
      });

      it('should generate different strings on multiple calls', () => {
        const result1 = authService.strRandom(20);
        const result2 = authService.strRandom(20);

        expect(result1).not.toBe(result2);
      });

      it('should generate string with valid characters only', () => {
        const result = authService.strRandom(50);
        const validChars = /^[A-Za-z0-9]+$/;

        expect(result).toMatch(validChars);
      });
    });

    describe('setCurrentUserName method', () => {
      it('should update current user name', () => {
        const testUserName = 'testuser123';

        authService.setCurrentUserName(testUserName);

        authService.currentUserName$.subscribe(userName => {
          expect(userName).toBe(testUserName);
        });
      });

      it('should emit new value to subscribers', () => {
        const testUserName = 'newuser456';
        let receivedUserName: string;

        authService.currentUserName$.subscribe(userName => {
          receivedUserName = userName;
        });

        authService.setCurrentUserName(testUserName);

        expect(receivedUserName).toBe(testUserName);
      });
    });

    describe('checkHasWritePermission method', () => {
      it('should return true and set isWriteOnly to true', () => {
        const mockState = { url: '/test/path' };

        const result = authService.checkHasWritePermission(mockState);

        expect(result).toBeTrue();
        expect(authService.isWriteOnly).toBeTrue();
      });

      it('should handle different state objects', () => {
        const mockState1 = { url: '/dashboard' };
        const mockState2 = { url: '/rules/create' };

        const result1 = authService.checkHasWritePermission(mockState1);
        const result2 = authService.checkHasWritePermission(mockState2);

        expect(result1).toBeTrue();
        expect(result2).toBeTrue();
        expect(authService.isWriteOnly).toBeTrue();
      });
    });

    describe('getExternalUserProfile method', () => {
      it('should get external user profile successfully', () => {
        const mockUserId = 'user123';
        const mockExpiryTime = '2024-12-31T23:59:59Z';
        const mockUserProfile = {
          responseCode: 200,
          responseName: 'Success',
          responseData: { id: mockUserId, name: 'Test User' }
        };

        authService.getExternalUserProfile(mockUserId, mockExpiryTime).subscribe(profile => {
          expect(profile).toEqual(mockUserProfile);
        });

        const req = httpTestingController.expectOne(`${environment.authService}/api/v1/validateToken/v1`);
        expect(req.request.method).toBe('GET');
        expect(req.request.headers.get('loginType')).toBe(environment.loginType);
        expect(req.request.headers.get('expiryTime')).toBe(mockExpiryTime);
        expect(req.request.headers.get('userId')).toBe(mockUserId);
        req.flush(mockUserProfile);
      });

      it('should handle external user profile error', () => {
        const mockUserId = 'invaliduser';
        const mockExpiryTime = '2024-12-31T23:59:59Z';

        authService.getExternalUserProfile(mockUserId, mockExpiryTime).subscribe({
          next: () => fail('should have failed'),
          error: (error) => {
            expect(error.status).toBe(401);
          }
        });

        const req = httpTestingController.expectOne(`${environment.authService}/api/v1/validateToken/v1`);
        req.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
      });
    });

    describe('setUserProfile private method', () => {
      it('should set user profile and emit to subject', () => {
        const mockProfile = {
          responseCode: 200,
          responseName: 'Success',
          responseData: { id: 1, name: 'Test User' }
        };

        let receivedProfile: any;
        authService.currentUserProfile.subscribe(profile => {
          receivedProfile = profile;
        });

        // Call private method through public method
        authService.getExternalUserProfile('user123', '2024-12-31T23:59:59Z').subscribe();

        const req = httpTestingController.expectOne(`${environment.authService}/api/v1/validateToken/v1`);
        req.flush(mockProfile);

        expect(receivedProfile).toEqual(mockProfile);
        const storedProfile = authService.getStoredUserProfile();
        expect(storedProfile.responseCode).toBe(mockProfile.responseCode);
        expect(storedProfile.responseName).toBe(mockProfile.responseName);
      });
    });

    describe('Additional Method Coverage', () => {
      describe('strRandom method - Enhanced Coverage', () => {
        it('should generate random string of specified length', () => {
          const length = 10;
          const result = authService.strRandom(length);

          expect(result).toBeDefined();
          expect(result.length).toBe(length);
          expect(typeof result).toBe('string');
        });

        it('should generate different strings on multiple calls', () => {
          const result1 = authService.strRandom(20);
          const result2 = authService.strRandom(20);

          expect(result1).not.toBe(result2);
        });

        it('should generate string with valid characters only', () => {
          const result = authService.strRandom(50);
          const validChars = /^[A-Za-z0-9]+$/;

          expect(result).toMatch(validChars);
        });

        it('should handle zero length', () => {
          const result = authService.strRandom(0);
          expect(result).toBe('');
        });

        it('should handle large length', () => {
          const result = authService.strRandom(1000);
          expect(result.length).toBe(1000);
        });

        it('should generate string with length 1', () => {
          const result = authService.strRandom(1);
          expect(result.length).toBe(1);
          expect(/[A-Za-z0-9]/.test(result)).toBeTrue();
        });
      });

      describe('checkHasWritePermission method - Enhanced Coverage', () => {
        it('should return true and set isWriteOnly to true', () => {
          const mockState = { url: '/test/path' };

          const result = authService.checkHasWritePermission(mockState);

          expect(result).toBeTrue();
          expect(authService.isWriteOnly).toBeTrue();
        });

        it('should handle different state objects', () => {
          const mockState1 = { url: '/dashboard' };
          const mockState2 = { url: '/rules/create' };

          const result1 = authService.checkHasWritePermission(mockState1);
          const result2 = authService.checkHasWritePermission(mockState2);

          expect(result1).toBeTrue();
          expect(result2).toBeTrue();
          expect(authService.isWriteOnly).toBeTrue();
        });

        it('should handle null state', () => {
          const result = authService.checkHasWritePermission(null);
          expect(result).toBeTrue();
          expect(authService.isWriteOnly).toBeTrue();
        });

        it('should handle undefined state', () => {
          const result = authService.checkHasWritePermission(undefined);
          expect(result).toBeTrue();
          expect(authService.isWriteOnly).toBeTrue();
        });

        it('should handle state without url property', () => {
          const mockState = { path: '/test' };
          const result = authService.checkHasWritePermission(mockState);
          expect(result).toBeTrue();
          expect(authService.isWriteOnly).toBeTrue();
        });

        it('should reset isWriteOnly to false then set to true', () => {
          authService.isWriteOnly = true; // initial state

          const result = authService.checkHasWritePermission({ url: '/test' });

          expect(result).toBeTrue();
          expect(authService.isWriteOnly).toBeTrue();
        });
      });

      describe('setCurrentUserName method - Enhanced Coverage', () => {
        it('should update current user name', () => {
          const testUserName = 'testuser123';

          authService.setCurrentUserName(testUserName);

          authService.currentUserName$.subscribe(userName => {
            expect(userName).toBe(testUserName);
          });
        });

        it('should emit new value to subscribers', () => {
          const testUserName = 'newuser456';
          let receivedUserName: string;

          authService.currentUserName$.subscribe(userName => {
            receivedUserName = userName;
          });

          authService.setCurrentUserName(testUserName);

          expect(receivedUserName).toBe(testUserName);
        });

        it('should handle empty string', () => {
          authService.setCurrentUserName('');

          authService.currentUserName$.subscribe(userName => {
            expect(userName).toBe('');
          });
        });

        it('should handle null value', () => {
          authService.setCurrentUserName(null as any);

          authService.currentUserName$.subscribe(userName => {
            expect(userName).toBeNull();
          });
        });

        it('should handle special characters', () => {
          const specialUserName = '<EMAIL>';
          authService.setCurrentUserName(specialUserName);

          authService.currentUserName$.subscribe(userName => {
            expect(userName).toBe(specialUserName);
          });
        });
      });

      describe('getStoredUserProfile method - Enhanced Coverage', () => {
        it('should return stored user profile', () => {
          const mockProfile = {
            responseCode: 200,
            responseName: 'Success',
            responseData: { id: 1, name: 'Test User' }
          };
          (authService as any).userProfile = mockProfile;

          const result = authService.getStoredUserProfile();

          expect(result.responseCode).toBe(mockProfile.responseCode);
          expect(result.responseName).toBe(mockProfile.responseName);
        });

        it('should return undefined when no profile stored', () => {
          (authService as any).userProfile = undefined;

          const result = authService.getStoredUserProfile();

          expect(result).toBeUndefined();
        });

        it('should return null when profile is null', () => {
          (authService as any).userProfile = null;

          const result = authService.getStoredUserProfile();

          expect(result).toBeNull();
        });

        it('should return empty object when profile is empty', () => {
          (authService as any).userProfile = {};

          const result = authService.getStoredUserProfile();

          expect(result).toBeDefined();
          expect(typeof result).toBe('object');
        });
      });
    });
    describe('Integration and Edge Case Tests', () => {
      it('should handle complete authentication flow', () => {
        // Test login
        const mockUser = {
          id: '1',
          username: 'testuser',
          password: 'password',
          firstName: 'Test',
          lastName: 'User',
          token: 'jwt-token',
          userId: '1'
        };
        authService.login('testuser', 'password').subscribe(user => {
          expect(user.username).toBe(mockUser.username);
          expect(user.token).toBe(mockUser.token);
        });

        const loginReq = httpTestingController.expectOne(`${environment.identityUrl}/api/v1/webusers/authenticate`);
        loginReq.flush(mockUser);

        // Test token refresh
        mockCookieService.get.and.returnValue('userToken');
        spyOn(sessionStorage, 'getItem').and.returnValue('appToken');

        authService.refreshToken().subscribe(response => {
          expect(response).toEqual({ newToken: 'refreshed' });
        });

        const refreshReq = httpTestingController.expectOne(`${environment.iamManagerUser}/refresh`);
        refreshReq.flush({ newToken: 'refreshed' });

        // Test logout
        authService.logoutPortal().subscribe(response => {
          expect(response).toEqual({ message: 'Logged out' });
        });

        const logoutReq = httpTestingController.expectOne(`${environment.iamManagerUser}/logout`);
        logoutReq.flush({ message: 'Logged out' });
      });

      it('should maintain state consistency across method calls', () => {
        // Set initial state
        authService.isLogin = false;
        authService.isWriteOnly = false;

        // Test state changes
        authService.checkHasWritePermission({ url: '/test' });
        expect(authService.isWriteOnly).toBeTrue();

        // Test SSO with tokens
        mockCookieService.get.and.returnValue('token');
        spyOn(sessionStorage, 'getItem').and.returnValue('apptoken');

        // Test without window.location mocking to avoid issues

        authService.singleSignOn();
        expect(authService.isLogin).toBeTrue();
      });

      it('should handle all boolean flags correctly', () => {
        // Test initial state
        expect(authService.isLogin).toBeFalse();
        expect(authService.isLogout).toBeFalse();
        expect(authService.isUnAuthorized).toBeFalse();
        expect(authService.isDeactivated).toBeFalse();
        expect(authService.isTimedout).toBeFalse();
        expect(authService.isWriteOnly).toBeTrue();
        expect(authService.isInternalUser).toBeFalse();

        // Test state changes
        authService.isLogin = true;
        authService.isLogout = true;
        authService.isUnAuthorized = true;
        authService.isDeactivated = true;
        authService.isTimedout = true;
        authService.isWriteOnly = false;
        authService.isInternalUser = true;

        expect(authService.isLogin).toBeTrue();
        expect(authService.isLogout).toBeTrue();
        expect(authService.isUnAuthorized).toBeTrue();
        expect(authService.isDeactivated).toBeTrue();
        expect(authService.isTimedout).toBeTrue();
        expect(authService.isWriteOnly).toBeFalse();
        expect(authService.isInternalUser).toBeTrue();
      });

      it('should handle Subject and BehaviorSubject operations', () => {
        // Test currentUserProfile Subject
        let profileReceived: any;
        authService.currentUserProfile.subscribe(profile => {
          profileReceived = profile;
        });

        const testProfile = {
          responseCode: 200,
          responseName: 'Test',
          responseData: { id: 1, name: 'Test User' }
        };
        authService.currentUserProfile.next(testProfile as any);
        expect(profileReceived).toEqual(testProfile);

        // Test currentUserName$ BehaviorSubject
        let userNameReceived: string;
        authService.currentUserName$.subscribe(userName => {
          userNameReceived = userName;
        });

        authService.setCurrentUserName('testUser');
        expect(userNameReceived).toBe('testUser');
      });

      it('should handle constructor injection properly', () => {
        expect(authService.http).toBeDefined();
        expect(authService.cookieService).toBeDefined();
        expect(authService.router).toBeDefined();
      });

      it('should handle multiple consecutive operations', () => {
        // Multiple strRandom calls
        const random1 = authService.strRandom(10);
        const random2 = authService.strRandom(10);
        const random3 = authService.strRandom(10);

        expect(random1).not.toBe(random2);
        expect(random2).not.toBe(random3);
        expect(random1).not.toBe(random3);

        // Multiple permission checks
        const result1 = authService.checkHasWritePermission({ url: '/path1' });
        const result2 = authService.checkHasWritePermission({ url: '/path2' });
        const result3 = authService.checkHasWritePermission({ url: '/path3' });

        expect(result1).toBeTrue();
        expect(result2).toBeTrue();
        expect(result3).toBeTrue();

        // Multiple user name updates
        authService.setCurrentUserName('user1');
        authService.setCurrentUserName('user2');
        authService.setCurrentUserName('user3');

        authService.currentUserName$.subscribe(userName => {
          expect(userName).toBe('user3');
        });
      });

      it('should handle error scenarios gracefully', () => {
        // Test with invalid environment values
        const originalIamManagerAuth = environment.iamManagerAuth;
        (environment as any).iamManagerAuth = '';

        mockCookieService.get.and.returnValue('');
        spyOn(sessionStorage, 'getItem').and.returnValue(null);
        spyOn(localStorage, 'setItem');
        const navigateToSpy = spyOn(authService, 'navigateTo');

        authService.singleSignOn();

        expect(navigateToSpy).toHaveBeenCalled();

        // Restore original value
        (environment as any).iamManagerAuth = originalIamManagerAuth;
      });

      it('should handle boundary conditions', () => {
        // Test with very long strings
        const longString = 'a'.repeat(1000);
        authService.setCurrentUserName(longString);

        authService.currentUserName$.subscribe(userName => {
          expect(userName.length).toBe(1000);
        });

        // Test with very large random string
        const largeRandom = authService.strRandom(500);
        expect(largeRandom.length).toBe(500);

        // Test with edge case permissions
        const edgeState = { url: '', path: null, query: undefined };
        const result = authService.checkHasWritePermission(edgeState);
        expect(result).toBeTrue();
      });
    });
  });
});