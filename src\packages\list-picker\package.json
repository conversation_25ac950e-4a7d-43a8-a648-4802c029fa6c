{"name": "marketplace-list-picker", "version": "0.0.1", "peerDependencies": {}, "dependencies": {"tslib": "^2.2.0"}, "main": "bundles/marketplace-list-picker.umd.js", "module": "fesm2015/marketplace-list-picker.js", "es2015": "fesm2015/marketplace-list-picker.js", "esm2015": "esm2015/marketplace-list-picker.js", "fesm2015": "fesm2015/marketplace-list-picker.js", "typings": "marketplace-list-picker.d.ts", "metadata": "marketplace-list-picker.metadata.json", "sideEffects": false}