import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/_services/authentication.services';

@Component({
  selector: 'app-setup-type',
  templateUrl: './setup-type.component.html',
  styleUrls: ['./setup-type.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class SetupTypeComponent implements OnInit {

  breadcrumbDataset: any = [{ label: 'Home', url: '/' }, { label: 'Rules Engine', url: '/rules' }, { label: 'Setup Rule Sub Type', url: '/rule-type' }];
  isUserTableReady: boolean = false;
  constructor(private router: Router, private authService: AuthService) { }

  ngOnInit(): void {
    this.isReadOnly = !this.authService.isWriteOnly;
    this.isReadOnly ? this.kebabOptions = this.kebabOptions_Readonly : this.kebabOptions;
    this.isUserTableReady = true;
  }
  kebabOptions: any = [{ label: '<i class="fa fa-trash" aria-hidden="true"></i> Delete SubType', id: 'Delete_SubType' }]
  kebabOptions_Readonly: any = [];
  public headerText = "Setup Rule Sub Type";
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  public isPriviousRedirectPage = true;
  isReadOnly: boolean = false;
  dataURL: string = "./assets/json/rule_types_table.json";
  dataRoot: string = "src";
  openConfirmationModal: any;
  columnConfig: any = {
    "switches": {
      "enableSorting": true,
      "enablePagination": true,
      "enableFiltering": true
    },
    "colDefs": [
      {
        "name": "Rule Type",
        "field": "id",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "Rule Subtype",
        "field": "ruleSubtype",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "Created By",
        "field": "rule_type",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "Created Date",
        "field": "start_date",
        "filterType": "Calendar",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": ""
      }
    ]
  };

  /**
  * function for closing confirmation pop-up for delete rule type
  */
  closeModelPopup() {
    this.openConfirmationModal = false;
  }
  /**
  * function for Deleting Rule Type after confirmation from user
  */
  deleteRuletype() {
  }

  /**
   * fires when kebab menu from table gets clicked
   */
  onKebabOptionsClick(event) {
    let action = event.text;
    switch (action) {
      case "Delete SubType":
        this.openConfirmationModal = true;
        break;
    }
  }

  /**
  * Delete Rules funstion
  * @param ruleId 
  */
  rulesDelete(ruleId) {
    // call delete rules API
  }

  /**
    * cell click event
    * @param event 
    */
  cellClicked(event: Event): void {
    if (event['eventData'].target.attributes) {
      if (event['eventData'].target.attributes.dataaction && event['eventData'].target.attributes.datevalue) {
        let ruleId = event['eventData'].target.attributes.datevalue.nodeValue;
        let rulesAction = event['eventData'].target.attributes.dataaction.nodeValue;
        switch (rulesAction) {
          case 'view':
            this.router.navigate(['/rules/rule-type'])
            break;
          case 'edit':
            this.router.navigate(['/rules/rule-type'])
            break;
          case 'delete':
            this.rulesDelete(ruleId);
            break;
        }
      }
    }
  }

  /**
    * cellValueChanged Function for Table
    * @param event 
    */
  cellValueChanged(event: Event): void {

  }

  /**
    * tableReady Funtion
    * @param event 
    */
  tableReady(event: Event): void {

  }

  /**
    * AddNewRuleSubTypefun Funtion
    */
  AddNewRuleSubTypefun(): void {
    if (!this.isReadOnly)
      this.router.navigate(['/rules/rule-type/details']);
  }

  /**
    * breadcrumSelection Funtion
    */
  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }

}
