app-copy .dashbord-title {
  padding: 5px 0px 0px 14px !important;
}

app-copy .container,
app-copy .container-fluid,
app-copy .container-lg,
app-copy .container-md,
app-copy .container-sm,
app-copy .container-xl {
  width: 100%;
  padding-right: 5px;
  padding-left: 5px;
  margin-right: auto;
  margin-left: auto;
  margin-top: -15px;
}
app-copy .chip-Container{
  margin-top: 1%;
  margin-bottom: 1%;
}

app-copy  .chips {
  height: 24px;
  padding: 15px 15px 15px 15px;
  border-radius: 17px;
  grid-gap: 4px;
  gap: 4px;
  display: inline-flex;
  justify-content: center;
  background: #bab9b9;
  color: #fff;
  cursor: pointer;
  align-items: center;
  max-width: 220px;
  overflow: hidden;
  margin: 2px;
}
                    

app-copy .chips-text {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  font: Elevance Sans;
  text-align: left;
  color: #000000;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
                        

app-copy .close-button {
  cursor: pointer;
  margin-left: 5px;
  color: #fff;
  font-weight: bold;
  height: 24px;
  border-radius: 17px;
  grid-gap: 4px;
  gap: 4px;
  display: inline-flex;
  justify-content: center;
  background: #bab9b9;
  color: #000000;
  cursor: pointer;
  align-items: center;
  max-width: 190px;
  overflow: hidden;
  margin: 2px;
}

app-copy .ruleDefinition {
	margin-top: 3%;
  width: 100%;
}

app-copy .ruleDefinition {
	margin-top: 3%;
  width: 100%;
}
app-copy .pointerFuncNone {
  padding: 1rem;
  cursor: not-allowed;
  pointer-events: none;
}
app-copy .noResFound li {
  margin-left: 15px;
}

app-copy .noResFound {
  list-style-type: none;
  border-style: ridge;
  z-index: 999;
}

app-copy .statusHeader {
  color: white;
  font-size: 18px;
  font-weight: bold;
  text-align: left;
}

app-copy .info {
  color: rgb(0, 0, 0);
  background-color: #fff;
  margin-top: -30px;
}

app-copy .card .card-no-border {
  border: none !important;
  padding: 0px 25px 0px 25px;
}

app-copy .pd-15 {
  padding: 15px;
}

app-copy .query-builder-title {
  float: left;
  width: 200px;
  height: 34px;
  left: 195px;
  top: 384px;
  font-style: normal;
  font-weight: 500;
  font-size: 28px;
  line-height: 34px;
  color: #000000;
}

app-copy .btn-span {
  float: right;
}

app-copy .btn-criteria {
  background: #5009B5;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #ffffff;
}

app-copy .pd-bottom-15 {
  padding-bottom: 15px;
}

app-copy .pd-25 {
  padding: 25px 25px 25px 25px;
}

app-copy .custom-btn {
  padding: 0.375rem 3rem !important;
  margin-right: 20px;
}

app-copy .pd-5 {
  padding: 5px;
}

app-copy .level-indicator {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 50px;
  left: 1228px;
  top: 130px;
  border-radius: 4px;
  padding: 10px, 50px, 10px, 50px;
  background: #fde2b9;
}

app-copy .card-title {
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 29px;
  color: #5009B5;
  margin-left: -10px;
}

app-copy .tabs-padding {
  padding: 0px 25px 25px 0px !important;
}

app-copy .query-builder-title {
  color: #5009B5 !important;
  font-size: 24px !important;
  padding-bottom: 10px;
}

app-copy .notification-title {
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 22px;
  color: #161616;
  padding: 13px 0px 0px 15px;
}

app-copy .notification-font-wt {
  font-weight: 600 !important;
  padding: 13px 0px 0px 5px !important;
}

app-copy .breadcrumb-container {
  margin-left: -11px;
  margin-bottom: 46px;
}

app-copy .custom-switch {
  padding-left: 1em;
  margin-top: -4px;
}

app-copy .input-group.mb-3 {
  margin-bottom: 0px !important;
}

app-copy .mar-10 {
  margin-top: 10px;
}

app-copy .modal-content {
  margin-top: 5% !important;
}

app-copy #copyRulePopup .modal-content {
  width: 700px;
}

app-copy #copyRulePopup .modal-dialog .modal-body {
  overflow: unset;
}

app-copy .learnMoreAboutRuleTag {
  color: #4e4e4e;
  font-size: 12px;
  cursor: pointer;
}

app-copy .wrapper {
  max-width: 450px;
  margin: 47px auto;
}

app-copy .similarSuggestion {
  margin-left: 3px;
}

app-copy .fa-info-circle {
  margin-top: 3px;
  margin-left: 2px;
  margin-right: 3px;
}

app-copy .DescriptionProvider {
  position: relative;
  top: 65%;
}

app-copy .wrapper .search-input {
  margin-top: -13px;
  margin-left: 17px;
  background-color: #fff;
  width: 100%;
}

app-copy .selectedItemsInformation {
  font-size: 12px;
  border-style: ridge;
  margin-left: 2px;
  color: rgb(0, 0, 0);
  background-color: #ffffff;
}

  app-copy .selectedItemsInformation h6 {
    font-weight: bold;
  }

app-copy .searchResults {
  border-style: ridge;
  z-index: 999;
}

app-copy .search-input .searchResults {
  padding: 0px 1px;
  max-height: 150px;
  overflow-y: scroll;
}

app-copy .selectedItemsInformation p {
  margin-left: 7px;
}

app-copy .searchResults li {
  line-height: 1.2;
  list-style: none;
  padding: 2px 12px;
  width: 100%;
  cursor: pointer;
  border-radius: 3px;
  font-size: 20px;
}

  app-copy .searchResults li:hover {
    background: #efefef;
  }

app-copy .custom-title {
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 29px;
  color: #5009B5;
}

app-copy .custom-message {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 19px;
  color: #4e4e4e;
}

app-copy .modal-header-custom {
  width: 100%;
  display: flex;
  justify-content: center;
}

app-copy .pad-1rem {
  padding: 1rem;
}


@media (min-width: 576px) {
  app-copy .modal-dialog {
    max-width: 580px;
  }
}

app-copy .modal-footer {
  padding: 0px !important;
  margin-top: -20px !important;
}

app-copy .p-align {
  margin-bottom: 10px;
}

app-copy .pad-20 {
  padding-left: 3%;
}

app-copy .pad-30 {
  margin-left: 25%;
}

app-copy hr {
  margin-top: 1rem;
  border: 1px solid #dbdbdb;
}

  app-copy hr.qb {
    margin-top: 3rem;
  }

app-copy .attention-note {
  font-weight: normal;
  font-size: 17px;
}

app-copy .close-icon-color {
  color: #5009B5;
}

app-copy .pad-35 {
  margin-top: 30px;
  margin-left: 20%;
}

app-copy .redBorder {
  border-color: red !important;
}

app-copy .spinner-border {
  display: block;
  position: fixed;
  top: calc(50% - (58px / 2));
  right: calc(40% - (58px / 2));
  width: 5rem;
  height: 5rem;
}

app-copy .backdrop {
  position: fixed;
  top: 11%;
  left: 20%;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  background-color: rgb(0, 0, 0, 0.2);
}

app-copy .red-font {
  color: red;
  padding: 0px 0px 14px 14px;
}

app-copy .mar-lt-neg {
  margin-left: -25px;
}

app-copy .copyUploadPopup marketplace-popup .modal .modal-dialog.modal-sm .modal-content {
  height: 50vh !important;
}

app-copy .copyPopup marketplace-popup .modal .modal-dialog.modal-sm .modal-content {
  height: 80vh !important;
}

app-copy .rolefooterPopup {
  margin-top: 40px;
}

app-copy marketplace-popup .modal .modal-dialog.modal-sm .modal-header {
  height: 70px;
}

app-copy .mar-20 {
  margin-top: 20px;
}
app-copy #impactReportPopup .modal-body{
  min-width: 55px !important;
  min-height: 0px !important;
}
app-copy #impactReportPopup .modal-footer {
  margin-bottom: 3% !important;
  margin-right: 2% !important;

} 
app-copy #impactReportPopup .modal-content {
  width: 100% !important;
  max-width: 555px !important;
  min-width: 400px !important;
}
app-copy .bypassmessgae {
  font-family: 'elevance-medium';
}