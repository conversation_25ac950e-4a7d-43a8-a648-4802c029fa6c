{"switches": {"enableSorting": true, "enablePagination": true, "enableFiltering": true}, "colDefs": [{"name": "Tenant Name", "width": 120, "field": "tenantName", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Tenant ID", "width": 80, "filterType": "Number", "field": "tenantId", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Tenant Description", "width": 150, "field": "tenantDesc", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Client Id", "width": 80, "field": "clientId", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Created By", "width": 100, "field": "createdByUserId", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Status", "width": 90, "field": "active", "visible": "True", "editorType": "", "editorTypeRoot": "", "editorTypeLabel": ""}, {"name": "Created Date", "width": 120, "field": "createdDate", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Last Updated By UserId", "width": 130, "field": "lastUpdatedByUserId", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Last Updated Date", "width": 110, "field": "lastUpdatedDate", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}]}