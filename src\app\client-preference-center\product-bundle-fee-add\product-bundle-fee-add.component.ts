import {
  Component,
  OnInit,
  ViewEncapsulation,
  EventEmitter,
  Output,
  ChangeDetectorRef,
  ViewChild
} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ToastService } from 'src/app/_services/toast.service';
import { forkJoin } from 'rxjs';
import { UtilitiesService } from '../../_services/utilities.service';

import moment from 'moment'
import { standardFeeOptions } from '../client-preference';
import { CookieService } from 'ngx-cookie-service';

@Component({
  selector: 'app-product-bundle-fee-add',
  templateUrl: './product-bundle-fee-add.component.html',
  styleUrls: ['./product-bundle-fee-add.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class ProductBundleFeeAddComponent implements OnInit {
  public feeScheduleJson: any[];
  public product: any[];
  public bundle: any;
  public prodId: any;
  public bundleId: any;
  public createFeeScheduleFormData: any = {};
  public createFeeScheduleForm: any = {};
  public popupDisplayStyle: any = 'none';
  public products = [];
  showForm = false;
  formStatus: any;
  enableSumbitButton: boolean = false;
  bundleList: any = [];
  formObject: any = {};
  client: any;
  showLoader = false;
  feeType: any = [];
  feeMethod: any = [];
  callBundleList: boolean = true;
  callMapValuesChange: boolean = true;
  standardFeeOptions:any=  standardFeeOptions;
  @Output() addDataEvent = new EventEmitter<string>();

  constructor(
    private router: Router,
    private clientApiService: ClientApiService,
    private alertService: ToastService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef,
    private dateService: UtilitiesService,
    private cookieService: CookieService
  ) {
    this.client = Number(this.route.snapshot.paramMap.get('clientId'));
  }
  /**
   * Fetching all the master data details of feesetup and fee method
   * showing products based on client id
   * 
   */
  ngOnInit(): void {
    const obv1 = this.clientApiService.feeDetailsMasterData();
    const obv2 = this.clientApiService.getClientProducts(this.client);
    forkJoin([obv2, obv1]).subscribe(([data, feeset]) => {
      if (data) {
        if (this.clientApiService.selectedProductId) {
          const item = data.find((elem) => {
            return elem.productId == this.clientApiService.selectedProductId
          });
          this.products = [item];
        } else {
          this.products = data;
        }
        this.feeType = feeset.feeTypeList;
        this.feeMethod = feeset.feeCategoryList;
      }
      this.showForm = true;
      this.formConfig();

    }, err => {
      this.showForm = true;
      this.formConfig();
    });

  }
  /**
   * Table config and Binding master data to columns
   */
  formConfig() {
    this.feeScheduleJson = [
      {
        type: 'group',
        name: 'General 1',
        label: '',
        column: '2',
        groupControls: [
          {
            options: this.products,
            optionName: 'productName',
            optionValue: 'productId',
            label: 'Product Name',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'productId',
            column: '1',
            disabled: this.products.length == 1 ? true : false,
            selectedVal: this.products.length == 1 ? this.products[0].productId : null,
            hidden: false,
            placeholder: 'Choose Product Name',
            required: true,
            id: 'productName',
          },
          {
            label: 'Bundle Name',
            type: 'select',
            name: 'bundleId',
            column: '1',
            id: 'bundleId',
            placeholder: 'Choose Bundle Name',
            required: true,
            disabled: false,
            multiple: false,
            closeOnSelect: true,
            options: this.bundleList,
            optionName: 'bundleName',
            optionValue: 'bundleId',
          },
          {
            options: this.standardFeeOptions,
            optionName: "name",
            optionValue: "id",
            label: "Standard Fee (in %)",
            type: "numberSelect",
            multiple: false,
            closeOnSelect: true,
            name: "standardFee",
            column: "1",
            placeholder: 'Choose standard fee',
            disabled: false,
            hidden: false,
            id: "standardFee",
            required: true,
            customTags: true,
            minimum: 0,
            maximum: 100
          },
          {
            options: this.feeMethod,
            optionName: 'feeCategoryName',
            optionValue: 'feeCategoryId',
            label: 'Fee Method',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'feeMethodId',
            placeholder: 'Choose Fee Method',
            column: '1',
            disabled: false,
            hidden: false,
            id: 'feeMethod',
            required: true
          },
        ],
      },
      {
        type: 'group',
        name: 'General 2',
        label: '',
        column: '2',
        groupControls: [
          {
            label: 'Bundle Description',
            type: 'textarea',
            name: 'bundleDescription',
            id: 'bundleDesc',
            column: '1',
            disabled: true,
            placeholder: 'Read Bundle Description',
            value: '',
            required: false
          },
          {
            label: 'Start Date',
            type: 'date',
            name: 'startDate',
            column: '3',
            disabled: false,
            value: '',
            pickerType: 'single',
            id: 'startDate',
            dateFormat: "MM-DD-YYYY",
            placeholder: 'Choose Date',
            required: true,
            relatedDateControls: [{
              target: 'endDate'
            }]
          },
          {
            label: 'End Date',
            type: 'date',
            name: 'endDate',
            column: '3',
            disabled: false,
            value: '',
            pickerType: 'single',
            id: 'endDate',
            dateFormat: "MM-DD-YYYY",
            required: true,
            placeholder: 'Choose Date',
          },
          {
            options: this.feeType,
            optionName: 'feeTypeName',
            optionValue: 'feeTypeId',
            label: 'Fee Type',
            type: 'select',
            placeholder: 'Choose Fee Type',
            multiple: false,
            closeOnSelect: true,
            name: 'feeTypeId',
            column: '2',
            disabled: false,
            hidden: false,
            id: 'feeType',
            required: true
          },
        ],
      },
    ];
    if (this.clientApiService.selectedProductId) {
      this.feeScheduleJson[0].groupControls[0].selectedVal = this.products[0].productId;
      this.clientApiService.getActiveBundleList(this.products[0].productId).subscribe((data) => {
        if (data) {
          this.bundleList = data;
          this.feeScheduleJson[0].groupControls[1].options = this.bundleList;

        } else {
          this.bundleList = [];
          this.feeScheduleJson[0].groupControls[1].options = [];
        }
      })
    }
  }
  /**
   * Navigating back to screen
   */
  backToListPage() {
    this.addDataEvent.emit('add');
  }

  /**
 * merging and forming form valuess
 * @param event 
 */
  formValid(event) {
    const data = { ...event.value['General 1'], ...event.value['General 2'] };
    if (this.formObject) {
      this.callBundleList = data.productId != this.formObject.productId;
    }
    if (this.formObject?.productId != data.productId) {
      this.productValueChange(event);
    }
    if (this.formObject?.bundleId != data.bundleId) {
      this.bundleValueChange(event);
    }
    this.formObject = { ...event.value['General 1'], ...event.value['General 2'] };
    event.status == "VALID" ? this.enableSumbitButton = true : this.enableSumbitButton = false;
    this.formStatus = event.status;

  }

  /**
   * Submitted the fee record
   */
  validateCreateForm() {
    if (this.formStatus == 'VALID') {
      const startIsBeforeEnd = this.dateService.checkDate(this.formObject.startDate, this.formObject.endDate);
      if (startIsBeforeEnd) {
        const item = {
          "productName": this.fetchValue(this.products, 'productId', 'productName', 'productId'),
          "productId": this.formObject.productId,
          "bundleName": this.fetchValue(this.bundleList, 'bundleId', 'bundleName', 'bundleId'),
          "bundleId": this.formObject.bundleId,
          "bundleDescription": this.formObject.bundleDescription,
          "standardFee": Number(this.formObject.standardFee),
          "feeMethod": this.fetchValue(this.feeMethod, 'feeCategoryId', 'feeCategoryName', 'feeMethodId'),
          "feeMethodId": this.formObject.feeMethodId,
          "feeType": this.fetchValue(this.feeType, 'feeTypeId', 'feeTypeName', 'feeTypeId'),
          "feeTypeId": this.formObject.feeTypeId,
          "startDate": moment(this.dateService.getDbgDateFormat(this.formObject.startDate), 'MM-DD-YYYY').format('YYYY-MM-DD'),
          "endDate": moment(this.dateService.getDbgDateFormat(this.formObject.endDate), 'MM-DD-YYYY').format('YYYY-MM-DD'),
          "createdBy": (this.cookieService.get('userId'))?.toUpperCase(),

          "createdDate": new Date()
        }
        this.showLoader = true;
        this.clientApiService.saveClient(this.client, item).subscribe((data) => {
          if(data.responseCode==200){
          this.alertService.setSuccessNotification({
            notificationHeader: 'Success',
            notificationBody: `Fee Schedule successfully add to the list!!`,
          });
          this.showLoader = false;
          this.backToListPage();}
          else if(data.responseCode==500){
            this.alertService.setErrorNotification({
              notificationHeader: 'Fail',
              notificationBody: 'Fee schedule already exist for requested data',
            });
            this.showLoader = false;
          }
        }, (err: any) => {
          this.alertService.setErrorNotification({
            notificationHeader: 'Fail',
            notificationBody: `Data Not Saved`,
          });
          this.showLoader = false;
        })
      } else {
        this.alertService.setErrorNotification({
          notificationHeader: 'Warning',
          notificationBody: `Please fill all the fields`,
        });
      }
    } else {
      this.alertService.setErrorNotification({
        notificationHeader: 'Warning',
        notificationBody: `Please fill all the fields`,
      });
    }
  }

  /**
   * An utility function , fetching required data from the dropdown for different field
   * Ex: selecting fee ID  fetching fee Name
   * @param arr list of elements
   * @param elemKey - key to check 
   * @param elmValue - Value to send
   * @param matchkey - Match to the key
   */
  fetchValue(arr, elemKey, elmValue, matchkey) {
    const elemItem = arr.find((elem) => {
      return elem[elemKey] == this.formObject[matchkey]
    })
    return elemItem ? elemItem[elmValue] : '';
  }
  /**
   * closing the modal Popup
   */
  closePopup() {
    this.popupDisplayStyle = 'none';
  }
  /**
   * null checker
   * @param value 
   */
  isDefined(value) {
    return value != undefined && value != null && value != '';
  }
  /**
   * check empty Object
   * @param obj 
   */
  isEmpty(obj) {
    return Object.keys(obj).length === 0;
  }
  /*
  * clear form value based on id
  */
  clearFormValue(id: string) {
    let targetElement: any = document.getElementById(id) as HTMLElement;
    targetElement.querySelector('input') ? targetElement.querySelector('input').value = '' : '';
    targetElement.querySelector('input') && targetElement.querySelector('.ng-value-label') ? targetElement.querySelector('.ng-value-label').innerHTML = '' : '';
    targetElement.querySelector('input') && targetElement.querySelector('.ng-clear-wrapper') ? targetElement.querySelector('.ng-clear-wrapper').innerHTML = '' : '';
  }

  ngAfterViewInit(): void {
    const collection = document.querySelectorAll(
      'marketplace-dynamic-form button'
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].remove();
    }
  }
  /**
   * this is to clear the bundle column and bundle description
   * get all the bundle list based on product Id
   * @param event 
   */
  productValueChange(event) {
    this.clearFormValue('bundleId');
    this.feeScheduleJson[1].groupControls[0].value = ""
    this.formObject.bundleDescription = "";
    this.clientApiService.getActiveBundleList(event.value['General 1'].productId).subscribe((data) => {
      if (data) {
        this.bundleList = data;
        this.feeScheduleJson[0].groupControls[1].options = this.bundleList;
      } else {
        this.bundleList = [];
        this.feeScheduleJson[0].groupControls[1].options = [];
      }
    })

  }
  /**
   * based on bundle change add bundle description dynamically 
   * @param event 
   */
  bundleValueChange(event) {

    const item = this.bundleList.find((elem) => {
      return elem.bundleId == event.value['General 1'].bundleId
    })
    if (item) {
      this.feeScheduleJson[1].groupControls[0].value = item.bundleDesc;
      this.formObject.bundleDescription = item.bundleDesc;
    } else {
      this.feeScheduleJson[1].groupControls[0].value = ''
    }

  }


}
