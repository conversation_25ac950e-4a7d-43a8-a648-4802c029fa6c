app-create .dashbord-title{
  padding: 5px 0px 0px 14px !important;
}
app-create .container,
app-create .container-fluid,
app-create .container-lg,
app-create .container-md,
app-create .container-sm,
app-create .container-xl {
  width: 100%;
  padding-right: 5px;
  padding-left: 5px;
  margin-right: auto;
  margin-left: auto;
  margin-top: -15px;
}

app-create .pointerFuncNone {
  padding: 1rem;
  cursor: not-allowed;
  pointer-events: none;
}

app-create .ruleDefinition {
	margin-top: 3%;
  width: 100%;
}

app-create .noResFound li{
  margin-left: 15px;
}
app-create .noResFound {
  list-style-type: none;
  border-style: ridge;
  z-index: 999;
}
app-create .statusHeader{
  color: white;
  font-size: 18px;
  font-weight: bold;
  text-align: left;
}
app-create .info {
  color: rgb(0, 0, 0);
  background-color: #fff;
  margin-top: -30px;
}
app-create .card .card-no-border {
  border: none !important;
  padding: 0px 25px 0px 25px;
}
app-create .pd-15 {
  padding: 15px;
}
app-create .query-builder-title {
  float: left;
  width: 200px;
  height: 34px;
  left: 195px;
  top: 384px;
  
  font-style: normal;
  font-weight: 500;
  font-size: 28px;
  line-height: 34px;
  color: #000000;
}
app-create .btn-span {
  float: right;
}
app-create .btn-criteria {
  background: #794cff;
  
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #ffffff;
}
app-create .pd-bottom-15 {
  padding-bottom: 15px;
}
app-create .pd-25 {
  padding: 25px 25px 25px 25px;
}
app-create .custom-btn {
  padding: 0.375rem 3rem !important;
  margin-right: 20px;
}
app-create .pd-5 {
  padding: 5px;
}
app-create .level-indicator {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 50px;
  left: 1228px;
  top: 130px;
  border-radius: 4px;
  padding: 10px, 50px, 10px, 50px;
  background: #fde2b9;
}

app-create .card-title {
  
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 29px;
  color: #794cff;
  margin-left: 6px;
}
app-create .tabs-padding {
  padding: 0px 25px 25px 0px !important;
}
app-create .notification-title {
  
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 22px;
  color: #161616;
  padding: 13px 0px 0px 15px;
}
app-create .notification-font-wt {
  font-weight: 600 !important;
  padding: 13px 0px 0px 5px !important;
}
app-create .breadcrumb-container{
  margin-left: -11px;
  margin-bottom: 46px;
}
app-create .custom-switch {
  padding-left: 1em;
  margin-top: -4px;
}
app-create .input-group.mb-3{
  margin-bottom: 0px !important;
}
app-create .mar-10 {
  margin-top: 10px;
}
app-create .modal-content {
  margin-top: 5% !important;
}
app-create #createRulePopup .modal-content {
  width: 700px;
}
app-create #createRulePopup .modal-dialog .modal-body {
  overflow: unset;
}
app-create .learnMoreAboutRuleTag{
  color: #4e4e4e;
  font-size: 12px;
  cursor: pointer;
}
app-create .wrapper{
  max-width: 450px;
  margin: 47px auto;
}
app-create .similarSuggestion{
  margin-left: 3px;
}
app-create .fa-info-circle{
  margin-top: 3px;
  margin-left: 2px;
  margin-right: 3px;
}
app-create .DescriptionProvider{
  position: relative;
  top: 65%;
}
app-create .wrapper .search-input{
  margin-top: -13px;
  margin-left: 17px;
  background-color: #fff;
  width: 100%;
}
app-create .selectedItemsInformation {
  font-size: 12px;
  border-style: ridge;
  margin-left: 2px;
  color: rgb(0, 0, 0);
  background-color:#ffffff;
}
app-create .selectedItemsInformation h6 {
  font-weight: bold;
}
app-create .searchResults {
  border-style: ridge;
  z-index: 999;
}
app-create .search-input .searchResults{
  padding: 0px 1px;
  max-height: 150px;
  overflow-y: scroll;
}
app-create .selectedItemsInformation p{
  margin-left: 7px;
}
app-create .searchResults li{
  line-height: 1.2;
  list-style: none;
  padding: 2px 12px;
  width: 100%;
  cursor: pointer;
  border-radius: 3px;
  font-size: 20px;
}
app-create .searchResults li:hover{
  background: #efefef;
}
app-create .custom-title {
  
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 29px;
  color: #794cff;
}

app-create .save-sucess-popup {
  padding-bottom: 24px;
}
app-create .custom-message {
  
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 19px;
  color: #4e4e4e;
}
app-create .modal-header-custom {
  width: 100%;
  display: flex;
  justify-content: center;
}
app-create .pad-1rem {
  padding: 1rem;
}


@media (min-width: 576px) {
  app-create .modal-dialog {
    max-width : 580px;
  }
}

app-create .modal-footer {
  padding: 0px !important;
  margin-top: -20px !important;
}

app-create .p-align {
  margin-bottom: 20px;
}

app-create .pad-20 {
  padding-left: 3%;
}

app-create .pad-30 {
  margin-left: 25%;
}

app-create hr {
  margin-top: 1rem;
  border: 1px solid #dbdbdb;
}

app-create hr.qb{
  margin-top: 3rem;
}

app-create .attention-note {
  font-weight: normal;
  font-size: 17px;
}

app-create .close-icon-color {
  color: #794cff;
}

app-create .pad-35 {
  margin-bottom: 30px;
  margin-left: 20%;
}

app-create .redBorder {
  border-color: red !important;
}

app-create .spinner-border {
  display: block;
  position: fixed;
  top: calc(50% - (58px / 2));
  right: calc(40% - (58px / 2));
  width: 5rem;
  height: 5rem;
}

app-create .backdrop {
    position: fixed;
    top: 11%;
    left: 20%;
    width: 100vw;
    height: 100vh;
    z-index: 999;
    background-color: rgb(0, 0, 0, 0.2);
}

app-create .red-font {
  color: red;
  padding: 0px 0px 14px 14px;
}

app-create .mar-lt-neg {
  margin-left: -25px;
}

app-create .rolefooterPopup{
  margin-top: 40px ;
}

app-create marketplace-popup .modal .modal-dialog .modal-body {
  overflow: auto !important;
}

app-create .chip-Container{
  margin-top: 1%;
  margin-bottom: 1%;
}

app-create  .chips {
  height: 24px;
  padding: 15px 15px 15px 15px;
  border-radius: 17px;
  grid-gap: 4px;
  gap: 4px;
  display: inline-flex;
  justify-content: center;
  background: #bab9b9;
  color: #fff;
  cursor: pointer;
  align-items: center;
  max-width: 220px;
  overflow: hidden;
  margin: 2px;
}
                    

app-create .chips-text {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  font: Elevance Sans;
  text-align: left;
  color: #000000;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
                        

app-create .close-button {
  cursor: pointer;
  margin-left: 5px;
  color: #fff;
  font-weight: bold;
  height: 24px;
  border-radius: 17px;
  grid-gap: 4px;
  gap: 4px;
  display: inline-flex;
  justify-content: center;
  background: #bab9b9;
  color: #000000;
  cursor: pointer;
  align-items: center;
  max-width: 190px;
  overflow: hidden;
  margin: 2px;
}

app-create .mar-30 {
  margin-top: 30px;
}

marketplace-file-upload .file__upload-holder .file__inputBox .fileUpload__container-close-button {
  top: 10px !important;
  right: 16% !important;
}  

app-create .mar-20 {
  margin-top: 20px;
}

app-create marketplace-file-upload .file__upload-holder .file__inputBox input {
  width:85% !important;
}
app-create marketplace-file-upload .file__upload-holder .file__inputBox button.btn.btn-primary {
  margin-top: 0px !important;
  width: 15% !important;
}
app-create .bypassmessgae {
  font-family: 'elevance-medium';
}
app-create #impactReportPopup .modal-body{
  min-width: 55px !important;
  min-height: 0px !important;
}
app-create #impactReportPopup .modal-footer {
  margin-bottom: 3% !important;
  margin-right: 2% !important;

} 
app-create #impactReportPopup .modal-content {
  width: 100% !important;
  max-width: 555px !important;
  min-width: 400px !important;
}

.row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: 15px;
}