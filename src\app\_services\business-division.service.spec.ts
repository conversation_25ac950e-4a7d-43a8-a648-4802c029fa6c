import { TestBed } from '@angular/core/testing';
import { BusinessDivisionService } from './business-division.service';

describe('BusinessDivisionService', () => {
  let service: BusinessDivisionService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(BusinessDivisionService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Service Initialization', () => {
    it('should initialize with default values', () => {
      expect(service.userBUs).toEqual([{ name: 'CSBD', id: 'CSBD' }]);
      expect(service.businessDivisionDetails).toBe('CSBD');
      expect(service.selectedBusinessUnit).toBeNull();
      expect(service.isNavLinkSelected).toBe(false);
      expect(service.showBusinessDivisionPopup).toBeDefined();
    });

    it('should have showBusinessDivisionPopup as Subject', () => {
      expect(service.showBusinessDivisionPopup.next).toBeDefined();
      expect(service.showBusinessDivisionPopup.subscribe).toBeDefined();
    });
  });

  describe('showBusinessDivision', () => {
    beforeEach(() => {
      spyOn(service.showBusinessDivisionPopup, 'next');
      service.screenAccess_Cards = [
        { 
          label: 'Data Mining Solution', 
          subMenu: [
            { label: 'CAD Screen 1' },
            { label: 'CAD Screen 2' }
          ]
        },
        { 
          label: 'Other Solution', 
          subMenu: [
            { label: 'Other Screen 1' }
          ]
        }
      ];
    });

    it('should show business division popup for Rules', () => {
      service.showBusinessDivision('Rules');
      expect(service.showBusinessDivisionPopup.next).toHaveBeenCalledWith(true);
    });

    it('should show business division popup for CAD screens', () => {
      service.showBusinessDivision('Data Mining Solution');
      expect(service.showBusinessDivisionPopup.next).toHaveBeenCalledWith(true);
    });

    it('should show business division popup for CAD sub-menu items', () => {
      service.showBusinessDivision('CAD Screen 1');
      expect(service.showBusinessDivisionPopup.next).toHaveBeenCalledWith(true);
    });

    it('should not show business division popup for non-CAD screens', () => {
      service.showBusinessDivision('Other Solution');
      expect(service.showBusinessDivisionPopup.next).not.toHaveBeenCalled();
    });

    it('should not show business division popup for unknown screens', () => {
      service.showBusinessDivision('Unknown Screen');
      expect(service.showBusinessDivisionPopup.next).not.toHaveBeenCalled();
    });
  });

  describe('isCadScreen', () => {
    beforeEach(() => {
      service.screenAccess_Cards = [
        { 
          label: 'Data Mining Solution', 
          subMenu: [
            { label: 'CAD Screen 1' },
            { label: 'CAD Screen 2' }
          ]
        },
        { 
          label: 'Other Solution', 
          subMenu: [
            { label: 'Other Screen 1' }
          ]
        }
      ];
    });

    it('should return true for Data Mining Solution label', () => {
      const result = service.isCadScreen('Data Mining Solution');
      expect(result).toBe(true);
    });

    it('should return true for CAD sub-menu items', () => {
      const result1 = service.isCadScreen('CAD Screen 1');
      const result2 = service.isCadScreen('CAD Screen 2');
      expect(result1).toBe(true);
      expect(result2).toBe(true);
    });

    it('should return false for non-CAD screens', () => {
      const result = service.isCadScreen('Other Solution');
      expect(result).toBe(false);
    });

    it('should return false for unknown screens', () => {
      const result = service.isCadScreen('Unknown Screen');
      expect(result).toBe(false);
    });

    it('should handle empty screenAccess_Cards', () => {
      service.screenAccess_Cards = [];
      const result = service.isCadScreen('Data Mining Solution');
      expect(result).toBe(false);
    });

    it('should handle undefined screenAccess_Cards', () => {
      service.screenAccess_Cards = undefined;
      expect(() => service.isCadScreen('Data Mining Solution')).toThrow();
    });
  });

  describe('processBU', () => {
    it('should process role details with business units', () => {
      const roleDetails = [
        ['role1', 'permission1', 'GBD'],
        ['role2', 'permission2', 'CSBD'],
        ['role3', 'permission3', 'GBD'],
        ['role4', 'permission4', null]
      ];

      service.processBU(roleDetails);

      expect(service.userBUs).toEqual([
        { name: 'GBD', id: 'GBD' },
        { name: 'CSBD', id: 'CSBD' }
      ]);
    });

    it('should handle role details with no business units', () => {
      const roleDetails = [
        ['role1', 'permission1', null],
        ['role2', 'permission2', null]
      ];

      service.processBU(roleDetails);

      // Should keep original userBUs since no valid BUs found
      expect(service.userBUs).toEqual([{ name: 'CSBD', id: 'CSBD' }]);
    });

    it('should remove duplicate business units', () => {
      const roleDetails = [
        ['role1', 'permission1', 'GBD'],
        ['role2', 'permission2', 'GBD'],
        ['role3', 'permission3', 'CSBD'],
        ['role4', 'permission4', 'GBD']
      ];

      service.processBU(roleDetails);

      expect(service.userBUs).toEqual([
        { name: 'GBD', id: 'GBD' },
        { name: 'CSBD', id: 'CSBD' }
      ]);
    });

    it('should handle empty role details', () => {
      const roleDetails = [];

      service.processBU(roleDetails);

      // Should keep original userBUs
      expect(service.userBUs).toEqual([{ name: 'CSBD', id: 'CSBD' }]);
    });

    it('should handle role details with custom business units', () => {
      const roleDetails = [
        ['role1', 'permission1', 'CUSTOM_BU1'],
        ['role2', 'permission2', 'CUSTOM_BU2']
      ];

      service.processBU(roleDetails);

      expect(service.userBUs).toEqual([
        { name: 'CUSTOM_BU1', id: 'CUSTOM_BU1' },
        { name: 'CUSTOM_BU2', id: 'CUSTOM_BU2' }
      ]);
    });
  });

  describe('getBusinessDivision', () => {
    it('should return selectedBusinessUnit when it is set', () => {
      service.selectedBusinessUnit = 'GBD';
      service.businessDivisionDetails = 'CSBD';

      const result = service.getBusinessDivision();

      expect(result).toBe('GBD');
    });

    it('should return businessDivisionDetails when selectedBusinessUnit is null', () => {
      service.selectedBusinessUnit = null;
      service.businessDivisionDetails = 'CSBD';

      const result = service.getBusinessDivision();

      expect(result).toBe('CSBD');
    });

    it('should return businessDivisionDetails when selectedBusinessUnit is undefined', () => {
      service.selectedBusinessUnit = undefined;
      service.businessDivisionDetails = 'CSBD';

      const result = service.getBusinessDivision();

      expect(result).toBe('CSBD');
    });

    it('should handle empty string selectedBusinessUnit', () => {
      service.selectedBusinessUnit = '';
      service.businessDivisionDetails = 'CSBD';

      const result = service.getBusinessDivision();

      expect(result).toBe('CSBD');
    });

    it('should handle both values being null', () => {
      service.selectedBusinessUnit = null;
      service.businessDivisionDetails = null;

      const result = service.getBusinessDivision();

      expect(result).toBeNull();
    });
  });

  describe('Service Properties', () => {
    it('should allow setting and getting cardSelectionEvent', () => {
      const mockEvent = { type: 'click', target: 'button' };
      service.cardSelectionEvent = mockEvent;

      expect(service.cardSelectionEvent).toEqual(mockEvent);
    });

    it('should allow setting and getting selectedLabel', () => {
      const mockLabel = { id: 1, name: 'Test Label' };
      service.selectedLabel = mockLabel;

      expect(service.selectedLabel).toEqual(mockLabel);
    });

    it('should allow setting and getting screenAccess_Cards', () => {
      const mockCards = [{ label: 'Test', subMenu: [] }];
      service.screenAccess_Cards = mockCards;

      expect(service.screenAccess_Cards).toEqual(mockCards);
    });

    it('should allow toggling isNavLinkSelected', () => {
      expect(service.isNavLinkSelected).toBe(false);
      
      service.isNavLinkSelected = true;
      expect(service.isNavLinkSelected).toBe(true);
      
      service.isNavLinkSelected = false;
      expect(service.isNavLinkSelected).toBe(false);
    });
  });

  describe('Subject Behavior', () => {
    it('should emit values through showBusinessDivisionPopup Subject', () => {
      let emittedValue: boolean;
      service.showBusinessDivisionPopup.subscribe(value => {
        emittedValue = value;
      });

      service.showBusinessDivisionPopup.next(true);
      expect(emittedValue).toBe(true);

      service.showBusinessDivisionPopup.next(false);
      expect(emittedValue).toBe(false);
    });

    it('should handle multiple subscribers to showBusinessDivisionPopup', () => {
      let subscriber1Value: boolean;
      let subscriber2Value: boolean;

      service.showBusinessDivisionPopup.subscribe(value => {
        subscriber1Value = value;
      });

      service.showBusinessDivisionPopup.subscribe(value => {
        subscriber2Value = value;
      });

      service.showBusinessDivisionPopup.next(true);

      expect(subscriber1Value).toBe(true);
      expect(subscriber2Value).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('should handle malformed role details in processBU', () => {
      const roleDetails = [
        ['role1'], // Missing elements
        ['role2', 'permission2'], // Missing BU
        ['role3', 'permission3', 'VALID_BU'],
        null, // Null entry
        undefined // Undefined entry
      ];

      expect(() => service.processBU(roleDetails)).not.toThrow();
    });

    it('should handle complex screenAccess_Cards structure', () => {
      service.screenAccess_Cards = [
        { 
          label: 'Data Mining Solution', 
          subMenu: [
            { label: 'CAD Screen 1', nested: { deep: 'value' } },
            { label: 'CAD Screen 2' }
          ]
        }
      ];

      const result = service.isCadScreen('CAD Screen 1');
      expect(result).toBe(true);
    });
  });
});
