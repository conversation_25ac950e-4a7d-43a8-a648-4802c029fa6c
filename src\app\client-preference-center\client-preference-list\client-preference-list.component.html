<!-- Clinet Preference List -->
<div class="fixed-nav bg-gray" [hidden]="!isListing">
    <div class="content-wrapper">
        <div>
            <div>
                <div>
                    <span class="table-title"><b>Data Exchange List</b></span>
                    <span class='.btn-spanpreference' style="float: right;">
                        <marketplace-button [label]="'Add Data Exchange'" [type]="'primary'" [name]="'primary'" [enabled]="!isReadOnly"
                            (onclick)="createPrference()">
                        </marketplace-button>

                    </span>
                </div>
                <div>
                    <div>
                        <marketplace-table [dataset]="clientPreferenceData" [rowHeight]="ruleDashbordTableRowhg"
                            [headerRowHeight]="ruleDashbordTableHeaderhg"
                            [columnDefinitions]="clientPreferenceColConfig" [isRowSelectable]="false"
                            [customExportConfig]="customExport" *ngIf="showTable" [redraw]="tableRedraw"
                            [id]="'clntTable'" [dropdownOptions]="kebabOptions"
                            (onDropdownOptionsClick)="editViewPreference($event)">
                        </marketplace-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<app-client-preference-add *ngIf="enablePage =='Add'" (DataEvent)="receiveFromAddEdit($event)">
</app-client-preference-add>
<app-client-preference-edit *ngIf="enablePage == 'Edit'" [editDataFromList]=selectedRowData
    (DataEvent)="receiveFromAddEdit($event)"></app-client-preference-edit>
<app-client-preference-view *ngIf="enablePage== 'View'" [editDataFromList]=selectedRowData
    (DataEvent)="receiveFromAddEdit($event)"></app-client-preference-view>