export const MENU_ITEMS: any = {
  //Inventory Menu Items
  'Overview': 'inventory/fileUpload/overview',
  'Criteria': 'inventory/fileUpload/criteria',
  'Exports': 'inventory/fileUpload/exports',
  'Settings': 'inventory/fileUpload/settings',
  'Review': 'inventory/review/review',
  'Files': 'inventory/file/file',
  'Global Search': 'inventory/global-search/search',
  'Home': '',

  //Product Catalog Menu Items
  'Dashboard': 'product-catalog/dashboard',
  'Client Payment': 'product-catalog/client-payment',
  'Global View': 'product-catalog/global-view',
  'Roles': 'product-catalog/roles',
  'Users': 'product-catalog/users',
  'Rules': 'product-catalog/rules',
  'View Rules': 'product-catalog/view-rules',
  'Client Preference Center': 'product-catalog/client-preference-center',
  'Status Mappings': 'product-catalog/status-mappings',
  'Standard View': 'product-catalog/standard-view',
  'Standard File': 'product-catalog/standard-file',
  'Import Data': 'product-catalog/import-data',
  'Extracts': 'product-catalog/extracts',
  'Client': 'product-catalog/client',
  'Market': 'product-catalog/market',
  'Product': 'product-catalog/product',
  'Help Center': 'product-catalog/help-center',
  'Scheduler': 'product-catalog/scheduler',
}

export const ROUTING_LABELS: any = {
  BULK_ASSIGNMENT_API_URL: 'api/dbg-inventoryinsight/adjustment/getAllclaimsByUserId',
  CLIENT_ID: 'clientId',
  CLIENT_NAME: 'clientName',
  USER_PROFILE: 'userProfile',
  CLIENT_PRODUCT: 'clientProduct',
  CLIENT_INVENTORY: 'clientInventory',
  PRODUCT_NAME: 'productName',
  INVENTORY_NAME: 'inventoryName',
  AUTH_CHECK: 'verifyAuth',
  USER_ID: 'userId',
  CAD_ACCESS: 'cad_access',
  SIGN_IN: 'signin',
  FORM_DETAILS: 'formDetails',
  MAILID_LIST: 'mailId-list',
  ROLE_ID: 'RoleId',
  ROLE_NAME: 'RoleName',
  INTERNAL_FLAG: 'internalFlag',
  OFFSHORE: 'Offshore',
  ONSHORE: 'Onshore',
  CLIENT_SITE: 'clientSite',
  VALUE: 'value',
  SELECTED_VALUE: 'selectedVal',
  DISABLED: 'disabled',
  ROLE_SCREEN_ID: 'roleScreenId',
  CREATE: 'create',
  VIEW: 'view',
  PERMISSIONS: "permissions",
  NOT_APPLICABLE: " N/A",
  CREATE_PERMISSION: "create",
  READ_PERMISSION: "read",
  LISTOF_ACCESSIBLE_CARDS: "listOfAccessibleCards",
  LISTOF_ACCESSIBLE_LINKS: "listOfAccessibleQuickLinks",
  COB: "Coordination of Benefits",
  OUTER_TEXT: "outerText",
  CHANGE_PASS_MODE_NO_QUEST: "noSecureQuest",
  CHANGE_PASS_MODE_QUEST: "secureQuest"
}

export const SAMPLEMENU_DATA: any = {

  "screenAccess_Cards": [

    {
      "label": "Program View",
      "value": "<i class='fa fa-bar-chart' aria-hidden='true'></i>",
      "subMenu": [
        {
          "label": "Program Value"
        },
        {
          "label": "Concept Performance"
        },
        {
          "label": "CAD Concept Side by Side "
        }
      ]
    },
    {
      "label": "Data Mining Solution",
      "value": "<i class='fa fa-sitemap' aria-hidden='true'></i>",
      "subMenu": [
        {
          "label": "Inventory"
        },
        {
          "label": "CAD Claims"
        },
        {
          "label": "Client Review"
        },
        {
          "label": "Response Summary"
        },
        {
          "label": "Search"
        },
        {
          "label": "Files"
        },
        {
          "label": "Trigger Kafka"
        },
        {
          "label": "Work Assignment"
        },
        {
          "label": "Adjustments Assignment"
        },
        {
          "label": "Claims Client"
        },
        {
          "label": "Adjustments"
        },
        {
          "label": "Inventory Status"
        },
        {
          "label": "Client UAT Response"
        },
        {
          "label": "Analytic Scheduler"
        },
        {
          "label": "Billing"
        },
        {
          "label": "Member"
        },
        {
          "label": "Analytic Timeline"
        }
      ]
    },
    {
      "label": "Concept Management",
      "value": "<i class='fa fa-th-large' aria-hidden='true'></i>",
      "subMenu": [
        {
          "label": "Concept Analysis"
        },
        {
          "label": "Insight Discovery"
        }
      ]
    },
    {
      "label": "Coordination of Benefits",
      "value": "<i class='fa fa-handshake-o' aria-hidden='true'></i>",
      "subMenu": [
        {
          "label": "Inventory Dashboard"
        },
        {
          "label": "Member Research"
        },
        {
          "label": "Program Value"
        },
        {
          "label": "My Work Queue"
        },
        {
          "label": "Investigations"
        }
      ]
    },
    {
      "label": "Complex Clinical Audit",
      "value": "<i class='fa fa-cogs' aria-hidden='true'></i>",
      "subMenu": [
        {
          "label": "Import Data"
        },
        {
          "label": "Tagging Summary"
        }
      ]
    },
    {
      "label": "Financials",
      "value": "<i class='fa fa-university' aria-hidden='true'></i>",
      "subMenu": [
        {
          "label": "Payment Reconciliation"
        },
        {
          "label": "Invoices"
        }
      ]
    }
  ],
  "screenAccess_Quick_Links": [
    {
      "label": "Rules",
      icon: 'sitemap',
      "subMenu": [
        {
          "label": "Rule Management"
        },
        {
          "label": "Rules Types"
        }
      ]
    },
    {
      "label": "Clients",
      icon: 'building',
      "subMenu": [
        {
          "label": "Setup"
        },
        {
          "label": "Data Exchange"
        },
        {
          "label": "Client Preference Center"
        },
        {
          "label": "Workflow Status Mapping"
        },
        {
          "label": "Client Fee Schedule"
        },
        {
          "label": "Status and Codes Summary"
        },
        {
          "label": "Hierarchy Rules"
        }
      ]
    },
    {
      "label": "Users",
      icon: 'user-circle-o',
      "subMenu": []
    },
    {
      "label": "Teams",
      icon: 'group',
      "subMenu": []
    },
    {
      "label": "Settings",
      icon: 'cog',
      "subMenu": [
        {
          "label": "System"
        },
        {
          "label": "System Inventory Types"
        },
        {
          "label": "Market"
        },
        {
          "label": "Product"
        },
        {
          "label": "Standard View"
        },
        {
          "label": "Standard File"
        },
        {
          "label": "Roles"
        }
      ]
    },
    {
      "label": "External User Registration",
      icon: 'external-link',
      "subMenu": []
    },
    {
      "label": "Help Center",
      icon: 'info',
      "subMenu": []
    }
  ]
}

export const AUTH_CONFIG: any = {
  RESPONSE_TYPE: 'response_type',
  STATE: 'state',
  CLIENT_ID: 'client_id',
  SCOPE: 'scope',
  CODE_CHALLENGE: 'code_challenge',
  CODE_CHALLENGE_METHOD: 'code_challenge_method=S256',
  REDIRECT_URI: 'redirect_uri',
  CODE: 'code',
  GRANT_TYPE: 'grant_type',
  AUTHORIZATION_CODE: 'authorization_code',
  CALLBACK: 'callback',
  REFRESH_TOKEN: 'refresh_token',
  LOGOUT_TEXT: 'Logged Out',
  LOGOUT_MESSAGE: 'You have been logged out, Click below button to login again',
  STATUS: 'status',
  BEARER: 'Bearer ',
  INTERNAL: 'INTERNAL',
  EXTERNAL: 'EXTERNAL',
  BASIC: 'Basic ',
  FORM_URL_ENCODE: 'application/x-www-form-urlencoded'
}

export const TOKEN_INFO: any = {
  ID_TOKEN: 'idToken',
  APP_ID_TOKEN: 'appIdToken',
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  SUB: 'sub',
  APP_TOKEN: 'appToken'
}

export const CONSTANTS: any = {
  ROLE_NAME: 'RoleName',
  APP_NAME: 'Payment Integrity',
  NAVIGATETO: 'navigateTo',
  LANDING_OBJECT: 'landingObject',
  PANEL_OBJECT: 'panelObject',
  USERNM: 'userName',

}

export const nullValueRole: any = {
  updateDataset: [{ id: CONSTANTS.ROLE_NAME, dataset: [] }],
  when: null
}

export const NAVIGATION = {
  'Program Value': '/product-catalog/analytics-dashboards/program-value',
  'Dashboards': '/product-catalog/analytics-dashboards/dashboards',
  'Concept Performance': '/product-catalog/analytics-dashboards/client-concept-performance',
  'CAD Concept Side by Side ': '/product-catalog/analytics-dashboards/concept-side-by-side-performance',
  'Inventory': 'workflow-dashboard/dashboard',
  'CAD Claims': 'workflow-dashboard/CAD-claims',
  'Client Review': 'workflow-dashboard/client-review',
  'Response Summary': 'workflow-dashboard/response-summary',
  // 'Search': '/inventory/global-search/search',
  'Search': 'workflow-dashboard/search',
  'Files': '',
  'Trigger Kafka': 'product-catalog/analytics-dashboards/kafka-notifications-trigger',
  'Work Assignment': 'workflow-dashboard/work-assignment',
  'Adjustments Assignment': 'workflow-dashboard/adjustments-assignment',
  'Claims Client': '/workflow-dashboard/claims-client',
  'Adjustments': '/workflow-dashboard/adjustments',
  'Inventory Status': 'workflow-dashboard/inventory-history',
  'Client UAT Response': '/workflow-dashboard/client-carelon-uat-response',
  "Analytic Scheduler": '/product-catalog/content-management/scheduler',
  "Billing": '/workflow-dashboard/cad-invoices',
  "Member": '/workflow-dashboard/cad-members',
  "Analytic Timeline": '/product-catalog/content-management/analytic-timeline',
  'Payment Reconciliation': '',
  'Invoices': '',
  'Import Data': '/inventory/fileUpload/file-upload-metadata',
  'Tagging Summary': '/inventory/fileUpload/tagging-summary',
  'Inventory Dashboard': '/coordination-of-benefits/inventory-dashboard',
  'Member Research': '/coordination-of-benefits/member-research',
  'My Work Queue': '/coordination-of-benefits/my-work-queue',
  'COB Program Value': '/coordination-of-benefits/program-value',
  'Investigations': '/coordination-of-benefits/investigations',
  'Velocity Portal': '/pad/home',
  'Report': '/pad/report/:reportId',
  'Concept Analysis': '/workflow-dashboard/dashboard/conceptAnalysis',
  'Insight Discovery': '',
  'Rule Management': '/rules',
  'Rules Types': '/rules/frequently-used-criteria',
  'Setup': '',
  'Client Preference Center': '/clients',
  'Data Exchange': '/clients',
  'Concept Configuration': '/product-catalog/settings-config/client-preference',
  'Status and Codes': '/product-catalog/status-codes-summary',
  'Workflow Status Mapping': '/product-catalog/crosswalk',
  'Client Fee Schedule': '',
  'Status and Codes Summary': '/product-catalog/status-codes-summary',
  'Hierarchy Rules': '/product-catalog/hierarchy-rules',
  'Users': '/product-catalog/security/users',
  'Teams': '/product-catalog/security/teams',
  'System': '/settings/system',
  'System Inventory Types': '',
  'Market': '/product-catalog/market',
  'Product': '/settings/product',
  'Standard View': '/settings/files',
  'Standard File': '/product-catalog/standard-file',
  'Roles': '/settings/roles',
  'External User Registration': '/product-catalog/security/registration',
  'Help Center': '/product-catalog/help-center',
  'Coordination of Benefits': 'https://www.careloninsights.com/payment-integrity/coordination-of-benefits', //// External URL, when user is not having access
  'Hit Rate': 'workflow-dashboard/hitrate',
  'Change Password': 'change'
}

export const CHANGE_PASS_QUICK_LINK = {
  "label": "Change Password",
  "subMenu": [
    {
      "label": null,
      "permission": "create,read"
    }
  ]
}
export const NOTIFICATION_CONSTANTS: any = {
  NOTIFICATION_ACCESS_MENUS: [{ 'menu': 'Data Mining Solution', 'enableNotification': true }],
  NOTIFICATION_ACCESS_ENVIRONMENTS: ['dev', 'sit'],
  NOTIFICATION_PUBLISHER_SCREEN: "Publish Notifications",
  NOTIFICATION_PUBLISHER_URL: "#/workflow-dashboard/publish-notification",
  NOTIFICATION_TITLE: '`<div class="notication-title">Notifications <div class="info-tooltip"><i class="info-icon"><span class="info-text">Notifications will expire after 30 days.</span></i></div></div>`'

}

export const LOB_CARD_NAME: any = {
  DMS: 'Data Mining Solution',
  COB: 'Coordination of Benefits',
  SIU: 'Special Investigations Unit',
  CCA: 'Complex Clinical Audit'
}


