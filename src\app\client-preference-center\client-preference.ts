import { list } from './constant';

export const typeMapping = {
  'decimal': 'numeric',
  'string': 'text',
  'varchar': 'text',
  'VARCHAR': 'text',
  'DATE': 'calendar',
  'NUMBER': 'numeric',
  'TIMESTAMP_NTZ': 'calendar'
};


export const standardFeeOptions = [
  {
    name: "10",
    id: 10
  },
  {
    name: "20",
    id: 20
  },
  {
    name: "30",
    id: 30
  }
];

export const nullValueProduct = {
  updateDataset: [{ id: list.TEMPLATE_NAME, dataset: [] }],
  when: null
}

export const emptyProduct = {
  updateSelectedValue: [{ id: list.TEMPLATE_NAME, value: null }, { id: list.SYSTEM, value: null }],
  when: null
}

export const operatorMap = {
  "Equal": "=", 
  "Not Equal": "!=", 
  "Greater Than": ">", 
  "Greater Than Or Equal": ">=",
  "Less Than": "<", 
  "Less Than Or Equal": "<=", 
  "contains": "contains", 
  "like": "like", 
  "Does Not Contain(s)": "not contains", 
  "Begins With": "startswith",
  "Ends With": "endswith", 
  "Does Not Begins With": "not startswith", 
  "Does Not End With": "not endswith", 
  "Is Null": "isnull", 
  "Is Not Null": "isnotnull", 
  "Between": "between", 
  "Not Between": "not between", 
  "in": "in"
};

export const rules = [{
  "field": "",
  "operator": "Equal",
  "value": "",
  "static": true,
  "active": true
}
]

export const exchangeObj = {
  inventoryType: '',
  system: '',
  conceptState: null
}