import { Component, OnInit, ElementRef, ViewEncapsulation } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { forkJoin } from 'rxjs';
import { environment } from 'src/environments/environment';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { ClientApiService } from 'src/app/_services/client-preference-api.service';
import { UtilitiesService } from 'src/app/_services/utilities.service';
import { ToastService } from 'src/app/_services/toast.service';
import { ROLES_CONSTANTS } from './constants/roles-screen-constants';
import { ROUTING_LABELS } from 'src/app/_constants/menu.constant';
import { CookieService } from 'ngx-cookie-service';
import { AuthService } from 'src/app/_services/authentication.services';

const ROLESSLIST = 'Roles List';
const AUDITLIST = 'Audit Trail';
const CLIENT_OFFSHORE = 'Offshore';
const CLIENT_ONSHORE = 'Onshore';
@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class RolesComponent implements OnInit {
  selectedClientName: any;

  constructor(private alertService: ToastService,
    private router: Router, private userManagementSvc: UserManagementApiService,
    private clientApiService: ClientApiService,
    private dateService: UtilitiesService, private cookieService: CookieService, private authService: AuthService) {
    this.userId = this.cookieService.get(ROUTING_LABELS.USER_ID).toUpperCase();
  }

  openAddNewRoleModel: any;
  roleDataset: any = [];
  roleConfig: any = {};
  roleKebab: any = [
    { label: '<i class="fa fa-eye" aria-hidden="true"></i> View Role', id: 'view-role' },
    { label: '<i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit Role', id: 'edit-role' }
  ];
  clientNames: any;
  selectedBusinessDivision;
  productNames: any;
  businessDivisionNames: any;
  editRoleFormJSON: any;
  permissionsDS: any;
  permissionsColumnConfig: any;
  isTableReady: boolean = false;
  isdataReadyToGivePermission: boolean = false;
  roleFormJSON: any;
  isRoleJSONReady: boolean = false;
  isclientSiteJsonReady: boolean = false;
  isRedraw: any;
  isRoleListDataReady: boolean = false;
  isSkillsLoaded: boolean
  isStandard: boolean = true

  permissionListDataset: any = {
    left: [],
    right: []
  };
  breadcrumbDataset: any = [{
    label: 'Home',
    url: '/'
  }, {
    label: 'Roles Management'
  }]

  tabledraw: any;

  dataset: any = [{ id: 'option-create', name: 'option-create', label: 'Write', value: 'write' },
  { id: 'option-read', name: 'option-read', label: 'view', value: 'view' }
  ]

  simpleConfig = [
    { name: "Step 1", description: "Add Role" },
    { name: "Step 2", description: "Preview Role Details" }
  ]

  sgDashboardDataset: any = [
    { id: 'rolesList', label: 'Roles List', checked: true }
  ];

  currentRoleFormData: any = {};

  isRoleListSelected: boolean = true;
  roleAuditConfig: any = {};
  addDynamicRow: any = {};
  openAuditRolesModel: any;
  addUserViewListDS: any;
  clientNameJson: any;
  productNameJson: any;
  businessDivisionJson: any;
  inventoryNameJson: any;
  clientSiteJson: any;
  isclientNameJsonReady: boolean = false;
  isproductNameJsonReady: boolean = false;
  isBusinessDivisionJsonReady: boolean = false;
  isinventoryNameJsonReady: boolean = false;
  productsOfClientSelected: any = [];
  inventoryTypeIDsData: any = [];
  inventoryOptions: any = [];
  accessScreensData: any = [];
  roleNameandDescription: any = {};
  clientNameResponse: any = {};
  productNameResponse: any = {};
  inventoryTypeResponse: any = {};
  clientSiteChangeResponse: any = {};
  prodInvIdResponse: any = {};
  prodInvId: any;
  pickListSelectedItems: any = [];
  public isLoading: boolean = false;
  createErrorOpenPopup: any = false;
  userId: string = "";
  isCarelonAdmin: boolean = false;
  errorMessage: string = '';

  ngOnInit(): void {
    let _columnConfig = this.userManagementSvc.getAssetsJson(ROLES_CONSTANTS.ROLES_LIST_TABLE_COLUMNS);
    let _fetchRoles = this.userManagementSvc.getRolesList();
    let _fetchForm = this.userManagementSvc.getAssetsJson(ROLES_CONSTANTS.ADD_ROLE_FORM);
    let _fetchPage = this.userManagementSvc.getAssetsJson(ROLES_CONSTANTS.PERMISSIONS_JSON);
    let _fetchAuditRoles = this.userManagementSvc.getAssetsJson(ROLES_CONSTANTS.AUDIT_ROLE_JSON);
    let _getScreenList = this.userManagementSvc.getListOfScreensForAddRole();
    let _getAllClientNames = this.clientApiService.getAllClientsInPreferenceCenter();
    let _getAllTemplateData = this.clientApiService.getAllTemplates();
    let _allProductsData = this.clientApiService.getAllProducts();
    this.isCarelonAdmin = this.authService.isWriteOnly;
    forkJoin([_columnConfig, _fetchRoles, _fetchForm, _fetchPage, _fetchAuditRoles, _getScreenList, _getAllClientNames, _allProductsData])
      .subscribe(
        ([columnConfigurations, roles, form, pages, audit, screenListForAddRoleTable, allClients, productData]) => {
          columnConfigurations[ROLES_CONSTANTS.COLUMN_CONFIG].colDefs.forEach(e => {
            e.field == ROLES_CONSTANTS.STATUS ? e.customFormatter = this.customFormatterStatus : "";
          });

          this.roleConfig = columnConfigurations[ROLES_CONSTANTS.COLUMN_CONFIG];

          this.roleDataset = roles[ROLES_CONSTANTS.RESPONSEDATA];
          this.roleFormJSON = form[ROLES_CONSTANTS.BASIC_ROLE_FORM];
          this.clientNameJson = form[ROLES_CONSTANTS.CLIENT_NAME_JSON];
          this.productNameJson = form[ROLES_CONSTANTS.PRODUCT_NAME_JSON];
          this.businessDivisionJson = form[ROLES_CONSTANTS.BUSINESS_DIVISION_JSON];
          this.inventoryNameJson = form[ROLES_CONSTANTS.INVENTORY_TYPE_DETAILS_JSON];
          this.clientSiteJson = form[ROLES_CONSTANTS.CLIENT_SITE_JSON];
          if (this.isCarelonAdmin) {
            this.clientSiteJson.find((x) => x.name == ROLES_CONSTANTS.TEAM_TYPE)[ROLES_CONSTANTS.DISABLED] = false;
          } else {
            this.clientSiteJson.find((x) => x.name == ROLES_CONSTANTS.TEAM_TYPE ? this.clientSiteJson.pop() : '')
          }
          this.permissionsDS = []
          this.permissionsColumnConfig = JSON.parse(JSON.stringify(pages[ROLES_CONSTANTS.COLUMN_CONFIG]));
          this.permissionsColumnConfig.colDefs.filter(x => x.field == ROLES_CONSTANTS.CREATE).forEach(element => element.customFormatter = this.customFormatterFnCreate);
          this.permissionsColumnConfig.colDefs.filter(x => x.field == ROLES_CONSTANTS.VIEW).forEach(element => element.customFormatter = this.customFormatterFnRead);
          let navigationObjs: any = JSON.parse(JSON.stringify(screenListForAddRoleTable.responseData));
          for (let i = 0; i < navigationObjs.length; i++) {
            this.permissionListDataset.left.push(navigationObjs[i].name);
            this.permissionsDS.push({ "functional_page": navigationObjs[i].name, "roleScreenId": null, "roleId": null, "screenId": navigationObjs[i].masterId, "create": false, "view": false, "createUserId": navigationObjs[i].creatUserId, "createDateTime": null, "lastUpdateUserId": navigationObjs[i].lastUpdtUserId, "prodName": navigationObjs[i].prodName })
          }
          this.roleAuditConfig = audit[ROLES_CONSTANTS.COLUMN_CONFIG];
          this.roleAuditConfig.colDefs.filter(x => x.field == ROLES_CONSTANTS.FORM_AUDIT).forEach(element => {
            element.customFormatter = this.customFormatterFnAudit
          });
          this.roleAuditConfig.colDefs.filter(x => x.field == ROLES_CONSTANTS.ACTIONVIEW_AUDIT).forEach(element => {
            element.customFormatter = this.customFormatterFnActionAudit
          });
          this.isRoleListDataReady = true;
          this.isRedraw = Date.now()
          this.clientNames = allClients.map(x => ({ "id": x.clientId, "name": x.clientName }));
          this.productNames = productData.map(x => ({ "id": x.prodId, "name": x.productName }));
          this.clientNameJson[0].options = this.clientNames;
          this.productNameJson[0].options = this.productNames;
        }
      )

    this.isproductNameJsonReady = true;
    this.isinventoryNameJsonReady = true;
  }

  /**
 * Method fires on segemented selection
 * @param event 
 */
  _onDashboardSGSelection(event: any): void {
    switch (event.selection.label) {
      case AUDITLIST:
        this.isRoleListSelected = false;
        this.tabledraw = Date.now()
        break;
      case ROLESSLIST:
        setTimeout(() => this.isRoleListSelected = true, 100);
        this.tabledraw = Date.now()
        break;
      default:
        break;
    }
  }

  /**
   * method to route the page
   * @param event 
   */
  selectedLink(event: any): void {
    this.router.navigate([event.selected.url]);
  }

  /**
   * Open the model dialog
   * @param event 
   */
  openModalPopup(event: Event): void {
    this.errorMessage = '';
    if (!this.isCarelonAdmin) return;
    this.openAddNewRoleModel = Date.now();
    setTimeout(() => {
      this.isclientNameJsonReady = true;
      this.isclientSiteJsonReady = true;
      this.isRoleJSONReady = true;
      this.isSkillsLoaded = true
      this.isTableReady = true;
    }, 10);
  }

  /**
   * customFormatterStatus funtion for status button in Role table
   * @param event 
   */
  customFormatterStatus(event): void {
    let btn;
    switch (event[ROLES_CONSTANTS.DATACONTEXT].status) {
      case true:
        btn = "<button type='button' title='Active' class='btn btn btn-active btn-wrap-text'>Active</button>";
        break;
      case false:
        btn = "<button type='button' title='Inactive' class='btn btn btn-inactive btn-wrap-text'>Inactive</button>";
        break;
    }
    return btn;
  }

  /**
   * customFormatterStatus funtion for RoleId in Role table
   * @param event 
   */
  customFormatterRoleId(event): String {
    return `<div class="product-dashboard dropdown-container" tabindex=“-1”>
      <span class="bundles-text-field"> &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp${event.value}</span>
      </div>`;
  }

  /**
   * customFormatterStatus funtion for RoleName in Role table
   * @param event 
   */
  customFormatterRoleName(event): String {
    return `<div class="product-dashboard dropdown-container" tabindex=“-1”>
      <span class="bundles-text-field">&nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp${event.value}</span>
      </div>`;
  }

  /**
   * customFormatterStatus funtion for Modifiedby Field in Role table
   * @param event 
   */
  customFormatterLastUpdatedBy(event): String {
    return `<div class="product-dashboard dropdown-container" tabindex=“-1”>
      <span class="bundles-text-field">&nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp${event.value}</span>
      </div>`;
  }

  /**
   * customFormatterStatus funtion for ModifiedDate Field in Role table
   * @param event 
   */
  customFormatterModifiedDate(event): String {
    return `<div class="product-dashboard dropdown-container" tabindex=“-1”>
      <span class="bundles-text-field">&nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp${event.value}</span>
      </div>`;
  }

  /**
   * Close the modal dialog
   */
  closePopup(): void {
    this.isclientSiteJsonReady = false;
    this.isclientNameJsonReady = false;
    this.isRoleJSONReady = false;
    this.openAddNewRoleModel = false;
    this.isTableReady = false;
  }
  /**
   * Close the modal dialog
   */
  closeMandatoryFieldPopup(): void {
    this.createErrorOpenPopup = false;
  }

  /************************ On Table Kebab Selection *****************************/
  onRoleAction(event: any): void {
    ///To be replaced with service
    localStorage.setItem('selected-row-clientID', JSON.stringify(event.currentRow[ROLES_CONSTANTS.CLIENTID]));
    localStorage.setItem('selected-row-RoleID', JSON.stringify(event.currentRow[ROLES_CONSTANTS.ROLEID]));
    localStorage.setItem('selected-row-ProdID', JSON.stringify(event.currentRow[ROLES_CONSTANTS.PRODID]));
    localStorage.setItem('selected-business-division', (event.currentRow[ROLES_CONSTANTS.BUSINESS_DIVISION]));

    switch (event.text) {
      case ROLES_CONSTANTS.VIEWROLE:
        this.router.navigate([ROLES_CONSTANTS.NAVIGATION_VIEW_ROLE]);
        break;
      case ROLES_CONSTANTS.EDITROLE:
        this.router.navigate([ROLES_CONSTANTS.NAVIGATION_EDIT_ROLE]);
        break;
      default:
        break;
    }
  }

  /**
   * Method Fires on cell click of table
   * @param e 
   */
  onCellClick(e) {
    this.accessScreensData = e.dataset;

    let _clickedElement: any = e.eventData.target;
    if (_clickedElement.classList.contains(ROLES_CONSTANTS.CREATE_CHECK)) {
      e.currentRow[ROLES_CONSTANTS.CREATE] = !e.currentRow[ROLES_CONSTANTS.CREATE];
    }
    else if (_clickedElement.classList.contains(ROLES_CONSTANTS.READ_CHECK)) {
      e.currentRow[ROLES_CONSTANTS.VIEW] = !e.currentRow[ROLES_CONSTANTS.VIEW];
    }
    this.currentRoleFormData[ROLES_CONSTANTS.CREATE_SCREENS] = [];
    this.currentRoleFormData[ROLES_CONSTANTS.VIEW_SCREENS] = [];
    e.dataset.forEach(element => {
      if (element.create) {
        this.currentRoleFormData[ROLES_CONSTANTS.CREATE_SCREENS].push(element.functional_page)
      }
      if (element.view) {
        this.currentRoleFormData[ROLES_CONSTANTS.VIEW_SCREENS].push(element.functional_page)
      }
    });
  }

  /**
   * function triggred when tab is changed in Stepper component 
   * to populate data to View list component   
   * @param event 
   */
  addRoleStepChange = (event) => {
    if (event == 1) {
      this.addUserViewListDS = [
        {
          label: ROLES_CONSTANTS.ROLE_NAME_FIELD,
          value: this.currentRoleFormData[ROLES_CONSTANTS.ROLENAME]
        },
        {
          label: ROLES_CONSTANTS.ROLE_DESC_FIELD,
          value: this.currentRoleFormData[ROLES_CONSTANTS.DESCRIPTION]
        },
        {
          label: ROLES_CONSTANTS.ASSIGN_PROD_ID_FIELD,
          value: this.currentRoleFormData[ROLES_CONSTANTS.PRODUCT]
        },
        {
          label: ROLES_CONSTANTS.BUSINESS_DIVISION_FIELD,
          value: this.currentRoleFormData[ROLES_CONSTANTS.BUSINESS_DIVISION]
        },
        {
          label: ROLES_CONSTANTS.CLIENT_ID_FIELD,
          value: this.currentRoleFormData[ROLES_CONSTANTS.CLIENT]
        },
        {
          label: ROLES_CONSTANTS.REMINDER_DATE_FIELD,
          value: this.currentRoleFormData[ROLES_CONSTANTS.REMINDERDATE]
        },
        {
          label: ROLES_CONSTANTS.SELECTED_TEAM_FIELD,
          value: this.currentRoleFormData[ROLES_CONSTANTS.TEAMSELECTION]
        },
        {
          label: ROLES_CONSTANTS.CLIENT_SITE_FIELD,
          value: this.currentRoleFormData[ROLES_CONSTANTS.CLIENTSITE]
        },
        {
          label: ROLES_CONSTANTS.CREATE_SCREENS_FIELD,
          value: this.currentRoleFormData[ROLES_CONSTANTS.CREATE_SCREENS]
        },
        {
          label: ROLES_CONSTANTS.READ_SCREENS_FIELD,
          value: this.currentRoleFormData[ROLES_CONSTANTS.VIEW_SCREENS]
        }
      ]
    }
  }
  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFnCreate(event: any): string {
    return event?.dataContext.create ? `<input type="checkbox" class="create-check" checked="${event?.dataContext.create}">` : `<input type="checkbox" class="create-check">`;
  }

  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFnRead(event: any): string {
    return event?.dataContext.view ? `<input type="checkbox" class="read-check" checked="${event?.dataContext.view}">` : `<input type="checkbox" class="read-check">`;
  }

  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFnUpdate(event: any): string {
    return event?.dataContext.update ? `<input type="checkbox" class="update-check" checked="${event?.dataContext.update}">` : `<input type="checkbox" class="update-check">`;
  }

  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFnDelete(event: any): string {
    return event?.dataContext.delete ? `<input type="checkbox" class="delete-check" checked="${event?.dataContext.delete}">` : `<input type="checkbox" class="delete-check">`;
  }

  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFnAudit(event: any): string {
    return event?.value ? `<i class="fa fa-check-circle permission-granted" aria-hidden="true"></i>` : `<i class="fa fa-times-circle permission-denied" aria-hidden="true"></i>`;
  }

  /**
   * Custom Formatter method to represent cell as view Details
   * @param event 
   */
  customFormatterFnActionAudit(event: Event): string {
    return `<i class="fa fa-external-link action-view-more" aria-hidden="true"></i>`;
  }

  /**
 * Method to show standard/pick list view
 * @param e 
 */
  changeView(e: any): void {
    e.toggle ? this.isStandard = true : this.isStandard = false;

    if (!this.isStandard) {
      setTimeout(() => {
        let addPlaceholderInPicklist = document.getElementsByClassName(ROLES_CONSTANTS.PICKERTOOLBARLEFT,) as HTMLCollectionOf<HTMLElement>;
        for (let i = 0; i < addPlaceholderInPicklist.length; i++) {
          let textbox = addPlaceholderInPicklist[i]?.children[1] as HTMLInputElement;
          textbox.placeholder = "Search Screens"
        }
      }, 1);
    }

  }

  /**
     * Method Fires on selection of Role Name and Role Description in form
     * @param event 
     */
  _onRoleSelection(event: any): void {
    this.currentRoleFormData.roleName = event.current.roleName;
    this.currentRoleFormData.description = event.current.description;
    this.roleNameandDescription = event.current;
  }
  /**
     * Method Fires on Selection of Client in Form
     * @param event 
     */
  _onclientNamesSelection(event: any): void {
    this.currentRoleFormData.client = event.current.clientId;
    this.clientNameResponse = event.current;
    this.selectedClientName = this.clientNames.find(x => x.id == event.current.clientId).name;
    this.selectedClientName == ROLES_CONSTANTS.ANTHEM && this.productNameResponse.productId == 11 ? this.isBusinessDivisionJsonReady = true : this.isBusinessDivisionJsonReady = false;
    this.inventoryTypeIDsData = [];
    this.inventoryNameJson[0].options = this.inventoryTypeIDsData;
  }
  /** Method fires on selection of business division in form */
  _onBusinessDivisionSelection(event: any): void {
    this.currentRoleFormData.businessDivision = event.current.businessDivision;
    this.selectedBusinessDivision = event.current;

  }
  /**
     * Method Fires on Selection of Product in Form
     * @param event 
     */
  _onproductNamesSelection(event): void {
    this.currentRoleFormData.product = event.current.productId;
    this.productNameResponse = event.current;
    this.selectedClientName == ROLES_CONSTANTS.ANTHEM && event.current.productId == 11 ? this.isBusinessDivisionJsonReady = true : this.isBusinessDivisionJsonReady = false;
    this.inventoryTypeIDsData = [];
    this.inventoryNameJson[0].options = this.inventoryTypeIDsData;
    if (event.current.productId != event.previous.productId) {
      this.isinventoryNameJsonReady = false;
      let inventoryTypeIdHttp = this.clientApiService.getInventoryTypeId(event.current.productId);

      inventoryTypeIdHttp.subscribe(inventoryData => {
        inventoryData[ROLES_CONSTANTS.INVDETAILS].forEach(invTypeIdField => {
          let inventoryTypeObject: any = {};
          inventoryTypeObject.name = this.fetchInventoryTypeName(invTypeIdField[ROLES_CONSTANTS.INVTYPEID]).toString();
          inventoryTypeObject.id = invTypeIdField[ROLES_CONSTANTS.INVTYPEID];
          this.inventoryTypeIDsData.push(inventoryTypeObject);
        });
        this.inventoryNameJson[0].options = this.inventoryTypeIDsData;
        this.isinventoryNameJsonReady = true;
      })
    }
  }
  /**
   * function to compare filetemplate results with inventory type Ids to get Inventory name for particular product
   */
  fetchInventoryTypeName(invTypeId): string {
    let fileTemplateData = this.inventoryOptions.find(x => x.id == invTypeId);
    return fileTemplateData.name;
  }
  /**
     * Method Fires on Selection of Inventory Type Name in Form
     * @param event 
     */
  _oninventoryTypeSelection(event: any): void {
    this.inventoryTypeResponse = event.current;
  }

  /**
  * Method Fires on Selection of Client Site in Form
  * @param event 
  */
  _onClientSiteChanged(event: any): void {
    event.current.clientSite = event.current.clientSite ? CLIENT_OFFSHORE : CLIENT_ONSHORE;
    this.currentRoleFormData.clientSite = event.current.clientSite;
    this.currentRoleFormData.reminderDate = event.current.reminderDate;
    this.currentRoleFormData.teamSelection = event.current.teamSelection;
    this.clientSiteChangeResponse = event.current;
  }

  /**
 * Method to show changed Audit values
 *  @param event 
 */
  rendererTableClicked(e: any): void {
    let _clickedElement: any = e.eventData.target;
    // On changed click
    if (_clickedElement.classList.contains(ROLES_CONSTANTS.ACTION_VIEW_MORE)) {
      this.openAuditModalPopup();
      this.currentRoleFormData = this.roleDataset.find(x => x.roleName == e.currentRow.roleName)
    }
  }

  /**
  * Method to open Audit modeldialog
  */
  openAuditModalPopup(): void {
    this.openAuditRolesModel = Date.now();
  }

  /**
   * Method to close modal dialog
   */
  closeAuditPopup(): void {
    this.openAuditRolesModel = false;
  }

  /**
   * function to collect transfered set of data
   */
  _onListSelection(e): void {
    this.isdataReadyToGivePermission = false;
    this.pickListSelectedItems = e.selectedItems;
    setTimeout(() => {
      this.isdataReadyToGivePermission = true;
    }, 10);
  }

  /**
   * To remove highlighting from fieds which passed validation
  */
  resetValidFields(): void {
    const collection = document.querySelectorAll(
      `marketplace-dynamic-form input.form-control.ng-valid ,
      marketplace-dynamic-form .ng-select.ng-select-single.ng-valid .ng-select-container,
      marketplace-textarea.ng-valid .textarea-holder textarea,
      marketplace-date-picker.ng-valid input.ng2-flatpickr-input.flatpickr-input`
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].classList.remove(ROLES_CONSTANTS.REDBORDER);
    }
  }

  /**
  * To highlight fieds which failed validation
 */
  showAllInvalidFields(): void {
    this.errorMessage = ROLES_CONSTANTS.MANDATORY_ERROR_MSG;
    this.createErrorOpenPopup = true;
    this.resetValidFields();
    const invalidCollection = document.querySelectorAll(
      `marketplace-dynamic-form input.form-control.ng-invalid ,
        marketplace-dynamic-form .ng-select.ng-select-single.ng-invalid .ng-select-container,
        marketplace-textarea.ng-invalid .textarea-holder textarea,
        marketplace-date-picker.ng-invalid input.ng2-flatpickr-input.flatpickr-input`
    );
    for (let i = 0; i < invalidCollection.length; i++) {
      invalidCollection[i].classList.add(ROLES_CONSTANTS.REDBORDER);
    }
  }

  /**
   * function to push selected values from picklist to the respective create, edit , view, update array
   */
  onCheckBoxSelection(event): void {
    this.currentRoleFormData[ROLES_CONSTANTS.CREATE_SCREENS] = [];
    this.currentRoleFormData[ROLES_CONSTANTS.VIEW_SCREENS] = [];
    for (let i = 0; i < event.value.length; i++) {
      switch (event.value[i]) {
        case ROLES_CONSTANTS.CREATE:
          this.pickListSelectedItems.forEach(screen => {
            this.currentRoleFormData[ROLES_CONSTANTS.CREATE_SCREENS].push(screen.value)
          });
          break;
        case ROLES_CONSTANTS.VIEW:
          this.pickListSelectedItems.forEach(screen => {
            this.currentRoleFormData[ROLES_CONSTANTS.VIEW_SCREENS].push(screen.value)
          });
          break;
      }
    }
  }

  /**
  * Validate query builder fields and evaluate rule level
  */
  validateRole(): void {
    if (this.isCarelonAdmin) {
      if (this.accessScreensData?.args?.length === 0 || this.roleNameandDescription.roleName === "" || this.roleNameandDescription.roleName === undefined || this.roleNameandDescription.description == ""
        || this.roleNameandDescription.description === undefined || this.clientNameResponse.clientId === "" || this.clientNameResponse.clientId == undefined || this.productNameResponse.productId == "" || this.productNameResponse.productId == undefined
        || this.clientSiteChangeResponse.reminderDate === "" || this.clientSiteChangeResponse.reminderDate == undefined || this.clientSiteChangeResponse.teamType == "" || this.clientSiteChangeResponse.teamType == undefined) {
        this.showAllInvalidFields();
      }
      else if ((this.accessScreensData.length > 0 && this.accessScreensData?.find(c => c.view == true || c.create == true)) || (this.accessScreensData?.dataset && this.accessScreensData?.dataset?.find(c => c.view == true || c.create == true))) {
        this.addRole();
      }
      else {
        this.errorMessage = ROLES_CONSTANTS.MANDATORY_SCREEN_ACCESS_ERROR_MSG;
        this.createErrorOpenPopup = true;
      }
    }

  }

  /**
     * Method calls Upon to Create Payload for Add Role method
     */
  addRole(): void {
    if (this.accessScreensData.dataset) {
      this.accessScreensData.dataset.forEach(function (v) {
        delete v.roleScreenId;
        delete v.id;
        delete v.roleId;
        delete v.functional_page;
        delete v.prodName;
      });
    }
    else {
      this.accessScreensData.forEach(function (v) {
        delete v.roleScreenId;
        delete v.id;
        delete v.roleId;
        delete v.functional_page;
        delete v.prodName;
      });
    }


    let payloadForAddingRole = {
      "roleId": null,
      "roleName": this.roleNameandDescription.roleName,
      "description": this.roleNameandDescription.description,
      "clientId": this.clientNameResponse.clientId,
      "clientName": this.selectedClientName,
      "status": 1,
      "clientSite": this.clientSiteChangeResponse.clientSite,
      "reminderDate": this.dateService.getECPDateFormat(this.clientSiteChangeResponse.reminderDate),
      "teamType": this.clientSiteChangeResponse.teamType,
      "roleScreenModels": this.accessScreensData,
      "createDateTime": null,
      "requestType": "create",
      "createUserId": "AL05354",
      "lastUpdateUserId": this.userId,
      "prodId": this.productNameResponse.productId,
      "prodName": this.productNames.find(p => p.id === this.productNameResponse.productId)?.name,
      "businessDivision": this.selectedClientName == ROLES_CONSTANTS.ANTHEM && this.productNameResponse.productId == 11 ? this.selectedBusinessDivision.businessDivision : null,
    }

    this.clientApiService.addUpdateRoleData(payloadForAddingRole).subscribe((data) => {
      if (data) {
        this.openAddNewRoleModel = false;
        this.alertService.setSuccessNotification({
          notificationHeader: ROLES_CONSTANTS.SUCCESS,
          notificationBody: ROLES_CONSTANTS.ROLE_SUCCESS_MESSAGE,
          notificationDuration: 2000,
        });
        setTimeout(() => window.location.reload(), 2000);
      }
    },
      error => {
        this.alertService.setErrorNotification({
          notificationHeader: ROLES_CONSTANTS.FAIL,
          notificationBody: error,
        });
        this.isLoading = false;
      });
  }
}
