import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UsersComponent } from './users.component';
import { EditUserComponent } from './edit-user/edit-user.component';
import { ViewUserComponent } from './view-user/view-user.component';

import { AuthGuard } from '../_helpers/auth.guard';
//C2P
//import { PermissionGuard } from 'src/app/_helpers/permission.guard';
//C2P
// import { AuthGuard } from 'src/app/_helpers/auth.guard';
//C2P
//import { EditUserManagementComponent } from './user-management/edit-user-management/edit-user-management.component';
//import { EditUserManagementComponent } from '../user-management/edit-user-management/edit-user-management.component';

const routes: Routes = [{
  path: '', 
  component: UsersComponent,
  canActivate : [AuthGuard]  
},{
  path: 'edit-user', 
  component: EditUserComponent,
 //C2P canActivate:[PermissionGuard]
},
{
  path: 'view-user',
  component: ViewUserComponent,
 //C2P canActivate:[PermissionGuard]
},
//C2P
// {
//   path: 'edit-user-management', 
//   component: EditUserManagementComponent,
//   canActivate:[PermissionGuard]
// }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class UsersRoutingModule { }
