<div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>
<div>
  <marketplace-breadcrumb
    [dataset]="breadcrumbDataset"
    (onSelection)="breadcrumSelection($event)"
  >
  </marketplace-breadcrumb>
  
  <div class="dynamix-form-container">
    <span class="title-topheading">
      <a (click)="backToPreviousPage()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="icon icon-tabler icons-tabler-filled icon-tabler-circle-chevron-left" aria-hidden="true" focusable="false">
              <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
              <path d="M17 3.34a10 10 0 0 1 5 8.66c0 5.523 -4.477 10 -10 10s-10 -4.477 -10 -10a10 10 0 0 1 15 -8.66m-3.293 4.953a1 1 0 0 0 -1.414 0l-3 3a1 1 0 0 0 0 1.414l3 3a1 1 0 0 0 1.414 0l.083 -.094a1 1 0 0 0 -.083 -1.32l-2.292 -2.293l2.292 -2.293a1 1 0 0 0 0 -1.414"/>
          </svg>
      </a>
      Add New Bundle
  </span>
  
    <marketplace-dynamic-form
    [formJSON]="generalDetailsJson"
    (onValueChange)="mapValuesToJson($event)"
    *ngIf="isFormready || showLoader"
    [isSubmitNeeded]="false"
    >
  </marketplace-dynamic-form>

  <span class="btn-span">
    <marketplace-button
      [label]="'Cancel'"
      [type]="'secondary'"
      [name]="'secondary'"
      (onclick)="backToPreviousPage()"
    >
    </marketplace-button>

    <marketplace-button
      [label]="'Submit'"
      [type]="'primary'"
      [name]="'primary'"
      [enabled]="isEnabled || isLoading"
      (onclick)="postApiSubmit()"
    >
      <i class="fa fa-spinner fa-spin" *ngIf="isLoading"></i>
    </marketplace-button>
  </span>
  </div>
</div>
