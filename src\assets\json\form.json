{"src": [{"optionName": "name", "optionValue": "code", "multiple": false, "closeOnSelect": true, "label": "Rule Type", "group": "Edit Rule", "type": "select", "name": "memberID", "column": "1", "groupColumn": "2", "disabled": false, "value": "Exclusion", "options": [{"name": "Expiration/Lookback Rule", "value": "Expiration/Lookback Rule"}, {"name": "Exclusion", "value": "Expiration/Lookback Rule"}, {"name": "No Recovery", "value": "Expiration/Lookback Rule"}, {"name": "Exclusion", "value": "Exclusion"}]}, {"optionName": "name", "optionValue": "code", "multiple": false, "closeOnSelect": true, "label": "Rule sub-type", "group": "Edit Rule", "type": "select", "name": "ruleSubType", "column": "1", "groupColumn": "2", "disabled": false, "value": "contract", "options": [{"name": "Contract", "value": "contract"}, {"name": "State/Product", "value": "state_and_product"}]}, {"label": "Rule Name", "group": "Edit Rule", "type": "text", "name": "ruleName", "column": "1", "groupColumn": "2", "disabled": false, "value": "", "placeholder": "Rule Name"}, {"label": "Rule Description", "group": "Edit Rule", "type": "textarea", "name": "ruleDescription", "column": "1", "groupColumn": "2", "disabled": false, "value": ""}, {"optionName": "name", "optionValue": "code", "multiple": false, "closeOnSelect": true, "label": "Calculation Fields", "group": " ", "type": "select", "name": "calculationFields", "column": "1", "groupColumn": "2", "disabled": false, "value": "Date Of Service", "options": [{"name": "Date Of Service", "value": "date_of_service"}, {"name": "Paid <PERSON>", "value": "12"}, {"name": "Received Date", "value": "15"}, {"name": "Claim Service Date", "value": "18"}, {"name": "Paid Date - Calendar Year", "value": "20"}, {"name": "Received Date - Calendar Year", "value": "24"}, {"name": "30", "value": "30"}, {"name": "36", "value": "36"}, {"name": "48", "value": "48"}]}, {"optionName": "name", "optionValue": "code", "multiple": false, "closeOnSelect": true, "label": "Lookback Period", "group": " ", "type": "select", "name": "lookbackPeriod", "column": "1", "groupColumn": "2", "disabled": false, "value": "12", "options": [{"name": "6", "value": "6"}, {"name": "12", "value": "12"}, {"name": "15", "value": "15"}, {"name": "18", "value": "18"}, {"name": "20", "value": "20"}, {"name": "24", "value": "24"}, {"name": "30", "value": "30"}, {"name": "36", "value": "36"}, {"name": "48", "value": "48"}]}, {"label": "Start Date", "group": " ", "type": "date", "name": "startDate", "column": "1", "groupColumn": "2", "disabled": false, "value": "02-02-2022"}, {"label": "End Date", "group": " ", "type": "date", "name": "endDate", "column": "1", "groupColumn": "2", "disabled": false, "value": "02-02-2022"}, {"group": " ", "type": "checkbox", "name": "applyRules", "column": "2", "groupColumn": "2", "checkboxDescription": "Apply Rules Retroactively to Inventory", "disabled": false}, {"group": " ", "type": "checkbox", "name": "Active", "column": "2", "groupColumn": "2", "required": true, "checkboxDescription": "Active", "value": "1"}], "internal_poc": [{"name": "User 1", "code": "0"}, {"name": "User 2", "code": "1"}, {"name": "User 3", "code": "2"}], "external_poc": [{"name": "External User 1", "code": "0"}, {"name": "External User 2", "code": "1"}, {"name": "External User 3", "code": "2"}]}