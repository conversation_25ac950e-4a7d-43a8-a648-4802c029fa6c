import { Component, OnInit, Input, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-filebreadcum',
  templateUrl: './filebreadcum.component.html',
  styleUrls: ['./filebreadcum.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class FilebreadcumComponent implements OnInit {

  @Input()
  public schedulerBreadCrumbData: any = [];

  constructor(private router: Router) { }

  ngOnInit(): void {
  }
  /**
    * breadcrumSelection Funtion
    */
  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }
}
