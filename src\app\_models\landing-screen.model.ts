export interface ILandingScreen{
    cards: ILandingScreenCards[],
    quickLinks: ILandingScreenQuickLinks[]
}

export interface ILandingScreenCards{
    label: string,
    value: string,
    submenu?: ISubMenu[],
    name?: string;
}

export interface ILandingScreenQuickLinks{
    label: string,
    value: string,
    subMenu?:any
}

export interface ISubMenu{
    label: string,
    permission: string
}
