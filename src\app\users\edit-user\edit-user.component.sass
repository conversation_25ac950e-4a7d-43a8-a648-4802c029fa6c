app-edit-user
    
    .header-blue
        color: #5009B5

    .header-blue-client 
        margin-top: 10px
        margin-bottom: -20px
        color: #390681
        font-size: 18px
        font-family: elevance-semi-bold
    

    .page-wrapper
        padding: 1rem 1rem

        .page-header
            h3
                color: #5009B5
                font-family: 'elevance-medium'
                display: inline-block
                
        .button-container
            float: right

    .asterisk
        color: red
            
    .form-container

        .stepper-container__footer
            margin-top: 30px

        marketplace-dynamic-form 
            label.form-label 
                margin-top: 1rem
            
            .form-row.form-element-group
                border-radius: 8px
                padding: 4px 8px
                border-style: dashed
                
            .grp-header-blue
                margin-top: 8px
                color: #390681
                font-size: 18px
                font-family: elevance-semi-bold

            .form-radio-button
                .custom-control-label
                    margin-top: 5px

            .form-group
                marketplace-switch .switch-holder
                    flex-direction: row
                    align-items: inherit 
                    float: left

                .switch 
                        margin-right: 1rem
                        margin-left: 1rem  
                 


        .note-color
            color: #da1e28
            font-size: 1rem
            margin-top: 0px
            margin-left: 5px
            align-items: center
        
        .dashed-container
            marketplace-dynamic-form
                .form-row.form-element-group 
                    
                    height: fit-content
                
    
                
            
        
        .skill-container
            height: 510px
            position: relative
            overflow: auto
            marketplace-select .select-holder .ng-select#memberBrand ng-dropdown-panel,
            marketplace-select .select-holder .ng-select#serviceProviderRegion ng-dropdown-panel,
            marketplace-select .select-holder .ng-select#conceptStateSelect ng-dropdown-panel
                top: auto
                bottom: 100%

            .show-more-container 
                position: absolute                
                z-index: 2
                font-size: 16px
                right: 1.8rem
                top: 6px
                cursor: pointer

                .angle-icon
                    color: #794cff
                
             
            
        
        .show 
            overflow: visible
            height: auto
                    
        .action-container 
            position: relative   

            .flex-note
                display: flex
                justify-content: center
               
        .label-title 
            color: #666666 !important
            font-family: 'elevance-semi-bold'
        
          
        .label-value 
            font-weight: 700
            font-size: 16px
            color: #666
            word-break: break-word
            font-family: 'elevance'

        .uimp-key-values
            margin: 0.8rem 0

        .refresh-btn 
            right: 0        
        
        .action-btns 
            display: flex
            justify-content: space-between
            align-items: center
            height: 20px            
            position: relative
            z-index: 2 
        
        .action-container marketplace-table 
            margin-top: -2rem
        
    
    .red-font
        color:#FF0000
        text-align: center      


    .plus-hide
    
        marketplace-form-repeater 
            .form-repeater--holder 
                .form-repeater__container 
                    border: 1px solid #E4E4E4one
                    width: 100%
                    margin:2px
                    border-style: dashed
                    padding: 1px
                    border-radius:6px
                    padding-top: 10px

                    .form-repeater__icon-holder
                        display: none !important

        marketplace-dynamic-form
            .form-row 
                width: 109%
                
               