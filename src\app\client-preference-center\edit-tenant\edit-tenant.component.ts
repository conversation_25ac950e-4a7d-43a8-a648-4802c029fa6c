import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastService } from 'src/app/_services/toast.service';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { AuthService } from 'src/app/_services/authentication.services';
import { NOTIFICATION_CONSTANT } from 'src/app/_constants/notification_constants';

@Component({
  selector: 'app-edit-tenant',
  templateUrl: './edit-tenant.component.html',
  styleUrls: ['./edit-tenant.component.css']
})
export class EditTenantComponent implements OnInit {
  public ViewTenantJson: any;
  TENANTS_JSON: any = "./assets/json/client-preference/add-edit-tenant-form.json";
  showLoader: boolean = false;
  enableForm: boolean = false;
  tenantId: number;
  tenantDataById: any;
  clientId: Number;
  clientCode: any;
  clientName: string;
  userProfile: any;
  @Output() DataEvent = new EventEmitter<string>();
  public popupDisplayStyle: any = 'none';
  currentTenantValues: any = {};
  isSubmitEnabled: boolean = false;
  constructor(private clientApiService: ClientApiService, private route: ActivatedRoute, public authService: AuthService, private notificationService: ToastService) {
    this.clientId = Number(this.route.snapshot.paramMap.get('clientId'));
    this.clientName = this.route.snapshot.paramMap.get('clientName');
  }

  ngOnInit(): void {
    this.userProfile = sessionStorage.getItem('userId');
    this.clientApiService.getTenantsAssetsJson(this.TENANTS_JSON).subscribe((jsonData) => {
      this.ViewTenantJson = jsonData;
      this.getFormdetails();
    })

    this.tenantId = this.clientApiService.selectedTenantId;

    this.clientApiService.getAllClientsMasterData().subscribe((data) => {
      this.clientCode = data.find((item) => item.clientId === this.clientId)?.clientCode;
    })
  }

  /**
   *  method takes back to preference list
  */
  backToListPage(): void {
    this.DataEvent.emit('back to list');
  }

  /**
   *  method to get the form Details
  */
  getFormdetails(): void {
    this.clientApiService.getTenantDetailsById(this.tenantId).subscribe((data) => {
      this.tenantDataById = data['body'];
      if (data) {
        this.ViewTenantJson.forEach((x) => {
          switch (x.name) {
            case 'tenantId':
              x.name == 'tenantId' ? x.selectedVal = data['body']?.tenantId : x.selectedVal = null;
              x.name == 'tenantId' ? x.value = data['body']?.tenantId : x.value = null
              break;

            case 'tenantName':
              x.name == 'tenantName' ? x.selectedVal = data['body']?.tenantName : x.selectedVal = null;
              x.name == 'tenantName' ? x.value = data['body']?.tenantName : x.value = null
              break;

            case 'tenantCode':
              x.name == 'tenantCode' ? x.selectedVal = data['body']?.tenantCode : x.selectedVal = null;
              x.name == 'tenantCode' ? x.value = data['body']?.tenantCode : x.value = null
              break;

            case 'tenantDesc':
              x.name == 'tenantDesc' ? x.selectedVal = data['body']?.tenantDesc : x.selectedVal = null;
              x.name == 'tenantDesc' ? x.value = data['body']?.tenantDesc : x.value = null
              break;

            case 'offshoreAccess':
              x.name == 'offshoreAccess' ? x.selectedVal = data['body']?.offshoreAccessibleIndicator : x.selectedVal = null;
              x.name == 'offshoreAccess' ? x.value = data['body']?.offshoreAccessibleIndicator : x.value = null
              break;

            case 'active':
              x.name == 'active' ? x.selectedVal = data['body']?.active : x.selectedVal = null;
              x.name == 'active' ? x.value = data['body']?.active : x.value = null
              break;

            case 'offshoreAccLogic':
              x.name == 'offshoreAccLogic' ? x.selectedVal = data['body']?.offshoreAccessibleIndicatorLogicText : x.selectedVal = null;
              x.name == 'offshoreAccLogic' ? x.value = data['body']?.offshoreAccessibleIndicatorLogicText : x.value = null
              break;

            case 'SecuLvlCd':
              x.name == 'SecuLvlCd' ? x.selectedVal = data['body']?.securityLevelCode : x.selectedVal = null;
              x.name == 'SecuLvlCd' ? x.value = data['body']?.securityLevelCode : x.value = null
              break;

            default:
              break;
          }
        })
        this.enableForm = true;
      }
    })
  }

  /**
   * Method to take latest values of tenant form
   */
  getPreviousCurrentValues(event: any): void {
    if (!event.current.tenantCode || !event.current.tenantName || !event.current.tenantId || (event.current.tenantDesc && event.current.tenantDesc.length > 250) || (event.current.SecuLvlCd && event.current.SecuLvlCd.length > 10) || (event.current.tenantCode && event.current.tenantCode.length > 50) || (event.current.tenantName && event.current.tenantName.length > 100)) {
      this.isSubmitEnabled = false;
    } else {
      this.isSubmitEnabled = true;
      this.currentTenantValues = event.current;
    }
  }

  /**
   * Generic method for showing notification message as per params
   * @param header - string, to show notification pop-up header
   * @param body - string, to show notification pop-up body
   */
  notificationPopUpMsg(header: string, body: string) {
    if (header === NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER.toString()) {
      this.notificationService.setSuccessNotification({
        notificationHeader: header,
        notificationBody: body,
      });
    }
    else {
      this.notificationService.setErrorNotification({
        notificationHeader: header,
        notificationBody: body,
      });
    }
  }

  /**
   *  Method to save the edited details
   */
  savePreference(): void {

    let payloadForApi = {
      "tenantId": this.currentTenantValues.tenantId,
      "tenantCode": this.currentTenantValues.tenantCode,
      "clientId": this.clientId,
      "clientCode": this.clientCode,
      "offshoreAccessibleIndicator": this.currentTenantValues.offshoreAccess,
      "offshoreAccessibleIndicatorLogicText": this.currentTenantValues.offshoreAccLogic,
      "securityLevelCode": this.currentTenantValues.SecuLvlCd,
      "tenantName": this.currentTenantValues.tenantName,
      "tenantDesc": this.currentTenantValues.tenantDesc,
      "active": this.currentTenantValues.active,
      "lastUpdtUserId": this.userProfile.responseData.userId,
      "actionType": "Edit"
    }

    this.clientApiService.addEditTenantData(payloadForApi).subscribe((data) => {
      if (data['responseCode'] == 200) {
        this.notificationPopUpMsg(NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER, `Tenant Edited Successfully`);
        this.backToListPage();
      }
      else {
        this.notificationPopUpMsg(NOTIFICATION_CONSTANT.ERROR_NOTIFICATION_HEADER, data['responseData']);
      }
    },
      error => {
        this.notificationPopUpMsg(NOTIFICATION_CONSTANT.WARNING_NOTIFICATION_HEADER, error.responseData);
      });

  }

  /**
   *  Method closes the validation popup
   */
  closePopup(): void {
    this.popupDisplayStyle = 'none';
  }

}
