FROM quay-nonprod.elevancehealth.com/cognitiveplatform/node:18.19.0-alpine AS compile-image
###
WORKDIR /opt/ng
COPY . .
 
RUN npm install
 
ENV PATH="./node_modules/.bin:$PATH"

#ARG environment=develop
 
RUN ng build --configuration=$environment

FROM quay-nonprod.elevancehealth.com/dbgoptimizer/images/nginx:1.27.0-alpine
 
RUN chgrp -R 0 /var/cache/nginx && chmod -R g=u /var/cache/nginx
RUN chgrp -R 0 /usr/share/nginx/html && chmod -R g=u /usr/share/nginx/html
RUN chgrp -R 0 /var/run && chmod -R g=u /var/run
RUN chgrp -R 0 /etc/nginx && chmod -R g=u /etc/nginx
COPY build/nginx/nginx.conf /etc/nginx/nginx.conf
 
COPY --from=compile-image /opt/ng/dist/pi-pf-portal-ui/browser /usr/share/nginx/html
 
CMD ["nginx", "-g", "daemon off;"]
EXPOSE 8080
 
 
