<div class="breadcrumb-holder">
    <marketplace-breadcrumb [id]="'breadcrumb-role'" [dataset]="breadcrumbDataset" (onSelection)="selectedLink($event)">
    </marketplace-breadcrumb>
</div>
<div class="page-wrapper">
    <div class="page-header">
        <h3>Roles Management</h3>
    </div>
    <div [hidden]="!isRoleListSelected">
        <div class="btn-holder" [hidden]="!isCarelonAdmin">
            <marketplace-button [label]="'Add New Role'" [type]="'primary'" (onclick)="openModalPopup($event)">
            </marketplace-button>
        </div>
        <div class="content-holder">
            <div class="roles-table_container">
                <marketplace-table [id]="'cad-role-table'" [dataset]="roleDataset" [dropdownOptions]="roleKebab"
                    [columnDefinitions]="roleConfig" [isRowSelectable]="false" [isExcelExportNeeded]="false"
                    [isToggleColumnsNeeded]="false" (onDropdownOptionsClick)="onRoleAction($event)"
                    *ngIf="isRoleListDataReady">

                </marketplace-table>
            </div>
        </div>
    </div>
    <div [hidden]="isRoleListSelected">
        <div class="roles-audit-table_container">
            <marketplace-table [id]="'audit-roles-table'" [dataset]="roleDataset" [columnDefinitions]="roleAuditConfig"
                [isRowSelectable]="false" [isActionButtonsNeeded]='false' [redraw]="tabledraw"
                (onCellClick)="rendererTableClicked($event)">
            </marketplace-table>
        </div>
    </div>
</div>
<marketplace-popup [open]="openAddNewRoleModel" [size]="'xlarge'" (onClose)="closePopup()">
    <div mpui-modal-header>Add New Role</div>
    <div mpui-modal-body>
        <div class="skill-category">
            <div class="skill-category-form-box">
                <div class="first-form">
                    <marketplace-dynamic-form *ngIf="isRoleJSONReady" [formJSON]="roleFormJSON" [isSubmitNeeded]="false"
                        (onValueChanges)="_onRoleSelection($event)">
                    </marketplace-dynamic-form>
                </div>
                <div>
                    <div class="row">
                        <div class="col-sm-4">
                            <marketplace-dynamic-form *ngIf="isclientNameJsonReady" [formJSON]="clientNameJson"
                                [isSubmitNeeded]="false" (onValueChanges)="_onclientNamesSelection($event)">
                            </marketplace-dynamic-form>
                        </div>
                        <div class="col-sm-4">
                            <marketplace-dynamic-form *ngIf="isproductNameJsonReady" [formJSON]="productNameJson"
                                [isSubmitNeeded]="false" (onValueChanges)="_onproductNamesSelection($event)">
                            </marketplace-dynamic-form>
                        </div>
                        <div class="col-sm-4">
                            <marketplace-dynamic-form *ngIf="isBusinessDivisionJsonReady"
                                [formJSON]="businessDivisionJson" [isSubmitNeeded]="false"
                                (onValueChanges)="_onBusinessDivisionSelection($event)">
                            </marketplace-dynamic-form>
                        </div>
                    </div>
                    <div class="row col-sm-3">
                        <marketplace-dynamic-form *ngIf="isclientSiteJsonReady" [formJSON]="clientSiteJson"
                            [isSubmitNeeded]="false" (onValueChanges)="_onClientSiteChanged($event)">
                        </marketplace-dynamic-form>
                    </div>
                </div>
            </div>
            <div class="skill-category-switch-view-box">
                <div class="switch-view">
                    <marketplace-switch class="switch floating" [enabled]="false" [label]="'Standard'"
                        [preLabel]="'Pick List'" (onChange)="changeView($event)">
                    </marketplace-switch>
                </div>
            </div>
            <div class="table-container" *ngIf="isStandard">
                <marketplace-table *ngIf="isTableReady" [id]="'permission-table'" [dataset]="permissionsDS"
                    [columnDefinitions]="permissionsColumnConfig" [isRowSelectable]="false"
                    [isActionButtonsNeeded]='false' [(tableModel)]="accessScreensData"
                    (onCellClick)="onCellClick($event)">
                </marketplace-table>
            </div>
            <div class="picklist-table-container" *ngIf="!isStandard">
                <div class="picklist-box" *ngIf="isSkillsLoaded">
                    <marketplace-pick-list [dataset]="permissionListDataset" [leftTitle]="'Available Screens'"
                        [rightTitle]="'Selected Screens'" (onSelection)="_onListSelection($event)">
                    </marketplace-pick-list>
                </div>
                <div class="table-box">
                    <marketplace-checkbox *ngIf="isdataReadyToGivePermission" [groupLabel]="'Choose Permissions'"
                        [dataset]="dataset" (onSelection)="onCheckBoxSelection($event)" [type]="'vertical'"
                        ngDefaultControl>
                    </marketplace-checkbox>
                </div>
            </div>
        </div>
    </div>
    <div mpui-modal-footer>
        <marketplace-button [type]="'ghost'" [label]="'Close'" (onclick)="closePopup()">
        </marketplace-button>
    </div>
    <div mpui-modal-footer>
        <marketplace-button [label]="'Add Role'" (onclick)="validateRole()">
        </marketplace-button>
    </div>
</marketplace-popup>
<marketplace-popup [open]="openAuditRolesModel" [size]="'medium'">
    <div mpui-modal-header>Last Update {{currentRoleFormData['modified_date']}} by User ID:
        {{currentRoleFormData['modified_by']}}
        <label class="status-success"> {{currentRoleFormData['Status']}} </label>
    </div>
    <div mpui-modal-body>
        <div class="row">
            <div class="col-md-6 border-style">
                <div class="col-md mar-0">
                    <label for="name" class="label-title">Role Name: </label>
                    <span class="label-value">{{currentRoleFormData['roleName'] || 'NA'}}</span>
                </div>
                <div class="col-md mar-0">
                    <label for="name" class="label-title">Role Description: </label>
                    <span class="label-value">{{currentRoleFormData['description'] || 'NA'}}</span>
                </div>
                <div class="col-md mar-0">
                    <label for="name" class="label-title">Assign Product: </label>
                    <span class="label-value">{{currentRoleFormData['product'] || 'NA'}}</span>
                </div>
                <div class="col-md mar-0">
                    <label for="name" class="label-title">Business Division </label>
                    <span class="label-value">{{currentRoleFormData['businessDivision'] || 'NA'}}</span>
                </div>
                <div class="col-md mar-0">
                    <label for="name" class="label-title">client: </label>
                    <span class="label-value">{{currentRoleFormData['client'] || 'NA'}}</span>
                </div>
                <div class="col-md mar-0">
                    <label for="description" class="label-title">Remainder Date: </label>
                    <span class="label-value">{{currentRoleFormData['reminderDate'] || 'NA'}}</span>
                </div>
                <div class="col-md mar-0">
                    <label for="description" class="label-title">Selected Team: </label>
                    <span class="label-value">{{currentRoleFormData['teamSelection'] || 'NA'}}</span>
                </div>
                <div class="col-md mar-0">
                    <label for="description" class="label-title">Role Status: </label>
                    <span class="label-value">{{currentRoleFormData['Status'] || 'NA'}}</span>
                </div>
            </div>
            <div class="col-md-6 border-style">
                <div class="col-md mar-0">
                    <label for="description" class="label-title">Screens: </label>
                    <span class="label-value">{{currentRoleFormData['screens'] || 'NA'}}</span>
                </div>
                <div class="col-md mar-0">
                    <label for="name" class="label-title">Modified By: </label>
                    <span class="label-value">{{currentRoleFormData['modified_by'] || 'NA'}}</span>
                </div>
                <div class="col-md mar-0">
                    <label for="name" class="label-title">Modified Date: </label>
                    <span class="label-value">{{currentRoleFormData['modified_Date'] || 'NA'}}</span>
                </div>
                <div class="col-md mar-0">
                    <label for="name" class="label-title">Client Site </label>
                    <span class="label-value">{{currentRoleFormData['clientSite'] || 'NA'}}</span>
                </div>
            </div>
        </div>
    </div>
</marketplace-popup>

<marketplace-popup [open]="createErrorOpenPopup" [size]="'small'" (onClose)="closeMandatoryFieldPopup()">
    <div mpui-modal-header>
        <div class="modal-header-custom">
            <h4 class="modal-title custom-title">Attention !</h4>
        </div>
    </div>
    <div mpui-modal-body>
        <p class="pad-35">{{errorMessage}}</p>
    </div>
    <div mpui-modal-footer>
        <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closeMandatoryFieldPopup()">
        </marketplace-button>
    </div>
</marketplace-popup>