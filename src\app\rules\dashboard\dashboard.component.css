app-dashboard .rule-table{
    margin-top: 30px;
}
app-dashboard .container, app-dashboard .container-fluid, app-dashboard .container-lg, app-dashboard .container-md, app-dashboard .container-sm, app-dashboard .container-xl{
    width: 100%;
    padding-right: 5px; 
    padding-left: 5px;
    margin-right: auto;
    margin-left: auto;
}
app-dashboard .table-title {
    float: left;
    width: 200px;
    height: 34px;
    left: 195px;
    top: 384px;
    
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 34px;
    color: #000000;
    padding: 25px 0px 0px 30px;
}

app-dashboard .breadcrumb-container{
    margin-top: -32px;
}
app-dashboard .btn-span {
    float: right;
    margin-right: 16px;
    margin-top: 12px;
}
app-dashboard .btn-ruleadd{
    background: #5009B5;
    
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    color: #FFFFFF;
}
app-dashboard .add-new-rules-link{
    color: #FFFFFF;
    text-decoration: none;
}
app-dashboard .quickaction-title {
    float: left;
    width: 200px;
    height: 24px;
    left: 195px;
    top: 148px;
    
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 24px;
    color: #2453A6;
    padding: 25px 0px 20px 30px;
}
app-dashboard .mb-3{
    margin-top: 1rem;
    margin-left: 1rem;
}
app-dashboard .tp-bt-rem-1{
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: none;
    border-top: 3px solid rgba(5,5,6,0.125);
    border-radius: 0;
}
app-dashboard .dashbord-card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 2rem 1rem 2rem 1rem;
}
app-dashboard .fa-caret-right{
    font-size: 31px;
    color: #5009B5;
    float: right;
    padding-right: 20px;
}
app-dashboard .card-title{
    
    font-style: normal;
    font-weight: normal;
    font-size: 18px;
    line-height: 17px;
    color: #161616;
}
app-dashboard .setup-rule{
    margin-top: 5px;
}

app-dashboard .fa-list, app-dashboard .fa-chevron-circle-left{
    font-size: 30px;
    color: #5009B5;
}
app-dashboard .fa-plus:before{
    color: #FFFFFF;
}
app-dashboard .pad-1p{
    padding: 0%;
    margin-left: 5px;
}


/* ==============================Rules Dashboard start================================================== */
/*Rules dashboard Table col button*/
app-dashboard .btn-active{
    /* background:#D9F5F5; */
    width: 100%;
    background: #D9F5F5;
    border: 1px solid #00BBBA;
}
app-dashboard .btn-review-date-active {
    /* background:#D9F5F5; */
    width: 100%;
    background: #D9F5F5;
    border: 1px solid #00BBBA;
}
app-dashboard .btn-execute {
    background:#5009B5;
    width: 100%;
}
app-dashboard .btn-inactive, app-dashboard .btn-expired {
    width: 100%;
    background: #F5F5F5;
    border: 1px solid#231E33;
}
app-dashboard .btn-review-date-expired {
    background: #F5F5F5;
    width: 100%;
    border: 1px solid #231E33;
}
app-dashboard .btn-draft {
    background: #E1EDFF;
    width: 100%;
    border: 1px solid #44B8F3;
}
app-dashboard .btn-about-expire {
    background: #E1EDFF;
    width: 100%;
    border: 1px solid #44B8F3;
}
app-dashboard .btn-onhold {
    background: #EBE4FF;
    width: 100%;
    border: 1px solid #794CFF;
}
/*Rules dashboard Table action menu dropdown*/
app-dashboard .dropdown {
    position: absolute;
    background-color: gray;
    padding: 5px;
    outline: none;
    opacity: 0;
    /* min-width: 160px; */
    min-width: 100%;
    overflow: auto;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    background: #ffffff;
    box-shadow: 0px 0px 20px rgb(0 0 0 / 40%);
    border-radius: 4px;
    right: 10px;
}
app-dashboard .dropdown a {
    color: black;
    padding: 5px 10px;
    text-decoration: none;
    display: block;
}
app-dashboard input:not(:checked) ~ .dropdown {
    display: none;
}
app-dashboard input:checked ~ .dropdown {
    opacity: 1;
    z-index: 100;
    transition: opacity 0.2s;
    z-index: 1;
}
app-dashboard .three-dots:after {
    cursor: pointer;
    color: #444;
    content: "\2807";
    font-size: 20px;
    z-index: 999;
}
app-dashboard .table-action-menu .fa-eye, app-dashboard .table-action-menu .fa-edit, app-dashboard .table-action-menu .fa-trash {
    font-size: 20px;
    color: #5009B5;
    padding-right: 15px;
}
app-dashboard .table-action-menu {
    border: 8.5px solid #ffffff;
}
/* table overflow css overwrite*/
app-dashboard .btn.rule-dashboard {
    /* padding: 0.075rem 0.45rem !important; */
    color: #000000;
    font-weight:350;
}
app-dashboard .btn.focus.rule-dashboard, app-dashboard .btn:focus.rule-dashboard {
    outline: 0;
    box-shadow: 0 0 0 0rem;
}
app-dashboard .btn.rule-dashboard-big {
    padding: 0.075rem 0.1rem !important;
    color: white;
    font-weight:200;
}
app-dashboard .btn.focus.rule-dashboard-big, app-dashboard .btn:focus.rule-dashboard-big {
    outline: 0;
    box-shadow: 0 0 0 0rem;
}
app-dashboard .dropdown-container{
    padding-top: 5px;
}

app-dashboard .search-filter .operator.input-group-addon{
    display: none !important;
} 
/* app-dashboard css end */
/* ==============================Rules Dashboard end================================================== */
 app-dashboard marketplace-table .table-container .slickgrid-container .slick-column-name{
    font-size:.75rem!important;
  }
  
  app-dashboard marketplace-breadcrumb .breadcrumb__holder .breadcrumb__link {
    color: #5009B5 !important;
  }
  app-dashboard marketplace-table .table-container .kebab{
    padding: 0 8px !important;
  }
  app-dashboard .pad-tp-2p{
    padding-top: 2%;
}

app-dashboard .dashbord-title{
    padding: 5px 0px 0px 14px !important;
}

app-dashboard button:disabled,
button[disabled]{
  border: 1px solid #999999;
  background-color: #cccccc;
  color: #FFFFFF !important;
}
app-dashboard marketplace-cards .cards__holder .cards__wrapper .cards__container .cards__container-body-text {
    width: 62%;
}
app-dashboard marketplace-cards .cards__holder .cards__wrapper .cards__container .cards__container-icon{
    width: 38px;
}
app-dashboard .spinner-border {
    display: block;
    position: fixed;
    top: calc(50% - (58px / 2));
    right: calc(40% - (58px / 2));
    width: 5rem;
    height: 5rem;
}

app-dashboard .backdrop {
    position: fixed;
    /* top: 11%; */
    /* left: 20%; */
    width: 100vw;
    height: 100vh;
    z-index: 999;
    background-color: rgb(0, 0, 0, 0.2);
}

app-dashboard .btn-wrap-text {
    overflow: hidden;
    white-space: nowrap;
    display: inline-block;
    text-overflow: ellipsis;
}


app-dashboard marketplace-table .table-container .table-container-section .table-container__table_holder angular-slickgrid .gridPane .slickgrid-container .slick-pane .slick-viewport .grid-canvas .slick-row .slick-cell button {
    padding: 1px 8px;
}
