
  .container, app-system .container-fluid, app-system .container-lg, app-system .container-md, app-system .container-sm, app-system .container-xl{
    width: 100%;
    padding-right: 5px; 
    padding-left: 5px;
    margin-right: auto;
    margin-left: auto;
}

  .pd-30{
  padding-bottom: 30px;
}

  .dashbord-title {
  
  font-style: normal;
  font-weight: 900;
  font-size: 24px;
  line-height: 34px;
  color: #000000;
  padding: 0px;
  
}

   .fa-chevron-circle-left {
  font-size: 25px;
  color: #5009B5;
  margin-right: 5px;
}


  .cardqb {
  border: 3px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
}

  .cardqbdisabled {
    pointer-events: none;
  }
  
    .disabled {
    pointer-events: none;
  }

  .card  {
  border: 0px solid rgba(5,5,6,0.125);
  border-radius: 0.95rem;
}


  .dashbord-card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 2rem 1rem 2rem 1rem;
}


 .card-title{
  
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 17px;
  color: #161616;
}

  .pad-10{
  padding-left: 10px;
}

  .timebtn
{
  width: 193%;
  border: 1px solid #c4ceff;
  outline: none;
  border-radius: 5px;
  color: #818080;
  width: 173%;
  height: 36px;
  padding: 0.5rem;
}

  .col-6
{
  padding-left:102px;width: 53%;
}
  .pd-13
{
  padding-top: 8px;
}

  .pdg-13
{
  padding-top: 13px;
}

  .displayNone
{
    display: none;
}

  .label-title {
  color: #231e33 !important;
  font-family: 'elevance-semi-bold';
}

  .label-value {
  font-weight: 700;
  font-size: 16px;
  color: #666;
  word-break: break-word;
  font-family: 'elevance';

}
  .uimp-key-values {
  margin: 0.8rem 0;
}

  .pdL-50{
  padding-left:  50px !important;
}

.pointer{
  cursor: not-allowed;
  pointer-events: none;
}