{"config": {"switches": {"enableSorting": true, "enablePagination": true, "editable": false, "enableFiltering": false}, "colDefs": [{"name": "Recovery Status", "field": "status", "filterType": "Single Select", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 100}, {"name": "Claim Number", "field": "clmNbr", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 120}, {"name": "Claim Adjustment Key", "field": "clmAdjKey", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 300}, {"name": "Claim Line Number", "field": "clmLineNbr", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 100}, {"name": "Claim Disposition", "field": "clmDiposition", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 100}, {"name": "Adjudication Status", "field": "adjudicationStatus", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 100}, {"name": "Type of Bill", "field": "tobCd", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 80}, {"name": "Place of Service", "field": "placeOfServiceCd", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 80}, {"name": "Claim Type", "field": "clmTyp", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 80}, {"name": "Claim Line Start Date of Service", "field": "clmStmtFromDt", "filterType": "Calendar", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 150, "dateFormat": "MM/DD/YYYY"}, {"name": "Claim Line End Date of Service", "field": "clmStmtToDt", "filterType": "Calendar", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 150, "dateFormat": "MM/DD/YYYY"}, {"name": "Claim Allowance", "field": "alwdAmt", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 100}, {"name": "Paid Service Units", "field": "paidServcUnit", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 120}, {"name": "Diagnosis Code", "field": "diagCd", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 100}, {"name": "Revenue Code", "field": "revnuCd", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 120}, {"name": "CPT/HCPCS code", "field": "hcpcsCd", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 120}, {"name": "Modifiers 1", "field": "modifiers1", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 120}, {"name": "Modifiers 2", "field": "modifiers2", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 120}, {"name": "Rendering Provider NPI", "field": "rendrProvNPI", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 120}, {"name": "Provider Tax ID", "field": "provTaxId", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 110}, {"name": "Provider Status", "field": "provStatus", "filterType": "Text", "visible": "True", "editorType": null, "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "width": 110}]}, "dataset": [{"id": 1, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 2, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 3, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 4, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 5, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 6, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 7, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 8, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 9, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 10, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 11, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 12, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 13, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 14, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}, {"id": 15, "auditStatus": "Active", "clmNmbr": "A1B8R9", "clmAdjtmntKey": "89", "clmLnNmbr": "10", "clmDispostn": "True", "adjdictnStatus": "InActive", "typOfbill": "Paid", "plcOfService": "Texas", "clmType": "True", "clmLnStrtDtOfService": "9/23/2022", "clmLnEndDtOfService": "10/23/2022", "clmAlownce": "True", "pdServiceUnts": "True", "diagnsCd": "BLDRD", "revnueCd": "BLD152", "cptHcpcsCd": "1045", "modfers1": "True", "modfers2": "True", "rndrngPrvdrNPI": "True", "prvdrTxId": "ID968247", "prvdrStatus": "Active"}]}