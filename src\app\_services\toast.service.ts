import { Injectable } from '@angular/core';
import { NOTIFICATION_CONSTANT } from '../_constants/notification_constants';

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  notification: any = {};
  showAlert: boolean = false;

  constructor() { }

  /**
   * setNotification function to set notification and show it
   * @param notification 
   * parameter will hold object with these keys:
   * notificationOpen : true,
     notificationHeader : "Success",
     notificationBody : "File uploaded successfully",
     notificationType : 'success',
     notificationPosition : 'top-right',
     notificationDuration : 3000,
   */
  setNotification(notification) {
    this.showAlert = true;
    this.notification = notification;
    setTimeout(() => (this.clearNotification(), this.notification.notificationDuration));
  }

  /**
   * clearNotification function to clear notification
   */
  clearNotification() {
    this.notification = {};
    this.showAlert = false;
  }

  /**
   * setSuccessNotification function to set success notification and show it
   * mandatory
   * @param notificationBody 
   * optional
   * @param notificationHeader 
   * optional
   * @param notificationDuration
   * optional
   * @param notificationPosition
  */
  setSuccessNotification(notification: {notificationBody: string , notificationHeader?: string, notificationDuration?: number,  notificationPosition?: string}){
    this.showAlert = true;
    this.notification = {
      notificationOpen : true,
      notificationHeader : notification.notificationHeader? notification.notificationHeader : NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER,
      notificationBody : notification.notificationBody,
      notificationType : NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION,
      notificationPosition : notification.notificationPosition? notification.notificationPosition : NOTIFICATION_CONSTANT.NOTIFICATION_POSITION,
      notificationDuration : notification.notificationDuration? notification.notificationDuration : NOTIFICATION_CONSTANT.NOTIFICATION_DURATION,
    }
    setTimeout(() => (this.clearNotification(), this.notification.notificationDuration));
  }

  /**
   * setErrorNotification function to set error notification and show it
   * mandatory
   * @param notificationBody 
   * optional
   * @param notificationHeader 
   * optional
   * @param notificationDuration
   * optional
   * @param notificationPosition
  */
  setErrorNotification(notification: {notificationBody: string , notificationHeader?: string, notificationDuration?: number,  notificationPosition?: string}){
    this.showAlert = true;
    this.notification = {
      notificationOpen : true,
      notificationHeader : notification.notificationHeader? notification.notificationHeader : NOTIFICATION_CONSTANT.ERROR_NOTIFICATION_HEADER,
      notificationBody : notification.notificationBody,
      notificationType : NOTIFICATION_CONSTANT.ERROR_NOTIFICATION,
      notificationPosition : notification.notificationPosition? notification.notificationPosition : NOTIFICATION_CONSTANT.NOTIFICATION_POSITION,
      notificationDuration : notification.notificationDuration? notification.notificationDuration : NOTIFICATION_CONSTANT.NOTIFICATION_DURATION,
    }
    setTimeout(() => (this.clearNotification(), this.notification.notificationDuration));
  }

  /**
   * setWarningNotification function to set warning notification and show it
   * mandatory
   * @param notificationBody 
   * optional
   * @param notificationHeader 
   * optional
   * @param notificationDuration
   * optional
   * @param notificationPosition
  */
  setWarningNotification(notification: {notificationBody: string , notificationHeader?: string, notificationDuration?: number,  notificationPosition?: string}){
    this.showAlert = true;
    this.notification = {
      notificationOpen : true,
      notificationHeader : notification.notificationHeader? notification.notificationHeader : NOTIFICATION_CONSTANT.WARNING_NOTIFICATION_HEADER,
      notificationBody : notification.notificationBody,
      notificationType : NOTIFICATION_CONSTANT.WARNING_NOTIFICATION,
      notificationPosition : notification.notificationPosition? notification.notificationPosition : NOTIFICATION_CONSTANT.NOTIFICATION_POSITION,
      notificationDuration : notification.notificationDuration? notification.notificationDuration : NOTIFICATION_CONSTANT.NOTIFICATION_DURATION,
    }
    setTimeout(() => (this.clearNotification(), this.notification.notificationDuration));
  }

  /**
   * setInfoNotification function to set info notification and show it
   * mandatory
   * @param notificationBody 
   * optional
   * @param notificationHeader 
   * optional
   * @param notificationDuration
   * optional
   * @param notificationPosition
  */
  setInfoNotification(notification: {notificationBody: string , notificationHeader?: string, notificationDuration?: number,  notificationPosition?: string}){
    this.showAlert = true;
    this.notification = {
      notificationOpen : true,
      notificationHeader : notification.notificationHeader? notification.notificationHeader : NOTIFICATION_CONSTANT.INFO_NOTIFICATION_HEADER,
      notificationBody : notification.notificationBody,
      notificationType : NOTIFICATION_CONSTANT.INFO_NOTIFICATION,
      notificationPosition : notification.notificationPosition? notification.notificationPosition : NOTIFICATION_CONSTANT.NOTIFICATION_POSITION,
      notificationDuration : notification.notificationDuration? notification.notificationDuration : NOTIFICATION_CONSTANT.NOTIFICATION_DURATION,
    }
    setTimeout(() => (this.clearNotification(), this.notification.notificationDuration));
  }


  setDownloadNotification(notification: { notificationBody: string, notificationHeader?: string, notificationDuration?: number, notificationPosition?: string }) {
    this.showAlert = true;
    this.notification = {
      notificationOpen: true,
      notificationHeader: notification.notificationHeader ? notification.notificationHeader : NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER,
      notificationBody: notification.notificationBody,
      notificationType: NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION,
      notificationPosition: notification.notificationPosition ? notification.notificationPosition : NOTIFICATION_CONSTANT.NOTIFICATION_POSITION,
      notificationDuration: notification.notificationDuration ? notification.notificationDuration : NOTIFICATION_CONSTANT.DOWNLOAD_NOTIFICATION_DURATION,
    }
    setTimeout(() => (this.clearNotification(), 10));
  }
}
