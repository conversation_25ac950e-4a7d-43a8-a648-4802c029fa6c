import { TestBed } from '@angular/core/testing';
import { LoaderService } from './loader.service';
import { BehaviorSubject } from 'rxjs';

describe('LoaderService', () => {
  let service: LoaderService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [LoaderService]
    });

    service = TestBed.inject(LoaderService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should have an initial loading state of false', (done: DoneFn) => {
    service.isLoading$.subscribe(value => {
      expect(value).toBe(false);
      done();
    });
  });

  it('should set loading state to true when show is called', (done: DoneFn) => {
    service.show();
    service.isLoading$.subscribe(value => {
      expect(value).toBe(true);
      done();
    });
  });

  it('should set loading state to false when hide is called', (done: DoneFn) => {
    service.show(); // Set to true first
    service.hide();
    service.isLoading$.subscribe(value => {
      expect(value).toBe(false);
      done();
    });
  });

  describe('Additional Comprehensive Tests', () => {
    it('should handle multiple show calls correctly', () => {
      let emittedValue: boolean;
      service.isLoading$.subscribe(value => {
        emittedValue = value;
      });

      service.show();
      service.show();
      service.show();

      expect(emittedValue).toBe(true);
    });

    it('should handle multiple hide calls correctly', () => {
      let emittedValue: boolean;
      service.isLoading$.subscribe(value => {
        emittedValue = value;
      });

      service.hide();
      service.hide();
      service.hide();

      expect(emittedValue).toBe(false);
    });

    it('should toggle between show and hide correctly', () => {
      const emittedValues: boolean[] = [];
      service.isLoading$.subscribe(value => {
        emittedValues.push(value);
      });

      service.show();
      service.hide();
      service.show();
      service.hide();

      expect(emittedValues).toEqual([false, true, false, true, false]);
    });

    it('should notify multiple subscribers', () => {
      let subscriber1Value: boolean;
      let subscriber2Value: boolean;

      service.isLoading$.subscribe(value => {
        subscriber1Value = value;
      });

      service.isLoading$.subscribe(value => {
        subscriber2Value = value;
      });

      service.show();

      expect(subscriber1Value).toBe(true);
      expect(subscriber2Value).toBe(true);
    });

    it('should provide current state to new subscribers', () => {
      service.show();

      let newSubscriberValue: boolean;
      service.isLoading$.subscribe(value => {
        newSubscriberValue = value;
      });

      expect(newSubscriberValue).toBe(true);
    });

    it('should handle rapid show/hide sequence', () => {
      service.show();
      service.hide();
      service.show();

      let finalValue: boolean;
      service.isLoading$.subscribe(value => {
        finalValue = value;
      });

      expect(finalValue).toBe(true);
    });

    it('should work correctly after unsubscription', () => {
      const subscription = service.isLoading$.subscribe();
      subscription.unsubscribe();

      let newValue: boolean;
      service.isLoading$.subscribe(value => {
        newValue = value;
      });

      service.show();
      expect(newValue).toBe(true);
    });

    it('should maintain state consistency', () => {
      // Initial state
      expect(service.isLoading$.subscribe).toBeDefined();

      // Show state
      service.show();
      let showValue: boolean;
      service.isLoading$.subscribe(value => {
        showValue = value;
      });
      expect(showValue).toBe(true);

      // Hide state
      service.hide();
      let hideValue: boolean;
      service.isLoading$.subscribe(value => {
        hideValue = value;
      });
      expect(hideValue).toBe(false);
    });

    it('should handle edge case of hide without show', () => {
      service.hide();

      let value: boolean;
      service.isLoading$.subscribe(val => {
        value = val;
      });

      expect(value).toBe(false);
    });

    it('should be a BehaviorSubject that replays last value', () => {
      service.show();
      service.hide();

      let lateSubscriberValue: boolean;
      service.isLoading$.subscribe(value => {
        lateSubscriberValue = value;
      });

      expect(lateSubscriberValue).toBe(false);
    });

    it('should handle concurrent subscriptions correctly', () => {
      const values1: boolean[] = [];
      const values2: boolean[] = [];

      service.isLoading$.subscribe(value => values1.push(value));
      service.show();
      service.isLoading$.subscribe(value => values2.push(value));
      service.hide();

      expect(values1).toEqual([false, true, false]);
      expect(values2).toEqual([true, false]);
    });

    it('should maintain service singleton behavior', () => {
      const service2 = TestBed.inject(LoaderService);
      expect(service).toBe(service2);
    });
  });
});
