.pd-30{
    padding-bottom: 30px;
}
.dashbord-nav-icon{
    float: right;
    padding: 0px 20px 20px 30px;
}
.dashbord-title {
    float: left;
    /* width: 300px; */
    height: 41px;
    left: 195px;
    top: 49px;
    
    font-style: normal;
    font-weight: bold;
    font-size: 28px;
    line-height: 41px;
    color: #000000;
    padding: 5px 0px 0px 22px;
}
.input-placeholder{
    width: 330px;
    height: 40px;
    left: 833px;
    top: 48px;
    background: #FFFFFF;
    border: 1px solid #ADB7EA;
    box-sizing: border-box;
    border-radius: 8px;
}
.fa-search{
    padding-right: 15px;
}
input {
    border: 0;
    background: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    -ms-appearance: none;
    -o-appearance: none;
    appearance: none;
}
input:focus{
    outline: 0;
}
.input-placeholder{
    display: inline-block;
    padding: 10px;
}
.fa-user-circle, .fa-bell, .fa-stack-exchange, .fa-list, .fa-chevron-circle-left{
    font-size: 30px;
    color: #5009B5;
}
.fa-stack-exchange{
    padding-left: 50px;
    padding-right: 30px;
}
.fa-bell{
    padding-right: 30px;
}
.fa-search{
    padding-right: 15px;
}
.pd-10{
    padding-top: 10px;
}
marketplace-breadcrumb {
    margin-left: 5px;
  }