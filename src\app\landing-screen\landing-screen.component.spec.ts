import { TestBed, ComponentFixture, fakeAsync, tick } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { LandingComponent } from './landing-screen.component';
import { CookieService } from 'ngx-cookie-service';
import { MPUIJwtVerifierService } from "marketplace-jwt-verifier";
import { LoaderService } from '../_services/loader.service';
import { APP_CONSTANTS, LOB_CARD_NAME, QUICK_LINKS } from '../_constants/app.constants';
import { environment } from '../../environments/environment';

describe('LandingComponent', () => {
  let component: LandingComponent;
  let fixture: ComponentFixture<LandingComponent>;
  let cookieService: jasmine.SpyObj<CookieService>;
  let mpuiJwtVerifierService: jasmine.SpyObj<MPUIJwtVerifierService>;
  let loaderService: jasmine.SpyObj<LoaderService>;

  beforeEach(() => {
    const appServiceSpy = jasmine.createSpyObj('AppService', ['getLandingPageDetails']);
    const cookieServiceSpy = jasmine.createSpyObj('CookieService', ['get', 'set']);
    const mpuiJwtVerifierServiceSpy = jasmine.createSpyObj('MPUIJwtVerifierService', ['verifyToken']);
    const loaderServiceSpy = jasmine.createSpyObj('LoaderService', ['show', 'hide']);

    TestBed.configureTestingModule({
      imports: [LandingComponent, HttpClientTestingModule],
      providers: [
        { provide: CookieService, useValue: cookieServiceSpy },
        { provide: MPUIJwtVerifierService, useValue: mpuiJwtVerifierServiceSpy },
        { provide: LoaderService, useValue: loaderServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LandingComponent);
    component = fixture.componentInstance;
    cookieService = TestBed.inject(CookieService) as jasmine.SpyObj<CookieService>;
    mpuiJwtVerifierService = TestBed.inject(MPUIJwtVerifierService) as jasmine.SpyObj<MPUIJwtVerifierService>;
    loaderService = TestBed.inject(LoaderService) as jasmine.SpyObj<LoaderService>;
  });

  it('should create the landing component', () => {
    expect(component).toBeTruthy();
  });

  it('should call loadScreenDetails on ngOnInit', () => {
    spyOn(component, 'loadScreenDetails');
    component.ngOnInit();
    expect(component.loadScreenDetails).toHaveBeenCalled();
  });

  it('should load screen details and set properties correctly', fakeAsync(() => {
    const mockToken = {
      [APP_CONSTANTS.SCREEN_ACCESS_CARDS]: [{ label: 'Card1', name: 'Card Name 1', value: 'Value1', submenu: [] }],
      [APP_CONSTANTS.SCREEN_ACCESS_LINKS]: [{ label: 'Link1', value: 'Value1' }]
    };
    const mockUserToken = {
      [APP_CONSTANTS.FIRST_NAME]: 'John',
      [APP_CONSTANTS.LAST_NAME]: 'Doe'
    };

    spyOn(sessionStorage, 'getItem').and.returnValue('appToken');
    mpuiJwtVerifierService.verifyToken.and.returnValue(Promise.resolve(mockToken));
    mpuiJwtVerifierService.verifyToken.and.callFake((token) => {
      return Promise.resolve(token === 'appToken' ? mockToken : mockUserToken);
    });

    cookieService.get.and.callFake((key) => {
      return key === APP_CONSTANTS.USER_NAME ? '' : 'mockUserToken';
    });
    cookieService.set.and.callFake(() => {});

    component.loadScreenDetails();
    tick();

    expect(loaderService.show).toHaveBeenCalled();
    expect(loaderService.hide).toHaveBeenCalled();
    expect(component.listOfAccessibleCards).toEqual(mockToken[APP_CONSTANTS.SCREEN_ACCESS_CARDS]);
    expect(component.listOfAccessibleQuickLinks).toEqual(mockToken[APP_CONSTANTS.SCREEN_ACCESS_LINKS]);
    expect(cookieService.set).toHaveBeenCalledWith(APP_CONSTANTS.LANDING_SCREEN_DETAILS, jasmine.any(String));
    expect(component.userName).toEqual('John Doe');
    expect(cookieService.set).toHaveBeenCalledWith(APP_CONSTANTS.USER_NAME, 'John Doe');
  }));

  it('should navigate to card link on screen selection', () => {
    const cad_event = { selected: { label: LOB_CARD_NAME.DMS } };
    spyOn(window, 'open').and.returnValue({} as Window);

    component.onScreenSelection(cad_event);
    expect(window.open).toHaveBeenCalledWith(`https://ui.cad.pi.${environment.name}.gcpdns.internal.das`);

    const cob_event = { selected: { label: LOB_CARD_NAME.COB } };

    component.onScreenSelection(cob_event);
    expect(window.open).toHaveBeenCalledWith(`https://ui.cob.pi.${environment.name}.gcpdns.internal.das`);

    const siu_event = { selected: { label: LOB_CARD_NAME.SIU } };

    component.onScreenSelection(siu_event);
    expect(window.open).toHaveBeenCalledWith(`https://ui.siu.pi.${environment.name}.gcpdns.internal.das`);

    const cca_event = { selected: { label: LOB_CARD_NAME.CCA } };

    component.onScreenSelection(cca_event);
    expect(window.open).toHaveBeenCalledWith(`https://ui.cca.pi.${environment.name}.gcpdns.internal.das`);

    const portal_event = { selected: { label: 'Portal' } };

    component.onScreenSelection(portal_event);
    expect(window.open).toHaveBeenCalledWith(`https://ui.portal-card.pi.${environment.name}.gcpdns.internal.das`);
    
  });

  it('should navigate to quick link on quick link click', () => {
    const rules_event = { selected: { label: QUICK_LINKS.RULES } };
    spyOn(window, 'open').and.returnValue({} as Window);

    component.quickLinkClick(rules_event);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}rules`, "_self");

    const registration_event = { selected: { label: QUICK_LINKS.EXTERNAL_USER_REGISTRATION } };

    component.quickLinkClick(registration_event);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}registration`, "_self");


    const help_event = { selected: { label: QUICK_LINKS.HELP_CENTER } };

    component.quickLinkClick(help_event);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}help-center`, "_self");

    const settings_event = { selected: { label: QUICK_LINKS.SETTINGS } };

    component.quickLinkClick(settings_event);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}settings`, "_self");

    const client_event = { selected: { label: QUICK_LINKS.CLIENTS } };

    component.quickLinkClick(client_event);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}clients`, "_self");

    const user_event = { selected: { label: QUICK_LINKS.USERS } };

    component.quickLinkClick(user_event);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}users`, "_self");

    const default_event = { selected: { label: 'default' } };

    component.quickLinkClick(default_event);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}`, "_self");
  });
});