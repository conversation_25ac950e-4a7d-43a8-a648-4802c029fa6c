{"columnConfig": {"switches": {"enableSorting": true, "enablePagination": true, "editable": true, "enableFiltering": true}, "colDefs": [{"name": "User ID", "field": "userId", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "User Name", "field": "userName", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Manager Name", "field": "<PERSON><PERSON><PERSON>", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "User Type", "field": "userType", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Client Site", "field": "clientSite", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Experience Level", "field": "experienceLevel", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Status", "field": "registrationStatus", "filterType": "Text", "visible": "True", "id": "registrationStatus", "width": 120, "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Created By", "field": "created<PERSON>y", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Created Date", "width": 90, "field": "createdDate", "filterType": "Calendar", "visible": "True", "editorType": "", "editorTypeRoot": "", "editorTypeLabel": "", "dateFormat": "MM/DD/YYYY"}, {"name": "Updated By", "field": "updatedBy", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Updated Date", "width": 90, "field": "updatedDate", "filterType": "Calendar", "visible": "True", "editorType": "", "editorTypeRoot": "", "editorTypeLabel": "", "dateFormat": "MM/DD/YYYY"}, {"name": "", "field": "action", "filterType": "", "visible": "True", "editorType": "", "editorTypeRoot": "", "editorTypeLabel": "", "id": "approve", "sortable": false, "excludeFromExport": "true"}, {"name": "", "field": "action", "filterType": "", "visible": "True", "editorType": "", "editorTypeRoot": "", "sortable": false, "id": "reject", "editorTypeLabel": "", "excludeFromExport": "true"}]}, "dataset": [{"userId": "User_1", "userName": "<PERSON><PERSON>", "email_id": "<EMAIL>", "role_type": "Role Type 1", "Team": "Admin Team", "Status": "Active", "system": "ACR", "client": "Anthem", "role": "Role 2", "experienceLevel": "1", "product": null, "TeamName": "Team_1", "reminderDate": "10/11/2022", "managerId": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> (AH12345)", "blueCard": "Home ITS", "comments": "Admin team provides access to the selected users", "Market Skills": "Alabama", "Platform Skills": "FACETS", "Concept Skills": "C11-S284-101-A-X1", "Intake_Source": "Special Projects", "Funding_Model": "ASO", "lob_skills": "Medicare", "clientSite": "Offshore"}, {"userId": "User_2", "userName": "<PERSON><PERSON><PERSON>", "email_id": "<EMAIL>", "role_type": "Role Type 2", "Team": "Team 2", "Status": "Active", "system": "CCERT", "client": "Anthem,Carelon", "role": "Role 1", "experienceLevel": "2", "product": "Product 2", "TeamName": "Team_2", "reminderDate": "11/11/2022", "managerId": "<PERSON><PERSON><PERSON>, Sriram (AH2346)", "blueCard": "Home ITS", "comments": "Home ITS team provides access to the selected users", "clientSite": "Offshore"}, {"userId": "User_3", "userName": "Aroon", "email_id": "<EMAIL>", "role_type": "Role Type 3", "Team": "Super Admin", "Status": "Active", "system": "CCERT", "client": "Carelon", "role": "Role 3", "experienceLevel": "3", "product": "Product 1", "TeamName": "Team_3", "reminderDate": "12/11/2022", "managerId": "<PERSON><PERSON>, <PERSON>(AL45678)", "blueCard": "Host", "comments": "Super Admin team provides access to the selected users", "clientSite": "Offshore"}, {"userId": "User_4", "userName": "<PERSON><PERSON><PERSON>", "email_id": "<EMAIL>", "role_type": "Role Type 4", "Team": "Team 2", "Status": "Active", "system": "ACR", "client": "Anthem,Carelon", "role": "Role 1", "experienceLevel": "1", "product": null, "TeamName": "Team_1", "reminderDate": "20/12/2022", "managerId": "<PERSON><PERSON>, <PERSON>(AL23678)", "blueCard": "All", "comments": "Provides access to the selected users", "clientSite": "Offshore"}]}