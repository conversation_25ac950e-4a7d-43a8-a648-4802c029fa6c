<div class="breadcrumb-holder">
    <marketplace-breadcrumb [id]="'breadcrumb'" [dataset]="breadcrumbDataset" (onSelection)="selectedLink($event)">
    </marketplace-breadcrumb>
</div>
<div class="page-wrapper">
    <div class="page-header">
        <h3><a><i class="fa fa-chevron-circle-left backColor" aria-hidden="true" (click)="backToPreviousPage()"></i></a>
            {{pageHeadingTitle}}
        </h3>
    </div>
    <div class="form-container">
        <marketplace-stepper *ngIf="isSegmentStepperReady" [headerConfig]="userStepperConfig"
            [showFooterButtons]="showFooterButtons" [isRejectNeeded]="isRejectBtnVisible"
            [submitBtnLabel]="submitBtnLabel" (onStepChange)="stepperNext($event)" (onReject)="onRejectBtnclk()"
            (onSubmit)="onUserSubmit($event)">
            <marketplace-step>
                <div class="skill-category">
                    <marketplace-dynamic-form *ngIf="isUserJSONReady" [isSubmitted]="isFormSubmitted"
                        [formJSON]="userFormJSON" #userFormRef [isSubmitNeeded]="false"
                        (onValueChange)="_onUserSelection($event, 1)" (onValueChanges)="onClientSiteChange($event)">
                    </marketplace-dynamic-form>
                </div>
            </marketplace-step>
            <marketplace-step>
                <ng-container *ngIf="userConfigDataset?.length > 0">
                    <marketplace-form-repeater *ngIf="isFormRepeaterReady" [(formModel)]="userConfigFormValues"
                        [dataset]="userConfigDataset" #formRef [maxItems]="maxTargetSelection" [recreate]="redrawForms"
                        (onValueChange)="_onRolesFormValueChange($event)" (onAddOrDeleteRow)="onAddOrDeleteRow($event)">
                    </marketplace-form-repeater>
                </ng-container>
            </marketplace-step>
            <!-- <marketplace-step>
                <div class="row">
                    <div class="col-2">
                        <div class="d-flex flex-column">
                            <marketplace-select [label]="'Client'" [type]="'single'" [placeholder]="'Select Client'"
                                [(ngModel)]="selectedClientForSkillsId" [dataset]="distinctClients"
                                (onSelection)="_onClientSelectionForSkills($event)"
                                (onClear)="clearSelectionDropdown('all')">
                            </marketplace-select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="d-flex flex-column">
                            <marketplace-select [label]="'Product'" [type]="'single'" [placeholder]="'Select Product'"
                                [(ngModel)]="selectedPIProductForSkillsId" [dataset]="distinctProducts"
                                (onSelection)="_onPIProductSelectionForSkills($event)"
                                (onClear)="clearSelectionDropdown('product')" [disabled]="!selectedClientForSkillsId">
                            </marketplace-select>
                        </div>
                    </div>
                    <div class="col-12 skill-container" *ngIf="selectedPIProductForSkillsId">
                        <div>
                            <marketplace-dynamic-form *ngIf="isNewSkillsReady" [formJSON]="newSkillJSON"
                                [isSubmitNeeded]="false" (onValueChanges)="_onUserSkillSelection($event)">
                            </marketplace-dynamic-form>
                        </div>
                    </div>
                </div>
            </marketplace-step> -->
            <marketplace-step>
                <h4 class="header-blue">Preview User details</h4>
                <div class="row" *ngIf="userInfoPreviewDtls">
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">User ID </div>
                        <div class="label-value">{{userInfoPreviewDtls['userId'] || "NA"}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">User Name </div>
                        <div class="label-value">{{userInfoPreviewDtls['userNm'] || "NA"}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">User Experience Level </div>
                        <div class="label-value">{{userInfoPreviewDtls['experienceLevelId'] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">Assigned Manager </div>
                        <div class="label-value">{{userInfoPreviewDtls['managerName'] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">Reminder date </div>
                        <div class="label-value">{{userInfoPreviewDtls['reminderDate'] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">Comments </div>
                        <div class="label-value">{{userInfoPreviewDtls['comments'] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values" *ngIf="mode !== 'Preview'">
                        <div for="name" class="label-title">User Type </div>
                        <div class="label-value">{{userTypeOptions[userInfoPreviewDtls['internalFlag']] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">Client Site </div>
                        <div class="label-value">{{userInfoPreviewDtls['clientSite']}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">User Status</div>
                        <div class="label-value">{{userInfoPreviewDtls['status']}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values"
                        *ngIf="mode !== 'Preview' && userInfoPreviewDtls['status']==='Inactive' ">
                        <div for="name" class="label-title">Deactivation Status</div>
                        <div class="label-value">{{userInfoPreviewDtls['deactivationStatus']}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values"
                        *ngIf="mode !== 'Preview' && userInfoPreviewDtls['status']==='Inactive'">
                        <div for="name" class="label-title">Deactivation Reason</div>
                        <div class="label-value">{{userInfoPreviewDtls['deactivationReason']}}</div>
                    </div>
                    <div class="col-12" *ngIf="showPreviewRoleForm">
                        <label class="header-blue-client">User Role</label>
                        <div class="plus-hide">
                            <marketplace-form-repeater *ngIf="userConfigDataset.length > 0"
                                [dataset]="userConfigPreviewDataset" #formRef [maxItems]="maxTargetSelection"
                                [recreate]="redrawForms">
                            </marketplace-form-repeater>
                        </div>
                    </div>
                    <!-- <label class="header-blue">User Role</label>
                        <div class="col-12" disabled="disabled">
                            <marketplace-table 
                            [id]="'sample-table-1'"
                            [title]="'User Role'"
                            [dataset]="tableDataJSON" 
                            [columnDefinitions]="tableColumnConfig"
                            [isDraggableRowsNeeded]="false"
                            [isExcelExportNeeded] = "false"
                            [isToggleColumnsNeeded] = "false"></marketplace-table>
                        </div> -->
                    <!-- <div class="col-2">
                        <div class="d-flex flex-column">
                            <marketplace-select *ngIf="selectedClientForSkillsId" [label]="'Client'" [type]="'single'"
                                [placeholder]="'Select Client'" [(ngModel)]="selectedClientForSkillsId"
                                [dataset]="distinctClients" (onSelection)="_onClientSelectionForSkills($event)"
                                [disabled]="true">
                            </marketplace-select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="d-flex flex-column">
                            <marketplace-select *ngIf="selectedPIProductForSkillsId" [label]="'Product'"
                                [type]="'single'" [placeholder]="'Select Product'"
                                [(ngModel)]="selectedPIProductForSkillsId" [dataset]="distinctProducts"
                                (onSelection)="_onPIProductSelectionForSkills($event)" [disabled]="true">
                            </marketplace-select>
                        </div>
                    </div>
                    <div class="col-12" *ngIf="selectedPIProductForSkillsId">
                        <marketplace-dynamic-form *ngIf="previewSkillsJSON" [formJSON]="previewSkillsJSON"
                            [isSubmitNeeded]="false" (onValueChanges)="_onUserSkillSelection($event)">
                        </marketplace-dynamic-form>
                    </div> -->
                </div>
            </marketplace-step>
        </marketplace-stepper>
    </div>
</div>

<marketplace-popup [open]="openPIProductWarning" [size]="'small'">
    <div mpui-modal-header>
        <h3>Warning</h3>
    </div>
    <div mpui-modal-body><span style="text-align: center;">All altered data will be lost on changing this selection. Are
            you certain ? </span></div>
    <div mpui-modal-footer>
        <marketplace-button [label]="'No'" [type]="'ghost'" (onclick)="closeProductWarning()"></marketplace-button>
        <marketplace-button [label]="'Yes'" [type]="'primary'" (onclick)="updatePIProductSelection()">
        </marketplace-button>
    </div>
</marketplace-popup>
<marketplace-popup [open]="openExistingRolePopup" [size]="'small'">
    <div mpui-modal-header>
        <h3>Warning</h3>
    </div>
    <div mpui-modal-body><span style="text-align: center;"> These selections are already present. Please add on to
            existing selections.</span></div>
</marketplace-popup>
<marketplace-popup [open]="isRejectUserPopupReady" (onClose)="onRejectClose()" [size]="'medium'"
    [isFooterNeeded]="true">
    <div mpui-modal-header>Are you sure you want to reject this user?</div>
    <div mpui-modal-body>Please acknowledge that once rejected, this user cannot use or access this application.
    </div>
    <div mpui-modal-footer>
        <marketplace-button [label]="'Cancel'" [type]="'ghost'" (onclick)="onRejectClose()"></marketplace-button>
        <marketplace-button [label]="'Reject'" [type]="'primary'" (onclick)="onRejectUser($event)"></marketplace-button>
    </div>
</marketplace-popup>
<marketplace-popup [open]="isDeactivateUserPopupReady" (onClose)="onDeactivateClose()" [size]="'medium'"
    [isFooterNeeded]="true">
    <div mpui-modal-header>Are you sure you want to Deactivate this user?</div>
    <div mpui-modal-body>Please acknowledge that once deactivated, this user cannot use or access this application.
    </div>
    <div mpui-modal-footer>
        <marketplace-button [label]="'Cancel'" [type]="'ghost'" (onclick)="onDeactivateClose()"></marketplace-button>
        <marketplace-button [label]="'Deactivate'" [type]="'primary'" (onclick)="onDeactivateUser($event)">
        </marketplace-button>
    </div>
</marketplace-popup>