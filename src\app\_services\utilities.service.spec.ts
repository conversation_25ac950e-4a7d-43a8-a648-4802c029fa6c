import { TestBed } from '@angular/core/testing';
import { UtilitiesService } from './utilities.service';

describe('UtilitiesService', () => {
  let service: UtilitiesService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(UtilitiesService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('formatDate', () => {
    it('should format current date correctly', () => {
      const result = service.formatDate();
      const today = new Date();
      const expectedMonth = String(today.getMonth() + 1).padStart(2, '0');
      const expectedDay = String(today.getDate()).padStart(2, '0');
      const expectedYear = today.getFullYear();
      const expectedFormat = `${expectedMonth}-${expectedDay}-${expectedYear}`;
      
      expect(result).toBe(expectedFormat);
    });

    it('should set currentDate property', () => {
      service.formatDate();
      expect(service.currentDate).toBeDefined();
    });
  });

  describe('getDbgDateFormat', () => {
    it('should format valid date correctly', () => {
      const testDate = new Date('2023-12-25');
      const result = service.getDbgDateFormat(testDate);
      expect(result).toBe('12-25-2023');
    });

    it('should return empty string for undefined date', () => {
      const result = service.getDbgDateFormat(undefined);
      expect(result).toBe('');
    });

    it('should return empty string for null date', () => {
      const result = service.getDbgDateFormat(null);
      expect(result).toBe('');
    });

    it('should return empty string for empty string date', () => {
      const result = service.getDbgDateFormat('');
      expect(result).toBe('');
    });
  });

  describe('getHitRateDateFormat', () => {
    it('should format valid date correctly', () => {
      const testDate = new Date('2023-12-25');
      const result = service.getHitRateDateFormat(testDate);
      expect(result).toBe('12/25/2023');
    });

    it('should return empty string for undefined date', () => {
      const result = service.getHitRateDateFormat(undefined);
      expect(result).toBe('');
    });

    it('should return empty string for null date', () => {
      const result = service.getHitRateDateFormat(null);
      expect(result).toBe('');
    });

    it('should return empty string for empty string date', () => {
      const result = service.getHitRateDateFormat('');
      expect(result).toBe('');
    });
  });

  describe('getECPDateFormat', () => {
    it('should format valid date correctly', () => {
      const testDate = new Date('2023-12-25');
      const result = service.getECPDateFormat(testDate);
      expect(result).toBe('2023-12-25');
    });

    it('should return empty string for undefined date', () => {
      const result = service.getECPDateFormat(undefined);
      expect(result).toBe('');
    });

    it('should return empty string for null date', () => {
      const result = service.getECPDateFormat(null);
      expect(result).toBe('');
    });

    it('should return empty string for empty string date', () => {
      const result = service.getECPDateFormat('');
      expect(result).toBe('');
    });
  });

  describe('getFutureDate', () => {
    it('should add days to date correctly', () => {
      const baseDate = new Date('2023-12-25');
      const result = service.getFutureDate(baseDate, 5, 'MM-dd-yyyy');
      expect(result).toBe('12-30-2023');
    });

    it('should handle negative days (past dates)', () => {
      const baseDate = new Date('2023-12-25');
      const result = service.getFutureDate(baseDate, -5, 'MM-dd-yyyy');
      expect(result).toBe('12-20-2023');
    });

    it('should handle zero days', () => {
      const baseDate = new Date('2023-12-25');
      const result = service.getFutureDate(baseDate, 0, 'MM-dd-yyyy');
      expect(result).toBe('12-25-2023');
    });
  });

  describe('getFormatedFutureDate', () => {
    it('should format valid date correctly', () => {
      const testDate = new Date('2023-12-25');
      const result = service.getFormatedFutureDate(testDate, 'MM-dd-yyyy');
      expect(result).toBe('12-25-2023');
    });

    it('should return empty string for undefined date', () => {
      const result = service.getFormatedFutureDate(undefined, 'MM-dd-yyyy');
      expect(result).toBe('');
    });

    it('should return empty string for null date', () => {
      const result = service.getFormatedFutureDate(null, 'MM-dd-yyyy');
      expect(result).toBe('');
    });

    it('should return empty string for empty string date', () => {
      const result = service.getFormatedFutureDate('', 'MM-dd-yyyy');
      expect(result).toBe('');
    });

    it('should handle different date formats', () => {
      const testDate = new Date('2023-12-25');
      const result = service.getFormatedFutureDate(testDate, 'yyyy-MM-dd');
      expect(result).toBe('2023-12-25');
    });
  });

  describe('checkInputDatePastCurrentDt', () => {
    it('should return true for past dates', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);
      const result = service.checkInputDatePastCurrentDt(pastDate);
      expect(result).toBe(true);
    });

    it('should return false for future dates', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);
      const result = service.checkInputDatePastCurrentDt(futureDate);
      expect(result).toBe(false);
    });

    it('should return false for current date', () => {
      const currentDate = new Date();
      const result = service.checkInputDatePastCurrentDt(currentDate);
      expect(result).toBe(false);
    });
  });

  describe('checkActiveFlag', () => {
    it('should return true when current date is between start and end dates', () => {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 1);
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 1);
      
      const result = service.checkActiveFlag(startDate, endDate);
      expect(result).toBe(true);
    });

    it('should return false when current date is before start date', () => {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() + 1);
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 2);
      
      const result = service.checkActiveFlag(startDate, endDate);
      expect(result).toBe(false);
    });

    it('should return false when current date is after end date', () => {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 2);
      const endDate = new Date();
      endDate.setDate(endDate.getDate() - 1);
      
      const result = service.checkActiveFlag(startDate, endDate);
      expect(result).toBe(false);
    });

    it('should return true when current date equals start date', () => {
      const today = new Date();
      const startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

      const result = service.checkActiveFlag(startDate, endDate);
      expect(result).toBe(true);
    });

    it('should return true when current date equals end date', () => {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 1);
      const endDate = new Date();
      
      const result = service.checkActiveFlag(startDate, endDate);
      expect(result).toBe(true);
    });
  });

  describe('Service Properties', () => {
    it('should have today property defined', () => {
      expect(service.today).toBeDefined();
      expect(service.today instanceof Date).toBe(true);
    });

    it('should have dd property after formatDate is called', () => {
      service.formatDate();
      expect(service.dd).toBeDefined();
      expect(typeof service.dd).toBe('string');
    });

    it('should have mm property after formatDate is called', () => {
      service.formatDate();
      expect(service.mm).toBeDefined();
      expect(typeof service.mm).toBe('string');
    });

    it('should have yyyy property after formatDate is called', () => {
      service.formatDate();
      expect(service.yyyy).toBeDefined();
      expect(typeof service.yyyy).toBe('number');
    });

    it('should have currentDate property after formatDate is called', () => {
      service.formatDate();
      expect(service.currentDate).toBeDefined();
      expect(typeof service.currentDate).toBe('string');
    });
  });

  describe('checkDate', () => {
    it('should return true when start date is before end date', () => {
      const result = service.checkDate('01-01-2023', '12-31-2023');
      expect(result).toBe(true);
    });

    it('should return false when start date is after end date', () => {
      const result = service.checkDate('12-31-2023', '01-01-2023');
      expect(result).toBe(false);
    });

    it('should return false when start date equals end date', () => {
      const result = service.checkDate('01-01-2023', '01-01-2023');
      expect(result).toBe(false);
    });
  });

  describe('checkDateLatest', () => {
    it('should return true when start date is before end date', () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-12-31');
      const result = service.checkDateLatest(startDate, endDate);
      expect(result).toBe(true);
    });

    it('should return false when start date is after end date', () => {
      const startDate = new Date('2023-12-31');
      const endDate = new Date('2023-01-01');
      const result = service.checkDateLatest(startDate, endDate);
      expect(result).toBe(false);
    });

    it('should return false when start date equals end date', () => {
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-01');
      const result = service.checkDateLatest(startDate, endDate);
      expect(result).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('should handle leap year dates correctly', () => {
      const leapYearDate = new Date('2024-02-29');
      const result = service.getDbgDateFormat(leapYearDate);
      expect(result).toBe('02-29-2024');
    });

    it('should handle year boundary correctly', () => {
      const yearEndDate = new Date('2023-12-31');
      const result = service.getFutureDate(yearEndDate, 1, 'MM-dd-yyyy');
      expect(result).toBe('01-01-2024');
    });

    it('should handle month boundary correctly', () => {
      const monthEndDate = new Date('2023-01-31');
      const result = service.getFutureDate(monthEndDate, 1, 'MM-dd-yyyy');
      expect(result).toBe('02-01-2023');
    });

    it('should handle different date formats in getFormatedFutureDate', () => {
      const testDate = new Date('2023-12-25');
      const result1 = service.getFormatedFutureDate(testDate, 'dd/MM/yyyy');
      const result2 = service.getFormatedFutureDate(testDate, 'yyyy.MM.dd');
      expect(result1).toBe('25/12/2023');
      expect(result2).toBe('2023.12.25');
    });

    it('should handle all date format methods with same date', () => {
      const testDate = new Date('2023-12-25');
      const dbgFormat = service.getDbgDateFormat(testDate);
      const hitRateFormat = service.getHitRateDateFormat(testDate);
      const ecpFormat = service.getECPDateFormat(testDate);

      expect(dbgFormat).toBe('12-25-2023');
      expect(hitRateFormat).toBe('12/25/2023');
      expect(ecpFormat).toBe('2023-12-25');
    });
  });

  describe('Enhanced Utilities Tests', () => {
    describe('formatDate method comprehensive tests', () => {
      it('should format date with correct padding for single digit day and month', () => {
        // Mock a specific date
        const mockDate = new Date(2023, 0, 5); // January 5, 2023
        service.today = mockDate;

        const result = service.formatDate();

        expect(result).toBe('01-05-2023');
        expect(service.dd).toBe('05');
        expect(service.mm).toBe('01');
        expect(service.yyyy).toBe(2023);
      });

      it('should format date without padding for double digit day and month', () => {
        const mockDate = new Date(2023, 11, 25); // December 25, 2023
        service.today = mockDate;

        const result = service.formatDate();

        expect(result).toBe('12-25-2023');
        expect(service.dd).toBe('25');
        expect(service.mm).toBe('12');
        expect(service.yyyy).toBe(2023);
      });

      it('should handle leap year correctly', () => {
        const mockDate = new Date(2024, 1, 29); // February 29, 2024 (leap year)
        service.today = mockDate;

        const result = service.formatDate();

        expect(result).toBe('02-29-2024');
      });
    });

    describe('getDbgDateFormat edge cases', () => {
      it('should handle null input', () => {
        const result = service.getDbgDateFormat(null);
        expect(result).toBe('');
      });

      it('should handle undefined input', () => {
        const result = service.getDbgDateFormat(undefined);
        expect(result).toBe('');
      });

      it('should handle empty string input', () => {
        const result = service.getDbgDateFormat('');
        expect(result).toBe('');
      });

      it('should format valid date string', () => {
        const result = service.getDbgDateFormat('2023-12-25');
        expect(result).toBe('12-25-2023');
      });

      it('should format Date object', () => {
        const date = new Date(2023, 11, 25);
        const result = service.getDbgDateFormat(date);
        expect(result).toBe('12-25-2023');
      });
    });

    describe('getFutureDate comprehensive tests', () => {
      it('should add positive days correctly', () => {
        const baseDate = new Date(2023, 0, 15); // January 15, 2023
        const result = service.getFutureDate(baseDate, 10, 'MM-dd-yyyy');
        expect(result).toBe('01-25-2023');
      });

      it('should subtract days with negative input', () => {
        const baseDate = new Date(2023, 0, 15); // January 15, 2023
        const result = service.getFutureDate(baseDate, -5, 'MM-dd-yyyy');
        expect(result).toBe('01-10-2023');
      });

      it('should handle month boundary crossing', () => {
        const baseDate = new Date(2023, 0, 28); // January 28, 2023
        const result = service.getFutureDate(baseDate, 5, 'MM-dd-yyyy');
        expect(result).toBe('02-02-2023');
      });

      it('should handle year boundary crossing', () => {
        const baseDate = new Date(2023, 11, 30); // December 30, 2023
        const result = service.getFutureDate(baseDate, 5, 'MM-dd-yyyy');
        expect(result).toBe('01-04-2024');
      });

      it('should handle different date formats', () => {
        const baseDate = new Date(2023, 0, 15);
        const result = service.getFutureDate(baseDate, 1, 'yyyy-MM-dd');
        expect(result).toBe('2023-01-16');
      });
    });

    describe('getFormatedFutureDate edge cases', () => {
      it('should handle null date', () => {
        const result = service.getFormatedFutureDate(null, 'MM-dd-yyyy');
        expect(result).toBe('');
      });

      it('should handle undefined date', () => {
        const result = service.getFormatedFutureDate(undefined, 'MM-dd-yyyy');
        expect(result).toBe('');
      });

      it('should handle empty string date', () => {
        const result = service.getFormatedFutureDate('', 'MM-dd-yyyy');
        expect(result).toBe('');
      });

      it('should format valid date with custom format', () => {
        const date = new Date(2023, 11, 25);
        const result = service.getFormatedFutureDate(date, 'dd/MM/yyyy');
        expect(result).toBe('25/12/2023');
      });
    });

    describe('checkDate comprehensive tests', () => {
      it('should return true for same year different months', () => {
        const result = service.checkDate('01-15-2023', '06-15-2023');
        expect(result).toBe(true);
      });

      it('should return false for same dates', () => {
        const result = service.checkDate('01-15-2023', '01-15-2023');
        expect(result).toBe(false);
      });

      it('should handle different year scenarios', () => {
        const result = service.checkDate('12-31-2022', '01-01-2023');
        expect(result).toBe(true);
      });

      it('should return false when start is after end', () => {
        const result = service.checkDate('12-31-2023', '01-01-2023');
        expect(result).toBe(false);
      });
    });

    describe('checkDateLatest comprehensive tests', () => {
      it('should work with ISO date strings', () => {
        const start = '2023-01-15T00:00:00.000Z';
        const end = '2023-06-15T00:00:00.000Z';
        const result = service.checkDateLatest(start, end);
        expect(result).toBe(true);
      });

      it('should work with Date objects', () => {
        const start = new Date(2023, 0, 15);
        const end = new Date(2023, 5, 15);
        const result = service.checkDateLatest(start, end);
        expect(result).toBe(true);
      });

      it('should handle same dates', () => {
        const date = new Date(2023, 0, 15);
        const result = service.checkDateLatest(date, date);
        expect(result).toBe(false);
      });
    });
  });
});
