{"src": [{"id": "1", "rule_name": "Rule 1", "rule_type": "Lookback", "rule_sub_type": "<PERSON><PERSON>", "rule_level": "Client", "client_concept": "Anthem", "status": "Active", "owner": "<PERSON><PERSON>", "review_date": "Active", "start_date": "2022-02-02", "end_date": "2025-02-02", "action": true}, {"id": "2", "rule_name": "Rule 2", "rule_type": "Exclusion", "rule_sub_type": "<PERSON><PERSON>", "rule_level": "Globle", "client_concept": "C12", "status": "Inactive", "owner": "<PERSON><PERSON>", "review_date": "Expired", "start_date": "2022-02-02", "end_date": "2025-02-02", "action": true}, {"id": "3", "rule_name": "Rule 3", "rule_type": "Lookback", "rule_sub_type": "<PERSON><PERSON>", "rule_level": "Concept", "client_concept": "14-<PERSON><PERSON>", "status": "Active", "owner": "<PERSON><PERSON>", "review_date": "About to Expire", "start_date": "2022-02-02", "end_date": "2025-02-02", "action": true}, {"id": "4", "rule_name": "Rule 4", "rule_type": "No Recovery", "rule_sub_type": "<PERSON><PERSON>", "rule_level": "Client", "client_concept": "XYZ", "status": "Draft", "owner": "<PERSON><PERSON>", "review_date": "Expired", "start_date": "2022-02-02", "end_date": "2025-02-02", "action": true}, {"id": "5", "rule_name": "Rule 5", "rule_type": "Exclusion", "rule_sub_type": "<PERSON><PERSON>", "rule_level": "Globle", "client_concept": "D123", "status": "On Hold", "owner": "<PERSON><PERSON>", "review_date": "Expired", "start_date": "2022-02-02", "end_date": "2025-02-02", "action": true}]}