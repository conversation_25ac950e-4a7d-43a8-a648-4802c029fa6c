<div class="breadcrumb-holder">
    <marketplace-breadcrumb [id]="'breadcrumb'" [dataset]="breadcrumbDataset" (onSelection)="selectedLink($event)">
    </marketplace-breadcrumb>
</div>
<div class="page-wrapper">
    <div class="page-header">
        <h3>
            <a (click)="backToPreviousPage()">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="backColor" aria-hidden="true" focusable="false" style="vertical-align: middle;">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M17 3.34a10 10 0 0 1 5 8.66c0 5.523 -4.477 10 -10 10s-10 -4.477 -10 -10a10 10 0 0 1 15 -8.66m-3.293 4.953a1 1 0 0 0 -1.414 0l-3 3a1 1 0 0 0 0 1.414l3 3a1 1 0 0 0 1.414 0l.083 -.094a1 1 0 0 0 -.083 -1.32l-2.292 -2.293l2.292 -2.293a1 1 0 0 0 0 -1.414"/>
                </svg>
            </a>
            Edit Role
        </h3>
        
    </div>
    <div class="form-container">
        <div class="first-form">
            <marketplace-dynamic-form [formJSON]="editRoleFormJSON" [isSubmitNeeded]="false"
                (onValueChanges)="onRoleChange($event)">
            </marketplace-dynamic-form>
        </div>
        <div>
            <div class="row">
                <div class="col-sm-4">
                    <marketplace-dynamic-form [formJSON]="clientNameJson" [isSubmitNeeded]="false"
                        (onValueChanges)="_onclientNamesSelection($event)">
                    </marketplace-dynamic-form>
                </div>
                <div class="col-sm-4">
                    <marketplace-dynamic-form *ngIf="isproductNameJsonReady" [formJSON]="productNameJson"
                        [isSubmitNeeded]="false" (onValueChanges)="_onproductNamesSelection($event)">
                    </marketplace-dynamic-form>
                </div>
                <div class="col-sm-4">
                    <marketplace-dynamic-form *ngIf="isBusinessDivisionJsonReady" [formJSON]="businessDivisionJson"
                        [isSubmitNeeded]="false" (onValueChanges)="_onBusinessDivisionSelection($event)">
                    </marketplace-dynamic-form>
                </div>
            </div>
            <marketplace-dynamic-form [formJSON]="clientSiteJson" [isSubmitNeeded]="false"
                (onValueChanges)="_onClientSiteChanged($event)">
            </marketplace-dynamic-form>
        </div>
        <div class="table-container" *ngIf="isStandard">
            <marketplace-table *ngIf="isTableReady" [id]="'permission-table'" [dataset]="permissionsDS"
                [columnDefinitions]="permissionsColumnConfig" [isRowSelectable]="false" [isActionButtonsNeeded]='false'
                [(tableModel)]="accessScreensData" (onCellClick)="onCellClick($event)">
            </marketplace-table>
        </div>
    </div>
    <div class="button-container">
        <marketplace-button [type]="'ghost'" [label]="'Cancel'" (onclick)="onCancelEditRole()">
        </marketplace-button>
        <marketplace-button [label]="'Save'" [enabled]="isEditRoleButtonEnabled" (onclick)="updateRole()">
        </marketplace-button>
    </div>
</div>

<marketplace-popup [open]="createErrorOpenPopup" [size]="'small'" (onClose)="closeMandatoryFieldPopup()">
    <div mpui-modal-header>
        <div class="modal-header-custom">
            <h4 class="modal-title custom-title">Attention !</h4>
        </div>
    </div>
    <div mpui-modal-body>
        <p class="pad-35">{{errorMessage}}</p>
    </div>
    <div mpui-modal-footer>
        <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closeMandatoryFieldPopup()">
        </marketplace-button>
    </div>
</marketplace-popup>