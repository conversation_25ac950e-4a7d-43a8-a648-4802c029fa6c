<div class="fixed-nav bg-gray">
        <marketplace-breadcrumb [dataset]="breadcrumbDataset" (onSelection)="breadcrumSelection($event)">
        </marketplace-breadcrumb>
        <div class="table-container">
          <div class="table-title">{{productName}}</div>
          <div  class="btn-span">
            <marketplace-button [label]="'Add New Bundle'" [type]="'primary'" [name]="'primary'"  [enabled]="!isReadOnly"
            (onclick)="AddNewBundlesfun()">
          </marketplace-button>
        </div>
        <div class="card-body">
          
        <label>Total Entries: {{totalEntries}}</label>
          <div class="rule-table">
            <marketplace-table [id]="'bundlelist-table'" [dataset]="dataURL" *ngIf="dataURL" [rowHeight]="ruleDashbordTableRowhg"
              [headerRowHeight]="ruleDashbordTableHeaderhg" [columnDefinitions]="bundleTableColumnConfig"
              (onCellValueChange)="cellValueChanged($event)" (onCellClick)="cellClicked($event)"
              [isRowSelectable]="false" (onTableReady)="tableReady($event)" [dropdownOptions]="kebabOptions"
              (onDropdownOptionsClick)="onDropdownOptionsClick($event)">
            </marketplace-table>
          </div>
        </div>
      </div>
</div>