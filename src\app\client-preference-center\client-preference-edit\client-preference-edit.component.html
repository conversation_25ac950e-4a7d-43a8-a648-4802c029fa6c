<div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>
<div class="fixed-nav bg-gray">
  <div class="content-wrapper">
    <div class="container-fluid">
      <div class="card card-no-border">
        <div class="pd-30">
          <span class="dashbord-title"><a (click)="backToListPage()"><i class="fa fa-chevron-circle-left"></i></a> Edit
            Data Exchange</span>
        </div>

        <div class="row form-pad">
          <marketplace-dynamic-form *ngIf="enableForm" [formJSON]="dataExchangeJson" [isSubmitNeeded]="false" #formRef
            (onValueChange)="valuechange($event, true)" (onValueChanges)="getPreviousCurrentValues($event)">
          </marketplace-dynamic-form>
          <marketplace-dynamic-form *ngIf="!isFrequencyDaily" (onValueChange)="onTimepickerChange($event)"
            [formJSON]="dataExchangeTimeFormJson" [isSubmitNeeded]="false">
          </marketplace-dynamic-form>
          <marketplace-form-repeater class="form-repeater" *ngIf="isFrequencyDaily" [(formModel)]="userConfigFormValues"
            [dataset]="timeFrameDataset" [recreate]="redrawForms" [maxItems]="maxTargetSelection"
            (onValueChange)="formRepeaterValueChange()">
          </marketplace-form-repeater>
        </div>

        <div class="qb-mar-2" *ngIf="showCriteriaButton">
          <marketplace-button [label]="'Add Criteria'" [type]="'primary'" [name]="'primary'"
            (onclick)="constructQbConfigFields(selectedTemplate.invTypeName, productName, null, 'templateChange')">
          </marketplace-button>
        </div>
        <div class="qb-mar-2" *ngIf="isTemplateReady">
          <marketplace-query-builder *ngIf="!showLoader" [query]="dataExchangequery" [qbConfig]="dataExchangeconfig"
            (fieldValueChange)="onQueryBuilderFieldChange($event)" (addQBRule)="onQueryBuilderRuleSetAddition($event)"
            [headerText]="'Criteria'" [operators]="operators">
          </marketplace-query-builder>
        </div>

        <span [ngClass]="[isdisabled == true ? 'displayNone' : 'footer-btns pd-13']">
          <marketplace-button [label]="'Cancel'" [type]="'secondary'" [name]="'secondary'" (onclick)="backToListPage()">
          </marketplace-button>

          <marketplace-button [label]="'Submit'" [type]="'primary'" [name]="'primary'" (onclick)="UpdatePreference()"
            [enabled]="!enableFormButtton">
          </marketplace-button>

        </span>
      </div>
    </div>
  </div>
</div>
<div class="modal" tabindex="-1" role="dialog" [ngStyle]="{ display: popupDisplayStyle }">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-header-custom">
          <h4 class="modal-title custom-title">Attention !</h4>
        </div>
        <span class="btn-span"><a (click)="closePopup()"><i
              class="fa fa-times-circle-o fa-2x close-icon-color"></i></a></span>
      </div>
      <div class="modal-body custom-message">
        <p class="pad-30">Please fill all the fields</p>
      </div>
      <div class="modal-footer">
        <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closePopup()">
        </marketplace-button>

      </div>
    </div>
  </div>
</div>

<marketplace-notification [open]="notificationOpen" [header]="notificationHeader" [bodyText]="notificationBody"
  [type]="notificationType" [duration]="3000" [position]="'top-right'">
</marketplace-notification>