import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LogoutComponent } from './logout.component';
import { AuthService } from '../_services/authentication.services';
import { ActivatedRoute } from '@angular/router';
import { BehaviorSubject, of } from 'rxjs';

describe('LogoutComponent', () => {
  let component: LogoutComponent;
  let fixture: ComponentFixture<LogoutComponent>;
  let authServiceStub: Partial<AuthService>;
  let activatedRouteStub: any;

  beforeEach(async () => {
    authServiceStub = {
      isTimedout: false,
      isUnAuthorized: false,
      isDeactivated: false,
      isLogin: true
    };

    activatedRouteStub = {
      queryParams: new BehaviorSubject({ reason: 'timedout' })
    };

  

    await TestBed.configureTestingModule({
      imports: [LogoutComponent],
      providers: [
        { provide: AuthService, useValue: authServiceStub },
        { provide: ActivatedRoute, useValue: activatedRouteStub }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LogoutComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });


  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set the logout message and header text', () => {
    expect(component.logOutMessage).toBe('You have been logged out, Click below button to login again');
    expect(component.headerText).toBe('Logged Out');
  });

  it('should navigate to login page on loginClick', () => {
    spyOn(component, 'navigate');
    component.loginClick(new Event('click'));
    expect(component.navigate).toHaveBeenCalled();
  });

  it('should handle queryParams and set isTimedout accordingly', () => {
    const sessionTimeoutSpy = spyOn(sessionStorage, 'getItem').and.returnValue('true');

    component.ngOnInit();

    // Simulate condition where session timeout is false
    expect(component.isTimedout).toBeFalse();
    expect(sessionTimeoutSpy).toHaveBeenCalledWith('timedout');

    // Simulate condition where logout reason is 'timedout'
    activatedRouteStub.queryParams.next({ reason: 'logout' });
    fixture.detectChanges();
    component.ngOnInit();
    expect(component.isTimedout).toBeFalse();
  });

  it('should handle queryParams and set isTimedout accordingly', () => {
    const sessionTimeoutSpy = spyOn(sessionStorage, 'getItem').and.returnValue('false');
    const sessionTimeoutSetSpy = spyOn(sessionStorage, 'setItem');

    // Simulate condition where logout reason is 'timedout'
    activatedRouteStub.queryParams.next({ reason: 'logout' });
    fixture.detectChanges();
    component.ngOnInit();
    expect(component.isTimedout).toBeFalse();
    expect(sessionTimeoutSetSpy).toHaveBeenCalledWith('timedout', 'true');
  });

});
