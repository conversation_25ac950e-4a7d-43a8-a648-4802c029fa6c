{"switches": {"enableSorting": true, "enablePagination": true, "enableFiltering": true}, "colDefs": [{"name": "BUNDLE NAME", "width": 100, "field": "bundleName", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "BUNDLE DESCRIPTION", "width": 100, "field": "bundleDesc", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "STATUS", "width": 100, "field": "activeFlag", "filterType": "Multi Select", "visible": "True", "editorType": "", "editorTypeRoot": "", "editorTypeLabel": ""}, {"name": "START DATE", "width": 100, "field": "effStartDate", "visible": "True", "filterType": "Calendar", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "dateFormat": "MM/DD/YYYY"}, {"name": "END DATE", "width": 100, "field": "effEndDate", "visible": "True", "filterType": "Calendar", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "dateFormat": "MM/DD/YYYY"}]}