import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { catchError } from 'rxjs/operators';


@Injectable({ providedIn: 'root' })
export class FileService {

    constructor(
        private http: HttpClient
    ) { }

    /**
     * Fetching all Templates to list all templates in a table
     * @returns 
     */
    getAllTemplates(): Observable<any> {
        return this.http.get(environment.validatorSvc + '/invProxy/filetemplate/list');
    }

    /**
     * Fetching the Data for dropdowns in Template screens
     * @returns 
     */
    getAllReuiredFormDropDowns(): Observable<any> {
        return this.http.get(environment.validatorSvc + '/invProxy/filetemplate/formdata');
    }

    /**
     * Fetching Single Template Details
     * @param id 
     * @returns 
     */
    getSingleTemplateDetails(id: number): Observable<any> {
        return this.http.get(environment.validatorSvc + '/invProxy/filetemplate/detail/' + id);
    }

    /**
     * Checking template if its present in Client Preference Domian if yes cannot delete,
     * If not then we can delete the TEMPLATE 
     * @param fileTempID 
     * @returns 
     */
    checkBeforeDeletingFileTemplate(fileTempID): Observable<any> {
        return this.http.get(environment.clientPreferenceDomainUrl + `/clientPreference/getClntPrefNames/${fileTempID}`);
    }

    /**
     * Deleting existing Template 
     * @param id 
     * @returns 
     */
    getSingleTemplateDelete(id: number): Observable<any> {
        return this.http.delete(environment.validatorSvc + '/invProxy/filetemplate/delete/' + id);
    }

    /**
     * Creating new template with all given details 
     * @param tempDeatails 
     * @returns 
     */
    saveTemplateDetails(tempDeatails: any): Observable<any> {
        return this.http.post(environment.validatorSvc + '/invProxy/filetemplate/create', tempDeatails).pipe(
            catchError(err => of(err))
        );
    }

    /**
    * 
    * @returns All Inventory Types for Dropdown
    */
    getAllInventoryTypes() {
        return this.http.get(environment.validatorSvc + '/invProxy/system/formdata');
    }

    /**
     * Update the existing Template details  
     * @param tempDeatails 
     * @returns 
     */
    updateTemplateDetails(tempDeatails: any): Observable<any> {
        return this.http.put(environment.validatorSvc + '/invProxy/filetemplate/update', tempDeatails).pipe(
            catchError(err => of(err))
        );
    }

    /**
     * Fetch JSON content from URL
     * @param url 
     * @returns 
     */
    getJsonFromUrl(url): Observable<any> {
        return this.http.get(url);
    }

    /**
     * 
     * @returns all Product Names dropdowns
     */
    getAllProdNames() {
        return this.http.get(environment.productDomainUrl + '/getProdInvType');
    }

    /**
   * 
   * @returns all CffLinks Names dropdowns
   */
    getAllCFFLinks(selectedInvType: string, selectedProdName: string) {
        let url = decodeURI(environment.validatorSvc + '/invProxy/filetemplate/getcfffields?invType=' + selectedInvType + '&prodName=' + selectedProdName);
        return this.http.get(url);
    }
}