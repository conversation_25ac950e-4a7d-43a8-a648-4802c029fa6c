import { Component, OnInit, Input, Output, EventEmitter, ViewEncapsulation } from '@angular/core';
import { ExternalMFAService } from '../../_services/external-mfa.service';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { IExternalValidateOTP } from '../../_models/mfa/external.validate-otp';
import { externalAuthenticationConstants } from '../../_helpers/helpers.constants';
import { IExternalLoginThreat } from '../../_models/mfa/external-loginthreat';
import { IRequestContext } from '../../_models/external/external-request-context';
import { EXTERNALUSER } from '../../_models/external-user-constants';
import { ISearchUserFilter, IExternalUserSearch } from '../../_models/external/external-user-search';
import { IExternalSendOTP } from '../../_models/mfa/external-send-otp';
import { ExternalSOAService } from '../../_services/external-soa.service';
import { Router,RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { MPUIButtonModule } from 'marketplace-button';

const jsonPath = "./assets/json/external-signIn/external-signIn-form.json";
const otpFormName = "otpFormDetails";

@Component({
  selector: 'app-otp',
  standalone: true,
  templateUrl: './otp.component.html',
  styleUrls: ['./otp.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule, 
    FormsModule, 
    RouterModule, 
    HttpClientModule,
    MPUIDynamicFormModule,
    MPUIButtonModule
  ]
})
export class OtpComponent implements OnInit {
  otpFormJSON: any;
  otpValue: any;
  isLoading: boolean = false;
  isOtpEntered: boolean = false;
  pingRiskId: any;
  pingDeviceId: any;
  pingUserId: any;
  mfaToken: any;
  @Input() userId: string = "";
  @Input() email: string = "";
  loginThreatParameter: any = {} as IExternalLoginThreat;
  emailAddress: string = "";
  errorMsg: string = "";
  soaToken: any;
  @Output() isValidationSuccessful = new EventEmitter<any>();

  constructor(
    private router: Router,
    private userManagementSvc: UserManagementApiService,
    private mfaService: ExternalMFAService,
    private soaService: ExternalSOAService
  ) { }

  ngOnInit(): void {
    let _fetchPage = this.userManagementSvc.getAssetsJson(jsonPath);
    _fetchPage.subscribe(data => {
      this.otpFormJSON = data[1][otpFormName];
      console.log("Form JSON loaded: ", this.otpFormJSON);
    });
    this.loginThreat(this.loginThreatParameter);
  }

  /**
     * OTP Value Changes
     * @param event 
     */
  otpValueChanges(event: any) {
    event.current?.otp == '' ? this.isOtpEntered = false : this.isOtpEntered = true;
    this.otpValue = event.current.otp
  }

  /**
     * Validate OTP
     */
  validateOTP() {
    this.isLoading = true;
    let validateOTP: IExternalValidateOTP = {
      otp: this.otpValue?.trim(),
      duration: externalAuthenticationConstants.OTP_DURATION
    }
    if (!this.userId && this.email) {
      validateOTP.channel = externalAuthenticationConstants.EMAIL,
        validateOTP.contact = this.email
    }

    this.mfaService.validateOTP(validateOTP, this.userId, this.pingRiskId, this.pingDeviceId, this.pingUserId)
      .subscribe((data: any) => {
        if (!this.userId && this.email) {
          this.isValidationSuccessful.emit({ validated: true, message: data?.userId });
        } else {
          this.isValidationSuccessful.emit({ validated: true, message: "" });
        }
      }, (error: any) => {
        this.errorMsg = EXTERNALUSER.INVALID_OTP
        this.isLoading = false;
      });
  }

  /**
       * Login Threat Endpoint Check
       * @param loginThreat 
       */
  loginThreat(loginThreat: IExternalLoginThreat) {
    this.isLoading = true;
    if (!this.userId && this.email) {
      loginThreat.channel = externalAuthenticationConstants.EMAIL;
      loginThreat.contact = this.email;
    }
    this.mfaService.externalLoginThreatCall(loginThreat, this.userId).subscribe((loginThreatData: any) => {
      if (loginThreatData) {
        // navigate to home screen
        this.pingRiskId = loginThreatData.pingRiskId;
        this.pingDeviceId = loginThreatData.pingDeviceId;
        this.pingUserId = loginThreatData.pingUserId;
        this.isLoading = false;
        this.errorMsg = "Verify your identity by entering OTP sent to your email.";
      }
    }, error => {
      this.isLoading = false;
      this.isValidationSuccessful.emit({ validated: false, message: error.error.message });
    });
  }

  /**
    * This function is triggered when user clicks on re-send OTP in OTP screen     
    */
  onResendOtp() {
    this.errorMsg = "";
    this.isLoading = true;
    let sendOtp: IExternalSendOTP = {
      channel: externalAuthenticationConstants.EMAIL
    };
    if (!this.userId && this.email) { sendOtp.recoveryContact = this.email; }

    this.mfaService.sendOTP(sendOtp, this.userId).subscribe((otpData: any) => {
      this.pingDeviceId = otpData.pingDeviceId;
      this.pingUserId = otpData.pingUserId;
      this.isLoading = false;
    });
  }


}
