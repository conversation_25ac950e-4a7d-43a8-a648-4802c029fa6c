import { Component, ViewEncapsulation } from '@angular/core';
import { MPUIButtonModule } from 'marketplace-button';
import { AuthService } from '../_services/authentication.services';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-logout',
  standalone: true,
  imports: [MPUIButtonModule],
  templateUrl: './logout.component.html',
  styleUrls: ['./logout.component.sass'],
  encapsulation: ViewEncapsulation.None
})
export class LogoutComponent {
  logOutMessage = 'You have been logged out, Click below button to login again';
  headerText = 'Logged Out';
  isTimedout = this.authService.isTimedout;
  isUnAuthorized: boolean = this.authService.isUnAuthorized;
  isDeactivated: boolean = this.authService.isDeactivated;

  constructor(private authService: AuthService,
    public activatedRoute: ActivatedRoute) {
    this.authService.isLogin = false;
  }

  ngOnInit() {
    let logoutReason = '';
    this.activatedRoute.queryParams.subscribe(params => {
      logoutReason = params['reason'];

      let sessionTimeout = sessionStorage.getItem('timedout');
      if (sessionTimeout == 'true') {
        this.isTimedout = false;
        logoutReason = '';
      }

      if (logoutReason) {
        this.isTimedout = logoutReason === 'timedout';
        this.isUnAuthorized = logoutReason === 'unauthorized';

        if (this.isTimedout) {
          sessionStorage.setItem('timedout', 'true');
        }
      }
    });
  }

  /**
   * Login to Portal
   * @param event 
   */
  loginClick(event) {
    this.navigate();
  }

  navigate() {
    window.location.href = window.location.protocol + "//" + window.location.host;
  }
}
