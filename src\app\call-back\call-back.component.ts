import { CommonModule } from "@angular/common";
import { Component, ViewEncapsulation } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { decodeJwt } from "jose";
import { MPUINotificationModule } from "marketplace-notification";
import { CookieService } from "ngx-cookie-service";
import { APP_CONSTANTS, AUTH_CONFIG } from "../_constants/app.constants";
import { AuthService } from "../_services/authentication.services";
import { LoaderService } from "../_services/loader.service";
import { LoaderComponent } from "../loader/loader.component";
import { MPUIJwtVerifierService } from "marketplace-jwt-verifier";
import { environment } from "src/environments/environment";

@Component({
  selector: 'call-back',
  standalone: true,
  templateUrl: './call-back.component.html',
  styleUrls: ['./call-back.component.sass'],
  imports: [CommonModule, MPUINotificationModule, LoaderComponent],
  encapsulation: ViewEncapsulation.None
})

export class CallBackComponent {

  constructor(public activatedRoute: ActivatedRoute,
    public authService: AuthService,
    public router: Router,
    public cookie: CookieService,
    public jwtService: MPUIJwtVerifierService,
    public loaderService: LoaderService) {

  }

  ngOnInit() {
    let authCode = '';
    this.activatedRoute.queryParams.subscribe(params => {
      authCode = params[AUTH_CONFIG.CODE];
    });

    this.getToken(authCode);
  }

  notificationOpen: any;
  notificationHeader: string;
  notificationBody: string;

  /**
   * Get Token
   * @param authCode 
   */
  getToken(authCode: string) {
    let codeVerifier = localStorage.getItem(AUTH_CONFIG.CODE_VERIFIER);
    localStorage.removeItem(AUTH_CONFIG.CODE_VERIFIER);
    this.loaderService.show();
  
    if (codeVerifier) {
      this.authService.getToken(authCode, codeVerifier).subscribe({
        next: (tokenDetails: any) => {      
          this.authService.isLogin = true;
          sessionStorage.setItem('appToken', tokenDetails['appToken']);
          //For Deployment
          this.jwtService.verifyToken(this.cookie.get(AUTH_CONFIG.USER_TOKEN), environment.publicKey).then((userToken) => {
            // Store user ID in a cookie
            this.cookie.set(AUTH_CONFIG.SUB, userToken[AUTH_CONFIG.SUB]);
  
            // Set additional user information in cookies
            this.cookie.set(APP_CONSTANTS.USER_NAME, `${userToken[APP_CONSTANTS.FIRST_NAME]} ${userToken[APP_CONSTANTS.LAST_NAME]}`);
            this.cookie.set(APP_CONSTANTS.PORTAL_ACCESS, APP_CONSTANTS.TRUE);
            this.router.navigate(['']);
          }).catch(error =>{
            this.authService.clearSessions();
          });;
  
          //For Local Testing
          // this.jwtService.verifyToken(tokenDetails[AUTH_CONFIG.APP_TOKEN], environment.publicKey).then((appToken) => {
          //   this.cookie.set(APP_CONSTANTS.USER_NAME, appToken[AUTH_CONFIG.SUB]);
          //   this.cookie.set(APP_CONSTANTS.PORTAL_ACCESS, APP_CONSTANTS.TRUE);           
          // }).catch(error =>{
          //   this.authService.clearSessions();
          // });;

          //Call to get CAD DBG Token
          this.authService.piAuthorize().subscribe((tokenDetails) => {
            //Storing the CAD DBG Token
            this.loaderService.hide();
            sessionStorage.setItem('cadDbgToken', tokenDetails['cadDbgToken']);
            let cadToken = decodeJwt(tokenDetails['cadDbgToken']);
            let roleDetails = {
              roleId: <string>cadToken['priorityRoleId'],
              roleName: <string>cadToken['priorityRoleName']
            }
            sessionStorage.setItem('roleDetails', JSON.stringify(roleDetails));
            sessionStorage.setItem('isInternalUser', <string>cadToken['internalFlag'])
            this.router.navigate(['']);
          })
        }, error: (error) => {
          this.notificationHeader = APP_CONSTANTS.ERROR;
          this.notificationBody = `${error.error.error.errorCode} - ${error.error.error.errorMessage}`;
          this.notificationOpen = Date.now();
        }
      });
    }
  }
  
}
