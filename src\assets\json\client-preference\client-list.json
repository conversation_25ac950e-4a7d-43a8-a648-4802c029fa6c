{"switches": {"enableSorting": true, "enablePagination": true, "enableFiltering": true}, "colDefs": [{"name": "CLIENT NAME", "width": 120, "field": "clientName", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "CLIENT TYPE", "width": 120, "field": "clientType", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "CLIENT ID", "width": 80, "field": "clientId", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "ACCOUNT NO", "width": 150, "field": "accountNumber", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "STATUS", "width": 90, "field": "status", "visible": "True", "editorType": "", "editorTypeRoot": "", "editorTypeLabel": ""}, {"name": "CLIENT SITE", "width": 110, "field": "clientSite", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "# of PRODUCTS", "width": 110, "field": "products", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "# of BUNDLES", "width": 110, "field": "bundles", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "# of FEE SCHEDULES", "width": 150, "field": "feeSchedule", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}]}