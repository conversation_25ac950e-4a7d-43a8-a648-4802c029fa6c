{"columnConfig": {"switches": {"enableSorting": true, "enablePagination": true, "editable": false, "enableFiltering": true}, "colDefs": [{"name": "Role ID", "field": "roleId", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Role Name", "field": "<PERSON><PERSON><PERSON>", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Client Name", "field": "clientName", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Business Division", "field": "businessDivision", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Product Name", "field": "prodName", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Status", "field": "activeFlag", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Modified By", "field": "lastUpdateUserId", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Modified Date", "field": "lastUpdtDtm", "filterType": "Calendar", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "dateFormat": "MM/DD/YYYY"}]}}