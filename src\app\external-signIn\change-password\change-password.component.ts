import { Component, HostListener, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router'; // Import RouterModule
import { AUTH_CONFIG, RegistrationConstants } from 'src/app/_constants/app.constants';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { externalAuthenticationConstants } from 'src/app/_helpers/helpers.constants';
import { ToastService } from 'src/app/_services/toast.service';
import { IExternalChangePassword } from 'src/app/_models/external/external-change-password';
import { IRequestContext } from 'src/app/_models/external/external-request-context';
import { ExternalSOAService } from 'src/app/_services/external-soa.service';
import { EXTERNALUSER } from 'src/app/_models/external/external-user-constants';
import { ROUTING_LABELS } from 'src/app/_constants/menu.constant';
import { AuthService } from 'src/app/_services/authentication.services';
import { MPUIModalDialogModule } from 'marketplace-popup';
import { CommonModule } from '@angular/common'; 
import { FormsModule } from '@angular/forms'; 
import { HttpClientModule } from '@angular/common/http'; 
import { MPUIDynamicFormModule } from 'marketplace-form'; 
import { MPUIButtonModule } from 'marketplace-button'; 
import { CookieService } from 'ngx-cookie-service';

@Component({
  selector: 'app-change-password',
  standalone: true, 
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [ 
    CommonModule,
    FormsModule,
    RouterModule,
    HttpClientModule,
    MPUIModalDialogModule,
    MPUIDynamicFormModule, 
    MPUIButtonModule 
  ]
})
export class ChangePasswordComponent implements OnInit {
  changePassFormJSON: any;
  errorMsg: string = '';
  isFormSubmitted: boolean = false;
  changePasswordForm: any = {};
  isLoading: boolean = false;
  // changePasswordForm: {
  //   currentPasscode: string;
  //   newPasscode: string;
  //   confirmPasscode: string;
  //   securityQuestion1: string;
  //   securityAnswer1: string;
  //   securityQuestion2: string;
  //   securityAnswer2: string;
  // } = {
  //   currentPasscode: '',
  //   newPasscode: '',
  //   confirmPasscode: '',
  //   securityQuestion1: '',
  //   securityAnswer1: '',
  //   securityQuestion2: '',
  //   securityAnswer2: ''
  // };
  isUserRoutedFromExtSignIn: string;
  formJsonWithoutSecurityQues: any = [];
  isFromSignIn: boolean = true;
  emptyFieldsErrorPopup: boolean = false;
  changePasswordPayload: IExternalChangePassword;
  changePasswordFormData: any;
  loggedInUserId: string;
  userDetailsPayload: any;
  securityQuestionPayload: any;
  userDnData: any;
  securityQuestionsJSON: any;
  securityQuestionsJSONPath: any = "./assets/json/external-signIn/securityQuestions.json";
  secretQuestion1: string;
  secretQuestion2: string;
  isSecurityQuestion: boolean = false;
  secretAnswer1: string;
  secretAnswer2: string;
  constructor(
    private userManagementSvc: UserManagementApiService,
    public alertService: ToastService,
    private activated_route: ActivatedRoute,
    private soaService: ExternalSOAService,
    private router: Router,
    private authService: AuthService,
    private cookieService: CookieService
  ) {
    this.isUserRoutedFromExtSignIn =
      this.activated_route.snapshot.queryParams.FromSignin;
  }

  ngOnInit(): void {
    let _fetchPage = this.userManagementSvc.getAssetsJson(
      externalAuthenticationConstants.jsonPath
    );
    _fetchPage.subscribe((data) => {
      this.formJsonWithoutSecurityQues = data[
        externalAuthenticationConstants.FORM_DETAILS
      ].slice(0, 3);
      if (this.isUserRoutedFromExtSignIn == externalAuthenticationConstants.YES) {
        this.changePassFormJSON = this.formJsonWithoutSecurityQues;
        /*this.soaService.searchExtUser(this.soaService.getIdFromDnDetails()).subscribe(response => {
          this.secretQuestion1 = response[RegistrationConstants.USER][0]?.secretQuestionAnswers[0]?.question
          this.secretQuestion2 = response[RegistrationConstants.USER][0]?.secretQuestionAnswers[1]?.question
          this.userDnData = response[RegistrationConstants.USER][0].dn;
          let _fetchPage = this.userManagementSvc.getAssetsJson(this.securityQuestionsJSONPath);
          _fetchPage.subscribe(data => {
            this.securityQuestionsJSON = data[ROUTING_LABELS.FORM_DETAILS];
            this.securityQuestionsJSON[0].selectedVal = this.secretQuestion1;
            this.securityQuestionsJSON[2].selectedVal = this.secretQuestion2;
            this.isSecurityQuestion = true;
            this.isLoading = false;
          });
        }) */
      }
      else {
        //this.soaService.getSOAAccessToken().subscribe(( responsedata: any) => {
        this.soaService.getListOfSecurityQuestions().subscribe(response => {
          let secretQuestJsonArray = [];
          response.secretQuestions.forEach(quest => {
            secretQuestJsonArray.push({ name: quest });
          });
          data.formDetails[3].options = secretQuestJsonArray;
          data.formDetails[5].options = secretQuestJsonArray;
          this.changePassFormJSON = data[externalAuthenticationConstants.FORM_DETAILS];
          this.isFromSignIn = false;
        }, (error: any) => {
          this.alertService.setErrorNotification({
            notificationHeader: externalAuthenticationConstants.ERROR,
            notificationBody: externalAuthenticationConstants.GET_SECURITY_QUESTION_ERRMSG
          });
        })
        //})
      }
    });
  }

  /**
   * This function is triggered when user clicks on ChangePassword button
   * @param event
   */
  onChangePasswordClick(): void {
    this.errorMsg = '';
    if (
      this.changePasswordForm?.passcode &&
      this.changePasswordForm?.newPasscode &&
      this.changePasswordForm?.confirmPasscode &&
      this.changePasswordForm?.securityQuestion1 &&
      this.changePasswordForm?.securityAnswer1 &&
      this.changePasswordForm?.securityQuestion2 &&
      this.changePasswordForm?.securityAnswer2
    ) {
      //// API call
      if (this.changePasswordForm.newPasscode != this.changePasswordForm.confirmPasscode) {
        this.errorMsg = externalAuthenticationConstants.messageNotMatchingPasswords;
      }
      else {
        this.changePassword();
      }
    } else { }
  }

  /**
   * This function is triggered when user clicks on ChangePassword button
   * @param event
   */
  changePasswordFromSignin(): void {
    this.errorMsg = '';

    if (
      this.changePasswordForm?.passcode &&
      this.changePasswordForm?.newPasscode &&
      this.changePasswordForm?.confirmPasscode
    ) {
      //// API call
      if (
        this.changePasswordForm.newPasscode !=
        this.changePasswordForm.confirmPasscode
      ) {
        this.errorMsg =
          externalAuthenticationConstants.messageNotMatchingPasswords;
      } else {
        this.changePassword(ROUTING_LABELS.CHANGE_PASS_MODE_NO_QUEST);
      }
    } else {

    }
  }

  /**
   * Method call on closing the popup for unfilled fields
   */
  closePopup(): void {
    this.emptyFieldsErrorPopup = false;
  }
  /**
   * This function is triggered when any change happens in field values
   * @param event
   */
  onChangePasswordFldsChange(event): void {
    this.changePasswordForm.passcode = event.current["passcode"];
    this.changePasswordForm.newPasscode = event.current["newPasscode"];
    this.changePasswordForm.confirmPasscode = event.current["confirmPasscode"];

    if (event.current.securityQuestion1)
      this.changePassFormJSON[5].options = this.changePassFormJSON[5].options.filter(question => question !== event.current.securityQuestion1);
    if (event.current.securityQuestion1 == null && event.previous.securityQuestion1)
      this.changePassFormJSON[5].options = [...this.changePassFormJSON[5].options, event.previous.securityQuestion1]
    if (event.current.securityQuestion2)
      this.changePassFormJSON[3].options = this.changePassFormJSON[3].options.filter(question => question !== event.current.securityQuestion2);
    if (event.current.securityQuestion2 == null && event.previous.securityQuestion2)
      this.changePassFormJSON[3].options = [...this.changePassFormJSON[3].options, event.previous.securityQuestion2]

    this.changePasswordForm.securityQuestion1 = event.current.securityQuestion1;
    this.changePasswordForm.securityAnswer1 = event.current.answer1;
    this.changePasswordForm.securityQuestion2 = event.current.securityQuestion2;
    this.changePasswordForm.securityAnswer2 = event.current.answer2;
    this.checkPasswordValidations(event);
  }

  /**
   * Method to check password validations and show error message in change password form
   * @param pwd
   * @param userId
   */
  errMesShownOnWrongCharInput(pwd: any, userID: any, index: number): void {
    let allowedCharacters = /^[a-zA-Z0-9!@#\$%\^\*\)\(._-]+$/;
    let regexMinMax =
      /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,20}$/;
    this.changePassFormJSON[index].customErrMsg = '';
    if (allowedCharacters.test(pwd)) {
      if (pwd == userID) {
        this.isFormSubmitted = true;
        this.changePassFormJSON[index].customErrMsg = "UserId and Password cannot be same";

      } else if (userID && pwd.indexOf(userID.substring(0, 3)) > -1) {
        this.isFormSubmitted = true;
        this.changePassFormJSON[index].customErrMsg = "Password contains 3 characters of user ID";

      } else if (pwd.match(/(.)\1{2,}/)) {
        this.isFormSubmitted = true;
        this.changePassFormJSON[index].customErrMsg = RegistrationConstants.PWD_CONSECUTIVE_ERRMSG;

      } else if (!regexMinMax.test(pwd)) {
        this.isFormSubmitted = true;
        this.changePassFormJSON[index].customErrMsg = "Password should be min 8 and max 20 with valid characters";
      }
    } else {
      this.isFormSubmitted = true;
      this.changePassFormJSON[index].customErrMsg = RegistrationConstants.INVALID_CHAR;
    }
  }

  /**
   * Method to check password validations
   * @param pwd
   * @param userId
   */
  checkPasswordValidations(event: any): any {
    let currentpasscode = event?.current?.passcode != event?.previous?.passcode;
    let newpasscode = event?.current?.newPasscode != event?.previous?.newPasscode;
    let confirmpasscode = event?.current?.confirmPasscode != event?.previous?.confirmPasscode;
    // Retrieve user ID from cookies
    let userId: any = this.cookieService.get(AUTH_CONFIG.SUB);

    switch (currentpasscode || newpasscode || confirmpasscode) {
      case currentpasscode:
        return this.errMesShownOnWrongCharInput(event?.current?.passcode, userId, 0);
      case newpasscode:
        return this.errMesShownOnWrongCharInput(event?.current?.newPasscode, userId, 1);
      case confirmpasscode:
        return this.errMesShownOnWrongCharInput(event?.current?.confirmPasscode, userId, 2);
      default:
    }
  }

  /**
   * Change Password
   */
  changePassword(request_mode?) {
    this.isLoading = true;
    this.loggedInUserId = this.soaService.getIdFromDnDetails();

    this.changePasswordPayload = {
      currentPassword: this.changePasswordForm.passcode,
      newPassword: this.changePasswordForm.newPasscode
    }
    this.soaService.getDnSubscription().subscribe(userDetails => {
      this.userDnData = userDetails.userDnData;
    })
    this.securityQuestionPayload = {
      identityInfo: {
        dn: this.userDnData
      },
      modifyAttributes: [
        {
          modifyAttributeEnum: externalAuthenticationConstants.SECRET_QUESTION2,
          values: [this.changePasswordForm.securityQuestion1],
          modifyTypeEnum: externalAuthenticationConstants.REPLACE
        },
        {
          modifyAttributeEnum: externalAuthenticationConstants.SECRET_ANSWER2,
          values: [this.changePasswordForm.securityAnswer1],
          modifyTypeEnum: externalAuthenticationConstants.REPLACE
        },
        {
          modifyAttributeEnum: externalAuthenticationConstants.SECRET_QUESTION3,
          values: [this.changePasswordForm.securityQuestion2],
          modifyTypeEnum: externalAuthenticationConstants.REPLACE
        },
        {
          modifyAttributeEnum: externalAuthenticationConstants.SECRET_ANSWER3,
          values: [this.changePasswordForm.securityAnswer2],
          modifyTypeEnum: externalAuthenticationConstants.REPLACE
        }
      ],
      newUser: false
    }
    this.soaService.changePassword(this.loggedInUserId, this.changePasswordPayload).subscribe((data: any) => {
      this.alertService.setSuccessNotification({
        notificationHeader: externalAuthenticationConstants.SUCCESS,
        notificationBody: externalAuthenticationConstants.SUCCESS_MESSAGE,
      });

      if (ROUTING_LABELS.CHANGE_PASS_MODE_NO_QUEST != request_mode) {
        this.soaService.saveSecurityQuestion(this.loggedInUserId, this.securityQuestionPayload).subscribe(response => {
          this.isLoading = false;
          this.router.navigate(['/signin']);
        }, (error: any) => {
          this.alertService.setErrorNotification({
            notificationHeader: externalAuthenticationConstants.ERROR,
            notificationBody: externalAuthenticationConstants.SAVE_SECURITY_QUEST_ERRMSG
          });
        })
      }
      else {
        this.isLoading = false;
        this.router.navigate(['/signin']);
      }

      setTimeout(() => {
        window.location.reload();
      }, 2000);
    }, (error: any) => {
      this.isLoading = false;
      this.alertService.setErrorNotification({
        notificationHeader: externalAuthenticationConstants.ERROR,
        notificationBody: externalAuthenticationConstants.CHANGE_PASSWORD_ERRMSG
      });
    })
  }

  /**
   * After questions are answered and validated, go to reset password
   */

  continueToResetPassword(): void {
    this.isLoading = true;
    let secretAnswersPayload = {
      dn: this.userDnData,
      secretAnswerText1: this.secretAnswer1,
      secretAnswerText2: this.secretAnswer2
    }

    this.soaService.validateSecretAnswers(this.soaService.getIdFromDnDetails(), secretAnswersPayload).subscribe(response => {
      if (response.secretAnswerMatched == true) {
        this.isSecurityQuestion = false;
        this.isLoading = false;
      }
      else if (response.secretAnswerMatched == false) {
        this.isLoading = false;
        this.alertService.setErrorNotification({
          notificationHeader: RegistrationConstants.ERROR,
          notificationBody: RegistrationConstants.SECURITY_ANS_INVALID_MSG
        });
      }
    }, (error: any) => {
      this.isLoading = false;
      if (error == RegistrationConstants.UNAUTHORIZED) {
        this.alertService.setErrorNotification({
          notificationHeader: RegistrationConstants.ERROR,
          notificationBody: RegistrationConstants.MAXIMUM_LIMIT_EXCEEDED_MSG
        });
      }
    })
  }

  /**
  * This function is triggered when any change happens in field values
  * @param event
  */
  onSecurityQuestionsFormChange(event) {
    this.secretAnswer1 = event.current.answer1;
    this.secretAnswer2 = event.current.answer2;
  }
}
