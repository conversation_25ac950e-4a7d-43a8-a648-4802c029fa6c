<div [hidden]="!showFeeList">
  <div>
    <span class="table-title">Fee Schedule</span>
    <span class="btn-span">
      <marketplace-button [label]="'Add Fee Schedule'" [type]="'primary'" [name]="'primary'" [enabled]="!isReadOnly"
        (onclick)="AddFeeSchedule()">
      </marketplace-button>


    </span>
  </div>
  <marketplace-table *ngIf="tableIsready" [id]="'product-bundle-fee-table'" [dataset]="productBundleFeeData"
    [rowHeight]="ruleDashbordTableRowhg" [headerRowHeight]="ruleDashbordTableHeaderhg"
    [columnDefinitions]="productBundleFeeColConfig" [redraw]="tableRedraw" [dropdownOptions]="kebabOptions"
    [isRowSelectable]="false" [customExportConfig]="customExport" (onCellClick)="cellClicked($event)"
    (onDropdownOptionsClick)="moveToSelectedTab($event)">
  </marketplace-table>
</div>

<app-product-bundle-fee-edit *ngIf="showFeeEdit" [editData]="selectedRowData" [type]="type"
  (editDataEvent)="receiveFromAddEdit($event)"></app-product-bundle-fee-edit>
<app-product-bundle-fee-add *ngIf="showFeeAdd" (addDataEvent)="receiveFromAddEdit($event)"></app-product-bundle-fee-add>