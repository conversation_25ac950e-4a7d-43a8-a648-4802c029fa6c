export const SCREEN_NAME_CONSTANTS = {
    PRODUCT: "Product",
    FEE_SCHEDULE: "Fee Schedule",
    DATA_EXCHANGE: "Data Exchange",
    FEE_SETUP: "Fee setup",
    FILE_EXCHANGE: "File Exchange",
    HOME: "Home",
    PRODUCTS_LIST: "Products List",
    SAMPLE_VALIDATION_PERCENTAGE: 'Sample Validation Percentage',
    VIEW_TENANT: "View Tenant"
}

export const STATUS_CONSTANTS = {
    ACTIVE: "Active",
    IN_ACTIVE: "Inactive",
    PENDING: "Pending",
    HEADER_SUCCESS: "Success",
    ADHOC: "Adhoc",
    SUCCESS: "success",
    FAILURE: "Failure"
}

export const WORK_ASSIGNMENT_CONSTANTS = {
    MANAGE_ASSIGNEE: "Manage Assignee",
    FIRST_NAME: "first_name",
    COLDEFS: "colDefs",
    DATASET: "dataset",
    CUSTOM_FORMATTER: "customFormatter",
    COLUMN_DEFINITION: "columnDefinition",
    CONCEPT_NAME: "Concept ID",
    CONCEPT_ID: "Concept Name",
    EXECUTION_ID: "Execution ID",
    EXECUTION_DATE: "Execution Date",
    CLAIM_HEADER: "Claim Headers",
    PROGRESS: "Progress",
    AUTOCLOSECOUNT: "Auto Close Count",
    VERIFIER: "Verifier",
    ADJUSTMENT_RATE: "Hit Rate",
    PRIORITY_SCORE: "priorityScore",
    CONTEXT_BANNER_DATA: "contextBannerDS",
    CONTEXT_BANNER_VERIFIER_DATA: "verifierData",
    UNASSIGNED: "Unassigned",
    NOTI_HEADER_BULK_ASSIGNMENT: "Select other user to assign claims",
    NOTI_BODY_BULK_ASSIGNMENT: "All the matching claims are already assigned to selected Assignee",
    NOTI_BODY_SAVE_CLAIM_SUCCESS: "Claim assigned successfully",
    NOTI_HEADER_SAVE_CLAIM_ERROR: "Error",
    NOTI_BODY_SAVE_CLAIM_ERROR: "Claim assignment failed",
    NOTI_BODY_WORK_ASSIGNMENT_ERROR: "Error while fetching the Workers Assignment",
    ASSIGNEE_NAMES: "assigneNamesBlock",
    EXPIRED: "EXPIRED",
    AUDIT_STATUS: "Audit Status",
    MANUAL_ASSIGNMENT_JSON: './assets/json/work-assignment/manual-assignment.json',
    EXPIRATION_TIME_FRAME: 'Expiration Timeframe',
    SEARCH_ICON_SVG: `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.78223C3.68629 1.78223 1 4.46852 1 7.78223C1 11.0959 3.68629 13.7822 7 13.7822C8.38653 13.7822 9.66322 13.3119 10.6792 12.5221L13.7197 15.5626L13.8038 15.6352C14.0974 15.853 14.5141 15.8288 14.7803 15.5626C15.0732 15.2697 15.0732 14.7948 14.7803 14.5019L11.7399 11.4615C12.5297 10.4454 13 9.16876 13 7.78223C13 4.46852 10.3137 1.78223 7 1.78223ZM7 3.28223C9.48528 3.28223 11.5 5.29695 11.5 7.78223C11.5 10.2675 9.48528 12.2822 7 12.2822C4.51472 12.2822 2.5 10.2675 2.5 7.78223C2.5 5.29695 4.51472 3.28223 7 3.28223Z" fill="#231E33"/>
    </svg>`,
    VALIDATED: "VALIDATED",
    NO_RECOVERY: "NO_RECOVERY",
    NOT_VALIDATED: "NOT_VALIDATED",
    CLIENT_VALIDATED: "CLIENT_VALIDATED",
    CLIENT_APPROVED: "CLIENT_APPROVED",
    CLIENT_REJECTED: "CLIENT_REJECTED",
    CLIENT_NO_RECOVERY: "CLIENT_NO_RECOVERY",
    ECM_CNCPT_VERIFIER_NM: "ecmCncptVerifierNm",
    AUTH_VALIDATED: "AUTH_VALIDATED"
}

export const SCHEDULER_CONSTANTS = {
    CLIENT_NAME: "clientName",
    CLIENT_ID: "clientId",
    ELEVANCE: 59,
    OPTIONS: "options",
    SELECTED_VAL: "selectedVal",
    NOTI_HEADER_FAIL: "Fail",
    NOTI_BODY_SUCCESS: "test edited successfully",
    SUCCESS: "success",
    ERROR: "Some Error Occurred",
    RETIRED: "Retired",
    IN_PRODUCTION: "In Production",
    PRODUCTION: "Production",
    VIEW_DETAILS: "View Details",
    OVERRIDE_PARAMETERS: "Override parameters",
    DRIVENBY_ID: 5018,
    TEST_EDITS: '<i class="fa fa-refresh" aria-hidden="true"></i> Test edits',
    OVERRIDE_BUTTON: '<i class="fa fa-pencil" aria-hidden="true"></i> Override parameters',
    ADHOC_SCHEDULE_SUCCESS_MSG: 'Ad Hoc Concept Successfully Scheduled!',
    SCHEDULE_SUCCESS_MSG: 'Concept Successfully scheduled!',
    VALID_WARMING_MSG: 'Please fill all the mandatory fields with valid selections',
    DISPLAY_STYLE_BLOCK: 'block',
    VALID_DATE_WARMING_MSG: 'Please select valid date, date cannot be less then current date.',
    NO_CHANGES_TO_FORM: 'Please make a valid change to proceed.',
    ADHOC_FAILURE_MSG: 'No analytic details found for the concept id, try scheduling other concept id.'
}