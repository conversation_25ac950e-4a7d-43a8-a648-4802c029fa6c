import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { catchError, map } from 'rxjs/operators';
import { AUTH_CONFIG } from '../_constants/menu.constant';
import { IExternalUserSearch } from '../_models/external/external-user-search';
import { IExternalRegistration } from '../_models/external/external-registration';
import { IExternalChangePassword } from '../_models/external/external-change-password';
import { IExternalAddUser } from '../_models/external/external-add-user';
import { IExternalGeneratePassword } from '../_models/external/external-generate-password';
import { IExternalUserStatus } from '../_models/external/external-user-status';
import { BehaviorSubject, Observable, throwError } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class ExternalSOAService {
    soaParam: string = "";
    
    constructor(private http: HttpClient) { }

    /**
     * Get SM cookie withe authentication
     * @param authDetails 
    */
    authenticateExternalUser(requestBody: any) {
        return this.http.post(`${environment.identityUrl}/api/v1/webusers/authenticate`, requestBody)
            .pipe(map(details => {
                return details;
            }));
    }

    /**
     * Search User Api
     * @param token 
     * @param searchPaylod 
     */
    searchUser(token: string, searchPaylod: IExternalUserSearch) {

        return this.http.post(environment.soaWebUsersUrl + '/search', searchPaylod, {
            headers: {
                'apikey': this.soaParam,
                'Authorization': AUTH_CONFIG.BEARER + token
            }
        }).pipe(map(userDetails => {
            return userDetails;
        }));
    }

    /**
     * Create New User
     * @param token 
     * @param newUser 
     */
    createNewUser(user: string, newUser: IExternalRegistration) {
        return this.http.post(environment.identityUrl + '/api/v1/webusers', newUser, {
            headers: {
                'usernm': user
                // 'Content-Type': 'application/json'
            }
        }).pipe(map(userDetails => {
            return userDetails;
        }));
    }

    /**
     * Change Password
     * @param token 
     * @param changePassword 
     */
    changePassword(user: string, changePassword: IExternalChangePassword) {
        return this.http.put(environment.identityUrl + '/api/v1/webusers/changepassword', changePassword, {
            headers: {
                'usernm': user
            }

        }).pipe(map(confirmationDetails => {
            return confirmationDetails;
        }));
    }

    /**
     * Generate Password
     * @param token 
     * @param generatePassword 
     */
    generatePassword(user: string) {
        return this.http.post(environment.identityUrl + '/api/v1/webusers/generatepassword', {}, {
            headers: {
                'usernm': user
            }
        }).pipe(map(confirmationDetails => {
            return confirmationDetails;
        }));
    }


     /**
     * External Add user
     * @param addUser 
     */
    externalUserAdd(addUser: IExternalAddUser) {
        return this.http.post(environment.authorizationUrl + '/api/dbg-authorization/user/external/add', addUser).pipe(map(addUser => {
            return addUser;
        }));
    }

    /**
     * Enable User Api
     * @param token 
     * @param userStatus 
     */
   updateUserStatus(user: string, userStatus: IExternalUserStatus) {
        return this.http.post(environment.identityUrl + '/api/v1/webusers/userstatus', userStatus, {
            headers: {
                'usernm': user
            }
        }).pipe(map(userDetails => {
            return userDetails;
        }));
    }


/**
* Method to get firsTimeLogin is true or false for external users
* @returns 
*/
     getExternalUserLoginDetails(userId): Observable<any> {
        return this.http.get<any>(`${environment.authService}/api/v1/user/viewUsers/${userId}`).pipe(
            catchError(err => {
                return throwError(err);
            }));
    }

    /**
* Method to change firstTimeLogin as False for external users when they change password for the firstime
* @returns
*/
    saveUserLoginDetails(userDetailsPayload): Observable<any> {
        return this.http.post<any>(`${environment.authService}/api/v1/user/save`, userDetailsPayload).pipe(
            catchError(err => {
                return throwError(err);
            }));
    }

/**
* Subscription for login details
*/

    loginDetailsData = new BehaviorSubject<any>('');

    public setLoginDetailsSubscription(data): any {
        this.loginDetailsData.next(data);
    }

    public getLoginDetailsSubscription() {
        return this.loginDetailsData;
    }


    /**
    * API to get list of security questions
    */
   getListOfSecurityQuestions(): Observable<any> {
        return this.http.get<any>(environment.identityUrl + '/api/v1/webusers/secretquestions').pipe(
            catchError(err => {
                return throwError(err);
            }));
    }
    
    /**
    * API to save security questions and answers
    */
    saveSecurityQuestion(user: string, securityQuestionPayload) {
        return this.http.post(environment.identityUrl + '/api/v1/webusers', securityQuestionPayload, {
            headers: {
                'usernm': user
            }
        }).pipe(map(confirmationDetails => {
            return confirmationDetails;
        }));
    }

    /**
    * Subscription for login details
    */
    userDnData = new BehaviorSubject<any>('');

    public setDnSubscription(data): any {
        this.userDnData.next(data);
    }
    public getDnSubscription() {
        return this.userDnData;
    }
    public extractUserIDFromDn() {
        let userDetails = this.userDnData['_value']['userDnData'].split(",");
        let userId = userDetails[0].split("=")[1];
        return userId;
    }
    public getIdFromDnDetails() {
        return this.extractUserIDFromDn();
    }

    /**
    * API to validate secret answers
    */
    validateSecretAnswers(user: string, secretAnswersPayload: any): Observable<any> {
        return this.http.post<any>(environment.identityUrl + '/api/v1/webusers/answers/validate', secretAnswersPayload, {
            headers: {
                'usernm': user
            }
        }).pipe(
            catchError(err => {
                return throwError(err);
            }));
    }

    /**
     * Search User Api
     * @param token 
     * @param searchPaylod 
     */
   searchExtUser(user: string) {
        return this.http.get(environment.identityUrl + '/api/v1/webusers/search', {
            headers: {
                'usernm': user
            }
        }).pipe(map(userDetails => {
            return userDetails;
        }));
    }
}
