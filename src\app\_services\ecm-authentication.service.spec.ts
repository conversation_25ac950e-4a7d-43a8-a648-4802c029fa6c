import { TestBed } from '@angular/core/testing';
import { EcmAuthenticationService } from './ecm-authentication.service';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { ToastService } from './toast.service';
import { environment } from 'src/environments/environment';
import { of } from 'rxjs';

describe('EcmAuthenticationService', () => {
  let httpTestingController: HttpTestingController;
  let service: EcmAuthenticationService;
  let mockToastService: jasmine.SpyObj<ToastService>;

  beforeEach(() => {
    const toastSpy = jasmine.createSpyObj('ToastService', ['setErrorNotification']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        EcmAuthenticationService,
        { provide: ToastService, useValue: toastSpy }
      ]
    });

    httpTestingController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(EcmAuthenticationService);
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
  });

  afterEach(() => {
    localStorage.removeItem('token');
    httpTestingController.verify();
  });

  describe('Component Initialization', () => {
    it('should be created', () => {
      expect(service).toBeTruthy();
    });

    it('should initialize with undefined sessionToken', () => {
      expect(service.sessionToken).toBeUndefined();
    });

    it('should have ToastService injected', () => {
      expect(mockToastService).toBeTruthy();
    });
  });

  describe('authenticateECMUser Method', () => {
    it('should make HTTP request for authentication', () => {
      const mockResponse = {
        success: true,
        token: 'test-token-123'
      };

      // Mock the HTTP call to prevent actual network request
      spyOn(service['http'], 'get').and.returnValue(of(mockResponse));

      const subscription = service.authenticateECMUser();
      expect(subscription).toBeDefined();
      expect(service['http'].get).toHaveBeenCalledWith(`${environment.authorizationUrl}/api/dbg-authorization/ecm/accessToken`);
    });

    it('should handle authentication response processing', () => {
      const mockResponse = {
        success: true,
        token: 'test-token-123'
      };

      // Mock the HTTP call
      spyOn(service['http'], 'get').and.returnValue(of(mockResponse));

      service.authenticateECMUser();

      // Verify token is set
      expect(service.sessionToken).toBe('test-token-123');
      expect(localStorage.getItem('token')).toBe('Bearer test-token-123');
    });

    it('should handle authentication failure response', () => {
      const mockResponse = {
        success: false,
        message: 'Authentication failed'
      };

      // Mock the HTTP call
      spyOn(service['http'], 'get').and.returnValue(of(mockResponse));

      service.authenticateECMUser();

      expect(mockToastService.setErrorNotification).toHaveBeenCalledWith({
        notificationHeader: "Fail",
        notificationBody: "You don't have permission to access Scheduler module! "
      });
    });
  });

  describe('checkSessionToken Method', () => {
    beforeEach(() => {
      service.sessionToken = 'existing-token';
    });

    it('should make HTTP request for token refresh', () => {
      const mockResponse = {
        success: true,
        token: 'new-token-456'
      };

      // Mock the HTTP call
      spyOn(service['http'], 'post').and.returnValue(of(mockResponse));

      service.checkSessionToken();

      expect(service['http'].post).toHaveBeenCalledWith(
        `${environment.schedulerECMLgnUrl}/token`,
        {},
        jasmine.any(Object)
      );
    });

    it('should handle successful token refresh', () => {
      const mockResponse = {
        success: true,
        token: 'new-token-456'
      };

      // Mock the HTTP call
      spyOn(service['http'], 'post').and.returnValue(of(mockResponse));

      service.checkSessionToken();

      expect(service.sessionToken).toBe('new-token-456');
      expect(localStorage.getItem('token')).toBe('Bearer new-token-456');
    });

    it('should handle token refresh failure', () => {
      const mockResponse = {
        success: false,
        message: 'Token refresh failed'
      };

      // Mock the HTTP call
      spyOn(service['http'], 'post').and.returnValue(of(mockResponse));

      service.checkSessionToken();

      // Token should not be updated on failure
      expect(service.sessionToken).toBe('existing-token');
    });
  });

  describe('setDefaultHeaders Method', () => {
    it('should return headers with authorization token', () => {
      service.sessionToken = 'test-token';

      const headers = service.setDefaultHeaders();

      expect(headers.headers.get('Content-Type')).toBe('application/json');
      expect(headers.headers.get('Accept')).toBe('application/json');
      expect(headers.headers.get('Authorization')).toBe('Bearer test-token');
      expect(headers.headers.get('Product-Id')).toBe('10');
    });

    it('should handle undefined session token', () => {
      service.sessionToken = undefined;

      const headers = service.setDefaultHeaders();

      expect(headers.headers.get('Authorization')).toBe('Bearer undefined');
      expect(headers.headers.get('Product-Id')).toBe('10');
    });

    it('should return consistent header structure', () => {
      service.sessionToken = 'consistent-token';

      const headers1 = service.setDefaultHeaders();
      const headers2 = service.setDefaultHeaders();

      expect(headers1.headers.get('Content-Type')).toBe(headers2.headers.get('Content-Type'));
      expect(headers1.headers.get('Accept')).toBe(headers2.headers.get('Accept'));
      expect(headers1.headers.get('Product-Id')).toBe(headers2.headers.get('Product-Id'));
    });
  });

  describe('setInitialHeaders Method', () => {
    it('should return initial headers without authorization', () => {
      const headers = service.setInitialHeaders();

      expect(headers.headers.get('Content-Type')).toBe('application/json');
      expect(headers.headers.get('Accept')).toBe('application/json');
      expect(headers.headers.get('Authorization')).toBeNull();
      expect(headers.headers.get('Product-Id')).toBeNull();
    });

    it('should return consistent initial headers', () => {
      const headers1 = service.setInitialHeaders();
      const headers2 = service.setInitialHeaders();

      expect(headers1.headers.get('Content-Type')).toBe(headers2.headers.get('Content-Type'));
      expect(headers1.headers.get('Accept')).toBe(headers2.headers.get('Accept'));
    });
  });

  describe('startRefreshTokenTimer Method', () => {
    it('should start refresh token timer', () => {
      spyOn(service, 'checkSessionToken');

      // Call the private method through authenticateECMUser
      const mockResponse = { success: true, token: 'test-token' };
      const subscription = service.authenticateECMUser();
      expect(subscription).toBeDefined();

      const req = httpTestingController.expectOne(`${environment.authorizationUrl}/api/dbg-authorization/ecm/accessToken`);
      req.flush(mockResponse);

      // Timer should be set
      expect((service as any).refreshTokenTimeout).toBeDefined();
    });

    it('should clear existing timer before setting new one', () => {
      spyOn(window, 'clearTimeout');
      spyOn(window, 'setTimeout').and.returnValue(123 as any);

      (service as any).refreshTokenTimeout = 456;

      const mockResponse = { success: true, token: 'test-token' };
      const subscription = service.authenticateECMUser();
      expect(subscription).toBeDefined();

      const req = httpTestingController.expectOne(`${environment.authorizationUrl}/api/dbg-authorization/ecm/accessToken`);
      req.flush(mockResponse);

      expect(window.setTimeout).toHaveBeenCalled();
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete authentication flow', () => {
      const mockAuthResponse = {
        success: true,
        token: 'initial-token'
      };

      const mockRefreshResponse = {
        success: true,
        token: 'refreshed-token'
      };

      // Initial authentication
      const subscription = service.authenticateECMUser();
      expect(subscription).toBeDefined();

      const authReq = httpTestingController.expectOne(`${environment.authorizationUrl}/api/dbg-authorization/ecm/accessToken`);
      authReq.flush(mockAuthResponse);

      // Token refresh
      service.checkSessionToken();

      const refreshReq = httpTestingController.expectOne(`${environment.schedulerECMLgnUrl}/token`);
      refreshReq.flush(mockRefreshResponse);

      expect(service.sessionToken).toBe('refreshed-token');
      expect(localStorage.getItem('token')).toBe('Bearer refreshed-token');
    });

    it('should maintain state consistency across method calls', () => {
      service.sessionToken = 'state-token';

      const defaultHeaders = service.setDefaultHeaders();
      const initialHeaders = service.setInitialHeaders();

      expect(defaultHeaders.headers.get('Authorization')).toBe('Bearer state-token');
      expect(initialHeaders.headers.get('Authorization')).toBeNull();

      // State should remain unchanged
      expect(service.sessionToken).toBe('state-token');
    });
  });
});
