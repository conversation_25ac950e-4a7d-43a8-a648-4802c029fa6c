<div class="body-content">
    <div class="page-header-container">
        <img src="./assets/images/logo.png">
        <span class="seperator"></span>
        <div class="page-header-text">Payment Integrity</div>
    </div>

    <div class="card elevated-card-changepassword" *ngIf="isChangePassword">
        <div class="page-header">
            <h3>Choose a new password.</h3>
            <div class="red-font" *ngIf="isPasswordNotMatched">{{errorMsg}}</div>
        </div>
        <marketplace-dynamic-form [formJSON]="NewPasswordFormJSON" [isSubmitNeeded]="false"
            (onValueChanges)="onPasswordValueChange($event)">
        </marketplace-dynamic-form>
        <div class="red-font" *ngIf="isPasswordNotMatched">Passwords do not match</div>
        <div class="btn-holder">
            <marketplace-button [label]="'Update password'" [name]="'primary'" [type]="'primary'"
                (onclick)="onUpdatePasswordButtonClicked($event)">
            </marketplace-button>
        </div>
        <a routerLink="../">Log in</a>
    </div>

    <div class="card elevated-card-passwordsuccess" *ngIf="isPasswordMatch">
        <div class="page-header">
            <h3>Success!</h3>
            <span>You have successfully updated your password.</span>
        </div>
        <div class="btn-holder">
            <marketplace-button [label]="'Sign in >'" [name]="'primary'" [type]="'primary'"
                (onclick)="onSigninButtonClicked()">
            </marketplace-button>
        </div>
    </div>

    <div class="page-footer">
        Need Help ?
        <a href="{{contactUsUrl}}" target="_blank">Email Support</a>
    </div>
</div>