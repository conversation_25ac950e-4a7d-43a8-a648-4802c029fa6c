import { OperatorsRulesQB, OperatorsMapForQb, operatorsMapToShowInQb } from './Rules-QB-Constants';

describe('Rules-QB-Constants', () => {
  it('should have text operators', () => {
    expect(OperatorsRulesQB.text).toBeDefined();
    expect(Array.isArray(OperatorsRulesQB.text)).toBeTrue();
    expect(OperatorsRulesQB.text.some(op => op.name === 'Equal')).toBeTrue();
  });

  it('should have numeric operators', () => {
    expect(OperatorsRulesQB.numeric).toBeDefined();
    expect(Array.isArray(OperatorsRulesQB.numeric)).toBeTrue();
    expect(OperatorsRulesQB.numeric.some(op => op.name === 'Greater Than')).toBeTrue();
  });

  it('should map operator names to symbols', () => {
    expect(OperatorsMapForQb['Equal']).toBe('==');
    expect(OperatorsMapForQb['Not Equal']).toBe('!=');
    expect(OperatorsMapForQb['Greater Than']).toBe('>');
  });

  it('should map symbols to operator names', () => {
    expect(operatorsMapToShowInQb['==']).toBe('Equal');
    expect(operatorsMapToShowInQb['!=']).toBe('Not Equal');
    expect(operatorsMapToShowInQb['>']).toBe('Greater Than');
  });

  it('should have all operator types defined', () => {
    expect(OperatorsRulesQB.textarea).toBeDefined();
    expect(OperatorsRulesQB.time).toBeDefined();
    expect(OperatorsRulesQB.calendar).toBeDefined();
    expect(OperatorsRulesQB.singleselect).toBeDefined();
    expect(OperatorsRulesQB.checkbox).toBeDefined();
    expect(OperatorsRulesQB.multipleselect).toBeDefined();
  });

  it('should have correct mapping for in operator', () => {
    expect(OperatorsMapForQb['in']).toBe('in');
    expect(operatorsMapToShowInQb['in']).toBe('in');
  });

  describe('Comprehensive Operator Testing', () => {
    it('should have all text operators with correct structure', () => {
      const textOps = OperatorsRulesQB.text;
      expect(textOps.length).toBe(11);

      textOps.forEach(op => {
        expect(op.name).toBeDefined();
        expect(op.id).toBeDefined();
        expect(typeof op.name).toBe('string');
        expect(typeof op.id).toBe('string');
      });

      expect(textOps.some(op => op.name === 'Equal' && op.id === 'Equal')).toBe(true);
      expect(textOps.some(op => op.name === 'Not Equal' && op.id === 'Not Equal')).toBe(true);
      expect(textOps.some(op => op.name === 'contains' && op.id === 'contains')).toBe(true);
      expect(textOps.some(op => op.name === 'Does Not Contain(s)' && op.id === 'Does Not Contain(s)')).toBe(true);
      expect(textOps.some(op => op.name === 'Begins With' && op.id === 'Begins With')).toBe(true);
      expect(textOps.some(op => op.name === 'Ends With' && op.id === 'Ends With')).toBe(true);
      expect(textOps.some(op => op.name === 'Is Null' && op.id === 'Is Null')).toBe(true);
      expect(textOps.some(op => op.name === 'Is Not Null' && op.id === 'Is Not Null')).toBe(true);
      expect(textOps.some(op => op.name === 'In' && op.id === 'in')).toBe(true);
    });

    it('should have all numeric operators with correct structure', () => {
      const numericOps = OperatorsRulesQB.numeric;
      expect(numericOps.length).toBe(10);

      numericOps.forEach(op => {
        expect(op.name).toBeDefined();
        expect(op.id).toBeDefined();
        expect(typeof op.name).toBe('string');
        expect(typeof op.id).toBe('string');
      });

      expect(numericOps.some(op => op.name === 'Greater Than' && op.id === 'Greater Than')).toBe(true);
      expect(numericOps.some(op => op.name === 'Greater Than or Equal' && op.id === 'Greater Than Or Equal')).toBe(true);
      expect(numericOps.some(op => op.name === 'Less Than' && op.id === 'Less Than')).toBe(true);
      expect(numericOps.some(op => op.name === 'Less Than Or Equal' && op.id === 'Less Than Or Equal')).toBe(true);
      expect(numericOps.some(op => op.name === 'Between' && op.id === 'Between')).toBe(true);
      expect(numericOps.some(op => op.name === 'Not Between' && op.id === 'Not Between')).toBe(true);
    });

    it('should have all textarea operators', () => {
      const textareaOps = OperatorsRulesQB.textarea;
      expect(textareaOps.length).toBe(10);
      expect(textareaOps.some(op => op.name === 'Does Not Begins With')).toBe(true);
      expect(textareaOps.some(op => op.name === 'Does Not End With')).toBe(true);
    });

    it('should have all time operators', () => {
      const timeOps = OperatorsRulesQB.time;
      expect(timeOps.length).toBe(6);
      expect(timeOps.every(op => ['Equal', 'Not Equal', 'Greater Than', 'Greater Than or Equal', 'Less Than', 'Less Than Or Equal'].includes(op.name))).toBe(true);
    });

    it('should have all calendar operators', () => {
      const calendarOps = OperatorsRulesQB.calendar;
      expect(calendarOps.length).toBe(10);
      expect(calendarOps.some(op => op.name === 'Between')).toBe(true);
      expect(calendarOps.some(op => op.name === 'Not Between')).toBe(true);
    });

    it('should have all singleselect operators', () => {
      const singleselectOps = OperatorsRulesQB.singleselect;
      expect(singleselectOps.length).toBe(2);
      expect(singleselectOps.every(op => ['Equal', 'Not Equal'].includes(op.name))).toBe(true);
    });

    it('should have all checkbox operators', () => {
      const checkboxOps = OperatorsRulesQB.checkbox;
      expect(checkboxOps.length).toBe(2);
      expect(checkboxOps.every(op => ['Equal', 'Not Equal'].includes(op.name))).toBe(true);
    });

    it('should have all multipleselect operators', () => {
      const multipleselectOps = OperatorsRulesQB.multipleselect;
      expect(multipleselectOps.length).toBe(1);
      expect(multipleselectOps[0].name).toBe('Equal');
      expect(multipleselectOps[0].id).toBe('Equal');
    });
  });

  describe('OperatorsMapForQb Complete Testing', () => {
    it('should have all operator mappings', () => {
      const expectedMappings = {
        "Equal": "==",
        "Not Equal": "!=",
        "Greater Than": ">",
        "Greater Than Or Equal": ">=",
        "Less Than": "<",
        "Less Than Or Equal": "<=",
        "contains": "contains",
        "like": "like",
        "Does Not Contain(s)": "not contains",
        "Begins With": "startswith",
        "Ends With": "endswith",
        "Does Not Begins With": "not startswith",
        "Does Not End With": "not endswith",
        "Is Null": "isnull",
        "Is Not Null": "isnotnull",
        "Between": "between",
        "Not Between": "not between",
        "in": "in"
      };

      Object.keys(expectedMappings).forEach(key => {
        expect(OperatorsMapForQb[key]).toBe(expectedMappings[key]);
      });

      expect(Object.keys(OperatorsMapForQb).length).toBe(18);
    });

    it('should handle all comparison operators', () => {
      expect(OperatorsMapForQb['Greater Than Or Equal']).toBe('>=');
      expect(OperatorsMapForQb['Less Than Or Equal']).toBe('<=');
    });

    it('should handle all text operators', () => {
      expect(OperatorsMapForQb['Does Not Contain(s)']).toBe('not contains');
      expect(OperatorsMapForQb['Does Not Begins With']).toBe('not startswith');
      expect(OperatorsMapForQb['Does Not End With']).toBe('not endswith');
    });

    it('should handle null operators', () => {
      expect(OperatorsMapForQb['Is Null']).toBe('isnull');
      expect(OperatorsMapForQb['Is Not Null']).toBe('isnotnull');
    });

    it('should handle range operators', () => {
      expect(OperatorsMapForQb['Between']).toBe('between');
      expect(OperatorsMapForQb['Not Between']).toBe('not between');
    });
  });

  describe('operatorsMapToShowInQb Complete Testing', () => {
    it('should have all reverse mappings', () => {
      const expectedReverseMappings = {
        '==': 'Equal',
        '!=': 'Not Equal',
        '>': 'Greater Than',
        '>=': 'Greater Than Or Equal',
        '<': 'Less Than',
        '<=': 'Less Than Or Equal',
        'contains': 'contains',
        'like': 'like',
        'not contains': 'Does Not Contain(s)',
        'startswith': 'Begins With',
        'endswith': 'Ends With',
        'not startswith': 'Does Not Begins With',
        'not endswith': 'Does Not End With',
        'isnull': 'Is Null',
        'isnotnull': 'Is Not Null',
        "between": "Between",
        "not between": "Not Between",
        "in": "in"
      };

      Object.keys(expectedReverseMappings).forEach(key => {
        expect(operatorsMapToShowInQb[key]).toBe(expectedReverseMappings[key]);
      });

      expect(Object.keys(operatorsMapToShowInQb).length).toBe(18);
    });

    it('should handle bidirectional mapping consistency', () => {
      // Test that forward and reverse mappings are consistent
      Object.keys(OperatorsMapForQb).forEach(operatorName => {
        const symbol = OperatorsMapForQb[operatorName];
        const reversedName = operatorsMapToShowInQb[symbol];
        expect(reversedName).toBe(operatorName);
      });
    });
  });

  describe('Data Structure Validation', () => {
    it('should have all operator types as arrays', () => {
      expect(Array.isArray(OperatorsRulesQB.text)).toBe(true);
      expect(Array.isArray(OperatorsRulesQB.numeric)).toBe(true);
      expect(Array.isArray(OperatorsRulesQB.textarea)).toBe(true);
      expect(Array.isArray(OperatorsRulesQB.time)).toBe(true);
      expect(Array.isArray(OperatorsRulesQB.calendar)).toBe(true);
      expect(Array.isArray(OperatorsRulesQB.singleselect)).toBe(true);
      expect(Array.isArray(OperatorsRulesQB.checkbox)).toBe(true);
      expect(Array.isArray(OperatorsRulesQB.multipleselect)).toBe(true);
    });

    it('should have OperatorsMapForQb as object', () => {
      expect(typeof OperatorsMapForQb).toBe('object');
      expect(OperatorsMapForQb).not.toBeNull();
      expect(Array.isArray(OperatorsMapForQb)).toBe(false);
    });

    it('should have operatorsMapToShowInQb as object', () => {
      expect(typeof operatorsMapToShowInQb).toBe('object');
      expect(operatorsMapToShowInQb).not.toBeNull();
      expect(Array.isArray(operatorsMapToShowInQb)).toBe(false);
    });

    it('should have no undefined or null values in operators', () => {
      Object.values(OperatorsRulesQB).forEach(operatorArray => {
        operatorArray.forEach(operator => {
          expect(operator.name).toBeDefined();
          expect(operator.id).toBeDefined();
          expect(operator.name).not.toBeNull();
          expect(operator.id).not.toBeNull();
        });
      });
    });

    it('should have no empty strings in operator mappings', () => {
      Object.keys(OperatorsMapForQb).forEach(key => {
        expect(key).toBeTruthy();
        expect(OperatorsMapForQb[key]).toBeTruthy();
      });

      Object.keys(operatorsMapToShowInQb).forEach(key => {
        expect(key).toBeTruthy();
        expect(operatorsMapToShowInQb[key]).toBeTruthy();
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle accessing non-existent operator types', () => {
      expect(OperatorsRulesQB['nonexistent']).toBeUndefined();
    });

    it('should handle accessing non-existent mappings', () => {
      expect(OperatorsMapForQb['nonexistent']).toBeUndefined();
      expect(operatorsMapToShowInQb['nonexistent']).toBeUndefined();
    });

    it('should maintain immutability of constants', () => {
      const originalTextLength = OperatorsRulesQB.text.length;

      // Attempt to modify (should not affect original)
      const textCopy = [...OperatorsRulesQB.text];
      textCopy.push({ name: 'Test', id: 'Test' });

      expect(OperatorsRulesQB.text.length).toBe(originalTextLength);
    });
  });
});
