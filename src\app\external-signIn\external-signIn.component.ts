import { Component, HostListener, OnInit, ViewEncapsulation } from '@angular/core';
import { UserManagementApiService } from '../users/_services/user-management-api.service';
import { Router } from '@angular/router';
import { AuthService } from '../_services/authentication.services';
import { ExternalSOAService } from '../_services/external-soa.service';
import { HttpParams } from '@angular/common/http';
import { AUTH_CONFIG, ROUTING_LABELS, TOKEN_INFO } from '../_constants/menu.constant';
import { CookieService } from 'ngx-cookie-service';
import { IRequestContext } from '../_models/external/external-request-context';
import { EXTERNALUSER } from '../_models/external-user-constants';
import { IExternalSignIn } from '../_models/external/external-signin';
import { externalAuthenticationConstants } from '../_helpers/helpers.constants';
import { ExternalMFAService } from '../_services/external-mfa.service';
import { IExternalCookie } from '../_models/mfa/external-cookie';
import { IExternalLoginThreat } from '../_models/mfa/external-loginthreat';
import { IExternalSendOTP } from '../_models/mfa/external-send-otp';
import { ISearchUserFilter, IExternalUserSearch } from '../_models/external/external-user-search';
import { IExternalValidateOTP } from '../_models/mfa/external.validate-otp';
import { ToastService } from '../_services/toast.service';

const jsonPath = "./assets/json/external-signIn/external-signIn-form.json";
const signInFormName = "formDetails";
const otpFormName = "otpFormDetails";
const enterKey = "Enter";
const formInput = "input";

@Component({
    selector: 'app-external-signIn',
    templateUrl: './external-signIn.component.html',
    styleUrls: ['./external-signIn.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class ExternalSignInComponent implements OnInit {
    signInFormJSON: any;
    otpFormJSON: any;
    errorMsg: string = "";
    btnLabel: string = `Sign in`;
    loginSVG: string = `<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M15.153 13.7493C15.153 14.7162 14.3695 15.5 13.403 15.5L6.40295 15.5C5.98874 15.5 5.65295 15.1641 5.65295 14.7497C5.65295 14.3353 5.98874 13.9994 6.40295 13.9994L13.403 13.9994C13.541 13.9994 13.653 13.8874 13.653 13.7493L13.653 3.25071C13.653 3.11258 13.541 3.00061 13.403 3.00061L6.40296 3.00061C5.98874 3.00061 5.65296 2.66468 5.65296 2.2503C5.65296 1.83592 5.98874 1.5 6.40296 1.5L13.403 1.5C14.3695 1.5 15.153 2.28382 15.153 3.25071L15.153 13.7493Z" fill="black"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M10.3104 7.9697C10.6033 8.26259 10.6033 8.73747 10.3104 9.03036L7.48194 11.8588C7.18905 12.1517 6.71417 12.1517 6.42128 11.8588C6.12839 11.5659 6.12839 11.091 6.42128 10.7981L8.71938 8.50003L6.42128 6.20193C6.12839 5.90904 6.12839 5.43417 6.42128 5.14127C6.71417 4.84838 7.18905 4.84838 7.48194 5.14127L10.3104 7.9697Z" fill="black"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M10.347 8.50003C10.347 8.91424 10.0113 9.25003 9.59705 9.25003L1.59705 9.25003C1.18283 9.25003 0.847046 8.91424 0.847046 8.50003C0.847046 8.08582 1.18283 7.75003 1.59705 7.75003L9.59705 7.75003C10.0113 7.75003 10.347 8.08582 10.347 8.50003Z" fill="black"/>
    </svg>`;

    isOtpScreen: boolean = false;
    isOtpEntered: boolean = false;
    isLoading: boolean;
    otp: string;
    emailAddress: string;

    userDetails: any;
    pingRiskId: any;
    pingDeviceId: any;
    pingUserId: any;
    otpValue: any;
    mfaToken: any;
    soaToken: any;
    isFormSubmitted: boolean = false;
    loginThreatParameter: any = {} as IExternalLoginThreat;
    cookieExt: any = {
        saveDeviceOrCookieFlag: true
    } as IExternalCookie;
    mfaCookie: string = "";
    contactUsUrl: string = externalAuthenticationConstants.CONTACT_US_URL;
    generatedCode: boolean = false;

    constructor(private userManagementSvc: UserManagementApiService, private authService: AuthService,
        private router: Router, private soaService: ExternalSOAService, private cookie: CookieService,
        private cookieService: CookieService, private mfaService: ExternalMFAService, public alertService: ToastService) { }
    ngOnInit(): void {
        this.mfaCookie = this.cookieService.get("mfaCookie");
        let _fetchPage = this.userManagementSvc.getAssetsJson(jsonPath);
        _fetchPage.subscribe(data => {
            this.signInFormJSON = data[0][signInFormName];
            this.otpFormJSON = data[1][otpFormName];
            if (this.mfaCookie) {
                this.signInFormJSON.find((x) => x.name == "rememberMeControl").options[0].value = true;
                this.loginThreatParameter = {
                    cookieValue: this.mfaCookie
                }
            }
        });
    }

    /**
    * This function is triggered when SignIn form values change     
    */
    onOtpFormChange(event: any) {
        this.otp = event.current.otp;
    }

    /**
    * This function is triggered when user clicks on signIn button     
    */
    onSignInClick() {
        this.isLoading = true;
        this.errorMsg = "";
        this.isOtpScreen = false;
        if (this.userDetails.userId && this.userDetails.pwd) {
            this.authenticateUser()
        } else {
            this.errorMsg = externalAuthenticationConstants.SIGN_IN_ERROR_MSG;
            this.isLoading = false;
        }
    }

    /**
    * This function is triggered when user clicks on submit button in OTP screen     
    */
    onOtpSubmit() {
        this.errorMsg = "";
        if (this.otp) {
            this.cookie.set(ROUTING_LABELS.CAD_ACCESS, 'true');
            this.authService.isLogin = false;
            localStorage.setItem('verifyAuth', 'true');
            this.soaService.getExternalUserLoginDetails(this.userDetails.userId).subscribe(data => {
                if (data.firstTimeLogin == true) {
                    this.soaService.setLoginDetailsSubscription({ userData: data });
                    this.router.navigate([externalAuthenticationConstants.CHANGE_PASSWORD_NAVIGATION]);
                }
                else
                    this.router.navigate(['']);
            })
        } else {
            this.errorMsg = externalAuthenticationConstants.OTP_ERROR_MSG;
        }

    }

    /**
    * This function is triggered when user clicks on re-send OTP in OTP screen     
    */
    onResendOtp() {
        this.errorMsg = "";
        this.isLoading = true;
        let sendOtp: IExternalSendOTP = {
            channel: externalAuthenticationConstants.EMAIL
        }

        this.mfaService.sendOTP(sendOtp, this.userDetails.userId).subscribe((otpData: any) => {
            this.pingDeviceId = otpData.pingDeviceId;
            this.pingUserId = otpData.pingUserId;
            this.isLoading = false;
            this.isOtpScreen = true;
        });
    }

    /**
   * Authenticate User
   */
    authenticateUser(): void {
        this.isLoading = true;
        let userId = this.userDetails.userId;
        let passcode = this.userDetails.pwd;

        /* this.soaService.getSOAAccessToken().subscribe((data: any) => {
            let requestBody: IExternalSignIn = {
                requestContext: {
                    requestId: EXTERNALUSER.USER_AUTHNTICATE,
                    application: EXTERNALUSER.APPLICATION,
                    username: userId
                },
                repositoryEnum: EXTERNALUSER.REPOSITORYENUM,
                userRoleEnum: EXTERNALUSER.APPLICATION,
                username: userId,
                password: passcode
            };
            this.soaToken = data.access_token;
            this.soaService.soaParam = data.client_id;
            this.soaService.authenticateUser(this.soaToken, requestBody).subscribe((data) => {
                this.soaService.setDnSubscription({ userDnData: data });
                if (data[externalAuthenticationConstants.AUTHENTICATED]) {
                    this.cookie.set("SMSESSION", data["cookie"]);
                    this.mfaProcess();
                }
                else {
                    this.isLoading = false;
                    this.errorMsg = externalAuthenticationConstants.SIGN_IN_ERROR_MSG
                }
            }, error => {
                this.isLoading = false;
                this.errorMsg = externalAuthenticationConstants.ERROR
            }); 
        }); */
        let requestBody = {
            username: userId,
            password: passcode
        };
        this.soaService.authenticateExternalUser(requestBody).subscribe((data: any) => {
            this.soaService.setDnSubscription({ userDnData: data.user });
            this.generatedCode = data.userAccountStatus.forceChangePassword;
            if (data[externalAuthenticationConstants.AUTHENTICATED]) {
                document.cookie = `SMSESSION=${data["cookie"]};expires=5;domain=.careloninsights.com;path=/`;
                this.loginThreat(this.loginThreatParameter);
            }
            else {
                this.isLoading = false;
                this.errorMsg = externalAuthenticationConstants.SIGN_IN_ERROR_MSG
            }
        }, error => {
            this.isLoading = false;
            this.errorMsg = externalAuthenticationConstants.ERROR
        });
    }

    /**
     * OTP Value Changes
     * @param event 
     */
    otpValueChanges(event: any) {
        event.current?.otp == '' ? this.isOtpEntered = false : this.isOtpEntered = true;
        this.otpValue = event.current.otp
    }

    /**
     * On Sign In
     * @param event 
     */
    onSignIn(event: any) {
        this.userDetails = event.value;
        this.userDetails.rememberMe = event.value.rememberMeControl.rememberMe;
        this.authenticateUser();
    }

    /**
     * call MFA cookie API sevice call
     */
    callMFACookie(firstTimeLogin): void {
        if (this.userDetails.rememberMe) {
            this.mfaService.getMFACookie(this.cookieExt, this.userDetails.userId).subscribe((cookieData: any) => {
                if (cookieData && cookieData.cookie.status == externalAuthenticationConstants.COOKIE_VERIFIER) {
                    this.loginThreatParameter = {
                        cookieValue: cookieData.cookie.cookieValue
                    }
                    this.cookieService.set("mfaCookie", cookieData.cookie.cookieValue);
                    this.navigateBasedOnFirstLogin(cookieData.firstTime);
                }
            }, error => {
                this.isLoading = false;
                this.alertService.setErrorNotification({
                    notificationHeader: externalAuthenticationConstants.ERROR,
                    notificationBody: externalAuthenticationConstants.MFA_COOKIE_ERRMSG
                });
            })
        }
        else {
            this.navigateBasedOnFirstLogin(firstTimeLogin);
        }
    }

    /**
     * Validate OTP
     */
    validateOTP() {
        this.isLoading = true;
        let validateOTP: IExternalValidateOTP = {
            otp: this.otpValue?.trim(),
            duration: externalAuthenticationConstants.OTP_DURATION
        }

        this.mfaService.validateOTP(validateOTP, this.userDetails.userId, this.pingRiskId, this.pingDeviceId, this.pingUserId)
            .subscribe((data: any) => {
                this.callMFACookie(data.firstTime);
            }, (error: any) => {
                this.errorMsg = EXTERNALUSER.INVALID_OTP
                this.isLoading = false;
            });
    }

    /**
     * Two Factor Authentication
     * @param soaToken 
     * @param mfaToken 
     */
    twoFactor(soaToken: any, mfaToken: any) {
        let requestContext: IRequestContext = {
            username: this.userDetails.userId,
            requestId: EXTERNALUSER.SEARCH_REQUEST_ID,
            application: EXTERNALUSER.APPLICATION
        };
        let searchUserFilter: ISearchUserFilter = {
            username: this.userDetails.userId,
            repositoryEnum: [EXTERNALUSER.REPOSITORYENUM]
        };

        let userSearchPayload: IExternalUserSearch = {
            requestContext: requestContext,
            searchUserFilter: searchUserFilter
        }

        this.soaService.searchUser(soaToken, userSearchPayload).subscribe((data: any) => {

            if (data) {
                this.emailAddress = data.user[0].emailAddress;
                let sendOtp: IExternalSendOTP = {
                    channel: externalAuthenticationConstants.EMAIL
                }

                this.mfaService.sendOTP(sendOtp, this.userDetails.userId).subscribe((otpData: any) => {
                    this.pingDeviceId = otpData.pingDeviceId;
                    this.pingUserId = otpData.pingUserId;
                    this.isLoading = false;
                    this.isOtpScreen = true;
                    this.errorMsg = "Verify your identity by entering OTP sent to your email.";
                });
            }
        }, (error: any) => {
            this.isLoading = false;
        })
    }

    /**
     * Login Threat Endpoint Check
     * @param loginThreat 
     */
    loginThreat(loginThreat: IExternalLoginThreat) {
        /* this.mfaService.userIpAddress().subscribe((ipAddress: string) => {
            this.mfaService.loginThreat(loginThreat, this.mfaToken, this.userDetails.userId, ipAddress).subscribe((loginThreatData: any) => {
                if (loginThreatData) {
                    if (loginThreatData.status == externalAuthenticationConstants.CONTINUE) {
                        this.navigateBasedOnFirstLogin();
                    } else if (loginThreatData.status == externalAuthenticationConstants.TWOFACTOR) {
                        // navigate to home screen
                        this.pingRiskId = loginThreatData.pingRiskId;
                        this.twoFactor(this.soaToken, this.mfaToken);
                    }
                }
            }, error => {
                this.isLoading = false;
            })
        }); */

        this.mfaService.externalLoginThreatCall(loginThreat, this.userDetails.userId).subscribe((loginThreatData: any) => {
            if (loginThreatData) {
                if (loginThreatData.status && loginThreatData.status == externalAuthenticationConstants.CONTINUE) {
                    //this.navigateBasedOnFirstLogin();
                    this.callMFACookie(loginThreatData.firstTime);
                } else if (loginThreatData.pingDeviceId) {
                    // navigate to home screen
                    this.pingRiskId = loginThreatData.pingRiskId;
                    this.pingDeviceId = loginThreatData.pingDeviceId;
                    this.pingUserId = loginThreatData.pingUserId;
                    this.isLoading = false;
                    this.isOtpScreen = true;
                    this.errorMsg = "Verify your identity by entering OTP sent to your email.";
                }
            }
        }, error => {
            this.isLoading = false;
        })
    }

    /**
     * Navigate to Landing Screen
     */
    navigateToLanding() {
        this.isLoading = false;
        let timeStamp = new Date((new Date()).getTime() + 30 * 60000); //// timeStamp with 30 mins added
        let unixTimeStamp = Math.floor(timeStamp.getTime() / 1000);
        this.cookieService.set(ROUTING_LABELS.USER_ID, this.userDetails.userId);
        this.authService.getExternalUserProfile(this.userDetails.userId, unixTimeStamp.toString()).subscribe(data => {
            this.isLoading = false;
            this.authService.setCurrentUserName(this.userDetails.userId ?? '');
            this.cookie.set(ROUTING_LABELS.CAD_ACCESS, 'true');
            localStorage.setItem('verifyAuth', 'true');
            this.router.navigate(['']);
        }, error => {
            this.isLoading = false;
            //// User not found in our CAD DB
        });
    }

    /**
     * Navigate to Landing Screen if user is not first time user else navigate to change screen
     */
    navigateBasedOnFirstLogin(firstTimeLogin) {
        //this.soaService.getExternalUserLoginDetails(this.userDetails.userId).subscribe(data => {
        if (firstTimeLogin) {
            this.isLoading = false;
            //this.soaService.setLoginDetailsSubscription({ userData: data });
            this.router.navigate([externalAuthenticationConstants.CHANGE_PASSWORD_NAVIGATION]);
        }
        else if (this.generatedCode) {
            this.isLoading = false;
            this.router.navigate([externalAuthenticationConstants.CHANGE_PASSWORD_NAVIGATION], { queryParams: { FromSignin: 'yes' } });
        }
        else {
            //this.navigateToLanding();
            this.isLoading = false;
            let id_token = sessionStorage.getItem(TOKEN_INFO.ID_TOKEN);
            if (id_token == null && !window.location.href.includes(AUTH_CONFIG.CALLBACK)) {
                this.authService.singleSignOn();
            }
            else {
                this.router.navigate(['']);
            }
        }
        //})
    }
}
