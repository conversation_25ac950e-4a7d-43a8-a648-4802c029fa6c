import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-type-outcome',
  templateUrl: './type-outcome.component.html',
  styleUrls: ['./type-outcome.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class TypeOutcomeComponent implements OnInit {

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  public headerText = "Add New Rule Type";
  public isPriviousRedirectPage = true;
  dataURL: string = "./assets/json/tableOutcome.json";
  dataRoot: string = "src";

  columnConfigInlineEdit: any = {

    "switches": {
      "enableSorting": true,
      "enablePagination": true,
      "editable": true,
      "enableFiltering": true
    },
    "colDefs": [
      {
        "name": "Field Name",
        "field": "Org Name",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Single Select",
        "editorTypeRoot": "duplicateTitleSrc",
        "editorTypeLabel": "label",
        "editorTypeValue": "id",
        "id": "dup-name"
      },
      {
        "name": "Static",
        "field": "title",
        "filterType": "Text",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "customFormatter": this.customFormatterSwitch
      },
      {
        "name": "Value",
        "field": "percentComplete",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
    ]
  };

  rendererTableClicked(event: Event) {

  }

  customFormatterSwitch(event) {
    let toggleSwitch;
    toggleSwitch = "<div class='custom-control custom-switch'><input type='checkbox' class='custom-control-input' id='customSwitches'><label class='custom-control-label' for='customSwitches'></label></div>";
    return toggleSwitch;
  }

  tableReady(event) {
    console.log("Captured Table Ready event:::", event);
  }

  OnSubmit() {
    this.router.navigate(['product-catalog/rules/rule-type']);
  }

  OnCancel() {
    this.router.navigate(['product-catalog/rules/rule-type/details']);
  }

}






