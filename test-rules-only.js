// Simple test runner for rules folder only
const { execSync } = require('child_process');

try {
  console.log('Running tests for rules folder only...');
  
  // Run ng test with specific pattern for rules folder
  const result = execSync('npx ng test --include="src/app/rules/**/*.spec.ts" --watch=false --code-coverage --browsers=ChromeHeadless', {
    stdio: 'inherit',
    cwd: process.cwd()
  });
  
  console.log('Tests completed successfully!');
} catch (error) {
  console.error('Test execution failed:', error.message);
  process.exit(1);
}
