import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProductListComponent } from './product-list/product-list.component';
import { BundleListComponent } from './bundle-list/bundle-list.component';
import { AddBundleComponent } from './add-bundle/add-bundle.component';
import { EditBundleComponent } from './edit-bundle/edit-bundle.component';
import { ViewBundleComponent } from './view-bundle/view-bundle.component';
import { PermissionGuard } from 'src/app/_helpers/permission.guard';
const routes: Routes = [
  { path: '', component: ProductListComponent},
  { path: 'bundle/:id', component: BundleListComponent},
  { path: 'add-bundle', component: AddBundleComponent,canActivate:[PermissionGuard]},
  { path: 'add-bundle/:productId', component: AddBundleComponent,canActivate:[PermissionGuard]},
  { path: 'edit-bundle/:productId/:id', component: EditBundleComponent,canActivate:[PermissionGuard]},
  { path: 'view-bundle/:productId/:id', component: ViewBundleComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ProductRoutingModule { }