import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ClientPreferenceListComponent } from './client-preference-list/client-preference-list.component';
import { ClientListComponent } from './client-list/client-list.component';
import { ClientProductListComponent } from './client-product-list/client-product-list.component';
import { ProductBundleFeeEditComponent } from './product-bundle-fee-edit/product-bundle-fee-edit.component';
import { AuthGuard } from 'src/app/_helpers/auth.guard';
import { PermissionGuard } from 'src/app/_helpers/permission.guard';

const routes: Routes = [
  { path: '', component: ClientListComponent,canActivate:[AuthGuard]},
  { path: 'product/:clientId/:clientName', component: ClientProductListComponent,canActivate:[AuthGuard]},
  { path: 'edit-fee/:bundleId', component: ProductBundleFeeEditComponent,canActivate:[AuthGuard]},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ClientPreferenceCenterRoutingModule { }
