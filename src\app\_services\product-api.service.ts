import { Injectable } from '@angular/core';
import { of, Observable, throwError } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { catchError, map, switchMap } from 'rxjs/operators';
/**Merge */
//import { ENDPOINTS } from '../../content-management/scheduler/shared/_constants/endpoints-constants';

export interface IProductInventory {
  productId?: number,
  invTypeIds?: number[],
  createdByUserId?: string,
  lastUpdatedByUserId?: string
}

@Injectable({
  providedIn: 'root'
})
export class ProductApiService {
  sessionToken: string;
  public productName: any;
  public prodIdBundles: any;
  constructor(private http: HttpClient) { }
  /**
   * function to get product name from product list screen to bundle list screen
   */
  getProductName(pname: any) {
    this.productName = pname['productName'];
    this.prodIdBundles = pname['prodId'];
  }
  /**
   * function to get all the concepts id and description
   */
  getProductConceptsId(auth_token) {

    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `${auth_token}`
    });
    let options = { headers: headers };
    return this.http.get<any>(`${environment.validatorSvc}/ecmProxy/analyticconcept/fetchexecutionconcepts/10`, options);
    /**Merge */
    //return this.http.get<any>(`${environment.schedulerECMLibraryUrl}${ENDPOINTS.SCHEDULE_CONCEPT_LIST_ENDPOINT}`, options);
  }

  /**
   * Method to get all columnConfigs for product table
   * @returns 
   */
  getTableColumnForBundleList(url): Observable<any> {
    return this.http.get(url);
  }

  /**
   * Method to get all columnConfigs for product table
   * @returns 
   */
  getTableColumn(url): Observable<any> {
    return this.http.get(url);
  }

  /**
   * Method to get all the Products List
   * @returns 
   */
  getProductDetails(): Observable<any> {
    return this.http.get<any>(`${environment.productDomainUrl}/products`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method to get all the Bundles List for particular product id
   * @returns 
   */
  getProductBundleDetails(prodIdBund: any) {
    return this.http.get<any>(`${environment.productDomainUrl}/productBundle/getBundles/${prodIdBund}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method to get all the Bundles List for particular product id
   * @returns 
   */
  getparticularBundleDetails(bundleId: any) {
    return this.http.get<any>(`${environment.productDomainUrl}/productBundle/getbundle/${bundleId}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method for Adding a New Bundle
   */
  addandEditProductBundle(createResource): Observable<any> {
    return this.http.post<any>(`${environment.productDomainUrl}/productBundle/saveOrEditProductBundle`, createResource);
  }

  /**
   * Method to get all the Inventory List
   * @returns 
   */
  getInventoryList(): Observable<any> {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/inventory/inventoryTypes/list`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method to get Inventory List for product id
   * @returns 
   */
  getInventoryTypeByProduct(productId: number) {
    return this.http.get<any>(`${environment.productDomainUrl}/productInventoryType/getByProductId/${productId}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method for Adding a New Bundle
   */
  saveProductInventory(inventory: IProductInventory): Observable<any> {
    return this.http.post<any>(`${environment.productDomainUrl}/productInventoryType/save`, inventory);
  }

}
