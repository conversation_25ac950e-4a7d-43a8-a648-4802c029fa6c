.form-container{
    margin: 0 1.5rem;
    .page-header {
        h3 {
          color: #000000;
          font-family: "elevance-medium";
          font-weight: bold;
          font-size: 28px;
        }
    }
    
      .card-container {
        margin-top: 2rem;
        margin-bottom: 1rem;
        background: #ffffff;
        width: 100%;
        border-radius: 0.5rem;
        padding: 0.75rem;
        box-shadow: 0px 2px 2px 0px rgba(10, 18, 30, 0.12),
          0px 0px 2px 0px rgba(10, 18, 30, 0.16);

        display: flex;
        justify-content: space-between;
        align-items: center;

        .dynamicForm{
            width: 90%;
        }

        .performAnalysisBtn{
            margin-top: 27px;
        }
      }

      .backdrop {
        position: fixed;
        top: 11%;
        left: 20%;
        width: 100vw;
        height: 100vh;
        z-index: 999;
        background-color: rgb(0, 0, 0, 0.2);
      }

      .spinner-border {
        display: block;
        position: fixed;
        top: calc(50% - (58px / 2));
        right: calc(40% - (58px / 2));
        width: 5rem;
        height: 5rem;
      }

      .impact-report-card {
         
        ::ng-deep marketplace-cards .cards__holder.asTile .card_list_holder { 
          width: 50%;
          display: flex;
          justify-content: flex-start;
          margin-left: -30px;
                
          
        }  
        
        ::ng-deep marketplace-cards .cards__holder.asTile .tiles section {
          width: 50%;
          height: 100px;
          min-width: 28%;
          background: #F5F5F5;
          border-radius: 10px;
          margin-right: 20px;          
        }
        ::ng-deep marketplace-cards .cards__holder.asTile .tiles {
          display: inline-flex;      
          flex-direction: row;
          justify-content: center;
          align-items: center;
          flex-wrap: nowrap;
          flex-shrink: 0;
          margin-right: 0px;
          width: 70%;
          height: 30%;
        }
        ::ng-deep marketplace-cards .cards__holder.asTile .tiles section h4{
          height: 0;
          margin: 0;
          margin-top: 18px;
          font-family: "elevance-medium";
          font-weight: bold;
          font-size: 20px;
          text-align: left;
          margin-left: 20px;
        }
        ::ng-deep marketplace-cards .cards__holder.asTile .tiles section footer .stat .count{
          color: black !important;                    
          font-size: 20px;
          margin-top: 15px;          
          margin-left: 5px;

        }

        ::ng-deep marketplace-cards .cards__holder.asTile .tiles section footer .stat {
          display: flex;      ;
          justify-content: left;
          font-size: 1.1rem;
          font-family: "Lato";
          color: #000;
        
      }

      
          
      

      .brdcrumb{
        font-weight: bold;
        font-size: 13px; 

        .brdcrumbSymbol{
            margin-right: 10px;
        }
      }
}
}