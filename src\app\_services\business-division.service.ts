import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

const BusinessUnits = [
    { name: 'GBD', id: 'GBD' },
    { name: 'CSBD', id: 'CSBD' }
]
const CAD = "Data Mining Solution";
const RULES = "Rules";

@Injectable({
    providedIn: 'root'
})
export class BusinessDivisionService {
    userBUs: any = [BusinessUnits[1]];
    public showBusinessDivisionPopup = new Subject<boolean>();
    // Subject to store business division details
    public businessDivisionDetails: string = "CSBD";
    public selectedBusinessUnit: string = null;
    public cardSelectionEvent: any;
    public isNavLinkSelected: boolean = false;
    public selectedLabel: any;
    public screenAccess_Cards: any;




    constructor() { }

    /**
    * This method will show business division popup wherever called
    * @param showBusinessDivision 
    */
    showBusinessDivision(inputLabel: string) {
        if (inputLabel == RULES || this.isCadScreen(inputLabel)) {
            this.showBusinessDivisionPopup.next(true);
        }
    }

    /**
    * This method return the label from screen access cards
    * @param isCadScreen 
    */
    isCadScreen(inputLabel: string) {
        return this.screenAccess_Cards.find(c => c.label == inputLabel || c.subMenu.find(d => d.label == inputLabel))?.label == CAD
    }

    /**
     * This method processes BUs associated with the user
     * @param roleDetails 
     */
    processBU(roleDetails) {
        let userBUsFromRoleDetails = [];
        roleDetails.forEach((role) => {
            if (role[2] != null) {
                userBUsFromRoleDetails.push({ name: role[2], id: role[2] });
            }
        });
        if (userBUsFromRoleDetails.length > 0) {
            this.userBUs = userBUsFromRoleDetails.filter((item, index) => {
                return index === userBUsFromRoleDetails.findIndex(o => item.name === o.name)
            });
        }
    }

    /**
     * this method is used to send the selected business division wherever required in files
     * @param getBusinessDivision 
     */
    getBusinessDivision() {
        return this.selectedBusinessUnit ?? this.businessDivisionDetails;
    }


}
