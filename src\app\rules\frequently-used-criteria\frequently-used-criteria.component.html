<div class="breadcrumb-container">
  <app-breadcrumbs-nav
  [headerText]="headerText"
  [isPriviousRedirectPage]="isPriviousRedirectPage"
  [breadcrumbDataset]="breadcrumbDataset"
>
</app-breadcrumbs-nav></div>
<div class="table-title-container">
  <span class="table-title">List of Criterias</span>
  <span class="btn-span">
    <marketplace-button
      [label]="'Add New Criteria'"
      [type]="'primary'"
      [name]="'primary'"
      (onclick)="addNewCriteriaOnClick()"
    >
    </marketplace-button>
  </span>
</div>
<div class="card-body">
  <div class="rule-table">
    <marketplace-table
      [id]="'example-static-table'"
      [dataset]="dataJSON"
      [columnDefinitions]="columnConfig"
      [rowHeight]="ruleDashbordTableRowhg"
      [headerRowHeight]="ruleDashbordTableHeaderhg"
      [columnDefinitions]="columnConfig"
      [dropdownOptions]="kebabOptions"
      [isRowSelectable]="false"
      (onCellValueChange)="cellValueChanged($event)"
      (onCellClick)="cellClicked($event)"
      (onTableReady)="tableReady($event)"
      (onDropdownOptionsClick)="moveToOptionSelected($event)"
      *ngIf="getTableData"
    >
    </marketplace-table>


  </div>
</div>
