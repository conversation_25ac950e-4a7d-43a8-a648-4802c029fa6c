import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { of, throwError } from 'rxjs';
import { AuthService } from './_services/authentication.services';
import { environment } from '../environments/environment';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';


export class MockBnNgIdleService {
  startWatching(seconds: number) {
    return of(true);
  }
  stopTimer() {
  }
}

 export class MockAuthService {
  logout() {
    return of({ message: 'logout' })
  }
  singleSignOn() {
  }
}

describe('AppComponent', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;
  let mockAuthService: any;
  let mockCookieService: CookieService;
  let mockRouter: any;
  let cookieServiceMock: jasmine.SpyObj<CookieService>;
  let authServiceMock: jasmine.SpyObj<AuthService>;

  beforeEach(() => {
    mockAuthService = {
      logoutPortal: jasmine.createSpy('logoutPortal').and.returnValue(of({})),
      singleSignOn: jasmine.createSpy('singleSignOn').and.returnValue(of({})),
      isLogin: true,
      isTimedout: true
    };

    mockRouter = {
      navigate: jasmine.createSpy('navigate'),
    };

    
    const cookieServiceSpy = jasmine.createSpyObj('CookieService', ['get', 'deleteAll']);
    // Mock sessionStorage
    spyOn(sessionStorage, 'clear');

    TestBed.configureTestingModule({
      imports: [AppComponent],
      providers: [
        { provide: AuthService, useValue: mockAuthService },
        { provide: CookieService},
        { provide: Router, useValue: mockRouter },
      ]
    })
      .compileComponents();



    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    mockCookieService = TestBed.inject(CookieService);
    authServiceMock = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    //cookieServiceMock = TestBed.inject(CookieService) as jasmine.SpyObj<CookieService>;
    fixture.detectChanges();
  });

  it('should create app component', () => {
    expect(component).toBeTruthy();
  });

  it('on screen selection DMS', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Data Mining Solution'
      }
    }
    component.onScreenSelection(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`https://ui.cad.pi.${environment.name}.gcpdns.internal.das`)
  });

  it('on screen selection Portal/Default', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Portal'
      }
    }
    component.onScreenSelection(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`https://ui.portal-card.pi.${environment.name}.gcpdns.internal.das`)
  });

  it('on screen selection COB', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Coordination of Benefits'
      }
    }
    component.onScreenSelection(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`https://ui.cob.pi.${environment.name}.gcpdns.internal.das`)
  });

  it('on screen selection SIU', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Special Investigations Unit'
      }
    }
    component.onScreenSelection(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`https://ui.siu.pi.${environment.name}.gcpdns.internal.das`)
  });

  it('on screen selection CCA', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Complex Clinical Audit'
      }
    }
    component.onScreenSelection(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`https://ui.cca.pi.${environment.name}.gcpdns.internal.das`)
  });

  it('on quick link selection Rules', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Rules'
      }
    }
    component.quickLinkClick(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}rules`, "_self")
  });

  it('on quick link selection External User Registration', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'External User Registration'
      }
    }
    component.quickLinkClick(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}registration`, "_self")
  });

  it('on quick link selection Help Center', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Help Center'
      }
    }
    component.quickLinkClick(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}help-center`, "_self")
  });

  it('on quick link selection Settings', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Settings'
      }
    }
    component.quickLinkClick(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}settings`, "_self")
  });

  it('on quick link selection Clients', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Clients'
      }
    }
    component.quickLinkClick(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}clients`, "_self")
  });

  it('on quick link selection Users', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Users'
      }
    }
    component.quickLinkClick(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}users`, "_self")
  });

  it('on quick link selection Default', () => {
    spyOn(window, 'open');
    let selectedEvent = {
      selected: {
        label: 'Default'
      }
    }
    component.quickLinkClick(selectedEvent);
    expect(window.open).toHaveBeenCalledWith(`${environment.commonServicesUrl}`, "_self")
  });

  it('on overlay click', () => {
    let ovelayEvent;
    mockCookieService.set('LandingScreenDetails', `{"cards":[{"label":"Coordination of Benefits","name":"Coordination of Benefits","value":"<img src='./assets/icons/landing-screen/COB-icon.svg'>"},{"label":"Data Mining Solution","name":"Data Mining Solution","value":"<img src='./assets/icons/landing-screen/DMO-icon.svg'>"},{"label":"PORTAL","name":"PORTAL","value":"<img src='./assets/icons/landing-screen/PORTAL-icon.svg'>"},{"label":"Special Investigations Unit","name":"Special Investigations Unit","value":"<img src='./assets/icons/landing-screen/SIU-icon.svg'>"},{"label":"Complex Clinical Audit","name":"Complex Clinical Audit","value":"<img src='./assets/icons/landing-screen/CCA-icon.svg'>"}],"quickLinks":[{"label":"Clients","subMenu":[{"label":"Data Exchange","permission":"create,read,delete,update"},{"label":"Client Preference Center","permission":"read,update,delete,create"},{"label":"Setup","permission":"create,read,delete,update"}]},{"label":"Settings","subMenu":[{"label":"System","permission":"read,update,delete,create"},{"label":"Standard View","permission":"read,update,create,delete"},{"label":"Roles","permission":"read,update,delete,create"},{"label":"Product","permission":"read,update,delete,create"}]},{"label":"Rules","subMenu":[{"label":"Rule Management","permission":"create,read,update,delete"},{"label":"Rules Types","permission":"create,read,update,delete"}]},{"label":"External User Registration","subMenu":[{"label":"External User Registration","permission":"create,read,delete,update"}]},{"label":"Users","subMenu":[{"label":"Users","permission":"create,read,delete,update"}]}]}`)
    component._onOverlayClick()

    expect(component._onOverlayClick()).toBe();
  });


  it('on logout click', () => {
    let logOutEvent;
    expect(component._onLogoutClick()).toBe();
  })

  it('on handle session timeout ', () => {
    expect(component.handleSessionTimeout()).toBe();
  })

  it('should execute the error path in _onLogoutClick', () => {
    mockAuthService.logoutPortal = jasmine.createSpy('logoutPortal').and.returnValue(throwError(() => new Error('Error'))),
    component._onLogoutClick();
    expect(mockAuthService.logoutPortal).toHaveBeenCalled();

    component.handleSessionTimeout();
    expect(mockAuthService.logoutPortal).toHaveBeenCalled();
  });
});