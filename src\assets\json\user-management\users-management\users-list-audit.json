{"columnConfig": {"switches": {"enableSorting": true, "enablePagination": false, "editable": false, "enableFiltering": true}, "colDefs": [{"name": "", "field": "userId", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Basic Info", "field": "form", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Skills", "field": "skill", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Team", "field": "team", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Modified By", "field": "modified_by", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Modified Date", "field": "modified_date", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Action", "field": "actionView", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}]}, "dataset": [{"userId": "User_1", "form": false, "skill": true, "team": false, "modified_by": "AH1234", "modified_date": "10/12/2022 12:00:00", "Market Skills": "Alabama", "Platform Skills": "FACETS", "Concept Skills": "C11-S284-101-A-X1", "Intake_Source": "Special Projects", "Funding_Model": "ASO", "lob_skills": "Medicare", "Status": "Active", "clientSite": "Offshore"}, {"userId": "User_2", "form": true, "skill": false, "team": true, "modified_by": "AH2345", "modified_date": "12/12/2022 07:24:20", "role_type": "Role Type 2", "group": "Group 2", "Status": "Active", "system": "CCERT", "client": "Anthem,Carelon", "role": "Role 1", "experienceLevel": "2", "product": "Product 2", "groupName": "Group_2", "reminderDate": "11/11/2022", "managerId": "<PERSON><PERSON><PERSON>, Sriram (AH2346)", "blueCard": "Home ITS", "comments": "Home ITS team provides access to the selected users", "clientSite": "Offshore"}, {"userId": "User_3", "form": true, "skill": true, "team": true, "Status": "Active", "modified_by": "AH2345", "modified_date": "22/12/2022 09:02:19", "Market Skills": "", "Platform Skills": "FACETS", "Concept Skills": "", "Intake_Source": "Special Projects", "Funding_Model": "ASO", "lob_skills": "Medicare", "role_type": "Role Type 3", "group": "Super Admin", "system": "CCERT", "client": "Carelon", "role": "Role 3", "experienceLevel": "3", "product": "Product 1", "groupName": "Group_3", "reminderDate": "12/11/2022", "managerId": "<PERSON><PERSON>, <PERSON>(AL45678)", "blueCard": "Host", "comments": "Super Admin team provides access to the selected users", "clientSite": "Offshore"}, {"userId": "User_4", "form": false, "skill": false, "team": true, "Status": "Active", "modified_by": "AH1234", "modified_date": "26/12/2022 10:00:00", "reminderDate": "20/12/2022", "managerId": "<PERSON><PERSON>, <PERSON>(AL23678)", "blueCard": "All", "comments": "Provides access to the selected users", "clientSite": "Offshore"}]}