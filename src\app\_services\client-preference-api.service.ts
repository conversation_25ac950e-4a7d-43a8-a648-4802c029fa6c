import { Injectable } from '@angular/core';
import { of, Observable, throwError, forkJoin } from 'rxjs';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { catchError, switchMap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ClientApiService {
  public tabOptions: any = { 'View Products': 0, 'View Fee Schedule': 1, 'View Data Exchange': 2, "View File Exchange": 3, 'View Sample Validation Percentage': 4, 'View Tenant': 5 };
  public selectedTabOption: string = '';
  public selectedProductName = '';
  public selectedProductId = '';
  public selectedTenantId: number;
  constructor(private http: HttpClient) { }

  /**
   * returns all the File templates
   */
  getAllFileTemplates(): Observable<any> {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/inventory/inventoryTypes/list`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * calls get client products API 
   */
  getClientProducts(id: any): Observable<any> {
    return this.http.get<any>(`${environment.productDomainUrl}/client/clientproductList/${id}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }
  /**
   * calls get all tenants under the client using getAllTenant API 
   */
  getAllTenantsByClientId(clientId: any) {
    return this.http.get<any>(`${environment.clientPreferenceDomainUrl}/tenant/getallbyclientid/${clientId}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method to get any json's from the assets
   * @returns 
   */
  getTenantsAssetsJson(url): Observable<any> {
    return this.http.get<any>(url).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  getTenantDetailsById(id: any) {
    let url = `${environment.clientPreferenceDomainUrl}/tenant/getbyid/${id}`;
    return this.http.get<any>(url).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  addEditTenantData(payload: any) {
    return this.http.post(`${environment.clientPreferenceDomainUrl}/tenant/save`, payload).pipe(
      catchError(err => {
        return throwError(err);
      }));;
  }


  /**
   * Method to get all columnConfigs for client table
   * @returns 
   */
  getTableColumnforClientsTable(url): Observable<any> {
    return this.http.get(url);
  }
  /**
   * Method to get all fields for Add product Modal
   * @returns 
   */
  getproductFormJSON(url): Observable<any> {
    return this.http.get(url);
  }
  /**
   * method to get Inventory type Id from Inventory types API
   */
  getInventoryTypeId(prodId): Observable<any> {
    return this.http.get(`${environment.productDomainUrl}/productInventoryType/getByProductId/${prodId}`);
  }

  /**
   * method to update Product details
   */
  saveProduct(productObj: any): Observable<any> {
    return this.http.post(`${environment.productDomainUrl}/client/clntProdInvDetails/save`, productObj).pipe(
      catchError(err => {
        return throwError(err);
      }));;
  }

  /**
   * calls get client details API 
   */
  getClientDetails(): Observable<any> {
    return this.http.get<any>(`${environment.productDomainUrl}/client/allclients`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * calls get Prioritization Details API 
   */
  getPrioritizationDetails(clientId, productId, conceptId): Observable<any> {
    let headers = {
      'x-api-key': "fb5b6828-a0a6-4e90-be31-de48ce4fba3f"
    };
    return this.http.get<any>(`${environment.rulesDomainUrl}/hierarchy_weights?clientId=${clientId}&productId=${productId}&conceptId=${conceptId}`, { headers }).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * calls post Prioritization Details API 
   */
  savePrioritizationDetails(modifiedPrioritizationData: any): Observable<any> {
    let headers = {
      'x-api-key': "202b6f16-a66c-4d63-ad1f-98f24d678abb"
    };
    return this.http.post(`${environment.rulesDomainUrl}/hierarchy_weights/save/`, modifiedPrioritizationData, { headers }).pipe(
      catchError(err => {
        return throwError(err);
      }));;
  }

  /**
   * Method to add Role 
   */
  addUpdateRoleData(modifiedPrioritizationData: any): Observable<any> {
    return this.http.post(`${environment.authService}/api/v1/roles/addEditRole`, modifiedPrioritizationData).pipe(
      catchError(err => {
        return throwError(err);
      }));;
  }

  /**
   * calls get product bundle and fee details API 
   */
  getProductBundleFeeSchedules(client: any, product?: any): Observable<any> {

    const queryParams = {};
    queryParams['clientId'] = client;
    if (product) queryParams['productId'] = product;


    return this.http.get<any>(`${environment.productDomainUrl}/client/clientbundlefeesetup`, { params: queryParams }).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * get data Exchange based on client and product
   * @param url clientid/:productName?
   */
  getClientPreferences(url): Observable<any> {
    return this.http.get<any>(`${environment.clientPreferenceDomainUrl}/clientPreference/getDataExchange/${url}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   *  get data Exchange data in client preferences screen based on product
   */
  getClientPreferencesViewDataExchange(prefernceId: any): Observable<any> {
    return this.http.get<any>(`${environment.clientPreferenceDomainUrl}/clientPreference/ViewDataExchange/${prefernceId}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * returns selected tab
   */
  getSelectedTabOption(): Observable<any> {
    return this.tabOptions[this.selectedTabOption];
  }

  /**
   * sets the selected tab 
   */
  setSelectedTabOption(tabOption) {
    this.selectedTabOption = tabOption;
  }

  /**
   * returns last selectd tab option 
   */
  getTabOptionSelected(option): Observable<any> {
    return this.tabOptions[option];
  }

  /**
      *  get the data for recent file list table  in client preferance screen
      */
  getClientFileHistory(exId: any): Observable<any> {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/clientacknowledgement/files/` + exId).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
  *  get data Execution Ids and Concept Ids based on client Id
  */
  getConceptExecutionIds(clientSelected: any, clientName: any) {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/getConceptExecutionids/` + clientSelected + '/' + clientName).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
*  get data Prod Execution Ids and Concept Ids based on client Id
*/
  getConceptProdExecutionIds(clientSelected: any) {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/invinsight/getConceptProdExecutionids/` + clientSelected).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }


  /**
   * forks all the master data resultSet and retuens the single result set
   * @param clientId client id  based PRoducts
   */
  getMasterData(clientId: any): Observable<any> {
    let products = this.getClientProducts(clientId);
    let templates = this.getAllTemplates();
    let dbgUnits = this.getAllDBGUnits();
    let cff = this.getCffFields();
    return forkJoin([products, templates, dbgUnits, cff]);
  }

  /**
   * returns all the products 
   */
  getAllProducts(): Observable<any> {
    return this.http.get<any>(`${environment.productDomainUrl}/products`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

    /**
   * returns all the templates
   */
  getAllTemplates(): Observable<any> {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/filetemplate/list`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }


  /**
   * returns all the systems with correcsponding inventory types 
   */
  getSystemsAndInventoryTypes(): Observable<any> {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/system/list`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * get all DBG Units
   */
  getAllDBGUnits(): Observable<any> {
    return this.http.get<any>(`${environment.productDomainUrl}/DbgUnit/list`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * get all CFF field in query builder
   */
  getCffFields(): Observable<any> {
    return this.http.get<any>(`${environment.clientPreferenceDomainUrl}/clientPreference/getMasterColumns`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * get CFF field By Inventory type and product in query builder
   */
  getCffFieldsByInventoryTypeProduct(invType, prodName): Observable<any> {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/filetemplate/getMasterColumnDetails?invType=${invType}&prodName=${prodName}`,).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }
  /**
   * post DataExChange model data for both add and Edit 
   */
  createEditAddDE(dataExcangeObject: any): Observable<any> {

    return this.http.post(`${environment.clientPreferenceDomainUrl}/clientPreference/addDataExchange`, dataExcangeObject).pipe(
      catchError(err => {
        return throwError(err);
      }));;
  }
  /**
   * get Bundle list based on Product id
   * @param id 
   */
  getBundleList(id: any): Observable<any> {
    return this.http.get<any>(`${environment.productDomainUrl}/productBundle/getBundles/${id}`).pipe(
      switchMap((data: any) => {
        return of(data);
      }),
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
 * get Active Bundle list based on Product id
 * @param id 
 */
  getActiveBundleList(id: any): Observable<any> {
    return this.http.get<any>(`${environment.productDomainUrl}/productBundle/getActiveBundles/${id}`).pipe(
      switchMap((data: any) => {
        return of(data);
      }),
      catchError(err => {
        return throwError(err);
      }));
  }
  /**
   * save fee record based on client id 
   * @param id -client id
   * @param productBundle - product
   */
  saveClient(id: any, productBundle): Observable<any> {
    return this.http.post(`${environment.productDomainUrl}/client/saveclientfee/${id}`, productBundle).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }
  /**
   * edit record
   * @param productBundle - product
   */
  editClient(productBundle): Observable<any> {
    return this.http.post(`${environment.productDomainUrl}/client/editclientfee `, productBundle).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }
  /**
   * fee details 
   * fetch fee Type and Fee Methods
   */
  feeDetailsMasterData(): Observable<any> {
    return this.http.get(`${environment.productDomainUrl}/client/getFeeTypeAndFeeMethod`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Gets the data for all the organization to populate in extenal registration form from product domain API
   */
  getOrganizationList() {
    return this.http.get<any>(`${environment.productDomainUrl}/client/allorgs`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
    * Gets the data for all the clients from production domain API
    */
  getAllClientsInPreferenceCenter() {
    return this.http.get<any>(`${environment.productDomainUrl}/client/allclients`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
  * Gets the data for all the clients Master data 
  */
  getAllClientsMasterData() {
    return this.http.get<any>(`${environment.productDomainUrl}/client/allmasterclients`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
  * @function getTableColumn Gets config for columns in table
  * @param url 
  */
  getTableColumn(url): Observable<any> {
    return this.http.get(url);
  }

  /**
   * post DataExChange model data for both add and Edit 
   */
  editOnshoreOfshoreFields(changedFieldData: any): Observable<any> {
    return this.http.post(`${environment.productDomainUrl}/client/updateClientDetails`, changedFieldData).pipe(
      catchError(err => {
        return throwError(err);
      }));;
  }

  getFileReportDetails(filename: any, clientName: any): Observable<any> {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/clientacknowledgement/filereportdetail/${filename}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }




  getFileTimeLineDetails(filename: any, clientName: any) {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/clientacknowledgement/files/timeline/${filename}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }


  /**
     * Gets the data for recent file list table for all the cards from clientacknowledgement API
     */
  getRecentFileListDetails(payload): Observable<any> {
    let url = environment.validatorSvc + '/invProxy/clientacknowledgement/ackInsightDetails?fileName=' + payload.fileName + '&exceID=' + payload.exceID + '&status=' + payload.status;
    return this.http.get(url).pipe(
      catchError(err => of(err.error))
    );
  }


  /**
   * Get Insight Details
   * @returns 
   */
  getInsightDetails(exeId, selectedYear): Observable<any> {
    let url = environment.validatorSvc + `/invProxy/clientacknowledgement/viewInsightdetails/${exeId}/${selectedYear}`;
    return this.http.get(url).pipe(
      catchError(err => of(err.error))
    );
  }

  /**
   * Get Client Target Hit Rate
   * @returns target hit rate for particular client ID
   */
  getClientTargetHitRate(clientId): Observable<any> {
    let url = environment.validatorSvc + `/invProxy/hitrate/trgt-hitrt-by-clnt/${clientId}`;
    return this.http.get(url).pipe(
      catchError(err => of(err.error))
    );
  }

  /**
   * post Client Target Hit Rate to client id
   */
  editClientTargetHitRate(clientId, targetHitRate): Observable<any> {
    let url = environment.validatorSvc + `/invProxy/hitrate/client-target-hitrate`;
    return this.http.post(url, { clientId, targetHitRate }).pipe(
      catchError(err => {
        return throwError(err);
      }));;
  }

  /**
   * Get Target Hit Rate by concept ID
   * @returns target hit rate for particular concept ID
   */
  getHitRateByConceptID(cncptId, clntId, prodId): Observable<any> {
    let url = environment.validatorSvc + `/invProxy/hitrate/trgt-hitrt-by-cnct-id`;
    return this.http.post(url, { cncptId, clntId, prodId }).pipe(
      catchError(err => of(err.error))
    );
  }

  /**
   * post Target Hit Rate to concept ID
   */
  editHitRateByConceptID(cncptId, concptNm, clientId, prodId, targetHitRate, clientName): Observable<any> {
    let url = environment.validatorSvc + `/invProxy/hitrate/insert-hit-rate-conceptid`;
    return this.http.post(url, { cncptId, concptNm, clientId, prodId, targetHitRate, clientName }).pipe(
      catchError(err => {
        return throwError(err);
      }));;
  }
}
