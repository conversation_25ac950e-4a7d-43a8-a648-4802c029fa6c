import { Component, OnInit } from '@angular/core';
import { TEMPLATE_COLUMNS, File_COLUMNS } from '../file.constants';
import { Router } from '@angular/router';
import {has,get} from 'lodash';

@Component({
  selector: 'app-import-template',
  templateUrl: './import-template.component.html',
  styleUrls: ['./import-template.component.css'],
})
export class ImportTemplateComponent implements OnInit {
  currentStep = false;
  constructor(private router: Router) { }
  templateDataJSON = [];
  templateSelected:any;
  templateColumnConfigFilters: any = File_COLUMNS;
  notificationHeader :string ="";
  notificationBody :string ="FIle Imported Successfully";
  notificationPosition  :any = 'bottom-center';
  notificationDuration  :number=-1;
  notificationType:any = 'warning';
  notificationOpen:string = "false";
  open = false; 
  currentTemplate:any;
  currentTemplateData:any={
    prefix:"",
    sufix:"",
    fileType:"",
    system:"",
    Created:"",
    CreatedBy:"",
    activeStatus:false
  }
  public schedulerBreadCrumbData: any = [{ label: 'Files', url: '/settings/files' },{ label: 'File Import', url: '' }];
  nextSetup(){
    this.currentTemplateData={
      prefix:has(this.currentTemplate, 'nc.NamingConventions.prefix')?get(this.currentTemplate, 'nc.NamingConventions.prefix'):"",
      sufix:has(this.currentTemplate, 'nc.NamingConventions.sufix')?get(this.currentTemplate, 'nc.NamingConventions.sufix'):"",
      fileType:has(this.currentTemplate, 'details.details.fileType')?get(this.currentTemplate, 'details.details.fileType'):"",
      system:has(this.currentTemplate, 'details.details.system')?get(this.currentTemplate, 'details.details.system'):"",
      Created:has(this.currentTemplate,"Created")?get(this.currentTemplate,"Created"):"",
      CreatedBy:has(this.currentTemplate,"CreatedBy")?get(this.currentTemplate,"CreatedBy"):"",
      activeStatus:false
    }
   }
  ngOnInit(): void {
    let data = localStorage.getItem('data');
    this.templateSelected= JSON.parse(localStorage.getItem('templateSelected'));
    this.currentTemplate = this.templateSelected.currentTemplate;
    this.nextSetup();
    data = JSON.parse(data);
    const key =Object.keys(data);
    const templateTable = this.templateSelected.tableData;
    // const tableRows=[];
    //   templateTable.forEach(element => {
    //     tableRows.push(element.fieldName);
    //   });
    const arr = data[key[0]];

    var column = [];
    var keys = Object.keys(arr[0]);
    let elements;
      // if(tableRows.length){
    // elements=   tableRows.filter((elem)=>{
    //   return  !keys.includes(elem)
    //   });
  //  }
    keys.forEach((val) => {
      const obj = {
        "name": val,
        "field": val,
        "filterType": "Text",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      };
      column.push(obj);
    });
    this.templateColumnConfigFilters['colDefs'] = column;
    this.tableData(arr, keys);
    Math.random()
    if (Math.random()>0.4){
     
      this.notificationType ="success";
      this.notificationBody = "FIle Imported Successfully with all fields";
    }else {
      this.notificationType ="secondary";
      this.notificationBody = "FIle Imported with some missing fields.Please check the template";
    }
    this.notificationOpen="true";
  }
  item() {
    
    this.currentStep = !this.currentStep;
    if (this.currentStep) {
      setTimeout(() => {
        window.scrollBy(0, 500);
      }, 100)

    } else {
      setTimeout(() => {
        window.scrollBy(0, 0);
      }, 500)
    }
  }
  tableData(arr, keys) {
    this.templateDataJSON = arr;
  }
  editTemplate(){
    const templateId = this.currentTemplate.id;
    this.router.navigate([`/settings/file/addEditTemplate/${templateId}`]);
  }


/**
 * Going back to previos page on click of Back Button
 */
  backToPreviousPage(){
    this.router.navigate([`settings/files`]).then(() => {window.location.reload()});
  }

  close(){
    this.router.navigate(['/settings/file/file']);
  }
   tableReady(event){

   }
}
