<div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>
<div class="breadcrumb-container">
  <app-breadcrumbs-nav [headerText]="headerText" [isPriviousRedirectPage]="isPriviousRedirectPage"
    [breadcrumbDataset]="breadcrumbDataset">
  </app-breadcrumbs-nav>
  <div class="pd-5">
    <span class="level-indicator float-right">{{levelIndicator}}</span>
  </div>
</div>
<!-- <hr /> -->
<marketplace-tabs class="container col-12" [selectedTabIndex]="selectedTabIndex"
  (onTabSelection)="onTabSelection($event)">
  <marketplace-tab [header]="'Edit Rule'">
    <div class="fixed-nav bg-gray mar-20" *ngIf="showForms">
      <div class="content-wrapper">
        <div class="container-fluid">
          <div>
            <div class="card-no-border mb-3">
              <div class="row">
                <div class="col-md-9">
                  <span class="card-title">Enter Below Details</span>
                  <marketplace-dynamic-form [formJSON]="relationSHJSON" [isSubmitNeeded]="false"
                    (onValueChange)="mapValuesFromMainToJson($event)">
                  </marketplace-dynamic-form>
                </div>

                <div class="col-md-3 wrapper" *ngIf="enableInventoryStatus">
                  <div class="search-input">
                    <marketplace-input [label]="labelName" [name]="inputname" [groupText]="groupIcon"
                      [(ngModel)]="selectedValue" (input)="giveDescriptionForStatus($event)"
                      (onFocusOut)="inventoryInputfocusOut($event)" [enabled]="false" ngDefaultControl>
                    </marketplace-input>
                    <div class="searchResults" *ngIf="searchResultsWindow">

                      <li *ngFor="let item of filteredResults" (click)="onSelect(item)">{{item.cdValName}}</li>

                    </div>
                    <div class="noResFound" *ngIf="noResultsFound">
                      <li>No Results Found</li>
                    </div>
                    <!-- DBGDATPLFM-39792 -->
                    <!-- <div class="selectedItemsInformation"
                         *ngIf="suggestionWindow">
                      <div class="row">
                        <i class="fa fa-info-circle"
                           aria-hidden="true"></i>
                        <h6> Status Description </h6>
                        <p> {{ statusDescription }} </p>
                      </div>
                      <hr>
                      <div class="similarSuggestion">
                        <h6>Suggestion Similar code</h6>
                        <p> {{ statusSuggestion }}</p>
                      </div>
                    </div>
                     <div class="DescriptionProvider" *ngIf="openAccordion">
                      <marketplace-accordion [openPanel]="openPanelIndex">
                        <marketplace-panel [header]="'Status Description'">
                          <div class="label info">
                            <span class="btn-span statusHeader"><i
                                class="fa-thin fa-circle-info close-icon-color"></i></span> <br>
                            {{statusDescription}}
                          </div>
                        </marketplace-panel>
                      </marketplace-accordion>
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
            <hr />
            <div class="card-no-border mb-3 tabs-padding">
              <marketplace-tabs class="container col-12" [selectedTabIndex]="0"
                (onTabSelection)="onTabSelection($event)">
                <marketplace-tab [header]="'General Details'">
                  <div class="mb-3 tabs-padding">
                    <marketplace-dynamic-form [formJSON]="generalDetailsJson" [isSubmitNeeded]="false"
                      (onValueChange)="mapValuesFromGeneralToJson($event)">
                    </marketplace-dynamic-form>
                  </div>
                  <div class="mb-3 tabs-padding">
                    <span class="notification-title">Attention: <span class="attention-note">The following change will
                        effect the entire inventory. Please be mindful ! </span></span>
                    <hr />

                    <div class="row">
                      <span class="notification-title notification-font-wt">
                        Retro apply the rules to inventory
                      </span>
                      <div class="custom-control custom-switch">
                        <marketplace-switch class="switch floating" [enabled]="isRetroEnabled" [value]="retroApply"
                          (onChange)="setRetro($event)">
                        </marketplace-switch>
                      </div>
                    </div>
                    <div class="row">
                      <span class="notification-title notification-font-wt">
                        Bypass Enhancement
                      </span>
                      <div class="custom-control custom-switch">
                        <!--25.1 -- <marketplace-switch class="switch floating"
                                                [enabled]="rule.rule_level.toUpperCase() !='GLOBAL' && isBypassEnabled" [value]="bypassApply"
                                                (onChange)="setBypass($event)">
                                              </marketplace-switch> -->
                        <marketplace-switch class="switch floating" [enabled]="false" [value]="bypassApply"
                          (onChange)="setBypass($event)">
                        </marketplace-switch>
                      </div>
                    </div>
                    <div class="row">
                      <span class="notification-title notification-font-wt">
                        Apply the rules to :&nbsp;&nbsp; Line level
                      </span>
                      <div class="custom-switch ">
                        <marketplace-switch class="switch floating" [enabled]="isDraftRule" [value]="headerLevel"
                          (onChange)="setLevel($event)">
                        </marketplace-switch>
                      </div>
                      <span class="notification-title notification-font-wt">
                        Header level
                      </span>
                    </div>
                  </div>
                </marketplace-tab>
                <marketplace-tab [header]="'Additional Details'">
                  <div class="mb-3 pd-25">
                    <marketplace-dynamic-form [formJSON]="additionalDetailsJson" [isSubmitNeeded]="false"
                      (onValueChange)="mapValuesFromAdditionalToJson($event)">
                    </marketplace-dynamic-form>
                  </div>
                  <div class="mb-3 pd-25">
                    <h5>File details</h5>
                    <hr />
                    <div *ngIf="!isDataReceivedFromSubcription">
                      <span class="btn-span"><a (click)="uploadFileInEditRule()" class="hyperlink-blue-color"><i
                            class="fa fa-paperclip fa-2x "></i> Attach File</a></span>
                    </div>
                    <marketplace-table [id]="'file-upload-details-table'" [dataset]="dataJSON" [isRowSelectable]="false"
                      [columnDefinitions]="columnConfigforFileUploadtable" [rowHeight]="ruleDashbordTableRowhg"
                      [headerRowHeight]="ruleDashbordTableHeaderhg" [dropdownOptions]="kebabOptions"
                      (onCellValueChange)="cellValueChanged($event)" (onCellClick)="cellClicked($event)"
                      (onTableReady)="tableReady($event)" (onDropdownOptionsClick)="moveToOptionSelected($event)">
                    </marketplace-table>
                  </div>
                </marketplace-tab>
              </marketplace-tabs>
            </div>
          </div>

        </div>
      </div>
    </div>
    <div class="fixed-nav bg-gray mar-10" *ngIf="showForms">
      <div class="content-wrapper">
        <div class="container-fluid">
          <div class="row">
            <div class="ruleDefinition" [ngClass]="isRuleDef ? 'disableQueryBuilder' : ''">
              <span class=" card-title">Rule Definition</span>
              <div class="chip-Container">
                <ng-container *ngIf="compatibleJsonForConcepts.length > 0">
                  <label *ngFor="let chip of compatibleJsonForConcepts" class="chips">
                    <span class="chips-text" title="{{ chip }}">Concept: {{ chip }}</span>
                    <span class="close-button" (click)="closeStateChip(chip)">&times;</span>
                  </label>
                </ng-container>
              </div>
              <marketplace-dynamic-form *ngIf="isConceptDataReady" [formJSON]="querySpecificationJson"
                [isSubmitNeeded]="false" (onValueChange)="ruleLevelChange($event)"
                (onValueChanges)="mapValuesFromQuerySpecToJson($event)">
              </marketplace-dynamic-form>
            </div>
          </div>
          <marketplace-dynamic-form *ngIf="showCustomSqlJson" [formJSON]="customSqlJson" [isSubmitNeeded]="false">
          </marketplace-dynamic-form>
          <div class="multi-criteria-div-align">
            <marketplace-button *ngIf="multipleCriteriaRule" [label]="'Download File'" [type]="'primary'"
              [name]="'primary'" (onclick)="DownloadMultiCriteriaFile()">
            </marketplace-button>
          </div>
          <div>
            <ng-container *ngIf="!showQueryBuilderComponents">
              <div class="mar-30">
                <!--25.1 - <marketplace-textarea [id]="'customSql'" [label]="'Custom Query'" [placeholder]="'Enter SQL here...'"
                  (modelChange)="_onSqlChange($event)" [model]="customSql" ngDefaultControl>
                </marketplace-textarea> -->
                <marketplace-textarea [id]="'customSql'" [readonly]="!isDraftRule" [label]="'Custom Query'"
                  [placeholder]="'Enter SQL here...'" (modelChange)="_onSqlChange($event)" [model]="customSql"
                  ngDefaultControl>
                </marketplace-textarea>
              </div>
            </ng-container>
            <ng-container *ngIf="showQueryBuilderComponents">
              <div>
                <marketplace-segmented-control *ngIf="showSegmentedControl" [name]="'queryBuilderToggle'"
                  [dataset]="sgDashboardDataset" (onSelection)="_onDashboardSGSelection($event)">
                </marketplace-segmented-control>
              </div>
              <div>
                <ng-container *ngIf="isStandardQBSelected">
                  <marketplace-query-builder *ngIf="showQBuilder" [switchToggleNames]="switchToggleNames"
                    [isReadableQueryRequired]=true [bulkUploadParams]="uploadParams" [query]="qbQuery"
                    [qbConfig]="qbConfig" [operators]="operators" [importTableProperties]="tableProperties"
                    [dragDropList]="recentQueryList" (onDropquery)="dropRecentList($event)"
                    (onFieldChange)="qbFieldChange($event)" (fieldValueChange)="getClientConceptValue($event)"
                    (onqbChange)="qbChange($event)">
                  </marketplace-query-builder>
                </ng-container>
                <ng-container *ngIf="!isStandardQBSelected">
                  <marketplace-file-parser [(parserModel)]="parserDataset" (onParseComplete)="onParseComplete($event)"
                    (onChange)="uploadMultiCriteriaFile($event)" [fileUploadType]="'single'"
                    [isMultipleSelectionAllowed]=false [buttonLabel]="'Select Document to Upload'"
                    [tableProperties]="fileParserTableProperties" [fileAccept]="fileParserFileAccept">
                  </marketplace-file-parser>
                  <!--25.1 feature <div [ngClass]="disableUploadBtn && multipleCriteriaRule ? 'pad-1rem-cursor' : 'pad-1rem'"> -->
                  <div>
                    <marketplace-query-builder *ngIf="showQBuilder" [switchToggleNames]="switchToggleNames"
                      [isReadableQueryRequired]=true [bulkUploadParams]="uploadParams" [query]="qbQuery"
                      [qbConfig]="qbConfig" [operators]="operators" [importTableProperties]="tableProperties"
                      [dragDropList]="recentQueryList" (onDropquery)="dropRecentList($event)"
                      (onFieldChange)="qbFieldChange($event)" (fieldValueChange)="getClientConceptValue($event)"
                      (onqbChange)="qbChange($event)">
                    </marketplace-query-builder>
                  </div>
                </ng-container>
              </div>
            </ng-container>
          </div>
          <span class="btn-span pd-25 align-right">
            <marketplace-button [label]="'Cancel'" [type]="'tertiary'" [name]="'tertiary'" (onclick)="cancelEdit()">
            </marketplace-button>

            <!--25.1 - <marketplace-button *ngIf="isDraftRule || isEditedRule" [label]="'Discard'" [type]="'destructive'"
              [name]="'destructive'" (onclick)="discardSavedRule()">
            </marketplace-button> -->
            <marketplace-button *ngIf="isDraftRule" [label]="'Discard'" [type]="'destructive'" [name]="'destructive'"
              (onclick)="discardSavedRule()">
            </marketplace-button>

            <!--25.1  <marketplace-button [label]="'Save'" [type]="'secondary'" *ngIf="showSubmit" [name]="'secondary'"
              (onclick)="validateEditDynamicForms('save')" [enabled]="isEdited">
            </marketplace-button> -->
            <marketplace-button *ngIf="isDraftRule" [label]="'Save'" [type]="'secondary'" [name]="'secondary'"
              (onclick)="validateEditDynamicForms('save')" [enabled]="isEdited">
            </marketplace-button>


            <marketplace-button [label]="'Submit'" [type]="'primary'" *ngIf="showSubmit" [name]="'primary'"
              (onclick)="validateEditDynamicForms('submit')" [enabled]="isEdited">
            </marketplace-button>

            <marketplace-button [label]="'Upload File'" [enabled]="!disableUploadBtn" *ngIf="!showSubmit"
              [type]="'primary'" [name]="'primary'" (onclick)="multipleCriteriaFileUpload()">
            </marketplace-button>

          </span>

        </div>
      </div>
    </div>




    <div [ngClass]="{'duplicatePopup':!showMessage }">
      <marketplace-popup [open]="editSubmitOpenModel" [size]="'small'" (onClose)="editSubmitClosePopup()"
        [ngStyle]="{'display':displayStyle}">

        <div mpui-modal-header>
          <h5 *ngIf="showMessage" class="modal-title custom-title">Attention !</h5>
          <h5 *ngIf="!showMessage" class="modal-title custom-title">Duplicate Rule(s) !</h5>
        </div>
        <div mpui-modal-body class="custom-message">
          <ng-container *ngIf="showMessage">
            <p class="pad-20">You are about to create a Global Rule that will affect all clients, concepts and insights.
            </p>
            <p class="pad-30">Please click continue if you wish to proceed</p>
          </ng-container>
          <div *ngIf="!showMessage">
            <p class="p-align">A rule(s) already exists for the given criteria. Please click Cancel to quit edit screen
              or
              click Edit to review rule and make changes.</p>
            <marketplace-table [id]="'example-static-table'" [redraw]="tableRedraw" [dataset]="duplicateRuleTableJson"
              [rowHeight]="ruleDashbordTableRowhg" [headerRowHeight]="ruleDashbordTableHeaderhg"
              [isRowSelectable]="false" [columnDefinitions]="columnConfigDuplicatePopup"
              (onTableReady)="tableReady($event)">
            </marketplace-table>
          </div>
        </div>
        <div mpui-modal-footer class="rolefooterPopup">
          <marketplace-button [label]="'EDIT'" [type]="'secondary'" [name]="'secondary'" (onclick)="closePopup()">
          </marketplace-button>
          <marketplace-button [label]="'Cancel'" [type]="'cyan-secondary'" [name]="'cyan-secondary'"
            *ngIf="!showMessage" (onclick)="cancelEdit()">
          </marketplace-button>
          <marketplace-button [label]="'Continue'" [type]="'primary'" [name]="'primary'" (onclick)="editRule()"
            *ngIf="showMessage">
          </marketplace-button>

        </div>

      </marketplace-popup>
    </div>


    <marketplace-popup [open]="editErrOpenModel" [size]="'small'" (onClose)="editErrClosePopup()"
      [ngStyle]="{'display':popupDisplayStyle}">
      <div mpui-modal-header>
        <h5 class="modal-title custom-title">Attention !</h5>
      </div>
      <div mpui-modal-body class="custom-message">
        <p class="pad-35">Please fill all the mandatory fields</p>
      </div>
      <div mpui-modal-footer>
        <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closePopup()">">
        </marketplace-button>

      </div>
    </marketplace-popup>

    <marketplace-popup id="impactReportPopup" [open]="openImpactReportPopup" [size]="'small'"
      (onClose)="savedConfirmPopupClose()">
      <div mpui-modal-header>
        <h6 class="modal-title">Rule {{rule.rule_id}} Successfully Saved </h6>
      </div>
      <div mpui-modal-body class="custom-message">
        <p>A preview of claims can be generated for this rule.</p>
      </div>
      <div mpui-modal-footer>
        <marketplace-button [label]="'Back to Rules Management'" [type]="'tertiary'" [name]="'tertiary'"
          (onclick)="cancelEdit()">
        </marketplace-button>

        <marketplace-button [label]="'Generate Preview'" [type]="'primary'" [enabled]="false" [name]="'primary'">">
        </marketplace-button>

      </div>
    </marketplace-popup>
    <!--25.1 -- <marketplace-popup id="impactReportPopup"
                       [open]="openImpactReportPopup"
                       [size]="'small'"
                       (onClose)="savedConfirmPopupClose()">
      <div mpui-modal-header>
        <h6 class="modal-title">Rule {{rule.rule_id}} Successfully Saved </h6>
      </div>
      <div mpui-modal-body
           class="custom-message">
        <p>You can now generate an impact report to see this rule's effect on past executions.</p>
      </div>
      <div mpui-modal-footer>
        <marketplace-button [label]="'Back to Rules Management'"
                            [type]="'tertiary'"
                            [name]="'tertiary'"
                            (onclick)="cancelEdit()">
        </marketplace-button>

        <marketplace-button [label]="'Generate Impact Report'"
                            [type]="'primary'"
                            [name]="'primary'"
                            (onclick)="generatePreview()">">
        </marketplace-button>

      </div>
    </marketplace-popup> -->
    <marketplace-popup [open]="openbypassConfirm" [size]="'large'">
      <div mpui-modal-header>
        Are you certain you want to override the enhancement of Concepts in ECM?
      </div>
      <div mpui-modal-body class="bypassmessgae">This action could potentially impact the concepts in production.
      </div>
      <div mpui-modal-footer>
        <marketplace-button [label]="'Cancel'" [type]="'tertiary'" [name]="'primary'" (onclick)="closebypassConfirm()">
        </marketplace-button>
        <marketplace-button [label]="'Submit'" [type]="'primary'" [name]="'primary'"
          (onclick)="validateEditDynamicForms('submitbypass')">
        </marketplace-button>
      </div>
    </marketplace-popup>


    <div class="fileDetailsExcelPopup">
      <marketplace-popup [open]="fileDetailsExcelOpenModel" [size]="'small'" (onClose)="fileDetailsExcelClosePopup()"
        [ngStyle]="{'display':fileUploadPopup}">

        <div mpui-modal-header>
          <h5 class="modal-title custom-title">Do you want to upload file(s)?</h5>
        </div>

        <div mpui-modal-body>
          <marketplace-file-upload [label]="fileUploadLabelText" [type]="fileUploadType" [accept]="fileAccept"
            (onSelection)="upload($event)" *ngIf="isFileReady">
          </marketplace-file-upload>
          <marketplace-dynamic-form [formJSON]="fileDetailsSectionJson"
            (onValueChange) = "mapValuesToUploadJson($event)" *ngIf="fileDetailsSectionJson && isTextReady"
            [isSubmitNeeded]="false">
          </marketplace-dynamic-form>
          <div class="red-font" *ngIf="showMaxLimitMsg">File size maxed out (Max limit is 25mb)</div>
        </div>

        <div mpui-modal-footer>
          <marketplace-button [label]="'Skip'" [type]="'secondary'" [name]="'secondary'"
            (onclick)="onSubmitSkipClickedEitRule()">
          </marketplace-button>

          <marketplace-button [label]="'Upload'" [type]="'primary'" [name]="'primary'"
            (onclick)="onSubmitUploadClickedEditRule()" [enabled]="!isDisabled || isLoading">
          </marketplace-button>
        </div>

      </marketplace-popup>

    </div>

    <marketplace-popup [open]="conceptsChangedPopUp" [size]="'small'" (onClose)="conceptsChangedPopUpClose()">
      <div mpui-modal-header>
        <h5 class="popUp-Header">Are you sure you want to make the following concept changes?</h5>
      </div>
      <div mpui-modal-body>
        <p *ngIf="addedConcepts.length > 0" class="conceptAddOrRemove"><img id="imgIcon"
            src="../../../../../assets/images/icons/Plus.svg" />Added Concepts</p>
        <div class="chip-Container">
          <ng-container *ngIf="addedConcepts.length > 0">
            <label *ngFor="let chip of addedConcepts" class="chips-Added-Concepts">
              <span class="chips-text-add" title="{{ chip }}">Concept: {{ chip }}</span>
            </label>
          </ng-container>
        </div>
        <p *ngIf="deletedConcepts.length > 0" class="conceptAddOrRemove"><img id="imgIcon"
            src="../../../../../assets/images/icons/Minus.svg" />Deleted Concepts</p>
        <div class="chip-Container">
          <ng-container *ngIf="deletedConcepts.length > 0">
            <label *ngFor="let chip of deletedConcepts" class="chips-deleted-concepts">
              <span class="chips-text-delete" title="{{ chip }}">Concept: {{ chip }}</span>
            </label>
          </ng-container>
        </div>
      </div>
      <div mpui-modal-footer>
        <marketplace-button [label]="'Cancel'" [type]="'tertiary'" [name]="'primary'"
          (onclick)="conceptsChangedPopUpClose()">">
        </marketplace-button>
        <marketplace-button [label]="'Confirm Changes'" [type]="'primary'" [name]="'primary'"
          (onclick)="SubmitConfirm()">">
        </marketplace-button>
      </div>
    </marketplace-popup>

    <marketplace-notification *ngIf="notificationOpen" [open]="notificationOpen" [header]="notificationHeader"
      [bodyText]="notificationBody" [type]="notificationType" [duration]="notificationDuration"
      [position]="notificationPosition" (onClose)="close()">
    </marketplace-notification>

    <div class="submissionConfirmationPopup">
      <marketplace-popup [open]="showConfirmSubmitModal" [size]="'medium'" [isFooterNeeded]="true">
        <div mpui-modal-header>
          <h3>Are you sure you want to submit this rule?</h3>
        </div>
        <div mpui-modal-body><span style="text-align: center;">Upon submission, this rule will be attached to selected
            concepts.</span></div>
        <div mpui-modal-footer>
          <marketplace-button [label]="'Cancel'" [type]="'tertiary'" (onclick)="cancelSubmission()">
          </marketplace-button>
          <marketplace-button [label]="'Submit'" [type]="'primary'" (onclick)="SubmitConfirm()"></marketplace-button>
        </div>
      </marketplace-popup>
    </div>
    <marketplace-popup [open]="openFileUploadConfirmModal" [size]="'small'">
      <div mpui-modal-header>
        <h3>{{ uploadFileStatus }}</h3>
      </div>
      <div mpui-modal-body><span style="text-align: center;">{{ uploadFileStatusMsg }}</span></div>
      <div mpui-modal-footer>
        <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closeFileUploadModal()">
        </marketplace-button>
      </div>
    </marketplace-popup>
    <marketplace-popup [open]="openConfirmationModal" [size]="'small'">
      <div mpui-modal-header>
        <h3>Attention</h3>
      </div>
      <div mpui-modal-body><span style="text-align: center;">All the changes done on Query builder will be lost</span>
      </div>
      <div mpui-modal-footer>
        <marketplace-button [label]="'Cancel'" [type]="'primary'" [name]="'primary'"
          (onclick)="closeConfirmationModal()">
        </marketplace-button>
        <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="clearQB()">
        </marketplace-button>
      </div>
    </marketplace-popup>
  </marketplace-tab>

  <marketplace-tab [header]="'Rule History'">
    <app-rule-history *ngIf="showHistory" (buttonClicked)="handleChildClick($event)" [ruleId]="ruleId"
      [ruleLevel]="rule.rule_level"></app-rule-history>
  </marketplace-tab>
</marketplace-tabs>