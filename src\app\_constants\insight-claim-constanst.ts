export const INSIGHT_CLAIM_CONSTANTS = {
  RESPONSE_CODE: 'responseCode',
  RESPONSE_DATA: 'responseData',
  PATIENT_LIABILITY: 'PATIENT LIABILITY',
  DISPUTE_IN_REVIEW: "DISPUTE IN REVIEW",
  <PERSON>BEL: "label",
  DOLLAR_AMOUNTS: "<PERSON><PERSON><PERSON><PERSON> AMOUNTS",
  AUDIT_STATUS_ADV_SEARCH: "CLR.AUDT_STTS_CD",
  PRMRY_RES_CODE: "Primary Responsibility Code",
  ADJ_DATE: "Adjudication Date",
  LINE_DET: "lineDetailsScreen",
  AUdit_TRAIL: "View Audit Trail",
  Validation_History: "View Validation History",
  COMPLETED_SMALL_CASE: "completed",
  YES_CARELON: "Yes: Carelon",
  EST_OVP: "EST_OVPYMNT_AMT",
  YES_CLIENT: "Yes: Client",
  SWITCH_REVERTED: "Switch Reverted",
  SAVE_DIPUTE_WORKBY: "saveDisputeWorkby",
  DISPUTE_CLAIM_NUMBER_LINK: "disputeClaimNumberLink",
  FAILURE_MESSAGE: "Unable to Switch target Claims something went wrong. please try again!",
  ONE: 1,
  LONG_TEXT: "Long Text",
  SINGLE_SEL: "single select",
  MULTI_SEL: "multiple select",
  CANCEL: "Cancel",
  THREE: 3,
  MODAL_BODY: 'modal-body',
  NG_DROPDOWN_PANEL: 'ng-dropdown-panel-items scroll-host',
  QB_ROW: 'q-row q-connector q-transition',
  NG_SELECT: "NG-SELECT",
  EQUALS: "=",
  NOT_EQUALS: "<>",
  NEGATIVE_NUM_ERROR: "Estimated Overpayment can not be negative number",
  NUMBER: "number",
  TEXT: "text",
  NUMERIC: "numeric",
  CALENDER: "calendar",
  BULK_SAMPLE_ERROR: "You Cannot edit Sample records",
  BULK_UNIVERSE_ERROR: "You Cannot edit Universe records",
  CLIENT_VALIDATED: "Client Validated",
  CLIENT_VALIDATED_ADV_SEARCH: "client validated",
  VAL: "Validated",
  AUDIT_STATUS: "CR.AUDT_STTS_CD",
  CLR_AUDIT_STATUS: "clmrsp.AUDT_STTS_CD",
  CLR_AUDIT_STATUS_ATRAIL: "clmrsphist.AUDT_STTS_CD",
  AUDIT__STATUS: "AUDT_STTS_CD",
  CLIENT_APPROVED: "CLIENT_APPROVED",
  CLIENT_REJECTED: "CLIENT_REJECTED",
  BUSINESS_VALIDATED: "validated",
  CLIENT__VALIDATED: "Client_Validated",
  CLIENT___VALIDATED: "client_validated",
  CLIENT_VAL: "CLIENT_VALIDATED",
  CLIENT_INREVIEW: "Client In Review",
  CLIENT_IN_REVIEW: "Client_In_Review",
  CLAIM_DET: "Claim Details",
  CLIENT__IN_REVIEW: "CLIENT_IN_REVIEW",
  INCOMPLETE: "INCOMPLETE",
  COMPLETE: "COMPLETE",
  IN_PROGRESS: "In Progress",
  PROGRESS: "Progress",
  PLAN_UAT_VALIDATION_COMPLETED: "Plan QA Validation Completed",
  PLAN_UAT_NOT_STARTED: "Plan QA Not Started",
  PLAN_ASSIGNED: "Plan Assigned",
  PLAN_NOT_STARTED: "Plan Not Started",
  PLAN_IN_REVIEW: "Plan In Review",
  UNVALIDATED: "Unvalidated",
  VALIDATED: "VALIDATED",
  PLAN_STATE: "Plan State",
  REC_DATA:
    [
      { "name": "ADJ", "id": "adj" },
      { "name": "No", "id": "no" },
      { "name": "OLB", "id": "olb" },
      { "name": "Yes", "id": "yes" },
    ],
  VLDN_STS: "VLDN_STS",
  VLDN__ST: "vldnSts",
  AUTH_VLDN___ST: "AUTH_VLDN_STS",
  VALIDATED_ADV_SEARCH: "validated",
  CLIENT_NOT_VALIDATED: "Client_Not_Validated",
  CLIENT_NOT_VALIDATED_ADV_SEARCH: "client_not_validated",
  INSIGHT_TYPE_EQUALTO_UNIVERSE_INCLUDES_CONDITION: "LOWER(CLR.INSGHT_TYPE_CD) = 'universe'",
  INSIGHT_TYPE_EQUALTO_UNIVERSE_QUERY: "CLR.INSGHT_TYPE_CD IS NULL",
  INSIGHT_TYPE_NOTEQUALTO_UNIVERSE_INCLUDES_CONDITION: "LOWER(CLR.INSGHT_TYPE_CD) <> 'universe'",
  INSIGHT_TYPE_NOTEQUALTO_UNIVERSE_QUERY: "CLR.INSGHT_TYPE_CD IS NOT NULL AND CLR.RULE_ID IS NULL",
  INSIGHT_TYPE: "Insight Type ",
  INSIGHT_TYPE_UPPER: "INSIGHT TYPE",
  INSIGHT_TYPE_CD: "INSIGHT_TYPE_CD",
  PRIORITYSCORE_FIELD_ADVANCE_SEARCH: "CR.PRTY_SCOR_NBR",
  LINE_NUM: "lnNbr",
  CLM_LINE_NUMBER: "clmLineNbr",
  AWAITING_ADJ_CONCEPTS: "adjustmentWaitingConcepts",
  AWAITING_HOLD_CONCEPTS: "adjustmentOnHoldConcepts",
  PAID_AMOUNT: "totalPaidAmt",
  PRIORITY_SCORE_FIELD: "priorityScore",
  ALLOWED_AMOUNT: "alwdAmt",
  RECOVERABLE: "Recoverable",
  MEETS_BRD: "Meets BRD Criteria",
  EST_OVERPAYMENT: "Estimated Overpayment",
  CLAIM_ALLOWANCE: "Claim Allowance",
  TOTAL_ALLWD_AMNT: "Total Allowed Amount",
  BILL_TYPE: "Bill Type",
  PROVIDER_TAX_ID: "Provider Tax ID",
  PROVIDER_STATUS: "Provider Status",
  CLAIM_ADJ_KEY: "Claim Adj Key",
  CLAIM_TOTAL_PAID: "Claim Total Paid",
  TOTAL_PAID: "ICL.TOTL_PAID_AMT",
  CLAIM_DISPOSITION: "Claim Disposition",
  CLAIM_TYPE: "Claim Type",
  RENDER_PROVIDER_NPI: "Render Provider NPI",
  ROI_EST_OVER_PAYMENT_DISP: "roiEstOverpaymentDisp",
  CLIENT_EST_OVRPAYMNT: "clientEstOverpayment",
  POTENTIAL_RCVRY_AMT: "ptntlRcvryAmt",
  DATE: "Date",
  LONG_TXT: "long text",
  ALLOWED_AMT: "alwdAmt",
  CLAIM_ADJUSTMENT_KEY: "Claim Adjustment Key",
  POTENTIAL_RECOVERY_AMOUNT: "Potential Recovery Amount",
  NUMBER_ONLY_ERROR: "Please enter numbers only",
  DECIMAL_ERROR: "Please enter only 2 decimal points",
  NEGATIVE_NUMBER_ERROR: "Estimated Overpayment can not be negative number",
  CLAIM_COLUM_CONFIG_URL: './assets/workflow-json/insight-claim/insight-claim-tbl-config.json',
  ADDITIONAL_GBD_HEADER_COLUMNS: './assets/workflow-json/insight-claim/gbd-additional-header-config.json',
  ADDITIONAL_GBD_LINE_COLUMNS: './assets/workflow-json/insight-claim/gbd-additional-line-config.json',
  LINE_NUMBER_DETAILS: "./assets/workflow-json/cad-claim-details/line-number-details.json",
  LINE_NUMBER_MASTER_DETAILS: "./assets/workflow-json/cad-claim-details/line-number-details-master.json",
  LINE_NUMBER_DETAILS_DYNA_FORM_URL: "./assets/workflow-json/cad-claim-details/line-number-details-Dyna-form.json",
  LINE_NUMBER_DETAILS_DYNA_FORM_URL_CLIENT: "./assets/workflow-json/cad-claim-details/line-number-details-dyna-form-client.json",
  CLAIM_COLUM_CONFIG_QUEUE_URL: './assets/workflow-json/insight-claim/insight-claim-tbl-config-queue.json',
  CLIENT_CLAIMS_DYNA_FORM: './assets/workflow-json/insight-claim/client-claims-dynamic-form.json',
  CLAIM_BULK_DYNA_FORM_URL: './assets/workflow-json/insight-claim/insight-claim-dynamic-form.json',
  DISPUTE_DETAIL_COMMENTS_URL: './assets/workflow-json/dispute_intake/commentsJsonDisputes.json',
  AUTHENTICATOR_LINE_LEVEL_TABLE_URL: './assets/workflow-json/insight-claim/authenticator-line-tbl-config.json',
  ECM_VERIFIER_LINE_LEVEL_TABLE_URL: './assets/workflow-json/insight-claim/ecm-verifier-line-table.json',
  ECM_VERIFIER_HEADER_LEVEL_TABLE_URL: './assets/workflow-json/workflow-details/ecm-verifier-header-table.json',
  COMMNET_REQUIRED_ERROR: "Please enter comments if recoverable is No",
  SAVE_SUCCESS_MESSAGE: "Changes Saved Successfully",
  SAVE_FAILURE_MESSAGE: "Failed to save changes",
  LINE_NUMBER_LINK: "lineNumberLink",
  CONCEPT_ID_LINK: "conceptIDLink",
  CLIENT_RESP_CMT_LINK: "clntResCmtLink",
  CLIENT: "Client",
  BUSINESS: "Business",
  FAILED: "Failed",
  CARELON_DESC_CMT_LINK: "carelonDescCmtLink",
  LINE_DETAILS: "lineDetails",
  LINE_DETAILS_RES_SCR: "lineNumberLink",
  UNVALIDATED_STR: "UNVALIDATED",
  VALIDATED_STR: "VALIDATED",
  TEMP_NUM: "221108635046",
  TEMP_NUM_1: "727361",
  ASSIGNED_CONTAINER: "#AssignedContainer",
  Ok: "Ok",
  BACK: "Back",
  ASSIGNED_NAME: ".assignedName",
  DISPLAY_NONE: "displayNoneEachElem",
  ASSIGNED_ACTIVE_SPACE: " assignedActive",
  ALL_STR: "ALL",
  INISGHTS_VALIDATIONS: "Insights Validation",
  USER_ID: "userId",
  YES: "yes",
  NO: "no",
  CLOSED: "closed",
  CLOSE: "Close",
  ADJ: "adj",
  OLB: "olb",
  SAVE_IF: "saveIf",
  SAVE: "Save",
  Closed: "Closed",
  PREVIOUS: "previous",
  NEXT: "next",
  STATUS: "status",
  CLIENT_NAME: "clientName",
  CLIENT_LIST: "client_list",
  CARELON_VALIDATED: "Carelon Validated",
  CARELON_INREVIEW: "Carelon In Review",
  CARELON_ASSIGNED: "Carelon Assigned",
  IN_REVIEW: "In Review",
  IN__REVIEW: "In_Review",
  IN_REVIEW_ADV_SEARCH: "in review",
  IN_REVIEW__ADV_SEARCH: "client_in_review",
  Yes: "Yes",
  No: "No",
  ANTHEM: "Anthem",
  ANTM: "ANTM",
  DBG: "DBG",
  BRD_CRITERIA: "brdCriteria",
  CLAIM_CMT: "cmntTxt",
  CLAIM__CMT: "roiComment",
  ROI_RECOVERABLE: "roiRecoverable",
  CLIENT_RECOVERABLE: "clientRecoverable",
  NEW: "new",
  NOT_STARTED: "not started",
  CARELON_NOT_STARTED: "Carelon Not Started",
  ADV_SEARCH_OPERATORS: "./assets/workflow-json/workflow-details/advSearchOperators.json",
  ADV_SEARCH_ADJUSTMENT_ASSIGNMENT: "./assets/json/adjustment-assignment/advSeachAdjdustmentAssiOperators.json",
  PRODUCTION: "Production",
  OBSERVATION: "Observations",
  SINGLE_SELECT: "singleselect",
  MULTIPLE_SELECT: "multipleselect",
  SUBMIT_FOR_ADV_SEARCH: [
    { "name": "Yes", "id": "yes" },
    { "name": "No", "id": "no" },
  ],
  YES_CLN: "Yes:Client",
  UNIQUE_CLAIM_COUNT: "Unique Claim Count",
  UNIVERSE_OBSERVATION: "Universe Observations",
  UNIVERSE_UNIQUE_CLAIM_COUNT: "Universe Unique Claim Count",
  UAT: "QA",
  REMOVE_DROPPED: "remove dropped",
  AUTH_CMNTS: "clntCmntTxt",
  REMOVE_SAME: "remove same",
  UNVALIDATED_LOI: "Unvalidated LOI",
  VALIDATED_LOI: "Validated LOI",
  ALL_LINES: "All Lines",
  MY_QUEUE_JSON_DATA_URL: "./assets/workflow-json/workflow-details/cad-claims-client-my-queue.json",
  MY_QUEUE_JSON_DATA_URL_BUSINESS: "./assets/workflow-json/workflow-details/cad-claims-business-my-queue.json",
  IN_COMPLETE_INSIGTS: "Incomplete ",
  COMPLETE_INSIGTS: "Complete ",
  INCOMPLETED_COUNT: "inCompletedCount",
  COMPLETED_COUNT: "completedCount",
  TOTAL_RECORD: "totalRecord",
  XREFANCHOR: "xreflineNumberLink",
  ALL_RECORD: "allCount",

  ALL_INSIGTS: "All ",
  UNDERLINE: "underlineBlueLink",
  WORK_INV_BREADCRUMB: "< Back to Work Inventory",
  CONTENT: "content",
  ADDED_TEXT: "Added",
  DROPPED_TEXT: "Dropped",
  SAME_TEXT: "Same",
  CAD_CLAIMS_BUSINESS_ENDPOINT: "api/dbg-inventoryinsight/invinsightvaldn/cadMyWork",
  CAD_CLAIMS_CLIENT_ENDPOINT: "api/dbg-inventoryinsight/invinsightvaldnClient/cadMyWork",
  VALIADATION_SUCCESS_MESSAGE: "Validation completed successfully",
  VALIDATION_ERROR_MESSAGE: "Failed to complete the validation",
  AUTHENTICATOR_SUBMIT_SUCCESS_MESSAGE: "Authenticator submitted successfully",
  AUTHENTICATOR_SUBMIT_ERROR_MESSAGE: "Failed to submit the Authenticator",
  EST_OVR_PAYMNT: "estOverpayment",
  CARELON_VALIDATED_STATUS: "CARELON_VALIDATED",
  SAMPLE: "Sample",
  UNIVERSE: "Universe",
  CLAIM__DET: "clmdet",
  XREF_CLM_NBR: "xrefClmNr",
  ADDEDCLAIMS: "Added Claims",
  DROPPEDCLIAMS: "Dropped Claims",
  SAMECLAIMS: "Same Claims",
  CONCEPTID: "C34-S270-59-B-X6-V1",
  ADDED: "ADDED",
  MISSED: "MISSED",
  MATCHED: "MATCHED",
  CLIENT_UPPERCASE: "CLIENT",
  CLIENT_VIEW: "Client View",
  CARELON_VIEW: "Carelon View",
  /**
  CAD Client Export
  */
  EXPORT_UAT_CLAIMS_FILENAME: "Plan-QAInsights_",
  EXPORT_CAD_CLAIM_FILENAME: "Plan-ClaimInsights_",
  EXPORT_CAD_CLAIM_LINES_FILENAME: "Plan-ClaimLineInsights_",
  /**
  CAD Carelon Export
  */
  EXPORT_CARELON_SAMPLE_INSIGHT_FILENAME: "Data mining Claims-Carelon-SampleInsights_",
  EXPORT_CARELON_CLAIMS_FILENAME: "Data mining Claims-Carelon-ClaimInsights_",
  EXPORT_CARELON_CLAIM_LINES_FILENAME: "Data mining Claims-Carelon-ClaimLineInsights_",
  /**
  Adjustment screen Export
*/
  ADJUSTMENT_CONCEPTS_EXPORT: "Plan-AdjustmentConcepts_",
  ADJUSTMENT_CLAIM_LEVEL_EXPORT: "Plan-AdjustmentClaims_",
  ADJUSTMENT_CLAIM_LINE_EXPORT: "Plan-AdjustmentClaimLines_",

  XLSX: ".xlsx",
  CLAIM__EXPORT: "CLAIM__EXPORT __",
  SPREAD_SHEET: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  EXCEL: "excel",
  CONFIG: "config",
  VALIDATION_COMPLETE: "validationCompleted",
  CLAIM_VALIDATED: "claimValidated",
  REVIEWD_BY: "Reviewed By",
  REV_BY_FULL_NAME: "reviewedByFullName",
  AUTH_REV_BY_FULL_NAME: "clntReviewedByFullName",
  PRIORITY_SCORE: "Priority Score",
  LINE_DETAILS_SPACE: "Line Details",
  BASIC_INFO: "Basic Info",
  REVIEWED_BY: "Reviewed By",
  LINE_OF_INTEREST: "Line of Interest",
  DASHS: "----",
  TOTAL_PAID_AMOUNT: "totalPaidAmt",
  PAID_AMNT: "paidAmt",
  MEETS_BRD_VAL: "meetsbrd",
  RECOVERABLE_VAL: "recoverable",
  EST_OVR_PAY: "estOvrPymnt",
  COMMENTS: "comments",
  ID: "id",
  OVERVIEW: "Overview",
  LINE_DETAILS_ROOT: "Line Details",
  SUMMARY_ROOT: "Summary",
  MEMBER_ROOT: "Member",
  GROUP_ROOT: "Group",
  PROVIDER_ROOT: "Provider",
  CLM_LN_COUNT: "Claim Line Count",
  CLM_LN_COUNT_XREF: "Claim Line Count Xref",
  SRC_ADJ_XREF: "Source Adjudication System Code Xref",
  CLM_LN_CDHP: "Claim Line CDHP Payout Amount",
  CLAIM_ROOT: "Claim",
  TAG_CLM_NBR_CNT: "trgtClmLineNbrCnt",
  TRG_CDHP_AMT: "trgtClmLineNbrCdhpAmt",
  BASIC_INFO_ROOT: "Basic Info",
  LINE_DETAILS_DATA_ROOT: "insightClaimLine",
  SUMMARY_DATA_ROOT: "insightSummary",
  SUMMARY_DETAIL_DATA_ROOT: "insightSmryDetail",
  MEMBER_DATA_ROOT: "insightMember",
  GROUP_DATA_ROOT: "insightGroup",
  PROVIDER_DATA_ROOT: "insightProvider",
  CLAIM_DATA_ROOT: "insightClaimLevel",
  XREF_VALUE: "XREFValue",
  TARGET_VALUE: "targetValue",
  XREF_IDENTIFIER: "N",
  TARGET_IDENTIFIER: "Y",
  ROI_REVIWED_BY: "roiReviewedBy",
  ROI_COMMENT: "roiComment",
  New: "New",
  LINES: "lines",
  CARELON: "CARELON",
  UPDATE: "update",
  CONCEPT_VERIFIER_ID: "conceptVerifierId",
  SUBMIT_COUNT_ENDPOINT_BUSINESS: "api/dbg-inventoryinsight/invinsightvaldn/submitbtnstatusCount",
  SUBMIT_COUNT_ENDPOINT_CLIENT: "api/dbg-inventoryinsight/invinsightvaldnClient/submitbtnstatusClntCount",
  SUBMIT: "Submit",
  UPDATE_BTN_TITLE: "After saving the claim, it will move from the Unvalidated to Validated tab if all claim lines with LOI Y have been validated.",
  UPDATE_BTN_COMP_TITLE: "Updating will make claim(s) read only",
  UPDATE_UP: "Update",
  SAVE_UP: "Save",
  SUBMIT_BTN_TITLE: "The count reflects the number of validated claims that need to be submitted",
  ROI_COMMENTS_FORM: "ROI Comments",
  NO_RECOVERY_OPTIONS_JSON: "./assets/workflow-json/workflow-details/no-recovery-reason-line-level-options.json",
  QA: "QA",
  QA_ALIAS: 'UAT',
  LINE_END_DT: "clmLineEndDt",
  LINE_START_DT: "clmLineStrtDt",
  RSP_NAME: "responseName",
  ECM_VER: "ECM Verifier",
  SECOND_TIME_VERIFIER: "Second Time Verifier",
  FIRST_TIME_VERIFIER: "First Time Verifier",
  No_RECOVERY: "No Recovery",
  CLNT_VLDTD_AMT: "CLNT_VLDTD_AMT",
  CLNT__VLDTD_AMT: "clmrsphist.CLNT_VLDTD_AMT",
  CLR_CLNT_VLDTD_AMT: "CLR.CLNT_VLDTD_AMT",
  CLR_CLNT_MEET_BRD_CRTRIA_IND: "CLR.CLNT_MEET_BRD_CRTRIA_IND",
  CLNT_MEET_BRD_CRTRIA_IND: "CLNT_MEET_BRD_CRTRIA_IND",
  CLNT_RCVRY_STTS_CD: "CLNT_RCVRY_STTS_CD",
  CLR_CLNT_RCVRY_STTS_CD: "CLR.CLNT_RCVRY_STTS_CD",
  CLNT_CMNT_TXT: "CLNT_CMNT_TXT",
  CLR_CLNT_CMNT_TXT: "CLR.CLNT_CMNT_TXT",
  VLDTD_AMT: "VLDTD_AMT",
  CLR_VLDT_AMT: "CLR.VLDTD_AMT",
  MEET_BRD_CRTRIA_IND: "MEET_BRD_CRTRIA_IND",
  CLR_MEET_BRD_CRTRIA_IND: "CLR.MEET_BRD_CRTRIA_IND",
  RCVRY_STTS_CD: "RCVRY_STTS_CD",
  CLR_RCVRY_STTS_CD: "CLR.RCVRY_STTS_CD",
  CMNT_TXT: "CMNT_TXT",
  CLR_CMNT_TXT: "CLR.CMNT_TXT",
  UNTIMELY_DISPUTE: "Untimely Dispute",
  UPHELD: "Upheld",
  OVERTURNED: "Overturned",
  No_RECOVERY_REASON: "No Recovery Reason",
  RECOVERY_REASON: "Recovery Reason",
  RECOVERY_DESC: "recoveryDesc",
  ENTER_COMMENTS_ERROR: "Please Enter Comments",
  NO_RECOVERY_REASON_REQUIRED_ERROR: "Please enter comments and select a recovery reason if recoverable is No",
  NO_RECOVERY_ADJ_OLB: "Please Select a Reason Code And Reason Code Description",
  NO_RECOVERY_ERROR_LINE_LEVEL: "Please select no recovery reason",
  RECOVERABLE_NO_ERROR: "For Recoverable No Estimated Overpayment Should Be 0 And Comments Required",
  NO_RCVRY_RSN: "reasonCode",
  REASON_ID: "reasonId",
  CLOSED_CLAIM_ERROR: "Cannot Edit Closed Claim(s)",
  SUBMITTED_CLAIM_ERROR: "Cannot Edit Submitted Claim(s)",
  CLOSED_CLAIM: "CLOSED",
  CLAIM_TOTAL_PAID_ERROR: "Estimated Overpayment Should Be Less Than Paid Amount ",
  STATUS_DISPLAY: 'statusDisplay',
  CAD_DASHBOARD_UAT_TABLE: "./assets/workflow-json/workflow-screen/UAT_table.json",
  PROVIDER_TBL_CONFIG: "./assets/workflow-json/workflow-screen/provider-table-config.json",
  ALL: "All",
  UAT_SAMPLE_OBSERVATION_LABEL: "Observations",
  UAT_SAMPLE_UNIQUE_CLAIM_COUNT: "Unique Claim Count",
  SUM_OF_PAID_TOT: "Sum Of Paid Tot",
  SUM_OF_PAID_LOI: "Sum Of Paid LOI",
  SUM_OF_POT_REC_AMT: "Sum Of Potential Recovery Amt",
  UAT_UNIVERSE_OBS: "UAT Universe Observations",
  UAT_UNIVERSE_UNIQUE_CLAIM: "UAT Universe Unique Claim Count",
  INV_API_URL: "api/dbg-inventoryinsight/invinsight/",
  QDA: "qda",
  RDA: "rda",
  PROD_CONST: "production",
  PAID_AMOUNT_LABEL: "Paid Amount",
  SRC: "src",
  LINE_CMNT: "CLR.CMNT_TXT",
  AMOUNT: "amount",
  TYPE: "type",
  COUNT: "count",
  SPLINE: "spline",
  LINE: "line",
  PROVIDE_STATE: "provideState",
  FINR_POT_REC_AMT_LABEL: "FINR Claims Potential Recovery Amt",
  FINR_CLAIMS_COUNT_LABEL: "FINR Claims Count",
  TPS_POT_REC_AMT: "TPS Potential Recovery Amt",
  TPS_CLAIMS_COUNT: "TPS Claims Count",
  OBS_REVD: "Observations Reviewed",
  UNIQ_CLAIMS_REVD: "Unique Claims Reviewed",
  UNIQ_REC_CLAIMS: "Unique Recoverable Claims",
  CALCULATED_HIT_RATE: "Calculated Hit Rate",
  CPT_HCPC_GRAPH: "CPT and HCPCS Graphs",
  REVENUE_CODE_GRAPH: "Revenue Codes Graphs",
  SUM_OF_PAID_AMT: "Sum of Paid Amount",
  COUNT_OF_OCCURANCES: "Count of Occurrences",
  CUSTOM_NAME: "Custom Name",
  ISOMETRIC: "Isometric",
  TYPES_BY_PRODUCT: "Types By Product",
  YAXIS_TITLE: "All Others except Medicaid & Medicare Advantage",
  REV_CODES_GRAPHS: "Revenue Codes Graphs",
  AMOUNT_PAID: "Amount Paid",
  COLUMN: "column",
  BAR: "bar",
  COUNT_OF_PRODUCTS: "Count Of Products",
  VERTICLE: "vertical",
  RIGHT: "right",
  MIDDLE: "middle",
  CHART_NAME: "Chart Name",
  TYPES: "types",
  NAME: "name",
  VALIDATOR: "CARELON",
  CONCEPT_STATE_QA: "QA",
  VALIDATION: "Validation",
  DISPUTES: "Disputes",
  VIEW_DISPUTE_DETAILS: "viewDisputeDetails",
  CLAIM_NUM: "claimNum",
  DISPUTE_RATIONAL: "disputeRational",
  OVP_AMNT: "ovrpaymntAmnt",
  CLAIM_STATUS: "claimStatus",
  REASON_CODE: "reasonCode",
  DISPUT_NO: "no",
  SUBMITTED: "Submitted",
  SUBMITTED_FLAG: "SUBMITTED_FLAG",
  SUBMITTED_SUCCESFULLY: "Submitted Succesfully!",
  SOMETHING_WRONG: "Something went wrong!",
  COMPLETED: "Completed",
  OPEN: "Open",
  DATA_SET: "dataSet",
  CONFIG_JSON: "configJSON",
  COLUMN_DEF: "colDefs",
  NO_RECOVERY_CAPS: "CLIENT_NO_RECOVERY",
  CUSTOM_FORMATTER: "customFormatter",
  DISPUTE_ID: "disputeId",
  DISPUTE_WORKED_BY: "disputeWorkedBy",
  DISPUTE_RECEIVED_DATE: "disputeReceivedDate",
  WORK_QUEUE: "Work Queue",
  CLIENT_PAYLOAD: "CLIENT",
  NO_RCVRY: "No_Recovery",
  REASON_CODE_BUS: "BSNS_REASON_TYPE",
  REASON_CODE_DES_BUS: "BSNS_INV_RSN_CD",
  REASON_CODE_CLT: "CLNT_REASON_TYPE",
  REASON_CODE_DES_CLT: "CLNT_INV_RSN_CD",
  ON_HOLD: "On_Hold",
  ONHOLD: "On Hold",
  REASON_TYPE: "reasonType",
  SUBMIT_WITH_ON_HOLD_ERROR: "There Are Claim(s) With On Hold Status. Please Remove The Hold To Submit",
  ALL_CONST: "all",
  SOURCE: "src",
  RGB_COLOR: "rgb(214,107,107)",
  COUNT_STR: "Count",
  DISPUTE_SAVE_SUCCESS_MESSAGE: "Saved Successfully",
  POINTER: "pointer",
  ACTIVE_SLICK_CELL: '.slick-cell.l4.r4.true.active',
  ADJUSTEMENT_HELD: "Adjustment Held",
  _OH_: "_OH_",
  _NR_: "_NR_",
  _AB_: "_AB_",
  _SW_: "_SW_",
  CONCEPT: "concept",
  LOB: "lob",
  LOB_SKILL: "Lob Skill",
  TOKEN: "token",
  DISPUTE: "dispute",
  GET_WORK_WARNING_MSG: 'Please fill all the fields',
  CLAIMS_TO_BE_SUBMITTED: "There are no claims to be submitted.",
  GET_WORK_LEFT_SECTION: 'leftSection',
  EXPIRED: "Expired",
  DUPLICATE: "Duplicate",
  CONCEPT_ADV_SEARCH_OPERATORS: "./assets/json/work-assignment/advSearchOperators.json",
  RULE_EXCLUDED: "ruleExcluded",
  CARELON_NO_RECOVERY: "Carelon No Recovery",
  SWITCHED: "Switched",
  SWITCH: "Switch",
  NOT_VALIDATED: "Not_Validated",
  CARELON_NOT_VALIDATED: "Carelon Not Validated",
  VALIDATION_COMPLETED: "Validation Completed",
  SWITCH_SUCCESS_HEADER: "Switching Started",
  SWITCH_SUCCESSFUL_MESSAGE: "Claim Switch Successfully.",
  SWITCH_SUCCESS_MESSAGE: "The Xref and Target claim switching is In-Progress.",
  SWITCHED_TO_TARGET: "CLM_SWCH_CD",
  SWITCHED_TO_TARGET_BY: "Switched To Target",
  SWITCHED_TO_TARGET_OPTIONS: [
    { "name": "In Progress", "id": "In Progress" },
    { "name": "Yes: Carelon", "id": "Yes: Carelon" },
    { "name": "Failed", "id": "Failed" },
  ],
  SWITCHED_TO_TARGET_OPTIONS_CLIENT: [
    { "name": "In Progress", "id": "In Progress" },
    { "name": "Yes: Client", "id": "Yes: Client" },
    { "name": "Failed", "id": "Failed" },
  ],
  COMPARISON_CODE: {
    name: 'Comparison Code', type: 'singleselect', dataset:
      [
        { "name": "Added", "id": "Added" },
        { "name": "Dropped", "id": "Dropped" },
        { "name": "Same", "id": "Same" },
      ]
  },
  SWITCH_IN_PROGRESS_ERROR: "Can't edit claims for which switch in progress",
  CLAIM_NUMBER_INT: "clmLineNbrInt",
  COMMENT_ERR_MSG: "Comments already captured can't edit or update",
  UNIVERSE_COMMENTS_EXISTS_ERROR: "Claim read only as updates already made to the comments",
  UNIVERSE_COMMENTS_EXISTS_ERROR_MULTIPLE_CLAIMS: "Some selected claims are read only as updates already made to the comments",
  EXPIRED_DUPLICATE_ERROR: "Cannot edit expired or duplicate records",
  DUPLICATE_DS: [{ id: "Duplicate", name: "Duplicate" }],
  EXPIRED_DS: [{ id: "Expired", name: "Expired" }],
  ABONDENED_DS: [{ id: "Abandoned", name: "Claim pull back" }],
  SWITCHED_DS: [{ id: "Switch", name: "Target and Cross Reference Switch" }],
  PASS_FAIL_DS: [
    { 'id': 'pass', 'value': "pass", 'label': 'Pass' },
    { 'id': 'fail', 'value': "fail", 'label': 'Failed' }
  ],
  COMPLETE_VER_COMMENTS_URL: './assets/workflow-json/workflow-details/completeVerificationComments.json',
  VERIFICATION_COMMENTS_REQUIRED_ERROR: "Please enter comments",
  VERIFICATION_DECISION_ERROR: "Please select verification decision",
  VERIFICATION_SUCCESS_MESSAGE: "Verification completed successfully",
  AUTHENTICATOR_SUCCESS_MESSAGE: "Authenticator completed successfully",
  FINAL_VERIFICATION_SUCCESS: "Final verification completed successfully",
  AUTHENTICATOR: "Authenticator",
  CARELON_VERIFIER: "Carelon Verifier",
  VERIFIER: "Verifier",
  CARELON_AUTHENTICATOR: "Carelon Authenticator",
  AUTHENTICATOR_REVIEWED_BY: "Authenticator Reviewed By",
  VERIFIER_EST_AMOUNT: "verfierEstOverpayment",
  VERIFIERESTOVERPAYMENT: "Verifier Estimated Overpayment",
  VERIFIERMEETSBRDCRITERIA: "Verifier Meets BRD Criteria",
  VERIFIERRECOVERABLE: "Verifier Recoverable",
  VERIFIERCLAIMCOMMENTS: "Verifier Claim Comments",
  VERIFIERCOMMENTS: "Verifier Comments",
  AUTHENTICATORESTOVERPAYMENT: "Authenticator Estimated Overpayment",
  AUTHENTICATORMEETSBRDCRITERIA: "Authenticator Meets BRD Criteria",
  AUTHENTICATORRECOVERABLE: "Authenticator Recoverable",
  AUTHENTICATORCLAIMCOMMENTS: "Authenticator Claim Comments",
  AUTHENTICATORCOMMENTS: "Authenticator Comments",
  CLM_CMT: "Claim Comments",
  AMOUNT_DIFFERENTIATER: "amountDifferentiater",
  CLAIM_HEADER_COLOUR_DIFFERENTIATER: "claimheadercolourDiffForAuthAndVerifier",
  COLOUR_DIFFERENTIATER: "colourDiffForAuthAndVerifier",
  REQUEST_AUTH_SUCCESS: "Requested authentication successfully",
  VERIFICATION: "verification",
  VERIFICATION_FAILURE_MSG: "Something went wrong. Please try again later",
  PASS: "Pass",
  FAIL: "Fail",
  ACCEPTED: "ACCEPTED",
  REJECTED: "REJECTED",
  CLIENT_MEETS_BRD: "clntBrdCriteria",
  CLIENT_REC_STS_CD: 'clntRcvrysttsCd',
  CLIENT_CMT_TEXT: "clntCmntTxt",
  CLIENT_EST_OVP: "clntEstOverpayment",
  VERIFICATION_PASS_UI: "Verification Completed - Pass",
  VERIFICATION_FAIL_UI: "Verification Completed - Fail",
  VERIFICATION_PASS_STATUS: "VERIFICATION_PASS",
  VERIFICATION_FAIL_STATUS: "VERIFICATION_FAIL",
  VERIFIER_EST_AMNT_LINE: "verifierEstAmnt",
  VERIFIER_CMNT: "verifierCmnt",
  VERIFIER_RECOVERABLE: "verifierRecoverable",
  VERIFIER_BRD: "verifierBrd",
  AUTH_VALIDATED: "AUTH_VALIDATED",
  AUTH_IN_REVIEW: "AUTH_IN_REVIEW",
  AUTH_IN_REVIEW_AUDT_STTS: "Authentication QA In Review",
  AUTH_NEW: "AUTH_NEW",
  AUTH_UAT_NOT_STARTED: "Authentication QA Not Started",
  AUTH_UAT_VALIDATED: "Authentication QA Validated",
  AUTH_IN_PROGRESS: "AUTH_IN_PROGRESS",
  AUTH_UAT_VALDTN_COMPLETED: "Authentication QA Validation Completed",
  AUTH_PASS: "AUTH_PASS",
  AUTH_FAIL: "AUTH_FAIL",
  FINAL_VERIFICATION_ASSIGNMENT: "Final Verification Assignment",
  NO_CHANGES_ERROR: "No changes to save",
  AUTHENTICATION: "authentication",
  ABANDONED: "Abandoned",
  EXCLUDED: "Excluded",
  CLAIM_LINE: "CLAIM_LINE"
}
export interface ITablePagination {
  pageSize: number;
  pageSizes: number[];
}

export const ADV_SEARCH_OBJ = {
  RULES_ADV_SEARCH_QB_CONFIG: {
    fields: {

    },
    validations: {
      unique: [
      ]
    }
  },
  DROP_QUERY: {
    "condition": "and",
    "rules": [
      {
        "field": "CLM_NBR",
        "operator": "Equal",
        "value": "",
        "static": true,
        "active": true
      }
    ]
  }
}

export const CLAIM_BUSINESS_HEADER_DROP_QUERY = {
  "condition": "and",
  "rules": [
    {
      "field": "CLM_NBR",
      "operator": "Equal",
      "value": "",
      "static": true,
      "active": true
    }
  ]
}

export const operators = {
  "Equal": "=",
  "Not Equal": "<>",
  "Less Than": "<",
  "Less Than Or Equal": "<=",
  "Greater Than": ">",
  "Greater Than Or Equal": ">=",
  "contains": "Like"
};

export const CLAIM_LINE_ADV_SEARCH = {
  CLAIM_LINE_COLUMS: {
    fields: {
    },
    validations: {
      unique: [
      ]
    }
  },
  DROP_QUERY: {
    "condition": "and",
    "rules": [
      {
        "field": "CLR.CLM_LINE_NBR",
        "operator": "Equal",
        "value": "",
        "static": true,
        "active": true
      }
    ]
  }
}

export const CLAIM_LINE_NUMBER_DROP_QUERY = {
  "condition": "and",
  "rules": [
    {
      "field": "CLR.CLM_LINE_NBR",
      "operator": "Equal",
      "value": "",
      "static": true,
      "active": true
    }
  ]
}

export const CLAIM_HEADER_ADV_SEARCH = {
  CLAIM_HEADER_COLUMS: {
    fields: {

      "ASGND_CLNT_USER_ID": { name: 'Client Reviewed By', type: 'text' },
      "CLNT_RCVRY_STTS_CD": {
        name: 'Client Recoverable', type: 'singleselect', dataset:
          [
            { "name": "ADJ", "id": "adj" },
            { "name": "No", "id": "no" },
            { "name": "OLB", "id": "olb" },
            { "name": "Yes", "id": "yes" },
          ]
      },
      "CLNT_CMNT_TXT": { name: 'Claim Client Comments', type: 'text' },
      "RNDRG_PROV_TAX_ID": { name: 'Rendering Provider Tax Id', type: 'text' },
      "PRTY_SCOR_NBR": { name: 'Priority Score', type: 'numeric' },
      "CLM_NBR": { name: 'Claim Number', type: 'text' },
      "TOTL_PAID_AMT": { name: 'Claim Total Paid', type: 'numeric' },
      "TOTL_ALWD_AMT": { name: 'Claim Allowance', type: 'numeric' },
      "VLDTD_AMT": { name: 'Carelon Estimated Overpayment', type: 'numeric' },
      "CLNT_VLDTD_AMT": { name: 'Client Estimated Overpayment', type: 'numeric' },
      "PTNTL_RCVRY_AMT": { name: 'Potential Recovery Amount', type: 'numeric' },
      "TOB_CD": { name: 'Type of Bill', type: 'text' },
      "CLM_TYPE_TXT": { name: 'Claim Type', type: 'text' },
      "RNDRG_PROV_NPI_ID": { name: 'Rendering Provider NPI', type: 'text' },
      "LOB": {
        name: 'LOB', type: 'singleselect', dataset:
          [
            { "name": "Medicare Advantage", "id": "Medicare Advantage" },
            { "name": "Medicaid", "id": "Medicaid" },
            { "name": "Medsupp", "id": "Medsupp" },
            { "name": "Commercial", "id": "Commercial" }
          ]
      },
      "GRP_NBR": { name: 'Group Number', type: 'text' },
      "BILLG_PROV_TAX_ID": { name: 'Provider Tax ID', type: 'text' },
      "INN_CD": { name: 'Provider Status', type: 'text' },
      "CLM_DISP_CD": { name: 'Claim Disposition', type: 'text' },
      "CLMADJ_KEY": { name: 'Claim Adjustment Key', type: 'text' },
      "validated": {
        name: 'Validated', type: 'singleselect', dataset:
          [
            { "name": "Yes", "id": "yes" },
            { "name": "No", "id": "no" },
          ]

      }


    },
    validations: {
      unique: [
        'ASGND_CLNT_USER_ID',
        'CLNT_RCVRY_STTS_CD',
        'PRTY_SCOR_NBR',
        'CLNT_CMNT_TXT',
        'CLM_NBR',
        'TOTL_PAID_AMT',
        'TOTL_ALWD_AMT',
        'VLDTD_AMT',
        'CLNT_VLDTD_AMT',
        'PTNTL_RCVRY_AMT',
        'TOB_CD',
        'CLM_TYPE_TXT',
        'RNDRG_PROV_NPI_ID',
        'LOB',
        'GRP_NBR',
        'BILLG_PROV_TAX_ID',
        'INN_CD',
        'CLM_DISP_CD',
        'CLMADJ_KEY',
        'validated'
      ]
    }
  },
  DROP_QUERY: {
    "condition": "and",
    "rules": [
      {
        "field": "ASGND_CLNT_USER_ID",
        "operator": "Equal",
        "value": "",
        "static": true,
        "active": true
      }
    ]
  }
}

export const ADJUSTMENTS_HEADER_ADV_SEARCH = {
  CLAIM_HEADER_COLUMS: {
    fields: {
    },
    validations: {
      unique: [
      ]
    }
  }
}


export const CLIENT_CLAIM_LINE_ADV_SEARCH = {
  CLAIM_LINE_COLUMS: {
    fields: {


      "RVWD_BY_USER_ID": { name: 'Client Reviewed By', type: 'text' },
      "CLR.CLNT_RCVRY_STTS_CD": {
        name: 'Client Recoverable', type: 'singleselect', dataset:
          [
            { "name": "ADJ", "id": "adj" },
            { "name": "No", "id": "no" },
            { "name": "OLB", "id": "olb" },
            { "name": "Yes", "id": "yes" },
          ]
      },
      "CLR.PRTY_SCOR_NBR": { name: 'Priority Score', type: 'numeric' },
      "CLR.VLDTD_AMT": { name: 'Carelon Estimated Overpayment', type: 'numeric' },
      "CLNT_EST_OVPYMT_AMT_LINE": { name: 'Client Estimated Overpayment', type: 'numeric' },
      "ICL.PAID_AMT": { name: 'Paid Amount', type: 'numeric' },
      "CLNT_CMNT_TXT": { name: 'Client Comments', type: 'text' },
      "CLR.AUDT_STTS_CD": { name: 'Audit Status', type: 'text' },
      "ICL.CLM_NBR": { name: 'Claim Number', type: 'text' },
      "ICL.RNDRG_PROV_TAX_ID": { name: 'Rendering Provider Tax Id', type: 'text' },
      "CLR.CLMADJ_KEY": { name: 'Claim Adjustment Key', type: 'text' },
      "RPTG_CLM_LINE_ADJDCTN_STTS_CD": { name: 'Adjudication Status', type: 'text' }, //
      "ICL.TOB_CD": { name: 'Type of Bill', type: 'text' },
      "ICL.CLM_LINE_NBR": { name: 'Line number', type: 'numeric' },
      "RULE_EXCLUDED": {
        name: 'Rule Excluded', type: 'singleselect', dataset:
          [
            { "name": "Yes", "id": "yes" },
            { "name": "No", "id": "no" },
          ]
      },
      "ICL.CLM_DISP_CD": { name: 'Claim Disposition', type: 'text' },
      "SRVC_RNDRG_TYPE_CD": { name: 'Claim Type', type: 'text' },
      "ICL.CLM_LINE_SRVC_STRT_DT": { name: 'Claim Line Start Date of Service', type: 'text' },
      "ICL.CLM_LINE_SRVC_END_DT": { name: 'Claim Line End  Date of Service', type: 'text' },
      "PAID_SRVC_UNIT_CNT": { name: 'Paid Service Units', type: 'numeric' },
      "DIAG_1_CD": { name: 'Diagnosis Code', type: 'text' },
      "PLOS_CD": { name: 'Place of Service', type: 'text' },
      "RVNU_CD": { name: 'Revenue Code', type: 'text' },
      "PROC_CD": { name: 'CPT/HCPCS code', type: 'text' },
      "ICL.TOTL_ALWD_AMT": { name: 'Total Allowed Amount', type: 'numeric' },
      "PROC_MDFR_1_CD": { name: 'Modifiers 1', type: 'text' },
      "PROC_MDFR_2_CD": { name: 'Modifiers 2', type: 'text' },
      "ICL.RNDRG_PROV_NPI_ID": { name: 'Rendering Provider NPI', type: 'text' },
      "ICL.BILLG_PROV_TAX_ID": { name: 'Provider Tax ID', type: 'text' },
      "ICL.INN_CD": { name: 'Provider Status', type: 'text' },
      "CLM_EDIT_TXT": { name: 'Claim Edits', type: 'text' },
      "LOI_IND": { name: 'LOI', type: 'text' },
    },
    validations: {
      unique: [
        'RVWD_BY_USER_ID',
        'CLR.CLNT_RCVRY_STTS_CD',
        'CLR.PRTY_SCOR_NBR',
        'CLR.VLDTD_AMT',
        'ICL.RNDRG_PROV_TAX_ID',
        'CLNT_EST_OVPYMT_AMT_LINE',
        'CLNT_CMNT_TXT',
        'ICL.PAID_AMT',
        'INSGHT_TYPE_CD',
        'STATUS',
        'CLR.AUDT_STTS_CD',
        'ICL.CLM_NBR',
        'RPTG_CLM_LINE_ADJDCTN_STTS_CD',
        'ICL.TOB_CD',
        'ICL.CLM_LINE_NBR',
        'RULE_EXCLUDED',
        'ICL.CLM_DISP_CD',
        'SRVC_RNDRG_TYPE_CD',
        'ICL.CLM_LINE_SRVC_STRT_DT',
        'ICL.CLM_LINE_SRVC_END_DT',
        'PAID_SRVC_UNIT_CNT',
        'DIAG_1_CD',
        'PLOS_CD',
        'CLR.CLMADJ_KEY',
        'RVNU_CD',
        'PROC_CD',
        'PROC_MDFR_1_CD',
        'PROC_MDFR_2_CD',
        'ICL.RNDRG_PROV_NPI_ID',
        'ICL.BILLG_PROV_TAX_ID',
        'ICL.INN_CD',
        'CLM_EDIT_TXT',
        'LOI_IND',
        'INV_ID',
        'ICL.TOTL_ALWD_AMT',
      ]
    }
  },
  DROP_QUERY: {
    "condition": "and",
    "rules": [
      {
        "field": "RVWD_BY_USER_ID",
        "operator": "Equal",
        "value": "",
        "static": true,
        "active": true
      }
    ]
  }
}

export const CLAIM_LINE_NUMBER_ADJ_DROP_QUERY = {
  "condition": "and",
  "rules": [
    {
      "field": "ICL.CLM_LINE_NBR",
      "operator": "Equal",
      "value": "",
      "static": true,
      "active": true
    }
  ]
}

export const ADJUSTMENT_ASSIGNEE_ADV_SEARCH = {
  CLAIM_HEADER_COLUMS: {
    fields: {

      "CLM_NBR": { name: 'Claim Number', type: 'text' },
      "FUNDG_DESC": { name: 'Funding Type', type: 'text' },
      "LOB": { name: 'LOB', type: 'singleselect', dataset: [] },
      "CLM_TYPE_TXT": { name: 'Claim Type', type: 'text' },
      "BILLG_PROV_NM": { name: 'Provider Name', type: 'text' },
      "CLM_ITS_HOST_CD": { name: 'ITS Home/Host', type: 'singleselect', dataset: [] },
      "GRP_NBR": { name: 'Member Group#', type: 'text' },
      "CR.RNDRG_PROV_NM": { name: 'Rendering Provider', type: 'text' },
      "AUDT_STTS_CD": { name: 'Audit Status', type: 'text' },
      "ASGND_CLNT_USER_ID": {
        name: 'Manage Assignee', type: 'singleselect', dataset: []
      }
    },
    validations: {
      unique: [
        'CLM_NBR',
        'CLM_TYPE_TXT',
        'LOB',
        'FUNDG_DESC',
        'BILLG_PROV_NM',
        'CLM_ITS_HOST_CD',
        'GRP_NBR',
        'CR.RNDRG_PROV_NM',
        'AUDT_STTS_CD',
        'ASGND_CLNT_USER_ID'
      ]
    }
  },
  DROP_QUERY: {
    "condition": "and",
    "rules": [
      {
        "field": "CLM_NBR",
        "operator": "Equal",
        "value": "",
        "static": true,
        "active": true
      }
    ]
  }
}

export const ADJUSTMENT_DROP_QUERY = {
  "condition": "and",
  "rules": [
    {
      "field": "CLM_NBR",
      "operator": "Equal",
      "value": "",
      "static": true,
      "active": true
    }
  ]
}

export const CONCEPT_CLAIM_LINE_ADV_SEARCH = {
  CONCEPT_LINE_COLUMS: {
    fields: {
      "CR.CLM_NBR": { name: 'Claim Number', type: 'text' },
      "CR.CLM_TYPE_TXT": { name: 'Claim Type', type: 'text' },
      "CR.BILLG_PROV_NM": { name: 'Provider Name', type: 'text' },
      "LOB": { name: 'LOB', type: 'singleselect', dataset: [] },
      "CR.FUNDG_DESC": { name: 'Funding Type', type: 'text' },
      "CR.PRTY_SCOR_NBR": { name: 'Priority Score', type: 'numeric' },
      "CR.CLM_ITS_HOST_CD": { name: 'ITS Home/Host', type: 'singleselect', dataset: [] },
      "CR.GRP_NBR": { name: 'Member Group#', type: 'text' },
      "CR.RNDRG_PROV_NM": { name: 'Rendering Provider', type: 'text' },
      "CR.MBR_ST_PRVNC_CD": { name: 'Plan State', type: 'text' },
      "CR.AUDT_STTS_CD": { name: 'Audit Status', type: 'text' },
      "CR.ASGND_USER_ID": { name: 'Manage Assignee', type: 'singleselect', dataset: [] }
    },
    validations: {
      unique: [
        'CR.CLM_NBR',
        'CR.CLM_TYPE_TXT',
        'CR.BILLG_PROV_NM',
        'LOB',
        'CR.FUNDG_DESC',
        'CR.CLM_ITS_HOST_CD',
        'CR.GRP_NBR',
        'CR.AUDT_STTS_CD',
        'CLR.PRTY_SCOR_NBR',
        'CR.RNDRG_PROV_NM',
        'CR.MBR_ST_PRVNC_CD',
        'CR.ASGND_USER_ID'
      ]
    }
  },
  DROP_QUERY: {
    "condition": "and",
    "rules": [
      {
        "field": "CR.CLM_NBR",
        "operator": "Equal",
        "value": "",
        "static": true,
        "active": true
      }
    ]
  }
}

export const CONCEPT_LINE_NUMBER_ADJ_DROP_QUERY = {
  "condition": "and",
  "rules": [
    {
      "field": "CR.CLM_NBR",
      "operator": "Equal",
      "value": "",
      "static": true,
      "active": true
    }
  ]
}


