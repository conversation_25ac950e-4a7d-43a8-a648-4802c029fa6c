import { Component, OnInit, AfterViewInit, AfterViewChecked, ViewEncapsulation } from '@angular/core';
import { Router, ActivatedRoute } from "@angular/router"
import { RulesApiService } from '../_services/rules-api.service';
import { UtilitiesService } from '../../_services/utilities.service';
import { OperatorsRulesQB, OperatorsMapForQb } from '../_services/Rules-QB-Constants';
import { CONSTANTS, ROUTING_LABELS } from '../../_constants/menu.constant';
import { CookieService } from 'ngx-cookie-service';
import { constants } from '../rules-constants'
import { RegistrationConstants } from 'src/app/registration/constants';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { data } from 'jquery';
import { ToastService } from 'src/app/_services/toast.service'
import { forkJoin } from 'rxjs';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ProductApiService } from 'src/app/_services/product-api.service';
import { AuthService } from 'src/app/_services/authentication.services';
import { BusinessDivisionService } from 'src/app/_services/business-division.service';

const STANDARD = "Standard";
const UPLOAD_FILE = "Upload File";
const ANTM_ID = 59;
const FAIL = "Fail";
const FILE_UPLOAD_MSG = "File successfully uploaded. You may proceed with the rule creation.";
const SUCCESS = "Success";
const SELECTED_PRODUCT = " Data Mining";
const qbQueryDefault = {
  condition: 'and',
  rules: [
    {
      field: '',
      operator: 'Equal',
      value: '',
      static: true,
      active: true,
    }
  ]
}

@Component({
  selector: 'app-create',
  templateUrl: './create.component.html',
  styleUrls: ['./create.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class CreateComponent implements OnInit {
  public headerText = 'Create New Rule';
  public isPriviousRedirectPage = true;
  selectedProfileClientId: number;
  selectedProfileClientName: string = "";
  levelIndicator: string = "";
  ruleLevelFormEvent: any;
  noResultsFound: boolean = false;
  fileUploadType: string = 'multiple';
  fileUploadLabelText: string = 'Upload File';
  fileAccept: string = '.png,.xlsx,.pdf,.jpeg,.PNG,.XLSX,.PDF,.JPEG';
  multipleCriteriaFileAccept: string = '.csv';
  fileEnable: boolean = true;
  duplicateRuleTableJson: any = [];
  public labelName: string = "Status*";
  public inputname: string = "inventory-status";
  filteredResults: any = [];
  groupIcon: string = '<i class="fa fa-search" aria-hidden="true"></i>'
  public drptype: string = "single";
  public openPanelIndex = "2";
  public selectedValue: string = "";
  public statusDescription: string = "No Status Code Selected";
  public statusSuggestion: string = "";
  public statusInfo: string = "";
  public fileUploadJSON: any;
  public isDisabled: any = true;
  public postUploadDataJson: any;
  isFileReady: boolean = false;
  isTextReady: boolean = false;
  public updatedRuleId: any;
  public isLoading: any = false;
  notificationHeader: string = "";
  notificationBody: string = "";
  notificationType: any = '';
  notificationPosition: any = 'top-right';
  notificationDuration: number = 3000;
  notificationOpen: boolean = false;
  showMaxLimitMsg: boolean = false;
  customSqlJson: any;
  ruleLevelSelectionJson: any;
  breadcrumbDataset = [
    { label: 'Home', url: '/' },
    { label: 'Rules engine', url: '/rules' },
    { label: 'Create new rule' },
  ];
  public ruleDashbordTableRowhg: number = 35;
  public ruleDashbordTableHeaderhg: number;
  displayStyle: any = 'none';
  scrollItems: any = 'none';
  openAccordion: boolean = false;
  fileUploadPopup: any = "none";
  popupDisplayStyle: any = 'none';
  showMessage: boolean = false;
  displayMessage: string = '';
  displayDuplicateMessage: boolean = false;
  public tableRedraw: any;
  ruleLevel: string = '';

  mainDetailsResponse: any = {};
  generalDetailsResponse: any = {};
  additionalDetailsResponse: any = {};
  public createFormData: any = {};
  inventoryStatusDataset: any = [];
  public clientData: any = [];
  public conceptData: any = [];

  httpRequestdata: any = {
    url: './assets/json/form.json',
    dataRoot: 'src',
  };
  ruleCreateUploadRedraw: any;
  mainDetailsFormEvent: any;
  generalDetailsFormEvent: any;
  additionalDetailsFormEvent: any;
  isFormSubmitted: boolean = false;
  ruleSubmitButton: boolean = true;
  retroApply: boolean = false;
  bypassApply: boolean = false;
  headerLevel: boolean = false;
  ruleTypes: any = [];
  businessOwners: any = [];
  searchResultsWindow: boolean = false;
  suggestionWindow: boolean = false;
  showForms: boolean = false;
  showQBuilder: boolean = true;
  showLoader: boolean = false;
  clientIdSelected: string = "";
  clientIdForECP: any;
  conceptIdSelected: any = [];
  enableInventoryStatus: boolean = false;
  createOpenPopup: any = false;
  clientIdQBConfigEntryBackUp: any = {};
  createUploadOpenPopup: boolean = false;
  savedConfirmPopup: boolean = false;
  openbypassConfirm: boolean = false;
  openImpactReportPopup: boolean = false;
  switchToggleNames: any = { 'onText': 'Value', 'offText': 'CFF' };
  uploadParams: any = { url: '', threshold: 2, HttpSuccessKey: 'status', HttpSuccessValue: 200, datasetKey: 'response', uploadSuccess: { header: 'success-header' }, uploadFailure: { header: 'failure-header' } };
  tableProperties: any = {
    "isBulkUploadNeeded": true,
  }
  operators: any = OperatorsRulesQB;
  userId: string = "";
  queryBuilderClientDataset: any = [];
  inventoryStatusOptions = {
    expiration: 'Expired',
    exception: 'Excluded',
    onhold: 'On Hold',
    noRecovery: 'No Recovery',
    lag: 'Lag Wait',
    letters: "Letters"
  };

  clientNameList = [
    { name: 'ANTM', value: 59 }
  ]
  dependentFieldsData: any = [
    {
      hide: ['rule_subtype', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'exception',

    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'onhold',
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'number_of_reminder_letter', 'ltr_rule_sub_type', 'letter_wait_duration_ovp_2'],
      when: 'noRecovery',
    },
    {
      hide: ['lagging_period', 'client_name', 'audit_type', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'expiration',
    },
    {
      hide: ['lookup_dates', 'release_by', 'letter_type', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'lag',
    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'rule_subtype', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'letters',
    }
  ];
  dependentLetterData: any = [
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'rule_subtype', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'ltr_rule_sub_type', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['lookup_dates', 'client_name', 'audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'provider', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'rule_subtype', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'overpayment',

    },
    {
      hide: ['lookup_dates', 'calculation_fields', 'lagging_period', 'client_name', 'audit_type', 'release_by', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'max_no_of_claims_per_letter', 'letter_concept_type', 'provider', 'rule_subtype', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'disregard'

    }
  ];

  dependentsubRuleData: any = [
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'number_of_reminder_letter', 'lagging_period', 'letter_wait_duration_in_days', 'grace_period_in_days', 'letter_concept_type', 'max_no_of_claims_per_letter', 'client_name', 'letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['audit_type', 'release_by', 'provider', 'lagging_period', 'letter_concept_type', 'max_no_of_claims_per_letter', 'letter_wait_duration_ovp_2'],
      when: 'duration',

    },
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'number_of_reminder_letter', 'lagging_period', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'consolidation'

    },
    {
      hide: ['audit_type', 'release_by', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'max_no_of_claims_per_letter', 'letter_concept_type', 'provider', 'calculation_fields', 'lagging_period', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'disregard'

    },
    {
      hide: ['audit_type', 'release_by', 'calculation_fields', 'lagging_period', 'type_of_days', 'letter_wait_duration_in_days', 'grace_period_in_days', 'number_of_reminder_letter', 'letter_wait_duration_ovp_2'],
      when: 'consolidations'

    }
  ];

  // if(sessionStorage.getItem('clientId') == constants.BCBSKC_CLIENT_ID){

  // }

  dependentsubRuleDurationData: any = [
    {
      hide: ['letter_wait_duration_ovp_2'],
      when: "",
    },
    {
      hide: ['letter_wait_duration_ovp_2'],
      when: 0,

    },
  ]



  relationSHJSON: any[] = [];
  generalDetailsJson: any[] = [];
  masterDataFromAPI: any;

  modalColumnConfig: any = {
    switches: {
      enableSorting: true,
      enablePagination: false,
      editable: false,
      enableFiltering: false,
    },
    colDefs: [
      {
        name: 'Rule Id',
        field: 'rule_id',
        filterType: '',
        visible: 'True',
        editorType: 'Text',
        editorTypeRoot: '',
        editorTypeLabel: '',
        editorTypeValue: '',
      },
      {
        name: 'Rule Name',
        field: 'rule_name',
        filterType: 'Text',
        visible: 'True',
        editorType: 'Text',
        editorTypeRoot: '',
        editorTypeLabel: '',
        editorTypeValue: '',
      },
      {
        name: 'Rule Type',
        field: 'rule_type',
        filterType: '',
        visible: 'True',
        editorType: 'Text',
        editorTypeRoot: '',
        editorTypeLabel: '',
        editorTypeValue: '',
      },
      {
        name: 'Rule SubType',
        field: 'rule_subtype',
        filterType: 'text',
        visible: 'True',
        editorType: 'Text',
        editorTypeRoot: '',
        editorTypeLabel: '',
        editorTypeValue: '',
      },
      {
        name: 'Rule Level',
        field: 'rule_level',
        filterType: 'text',
        visible: 'True',
        editorType: 'Text',
        editorTypeRoot: '',
        editorTypeLabel: '',
        editorTypeValue: '',
      },
      {
        name: 'Start Date',
        field: 'start_date',
        filterType: 'text',
        visible: 'True',
        editorType: 'Text',
        editorTypeRoot: '',
        editorTypeLabel: '',
        editorTypeValue: '',
      },
      {
        name: 'End Date',
        field: 'end_date',
        filterType: 'text',
        visible: 'True',
        editorType: 'Text',
        editorTypeRoot: '',
        editorTypeLabel: '',
        editorTypeValue: '',
      },
    ],
  };

  additionalDetailsJson: any[] = [
    {
      type: 'group',
      name: 'additionalDetailsTop',
      label: '',
      column: 1,
      groupControls: [
        {
          label: 'External Point Of Contact',
          group: '',
          type: 'text',
          name: 'external_point_of_contact',
          column: '2',
          groupColumn: '1',
          disabled: false,
          value: '',
          placeholder: 'Enter External Point Of Contact'
        },
      ],
    },
    {
      type: 'group',
      name: 'additionalDetailsBottom',
      label: '',
      column: 1,
      groupControls: [
        {
          label: 'Created By',
          group: '',
          type: 'text',
          name: 'created_by',
          column: '2',
          groupColumn: '1',
          disabled: true,
          value: '',
        },
        {
          label: 'Created Date',
          group: '',
          type: 'date',
          name: 'created_ts',
          column: '2',
          groupColumn: '1',
          disabled: true,
          value: '',
          pickerType: 'single',
          dateFormat: 'MM-DD-YYYY'
        },
        {
          label: 'Updated By',
          group: '',
          type: 'text',
          name: 'updated_by',
          column: '2',
          groupColumn: '1',
          disabled: true,
          value: '',
        },
        {
          label: 'Updated Date',
          group: '',
          type: 'date',
          name: 'updated_ts',
          column: '2',
          groupColumn: '1',
          disabled: true,
          value: '',
          pickerType: 'single',
          dateFormat: 'MM-DD-YYYY'
        },
      ],
    },
  ];

  public qbQuery = {
    condition: 'and',
    rules: [
      {
        field: '',
        operator: 'Equal',
        value: '',
        static: true,
        active: true,
      }
    ],
  };
  public qbLetterQuery = {
    condition: 'and',
    rules: [
      {
        field: 'CLNT_ID',
        operator: 'Equal',
        value: 65,
        static: true,
        active: true,
      }
    ],
  };
  qbConfig: any = {
    fields: {
      conceptID: { name: 'ConceptID', type: 'numeric', mutuallyExclusive: ['client'] },
      memberID: { name: 'MemeberID', type: 'text' },
      DOB: { name: 'DOB', type: 'calendar', dateFormat: 'MM-DD-YYYY' },
      market: {
        name: 'Market',
        type: 'multipleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      country: {
        name: 'Country',
        type: 'singleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      age: { name: 'age', type: 'numeric', regPattern: '^\\d+$', regexMsg: 'Enter only positive numbers', forceRegex: true },
      client: { name: 'client', type: 'text', mutuallyExclusive: ['conceptID'] },
    },
    validations: {
      unique: ['CLNT_ID', 'CNCPT_ID']
    }
  };

  fileDetailsSectionJson: any[] = [
    {
      label: 'Comments',
      group: '',
      type: 'textarea',
      name: 'comments',
      column: '2',
      groupColumn: '1',
      disabled: false,
      value: '',
      placeholder: 'Enter comments here...',
      required: true
    },
  ];

  sgDashboardDataset: any = [
    { id: 'standardQB', label: 'Standard', checked: true },
    { id: 'uploadQB', label: 'Upload File' }
  ];
  isStandardQBSelected: boolean = true;
  showSubmit: boolean = true;
  isDraft: boolean = false;
  multiCriteriaFile: any = {};
  corpusId: string = "";
  openConfirmationModal: boolean = false;
  unSelectedIndex: number = 0;
  showSegmentedControl: boolean = true;
  disableUploadBtn: boolean = true;
  openFileUploadConfirmModal: boolean = false;
  uploadFileStatus: string = "";
  uploadFileStatusMsg: string = "";
  fileParserFileAccept: string = '.csv';
  selectedRulelevel: string = "";
  querySpecificationJson: any = [];
  showQuerySpec: boolean = false;
  querySpecDetailsResponse: any = {};
  querySpecDetailsFormEvent: any = {};
  showQueryBuilderComponents: boolean = true;
  customSql: string = "";
  fileParserTableProperties: any = {
    "enableSorting": false,
    "enablePagination": true,
    "editable": false,
    "enableFiltering": false,
    "isExcelExportNeeded": false,
    "isToggleColumnsNeeded": false,
    "isAddNeeded": false,
    "isSaveNeeded": false,
    "isDeleteNeeded": false,
    "isBulkUploadNeeded": false,
    "isRowSelectable": false
  }
  qbFilled: boolean = true;
  isRuleLevelPresent: boolean = false;
  setStatusOfRuleLevel: boolean;
  conceptIdSelectedForChips: any = [];
  UsersScreenAccessList: any[];
  product: any;
  businessDivision: any;
  productsList: { name: string; value: string; }[];
  isUserTableReady: boolean = false;

  /**
   * onTabSelection gets triggered on tab selection
  */
  onTabSelection(event) {
    setTimeout(() => (this.ruleCreateUploadRedraw = Date.now()), 100);
  }

  /**
   * Gets triggered on click event on upload button
  */
  upload(event: Event): void {
    this.fileUploadJSON = event;
    let fileSizeMaxedOut = this.validateMaxFileSize();
    if (fileSizeMaxedOut) {
      this.showMaxLimitMsg = true;
      return;
    }
    else {
      this.showMaxLimitMsg = false;
    }
    this.checkValidationForUploadFile();
  }

  /**
   * To remove highlighting from fieds which passed validation
  */
  resetValidFields() {
    const collection = document.querySelectorAll(
      `marketplace-select.ng-valid .select-holder .ng-select .ng-select-container,
      marketplace-input.ng-valid input,
      marketplace-textarea.ng-valid .textarea-holder textarea,
      marketplace-date-picker.ng-valid marketplace-input input`
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].classList.remove('redBorder');
    }
  }

  createErrorOpenPopup: any = false

  /**
   * To highlight fieds which failed validation
  */
  showAllInvalidFields() {
    this.createErrorOpenPopup = true
    this.resetValidFields();
    const invalidCollection = document.querySelectorAll(
      `marketplace-select.ng-invalid .select-holder .ng-select .ng-select-container,
      marketplace-input.ng-invalid input,
      marketplace-textarea.ng-invalid .textarea-holder textarea,
      marketplace-date-picker.ng-invalid marketplace-input input`
    );
    console.log("invalid fields", invalidCollection);
    for (let i = 0; i < invalidCollection.length; i++) {
      invalidCollection[i].classList.add('redBorder');
    }
    if (this.isNull(this.selectedValue)) {
      let statusField = document.getElementsByName("inventory-status");
      statusField.forEach(field => field.classList.add('redBorder'));
    }
    this.popupDisplayStyle = "block";
  }

  /**
   * Changing the query builder structure to be sync with API
  */
  modifyQBuilderStructure(qbQuery) {
    const operatorMap = OperatorsMapForQb;
    let startVal;
    var parsed = JSON.parse(JSON.stringify(qbQuery), function (k, v) {
      switch (k) {
        case "condition":
          this.log = v;
          break;
        case "rules":
          this.conditions = v;
          break;
        case "field":
          this.json_path = "$";
          this.lval = v;
          break;
        case "startValue":
          startVal = JSON.parse(JSON.stringify(v));
          this.rval = { "start": startVal, "end": '' }
          break;
        case "endValue":
          this.rval = { "start": startVal, "end": v }
          break;
        case "value":
          if (v != null && v != 'undefined' &&
            v.toString().indexOf(',') > -1) {
            this.rval = v.toString().split(',');
          }
          else {
            this.rval = v;
          }
          break;
        case "operator":
          this.op = operatorMap[v];
          break;
        case "config":
        case "operatorList":
        case "delete":
        case "fieldsList":
        case "fieldsMapList":
        case "customfieldsList":
        case "tabsList":
          delete qbQuery[k];
          break;
        default:
          return v;

      }
    });

    return parsed;
  }

  /**
     * Method to show only roles based on client site
     */
  onBussinessOwnerChange(event: any) {
    this.ruleSubmitButton = true;
    this.isFormSubmitted = false
    let bussinessOwnerName = event.current.generalDetailsRight.business_owner;
    bussinessOwnerName ? this.userManagementSvc.checkManagerNameValidation(bussinessOwnerName) : '';
    if (this.userManagementSvc.validationOfName) {
      this.isFormSubmitted = true
      this.generalDetailsJson[1].groupControls[4].customErrMsg = RegistrationConstants.INVALID_CHAR
      this.ruleSubmitButton = false;
    }
  }

  /**
   * Validating all the dynamic forms for the mandatory fields 
   * and highlighting the failed fields by calling showAllInvalidFields()
  */
  validateCreateDynamicForms(buttonType: string) {
    if (buttonType == 'submit' && this.bypassApply) {
      this.openbypassConfirm = true;
    }
    else {
      this.openbypassConfirm = false;
      let isFormTouched = !this.isDefined(this.mainDetailsFormEvent) || !this.isDefined(this.generalDetailsFormEvent);
      let isRuleLevelValid = true;
      if (this.querySpecDetailsResponse.sqlType && this.querySpecDetailsResponse.sqlType != constants.QUERY_BUILDER && this.levelIndicator == constants.CLIENT_LEVEL) {
        isRuleLevelValid = this.ruleLevelFormEvent ? this.ruleLevelFormEvent['controls']['group']['controls']['clientId']['status'] == constants.VALID : false;
      }
      else if (this.querySpecDetailsResponse.sqlType && this.querySpecDetailsResponse.sqlType != constants.QUERY_BUILDER && this.levelIndicator == "Concept Level") {
        isRuleLevelValid = this.ruleLevelFormEvent ? this.ruleLevelFormEvent['controls']['group']['controls']['conceptId']['status'] == constants.VALID : false;
      }

      let ruleLevelStatus = this.ruleLevelFormEvent.controls.group.controls
      this.setStatusOfRuleLevel = true;
      if (ruleLevelStatus.rulesLevel.status == 'VALID' && ruleLevelStatus.rulesLevel.value == 'Global Level') {
        this.setStatusOfRuleLevel = false;
      } else {
        if (ruleLevelStatus.rulesLevel.value == 'Client Level' && ruleLevelStatus.clientId.status == 'VALID') {
          this.setStatusOfRuleLevel = false;
        } else if (ruleLevelStatus.rulesLevel.value == 'Concept Level' && ruleLevelStatus.conceptId.status == 'VALID') {
          this.setStatusOfRuleLevel = false;
        }
      }

      if (isFormTouched || this.mainDetailsFormEvent['status'] == constants.INVALID || this.generalDetailsFormEvent['status'] == constants.INVALID
        || this.isNull(this.selectedValue) || !isRuleLevelValid || !this.levelIndicator || this.setStatusOfRuleLevel) {
        this.showAllInvalidFields();
        return;
      }
      else {
        this.resetValidFields();
      }
      Object.keys(this.mainDetailsResponse).forEach((group) => {
        Object.keys(this.mainDetailsResponse[group]['value']).forEach(
          (formControl) => {
            this.createFormData[formControl] = this.mainDetailsResponse[group]['value'][formControl];
          }
        );
      });
      Object.keys(this.generalDetailsResponse).forEach((group) => {
        Object.keys(this.generalDetailsResponse[group]['value']).forEach(
          (formControl) => {
            this.createFormData[formControl] = this.generalDetailsResponse[group]['value'][formControl];
          }
        );
      });
      Object.keys(this.additionalDetailsResponse).forEach((group) => {
        Object.keys(this.additionalDetailsResponse[group]['value']).forEach(
          (formControl) => {
            this.createFormData[formControl] = this.additionalDetailsResponse[group]['value'][formControl];
          }
        );
      });
      this.isDraft = (buttonType === 'save') ? true : false;
      this.validateCreate();
    }
  }

  createClosePopup = () => {
    this.createOpenPopup = false;
  }

  /**
  * Validate query builder fields and evaluate rule level
  */
  validateCreate() {
    this.createOpenPopup = true;
    if (this.levelIndicator == "Global Level") {
      this.showMessage = true;
      this.displayDuplicateMessage = false;
      this.displayMessage =
        'You are about to create a Global Rule that will affect all clients, concepts and insights.';
      this.displayStyle = 'block';
    } else {
      /* this code is needed for the future purpose
      Object.keys(this.createFormData).forEach(key => {
        if(this.createFormData[key] == null){
          delete this.createFormData[key];
        }
      }); */
      this.createRule();
    }
  }

  /**
   * Making flag true to show duplicate modal
  */
  checkForDuplicateRules() {
    this.createOpenPopup = true;
    this.showMessage = false;
    this.displayDuplicateMessage = true;
    this.displayStyle = 'block';
    setTimeout(() => (this.tableRedraw = Date.now()), 100);
  }

  /**
   * Which closes the current screen and show dashboard
  */
  cancelCreate() {
    this.router.navigate([`${this.breadcrumbDataset[1].url}`]);
  }

  /**
   * Triggers on click of skip on file upload modal
  */
  skipFileUpload() {
    this.router.navigate([`${this.breadcrumbDataset[1].url}`]);
  }

  /**
   * Resetting all the fileds on the file upload modal
  */
  fileUploadpopUpReset() {
    this.isFileReady = false;
    this.isTextReady = false;
    this.fileUploadPopup = "none";
    this.skipFileUpload();
  }

  /**
   * Closes file upload modal
  */
  closePopupUpload() {
    this.fileUploadpopUpReset();
  }

  /**
   * Calls reset on click of skip on the modal
  */
  onSubmitSkipClicked() {
    this.createUploadOpenPopup = false;
    this.openImpactReportPopup = true;
  }

  /**
  * Navigating back to rules engine screen on click of cancel popup
 */
  cancelEdit() {
    this.fileUploadpopUpReset();
  }

  /**
   * Close save successful popup
   */
  savedConfirmPopupClose() {
    this.openImpactReportPopup = false;
  }

  /**
   * Navigates to impact report screen of rule's effect on past executions.
   */
  generatePreview() {
    this.router.navigate([`/rules/impact-report/${this.updatedRuleId}`], { queryParams: { level: this.levelIndicator.split(" ")[0] } })
    this.openImpactReportPopup = false;
  }

  /**
   * Triggers on click of submit on file uplaod modal
  */
  onSubmitUploadClicked() {
    this.showLoader = true;
    const formData = new FormData();
    Object.keys(this.fileUploadJSON).forEach(key => {
      formData.append("file", this.fileUploadJSON[key]);
    });
    formData.append("solution_id", 'claimsol1');
    formData.append("rule_id", this.updatedRuleId);
    formData.append("comments", this.postUploadDataJson.commentsInUpload);
    formData.append("attached_by", this.userId);
    formData.append("saved_date", this.dateService.formatDate());
    this.createUploadOpenPopup = false;
    this.createRuleService.addFilesToRules(formData, this.levelIndicator.split(" ")[0])
      .subscribe(
        data => {
          if (data) {
            this.showLoader = false;
            this.alertService.setSuccessNotification({
              notificationHeader: "Success",
              notificationBody: 'File successfully attached to the rule ' + this.updatedRuleId,
            });
            //25.1 this.openImpactReportPopup = true; // Showing a new popup instead of navigating back to rules screen
            this.router.navigate([`${this.breadcrumbDataset[1].url}`])
          }
        },
        error => {
          this.showLoader = false;
          this.alertService.setErrorNotification({
            notificationHeader: "Fail",
            notificationBody: `${error.statusText}`,
          });
          this.cancelCreate();
        });
    this.isFileReady = false;
    this.isTextReady = false;
    this.fileUploadPopup = "none";
  }

  /**
   * Triggers on change event of dynamic form
  */
  mapValuesToUploadJson(event: any) {
    this.postUploadDataJson = {
      "commentsInUpload": event.value['comments']
    }
    this.checkValidationForUploadFile();
  }

  /**
    * Validate the form to make submit button enable/disable
  */
  checkValidationForUploadFile() {
    this.isDisabled = true;
    if (this.fileUploadJSON !== undefined &&
        this.fileUploadJSON !== null &&
        this.postUploadDataJson &&
        this.postUploadDataJson.commentsInUpload !== undefined &&
        this.fileUploadJSON !== "" &&
        this.postUploadDataJson.commentsInUpload.trim() !== '' &&
        !this.showMaxLimitMsg) {
      this.isDisabled = false;
    }
  }

  /**
   * Closes all modals
  */
  closePopup() {
    this.createErrorOpenPopup = false;
    this.createOpenPopup = false;
    this.displayStyle = 'none';
    this.popupDisplayStyle = 'none';
    //this.showLoader = false;
  }

  /**
   * set the retro aplly property to a local variable
  */
  setRetro(event) {
    this.retroApply = event.toggle;
  }
  /**
   * set the bypass property to a local variable
  */
  setBypass(event) {
    this.bypassApply = event.toggle;
  }
  /**
  * set the headerlevel property to a local variable
 */
  setLevel(event) {
    this.headerLevel = event.toggle;
  }

  /**
   * Setting up the modal with visibility flags
  */
  uploadFileInCreateRule() {
    this.createUploadOpenPopup = true;
    this.isFileReady = true;
    this.isTextReady = true;
    this.fileUploadPopup = "block";
  }

  /**
   * Calls the service method to call Create API
  */
  createRule() {
    this.closePopup();
    this.showLoader = true;
    this.createFormData.inventory_status = this.selectedValue;
    this.createFormData.request_type = "create";
    this.createFormData.is_active = true; //sholud be calculated dynamically based on business logic(TBD)
    delete this.createFormData.status; //sholud be calculated dynamically based on business logic(ECP handling it)
    this.createFormData.rule_level = this.levelIndicator.split(" ")[0];
    if (this.createFormData.calculation_fields) this.createFormData.calculation_fields = [this.createFormData.calculation_fields];
    if (this.createFormData.lookup_dates) this.createFormData.lookup_dates = { "value": this.createFormData.lookup_dates, "type": "month" };
    if (this.createFormData.lagging_period) this.createFormData.lagging_period = { "value": this.createFormData.lagging_period, "type": "day" };

    this.createFormData.grace_period_in_days = this.createFormData.grace_period_in_days != "" ? this.createFormData.grace_period_in_days : null;
    this.createFormData.max_no_of_claims_per_letter = this.createFormData.max_no_of_claims_per_letter != "" ? this.createFormData.max_no_of_claims_per_letter : null;
    this.createFormData.retro_apply = this.retroApply;
    this.createFormData.bypass_apply = this.bypassApply;
    this.createFormData.header_level = this.headerLevel;
    this.createFormData.created_by = this.userId;
    this.createFormData.updated_ts = "";
    this.createFormData.start_date = this.dateService.getECPDateFormat(this.createFormData.start_date);
    this.createFormData.end_date = this.dateService.getECPDateFormat(this.createFormData.end_date);
    this.createFormData.is_draft = this.isDraft;


    if (this.clientIdSelected == "" && this.createFormData.client_name) {
      this.clientIdSelected = this.selectedProfileClientName;
    }
    if (this.levelIndicator == constants.CLIENT_LEVEL && this.clientIdSelected != "") {
      this.createFormData.client = this.clientIdSelected;
      this.createFormData.clientId = this.clientIdForECP;
    }
    if ((this.levelIndicator == constants.CONCEPT_LEVEL || this.createFormData.rule_type == "lag") && this.conceptIdSelected != "") {
      this.createFormData.concept = this.conceptIdSelected;
      this.createFormData.client = this.selectedProfileClientName;
      this.createFormData.clientId = this.selectedProfileClientId;
    }

    if (this.levelIndicator == constants.GLOBAL_LEVEL && !this.createFormData.client_name) {
      this.createFormData.clientId = null;
      this.createFormData.client = null;
      this.createFormData.concept = null;
      this.createFormData.client_name = null;
    }
    else if ((this.levelIndicator == constants.GLOBAL_LEVEL && this.createFormData.client_name) || this.levelIndicator == constants.CLIENT_LEVEL) {
      this.createFormData.concept = null;
    }
    else if (this.levelIndicator == constants.CONCEPT_LEVEL && !this.createFormData.client_name) {
      this.createFormData.client_name = null;
      this.createFormData.client = this.selectedProfileClientName;
      this.createFormData.clientId = this.selectedProfileClientId;
    }
    this.createFormData.concept = this.conceptIdSelected;
    //commenting because of not seeing any point. Will be removed once testing is done
    // this.createFormData.client = this.clientIdSelected;
    // this.createFormData.clientId = this.clientIdForECP;
    if (this.levelIndicator == constants.CLIENT_LEVEL && this.createFormData.clientId == ANTM_ID) {
      this.createFormData.business_area_name = this.businessDivisionService.getBusinessDivision().toUpperCase() + SELECTED_PRODUCT;
    }
    else {
      delete this.createFormData.business_area_name;
    }

    this.createFormData.conditions = [];
    if (this.createFormData.rule_type == "letters") {
      //removed once testing is done
      // var tstval = this.qbQuery.rules.filter(f => f.value == "");
      // var tstselectval = this.qbQuery.rules.filter(f => f.value == "select");
      this.qbLetterQuery.rules[0].value = this.selectedProfileClientId;
      if (this.qbQuery.rules.length == 0)
        this.createFormData.conditions.push(this.modifyQBuilderStructure(this.qbLetterQuery));
      else
        this.createFormData.conditions.push(this.modifyQBuilderStructure(this.qbQuery));
    }
    else if (this.createFormData.rule_type == "expiration" && this.showQueryBuilderComponents && this.qbQuery.rules.length == 0) {
      this.qbLetterQuery.rules[0].value = this.selectedProfileClientId;
      this.createFormData.conditions.push(this.modifyQBuilderStructure(this.qbLetterQuery));
    }
    else {
      if (this.showQueryBuilderComponents)
        this.createFormData.conditions.push(this.modifyQBuilderStructure(this.qbQuery));
      else {
        this.createFormData.conditions.push({ "query": this.customSql });
        this.createFormData.execution_type = "sql_query";
      }
    }
    if (this.corpusId != "") {
      this.createFormData.rule_metadata = { "corpus_id": this.corpusId };
    }
    let createRuleRequest = {
      "data": this.createFormData,
      "created_ts": this.dateService.getDbgDateFormat(Date.now())
    }

    this.createRuleService.createEditRule(createRuleRequest)
      .subscribe(
        data => {
          if (data) {
            if (data.duplicates_present != undefined) {
              this.duplicateRuleTableJson = [];
              this.showLoader = false;
              let a = [];
              let b: any = data.duplicate_rules;
              this.duplicateRuleTableJson = a.concat(b);
              this.duplicateRuleTableJson.map((s, i) => {
                if (s) {
                  s.id = i + 1;
                  if (s.rule_type == 'letters')
                    s.rule_subtype = s.ltr_rule_sub_type;
                }
              });
              this.checkForDuplicateRules();
            }
            else if (data.status.code == 200) {
              this.updatedRuleId = data.result.metadata.rule_id;
              this.showLoader = false;
              this.alertService.setSuccessNotification({
                notificationHeader: this.isDraft ? constants.RULE_SAVED_MESSAGE : constants.RULE_SUBMISSION_MESSAGE,
                notificationBody: `Rule Id : ${data.result.metadata.rule_id}`,
              });
              this.uploadFileInCreateRule();
            }
            else if (data.status.code == 500) {
              this.alertService.setErrorNotification({
                notificationBody: data.status.message,
              });
              this.showLoader = false;
            }
          }
        },
        error => {
          this.alertService.setErrorNotification({
            notificationBody: error,
          });
          this.showLoader = false;
        });
  }

  createUploadClosePopup() {
    this.createUploadOpenPopup = false;
    this.router.navigate([`${this.breadcrumbDataset[1].url}`]);
  }

  public dropquery = {
    condition: 'and',
    rules: [
      {
        field: 'market',
        operator: 'Not Equal',
        value: ['CA'],
        static: true,
        active: true,
      },
    ],
  };
  public dragdropconfig: any = {
    fields: {
      client: { name: 'Client', type: 'text', mutuallyExclusive: ['conceptID'] },
      conceptID: { name: 'ConceptID', type: 'numeric', mutuallyExclusive: ['client'] }, //textarea
      memberID: { name: 'MemberID', type: 'text' },
      DOB: { name: 'DOB', type: 'calendar' }, //calendar function in the works as of 2/22/2022

      market: {
        name: 'Market',
        type: 'multipleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },
      country: {
        name: 'Country',
        type: 'singleselect',
        dataURL: './assets/json/createRuleQueryBuilder.json',
        dataRoot: 'ddl3',
        key: 'name',
        id: 'value',
      },

      age: { name: 'age', type: 'numeric', regPattern: '^\\d+$', regexMsg: 'Enter only positive numbers', forceRegex: true },
    },
    validations: {
      unique: ['client', 'conceptID']
    }
  };
  recentQueryList = [
    {
      Name: 'Criteria One',
      'Rule Type': 'Global',
      'Rule SubType': 'Global',
      'Created By': 'Lakki Reddy',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'and',
        rules: [
          {
            field: 'DOB',
            operator: 'Equal',
            value: '22',
            static: true,
            active: true,
          },
          {
            field: 'client',
            operator: 'Equal',
            value: '100',
            static: true,
            active: true,
          },
        ],
      },
    },
    {
      Name: 'Criteria Two',
      'Rule Type': 'Global',
      'Rule SubType': 'Global',
      'Created By': 'Ajan Srinivas',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'and',
        rules: [
          {
            field: 'conceptID',
            operator: 'Equal',
            value: '55',
            static: true,
            active: true,
          },
          {
            field: 'memberID',
            operator: 'Equal',
            value: '400',
            static: true,
            active: true,
          },
        ],
      },
    },
    {
      Name: 'Criteria Three',
      'Rule Type': 'Regional',
      'Rule SubType': 'Local',
      'Created By': 'User 3',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'or',
        rules: [
          {
            field: 'DOB',
            operator: 'Equal',
            value: '40',
            static: true,
            active: true,
          },
          {
            field: 'client',
            operator: 'Equal',
            value: '12',
            static: true,
            active: true,
          },
        ],
      },
    },
    {
      Name: 'Criteria Four',
      'Rule Type': 'Regional',
      'Rule SubType': 'Local',
      'Created By': 'User 4',
      'Created Date': '02/22/2022',
      ruleSet: {
        condition: 'or',
        rules: [
          {
            field: 'DOB',
            operator: 'Equal',
            value: '40',
            static: true,
            active: true,
          },
          {
            field: 'client',
            operator: 'Equal',
            value: 'Smith',
            static: true,
            active: true,
          },
        ],
      },
    },
  ];





  constructor(private router: Router,
    private clientApiService: ClientApiService,
    private conceptApiService: ProductApiService,
    private createRuleService: RulesApiService,
    private alertService: ToastService,
    private dateService: UtilitiesService,
    private cookieService: CookieService,
    private userManagementSvc: UserManagementApiService,
    private authService: AuthService,
    private businessDivisionService: BusinessDivisionService) {

    this.getInventoryStatusData();
  }

  ngOnInit(): void {
    this.isUserTableReady = true;
    this.showLoader = true;
    this.userId = sessionStorage.getItem(ROUTING_LABELS.USER_ID)?.toUpperCase();
    this.selectedProfileClientId = Number(sessionStorage.getItem('clientId'));
    this.selectedProfileClientName = sessionStorage.getItem('clientName');
    this.additionalDetailsJson[1]["groupControls"][0].value = this.userId
    this.createRuleService.getMasterData()
      .subscribe(
        data => {
          if (data) {
            if (data.status.code == 200) {
              let masterData = data.result.fields;
              this.refineMasterData(masterData);
              this.showLoader = false;
            }
            else if (data.status.code == 500) {
              /* 
              notification part will be covered in the next sprint for success/error messages
              */
              console.log("Unsuccessful", data.status.traceback);
              this.showLoader = false;
            }
          }
        },
        error => {
          /* 
          notification part will be covered in the next sprint for success/error messages
          */
          this.showLoader = false;
        });
    this.getAllJsonFilesData();

  }

  /**
   * Refine master data into different objects for different fields
  */
  refineMasterData(masterDataFromAPI) {
    this.masterDataFromAPI = masterDataFromAPI;
    let ruleTypeMasterData = masterDataFromAPI['rule_type'];
    let letterTypeMasterData = masterDataFromAPI['letter_type'];
    let ruleFieldsIdMapping = { "rule_sub_type": "rule_subtype", "calculation_fields": "calculation_fields", "lookback_period": "lookup_dates", "lagging_period": "lagging_period", "letter_type": "letter_type", "provider": "provider", "type_of_days": "type_of_days", "letter_wait_duration_in_days": "letter_wait_duration_in_days", "grace_period_in_days": "grace_period_in_days", "concept": "letter_concept_type", "ltr_rule_sub_type": "ltr_rule_sub_type", "number_of_reminder_letter": "number_of_reminder_letter", "letter_wait_duration_ovp_2": "letter_wait_duration_ovp_2" };
    if (this.selectedProfileClientId === constants.KS_CLIENT_ID) {
      ruleTypeMasterData = ruleTypeMasterData.filter(item => !('Letters' in item))
    }
    ruleTypeMasterData.forEach(ruleTypeObj => {
      /* All the keys inside ruleTypeObj is rule type */
      Object.keys(ruleTypeObj).forEach((ruleType) => {
        this.ruleTypes.push({ name: ruleType, value: ruleTypeObj[ruleType].value });
        Object.keys(ruleTypeObj[ruleType]).forEach((field) => {
          if (field != 'value') {
            if (ruleFieldsIdMapping[field] === "lookup_dates" || ruleFieldsIdMapping[field] === "lagging_period" || ruleFieldsIdMapping[field] === "letter_wait_duration_in_days") {
              ruleTypeObj[ruleType][field].forEach(element => {
                element.id = element.value;
              });
            }
            this.dependentFieldsData.push({
              updateDataset: [
                {
                  id: ruleFieldsIdMapping[field],
                  dataset: ruleTypeObj[ruleType][field],
                },
              ],
              when: ruleTypeObj[ruleType].value,
            });
            if (ruleFieldsIdMapping[field] === "letter_type") {
              let letterdata = ruleTypeObj[ruleType][field];
              letterdata.forEach(letterTypeObj => {
                Object.keys(letterTypeObj).forEach((subFields) => {

                  this.dependentLetterData.push({
                    updateDataset: [
                      {
                        id: ruleFieldsIdMapping[subFields],
                        dataset: letterTypeObj[subFields],
                      },
                    ],
                    when: letterTypeObj['value'],
                  });

                  let subRuleData = letterTypeObj['ltr_rule_sub_type'];
                  subRuleData.forEach(ruleSubObj => {

                    Object.keys(ruleSubObj).forEach((otherFields) => {


                      if (ruleFieldsIdMapping[otherFields] === "letter_wait_duration_in_days" || ruleFieldsIdMapping[otherFields] === "number_of_reminder_letter" || ruleFieldsIdMapping[otherFields] === "lagging_period") {
                        ruleSubObj[otherFields].forEach(element => {
                          element.id = ruleFieldsIdMapping[otherFields] === "number_of_reminder_letter" ? element.value.toString() : element.value;
                        });
                      }
                      //Field reuired only for medica and KC client
                      if (![constants.BCBSKC_CLIENT_ID, constants.MEDICA_CLIENT_ID].includes(Number(sessionStorage.getItem('clientId')))) {
                        this.dependentsubRuleData.forEach(subRuleData => {

                          if (subRuleData['when'] === 'duration' && subRuleData.hide) {
                            subRuleData.hide.push('number_of_reminder_letter');
                          }
                        });
                      }
                      // console.log(ruleSubObj[otherFields])
                      if (otherFields == 'number_of_reminder_letter') {
                        Object.keys(ruleSubObj[otherFields][1]).forEach(letterDurationField => {
                          if (ruleFieldsIdMapping[letterDurationField] === "letter_wait_duration_ovp_2") {
                            ruleSubObj[otherFields][1][letterDurationField].forEach(element => {
                              element.id = element.value;
                            });
                          }

                          if (ruleSubObj[otherFields][1][letterDurationField].length > 1) {
                            this.dependentsubRuleDurationData.push({
                              updateDataset: [
                                {
                                  id: ruleFieldsIdMapping[letterDurationField],
                                  dataset: ruleSubObj[otherFields][1][letterDurationField],
                                },
                              ],
                              when: ruleSubObj[otherFields][1]['value'],
                            })
                          }
                        });

                      }
                      this.dependentsubRuleData.push({
                        updateDataset: [
                          {
                            id: ruleFieldsIdMapping[otherFields],
                            dataset: ruleSubObj[otherFields],
                          },
                        ],
                        when: ruleSubObj['value'],
                      });

                    });
                  });
                });
              });
            }
          }
        });
      });
    });
    // this.businessOwners = masterDataFromAPI['business_owner'];
    this.qbConfig.fields = this.modifyQBConfig(masterDataFromAPI['query_fields']);
    delete this.qbConfig.fields.CLNT_ID;
    delete this.qbConfig.fields.CNCPT_ID;
    if (Object.keys(this.qbConfig.fields).length) {
      this.qbQuery.rules[0].field = Object.keys(this.qbConfig.fields)[1];
    }
    this.PopulateMasterDataOnForm();
  }

  /**
   * Modify master data fields into the format query builder understands
  */
  modifyQBConfig(masterDataQBConfig) {
    let QBfields = {};
    let mutuallyExclusiveFields = { 'CLNT_ID': 'CNCPT_ID', 'CNCPT_ID': 'CLNT_ID', 'CNCPT_NM': 'CLNT_ID' };
    const typeMapping = {
      'decimal': 'numeric',
      'string': 'text',
      'date': 'calendar'
    };
    masterDataQBConfig.forEach(field => {
      switch (field.field_type) {
        case 'dropdown':
          QBfields[field.value] = { name: field.name, type: 'singleselect', dataset: field.options, key: 'name', id: 'id' };
          break;
        case 'freetext':
          QBfields[field.value] = { name: field.name, type: typeMapping[field.type] };
          if (field.type == 'date') {
            QBfields[field.value].dateFormat = 'YYYY-MM-DD';
          }
          if (field.type == 'decimal') {
            QBfields[field.value].regPattern = '^\\d+(\\.\\d)?\\d*$';
            QBfields[field.value].forceRegex = true;
          }
      }
      if (field.value == 'CLNT_ID' || field.value == 'CNCPT_ID' || field.value == 'CNCPT_NM') {
        QBfields[field.value].mutuallyExclusive = [mutuallyExclusiveFields[field.value]];
      }
      if (field.value == 'CLNT_ID') {
        let clientDropdownValues = field.options.find((item) => item.id === this.selectedProfileClientId);
        QBfields[field.value].dataset = [clientDropdownValues];
        this.queryBuilderClientDataset = [clientDropdownValues];
      }
    });
    return QBfields;
  }

  /**
   * Method TO show Description and similar codes according to Status Selected
   */
  onSelect(item) {
    this.statusDescription = item?.cdValLongDesc ? item?.cdValLongDesc : "No Description Available for Selected Status";
    this.statusSuggestion = item.cdValShrtDesc;
    this.selectedValue = item.cdValName;
    this.searchResultsWindow = false;
    this.suggestionWindow = false;
    this.openAccordion = true;
  }

  /**
  * Method TO close search results if input box loses focus
  */
  inventoryInputfocusOut(ev: any) {
    if (this.filteredResults.length == 0) {
      setTimeout(() => (this.selectedValue = "", 100));
    }
    this.noResultsFound = false;
  }

  /**
  * Method To get data from inventory status API
  */
  getInventoryStatusData() {
    this.createRuleService.getInventoryStatusData().subscribe((data) => {
      this.inventoryStatusDataset = data;
    });
  }
  /**
  * Method To populate accordion with description according to status code selected
  */
  giveDescriptionForStatus(event: any) {
    var checkingField = event.target?.value ? event.target.value : "";
    if (checkingField != "") {
      var target = event.target.value.toLowerCase();
      this.filteredResults = this.inventoryStatusDataset.filter(character => {
        return character.cdValName.toLowerCase().includes(target);
      });
      this.searchResultsWindow = true;
      this.openAccordion = false;
      if (this.filteredResults.length == 1) {
        this.suggestionWindow = true;
        this.statusDescription = this.filteredResults[0]?.cdValLongDesc ? this.filteredResults[0]?.cdValLongDesc : "No Description Available for Selected Status";
        this.statusSuggestion = this.filteredResults[0].cdValShrtDesc;
        this.selectedValue = this.filteredResults[0].cdValName;
        this.noResultsFound = false;
      }
      else if (this.filteredResults.length == 0) {
        this.noResultsFound = true;
        this.suggestionWindow = false;
        this.searchResultsWindow = false;
        this.openAccordion = false;
        this.selectedValue = "";
      }
      else {
        this.noResultsFound = false;
        this.suggestionWindow = false;
      }
    }
    else {
      this.noResultsFound = false;
      this.searchResultsWindow = false;
      this.suggestionWindow = false;
      this.openAccordion = false;
      this.filteredResults = [];
      this.selectedValue = "";
    }
  }

  /**
   * populates the data on all forms
  */
  PopulateMasterDataOnForm() {
    this.relationSHJSON = [
      {
        type: 'group',
        name: 'rules',
        label: '',
        column: 1,
        groupControls: [
          {
            type: 'select',
            name: 'rule_type',
            label: 'Rule Type',
            required: true,
            options: this.ruleTypes,
            optionName: 'name',
            optionValue: 'value',
            column: 2,
            closeOnSelect: true,
            id: 'rule_type',
            relationship: this.dependentFieldsData,
            placeholder: 'Choose Rule Type'
          },
          {
            type: 'select',
            name: 'letter_type',
            label: 'Letter Type',
            column: 2,
            id: 'letter_type',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: [],
            relationOptions: [],
            relationship: this.dependentLetterData,
            required: true,
            visible: false,
            placeholder: 'Choose Letter Type'
          },
          {
            type: 'select',
            name: 'ltr_rule_sub_type',
            label: 'Rule Subtype',
            column: '2',
            id: 'ltr_rule_sub_type',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: [],
            relationOptions: [],
            relationship: this.dependentsubRuleData,
            required: true,
            visible: false,
            placeholder: 'Choose Rule Subtype'
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'value',
            label: 'Calculation Fields',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'calculation_fields',
            column: '2',
            disabled: false,
            visible: false,
            id: 'calculation_fields',
            required: true,
            placeholder: 'Choose Calculation Field'
          },
          {
            type: 'select',
            name: 'rule_subtype',
            label: 'Rule Subtype',
            column: "2",
            id: 'rule_subtype',
            closeOnSelect: true,
            optionName: 'name',
            optionValue: 'value',
            options: [],
            relationOptions: [],
            required: true,
            visible: false,
            placeholder: 'Choose Rule Subtype'
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'id',
            label: 'Number Of Reminder Letters',
            type: 'select',
            closeOnSelect: true,
            name: 'number_of_reminder_letter',
            column: '2',
            disabled: false,
            visible: false,
            id: 'number_of_reminder_letter',
            required: true,
            customTags: true,
            placeholder: 'Choose Reminder Letters',
            relationOptions: [],
            relationship: this.dependentsubRuleDurationData,
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'id',
            label: 'Letter Wait Duration For OVP Letter 1(in days)',
            type: 'select',
            closeOnSelect: true,
            name: 'letter_wait_duration_in_days',
            column: '2',
            disabled: false,
            visible: false,
            id: 'letter_wait_duration_in_days',
            required: true,
            customTags: true,
            minimum: 0,
            maximum: 1000,
            placeholder: 'Choose Letter Wait Duration'
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'id',
            label: 'Letter Wait Duration For OVP Letter 2(in days)',
            type: 'select',
            closeOnSelect: true,
            name: 'letter_wait_duration_ovp_2',
            column: '2',
            disabled: false,
            visible: false,
            id: 'letter_wait_duration_ovp_2',
            required: true,
            customTags: true,
            minimum: 0,
            maximum: 1000,
            placeholder: 'Choose Letter Wait Duration'
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'id',
            label: 'Grace Period(in days)',
            type: 'numberSelect',
            closeOnSelect: true,
            name: 'grace_period_in_days',
            column: '2',
            disabled: false,
            visible: false,
            id: 'grace_period_in_days',
            required: true,
            customTags: true,
            minimum: 0,
            maximum: 100,
            value: null,
            placeholder: 'Choose Grace Period Min 0 Max 100'
          },

          {
            options: [],
            optionName: 'name',
            optionValue: 'value',
            label: 'Type of days',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'type_of_days',
            column: '2',
            disabled: false,
            visible: false,
            id: 'type_of_days',
            required: true,
            placeholder: 'Choose Type of days'
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'id',
            label: 'Lookback Period (in months)',
            type: 'numberSelect',
            closeOnSelect: true,
            name: 'lookup_dates',
            column: '2',
            disabled: false,
            visible: false,
            id: 'lookup_dates',
            required: true,
            customTags: true,
            minimum: 0,
            maximum: 100,
            placeholder: 'Choose Lookback Period'
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'id',
            label: 'Lagging Period (in days)',
            type: 'numberSelect',
            closeOnSelect: true,
            name: 'lagging_period',
            column: '2',
            disabled: false,
            visible: false,
            id: 'lagging_period',
            required: true,
            customTags: true,
            minimum: 0,
            maximum: 1000,
            placeholder: 'Choose Lagging Period'
          },
          {
            options: [
              { name: this.selectedProfileClientName, id: this.selectedProfileClientId }
            ],
            optionName: 'name',
            optionValue: 'name',
            label: 'Client Name',
            closeOnSelect: true,
            type: 'select',
            multiple: false,
            name: 'client_name',
            column: '2',
            disabled: false,
            visible: false,
            id: 'client_name',
            required: true,
            placeholder: 'Choose Client Name'
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'value',
            label: 'Provider',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'provider',
            column: '2',
            disabled: false,
            visible: false,
            id: 'provider',
            default: 'single',
            required: true,
            placeholder: 'Choose provider'
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'value',
            label: 'Concept',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'letter_concept_type',
            column: '2',
            disabled: false,
            visible: false,
            id: 'letter_concept_type',
            required: true,
            placeholder: 'Choose concept'
          },
          {
            options: [],
            optionName: 'name',
            optionValue: 'id',
            label: 'Max no Of Claims Per Letter',
            type: 'numberSelect',
            closeOnSelect: true,
            name: 'max_no_of_claims_per_letter',
            column: '2',
            disabled: false,
            visible: false,
            id: 'max_no_of_claims_per_letter',
            required: true,
            customTags: true,
            minimum: 1,
            maximum: 25,
            value: null,
            placeholder: 'Choose Max No Of Claims Per Letter Min 1 Max 25'
          },
          {
            options: [
              {
                name: 'Audit',
                value: 'audit',
              },
              {
                name: 'Provider Audit',
                value: 'providerAudit',
              },
              {
                name: 'Report Audit',
                value: 'reportAudit',
              },
              {
                name: 'Over Payment',
                value: 'overPayment',
              },
              {
                name: 'Business Audit',
                value: 'businessAudit',
              },
            ],
            optionName: 'name',
            optionValue: 'value',
            label: 'Inventory Type',
            type: 'select',
            multiple: false,
            closeOnSelect: true,
            name: 'audit_type',
            column: '2',
            disabled: false,
            visible: false,
            id: 'audit_type',
            required: true,
            placeholder: 'Choose Inventory Type'
          },
          {
            label: 'Release By',
            type: 'date',
            name: 'release_by',
            id: 'release_by',
            column: '2',
            disabled: false,
            visible: false,
            pickerType: 'single',
            required: true,
            dateFormat: 'MM-DD-YYYY',
            minDate: Date.now(),
            placeholder: 'Choose Date'
          },
        ],
      },
    ];
    this.showForms = true;
    this.generalDetailsJson = [
      {
        type: 'group',
        name: 'generalDetailsLeft',
        label: '',
        column: '2',
        groupControls: [
          {
            label: 'Rule Name',
            type: 'text',
            name: 'rule_name',
            column: '1',
            disabled: false,
            value: '',
            required: true,
            placeholder: 'Enter Rule Name'
          },
          {
            label: 'Rule Description',
            type: 'textarea',
            name: 'description',
            column: '1',
            disabled: false,
            value: '',
            required: true,
            placeholder: 'Enter Description'
          },
          {
            label: 'Term Reason',
            type: 'text',
            name: 'term_reason',
            column: '1',
            disabled: false,
            value: '',
            placeholder: 'Enter Term Reason'
          },
        ],
      },
      {
        type: 'group',
        name: 'generalDetailsRight',
        label: '',
        column: '2',
        groupControls: [
          {
            label: 'Start Date',
            type: 'date',
            name: 'start_date',
            id: 'start_date',
            column: '3',
            disabled: false,
            value: '',
            pickerType: 'single',
            required: true,
            dateFormat: 'MM-DD-YYYY',
            placeholder: 'Enter Date',
            relatedDateControls: [{
              target: 'end_date'
            }]
          },
          {
            label: 'End Date',
            type: 'date',
            name: 'end_date',
            id: 'end_date',
            column: '3',
            disabled: false,
            pickerType: 'single',
            value: '',
            required: false,
            dateFormat: 'MM-DD-YYYY',
            placeholder: 'Enter Date'
          },
          {
            label: 'Status',
            type: 'text',
            name: 'status',
            column: '3',
            disabled: true,
            value: ''
          },
          {
            label: 'Review Reminder Date',
            type: 'date',
            name: 'review_remainder_date',
            column: '1',
            disabled: false,
            pickerType: 'single',
            value: '',
            required: true,
            dateFormat: 'MM-DD-YYYY',
            placeholder: 'Enter Date'
          },
          {
            label: 'Business Owner',
            group: '',
            type: 'text',
            name: 'business_owner',
            column: '2',
            groupColumn: '1',
            disabled: false,
            required: true,
            maxLength: 50,
            placeholder: 'Please Enter Business Owner'
          },
        ],
      },
    ];
  }


  /**
   * method sets rule level and get called when rule type changes and 
   * query builder criteria changes
  */


  /**
   * method checks for client name changes and push it to query builder
  */
  validateValueChanges(event) {
    this.showQBuilder = false;
    let changedRuleTypeValue = event.current.rules.rule_type;
    let oldRuleTypeValue = event.previous.rules.rule_type;
    if (changedRuleTypeValue == 'lag' && oldRuleTypeValue != changedRuleTypeValue) {
      this.clientIdQBConfigEntryBackUp = this.qbConfig.fields.CLNT_ID;
      delete this.qbConfig.fields.CLNT_ID;
    }
    else if (oldRuleTypeValue == 'lag' && oldRuleTypeValue != changedRuleTypeValue) {
      this.showQBuilder = false;
      this.qbConfig.fields['CLNT_ID'] = this.clientIdQBConfigEntryBackUp;
    }
    else if (changedRuleTypeValue == 'letters') {
      this.suggestionWindow = false;
      this.enableInventoryStatus = false;
      this.showQBuilder = false;
      this.qbQuery.rules[0].field = Object.keys(this.qbConfig.fields)[1];
      if (this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != null && this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != 'consolidation' && this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != 'consolidations') {
        this.enableInventoryStatus = true;
      }
    }
    setTimeout(() => this.showQBuilder = true, 10);
  }

  /**
   * triggers on change of mainForm dynamic form
  */
  mapValuesFromMainToJson(event) {
    this.mainDetailsResponse = event.controls;
    this.mainDetailsFormEvent = event;

    let ruleTypeForValidation = this.mainDetailsFormEvent.value.rules.rule_type;
    if (this.isDefined(ruleTypeForValidation)) {
      switch (ruleTypeForValidation) {
        case 'expiration':
          this.selectedValue = this.inventoryStatusOptions.expiration;
          break;
        case 'exception':
          this.selectedValue = this.inventoryStatusOptions.exception;
          break;
        case 'onhold':
          this.selectedValue = this.inventoryStatusOptions.onhold;
          break;
        case 'noRecovery':
          this.selectedValue = this.inventoryStatusOptions.noRecovery;
          break;
        case 'lag':
          this.selectedValue = this.inventoryStatusOptions.lag;
          break;
        case 'letters':
          {
            //this.selectedValue = this.inventoryStatusOptions.letters; 
            this.selectedValue = "Awaiting Adjustment";
            this.suggestionWindow = false;
            if (this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != null && this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != 'consolidation' && this.mainDetailsFormEvent.value.rules.ltr_rule_sub_type != 'consolidations') {
              this.enableInventoryStatus = true;
            }
            if (this.mainDetailsFormEvent.value.rules.letter_type == "overpayment")
              this.selectedValue = "Awaiting Adjustment";
            else if (this.mainDetailsFormEvent.value.rules.letter_type == "disregard")
              this.selectedValue = "Closed";

            break;
          }
        default:
          break;
      }
      this.enableInventoryStatus = true;
    }
    else {
      this.enableInventoryStatus = false;
    }
    setTimeout(() => (this.resetValidFields(), 100));
  }

  /**
   * triggers on change of generalForm dynamic form
  */
  mapValuesFromGeneralToJson(event) {
    this.generalDetailsResponse = event.controls;
    this.generalDetailsFormEvent = event;
    setTimeout(() => (this.resetValidFields(), 100));
  }

  /**
   * triggers on change of additionalForm dynamic form
  */
  mapValuesFromAdditionalToJson(event) {
    this.additionalDetailsResponse = event.controls;
    this.additionalDetailsFormEvent = event;
    setTimeout(() => (this.resetValidFields(), 100));
  }

  /**
   * checks for undefined value
  */
  isDefined(fieldValue) {
    if (fieldValue != undefined) return true;
    else return false;
  }

  /**
   * checks for null value
  */
  isNull(fieldValue) {
    if (fieldValue == null || fieldValue == "") return true;
    else return false;
  }
  /**
   * checks for equal values
  */
  fieldEqualsCheck(field: string, stringLiteral: string) {
    let fieldArray = field.split(',');
    let checkPassed = true;
    fieldArray.forEach(entry => {
      if (entry != stringLiteral) {
        checkPassed = false;
      }
    });
    return checkPassed;
  }

  /* 
  * method evaluating client/concept ids from the event
  * triggered by querybuilder field value change
  */
  getClientConceptValue(event) {
    let fieldChanged = event.rule;
    let fieldValue = event.event.name;
    let fieldValueId = event.event.id;
    if (fieldChanged.field == "CLNT_ID") {
      this.clientIdSelected = fieldValue;
      this.clientIdForECP = fieldValueId;
      // this.conceptIdSelected = "";
    } else if (fieldChanged.field == "CNCPT_ID") {
      // this.conceptIdSelected = fieldChanged.value;
      this.clientIdSelected = "";
    }
  }

  /**
  * Validate the file size to make submit button enable/disable
  * Disable the Submit button if file size exceeds 25mb = 26214400bytes
*/
  validateMaxFileSize() {
    let combinedSize = 0;
    Object.keys(this.fileUploadJSON).forEach(key => {
      combinedSize += this.fileUploadJSON[key].size;
    });
    let isFileDisable = (combinedSize > 26214400) ? true : false;
    return isFileDisable;
  }

  ngAfterViewChecked(): void {

  }

  /**
 * Method fires on segemented selection
 * @param event 
 */
  _onDashboardSGSelection(event: any): void {
    switch (event.selection.label) {
      case STANDARD:
        this.unSelectedIndex = 1;
        this.openConfirmationModal = true;
        this.removeCloseButton();
        break;
      case UPLOAD_FILE:
        setTimeout(() => {
          this.unSelectedIndex = 0;
          this.openConfirmationModal = true;
          this.removeCloseButton();
        }, 50);
        break;
      default:
        break;
    }
  }

  removeCloseButton(): void {
    setTimeout(() => {
      const elements = document.querySelectorAll('marketplace-popup .modal-header .close');
      elements.forEach(element => {
        const htmlEle = element as HTMLElement;
        htmlEle.style.display = 'none';
      });
    }, 0);
  }

  /* 
  * recursive function to check empty field in querybuilder
  */
  recursiveFuncForCheckingEmptyField(event) {
    this.qbFilled = true;
    for (let i = 0; i < event.length; i++) {
      if (event[i].value == "" || event[i].value == constants.SELECT) {
        this.qbFilled = false;
        return;
      } else if (event[i].rules) {
        this.recursiveFuncForCheckingEmptyField(event[i].rules);
      }
    }
  }

  /**
   * Method will be called to send file and Query builder mapping to be saved 
   */
  multipleCriteriaFileUpload(): void {
    this.recursiveFuncForCheckingEmptyField(this.qbQuery[constants.RULES]);

    if (!this.qbFilled) {
      this.uploadFileStatus = constants.ATTENTION;
      this.uploadFileStatusMsg = constants.FILL_QB_STATUS_MESSAGE;
      this.openFileUploadConfirmModal = true;
    }
    if (this.qbFilled) {
      const formData = new FormData();
      let conditions = [];
      conditions.push(this.modifyQBuilderStructure(this.qbQuery));
      Object.keys(this.multiCriteriaFile).forEach(key => {
        formData.append("file", this.multiCriteriaFile[key]);
      });
      formData.append("conditions", JSON.stringify(conditions));
      this.showLoader = true;
      this.createRuleService.uploadFileAndQBCriteria(formData, this.levelIndicator)
        .subscribe(
          data => {
            if (data) {
              this.showSubmit = true;
              this.corpusId = data?.result?.uploaded_files[0]?.corpus_id;
              this.showLoader = false;
              this.uploadFileStatus = SUCCESS;
              this.uploadFileStatusMsg = FILE_UPLOAD_MSG;
              this.openFileUploadConfirmModal = true;
              this.removeFileParserTable();
            }
          },
          error => {
            this.showLoader = false;
            this.uploadFileStatus = FAIL;
            this.uploadFileStatusMsg = error.statusText;
            this.openFileUploadConfirmModal = true;
          });
    }

  }

  /**
   * Method to remove pointer function none from QB
   */
  enableQueryBuilder() {
    let queryBuilderDivElement = document.querySelector('div.enabledQb');
    queryBuilderDivElement?.classList?.remove('pointerFuncNone');
  }

  /**
   * Method triggers on file selection
   * @param event 
   */
  uploadMultiCriteriaFile(event: Event): void {
    this.showQBuilder = false;
    if ((Array.isArray(event) && event.length == 0) || event['changed'] == "") {
      this.disableUploadBtn = true;
    }
    else {
      this.disableUploadBtn = false;
    }
    delete this.qbConfig.customFieldList;
    this.showSubmit = false;
    this.multiCriteriaFile = event;
    setTimeout(() => {
      this.showQBuilder = true;
      this.enableQueryBuilder();
    }, 50);
  }

  /**
 * Method invoked when File Parser completes loading the table
 * @param event 
 */
  onParseComplete(event) {
    this.showQBuilder = false;
    this.qbConfig.customFieldList = {};
    this.qbConfig.customFieldList.dataset = [];
    let parserDataset = event?.sheet;
    parserDataset.forEach(element => {
      Object.keys(element.dataJSON[0]).forEach(column => {
        if (column != "")
          this.qbConfig.customFieldList.dataset.push({ "name": column, "id": column, "collection": "Worksheet Headers" });
      });
    });
    this.showQBuilder = true;
  }

  /**
   * Closes confirmation modal
  */
  closeConfirmationModal() {
    this.showSegmentedControl = false;
    this.openConfirmationModal = false;
    this.sgDashboardDataset.forEach(element => {
      element.checked = false;
    });
    this.sgDashboardDataset[this.unSelectedIndex].checked = true;
    if (this.unSelectedIndex == 0) {
      this.isStandardQBSelected = true;
      this.showSubmit = true;
    }
    else {
      this.isStandardQBSelected = false;
      this.showSubmit = false;
    }
    setTimeout(() => this.showSegmentedControl = true, 50);
  }

  /**
   * Clears QueryBuilder and closes the confirmation popup
  */
  clearQB() {
    this.enableQueryBuilder();
    this.showQBuilder = false;
    delete this.qbConfig.customFieldList;
    if (Object.keys(this.qbConfig.fields).length) {
      this.qbQuery = JSON.parse(JSON.stringify(qbQueryDefault));
      this.qbQuery.rules[0].field = Object.keys(this.qbConfig.fields)[2];
    }
    if (this.unSelectedIndex == 1) {
      this.isStandardQBSelected = true;
      this.showSubmit = true;
      this.corpusId = "";
      this.multiCriteriaFile = {};
    }
    else {
      this.isStandardQBSelected = false;
      this.showSubmit = false;
    }
    setTimeout(() => this.showQBuilder = true, 50);
    this.openConfirmationModal = false;
  }

  closeFileUploadModal(): void {
    this.openFileUploadConfirmModal = false;
  }

  closebypassConfirm(): void {
    this.openbypassConfirm = false;
  }

  /**
   * Remove table from the file parser micro front end
   */
  removeFileParserTable(): void {
    let tableDivElement = document.querySelector('div.sheetsData-container');
    let queryBuilderDivElement = document.querySelector('div.enabledQb');
    queryBuilderDivElement.classList.add('pointerFuncNone');
    tableDivElement.remove();
  }

  /**
   * Get all json file data and assign it to respective objevts
   */
  getAllJsonFilesData(): void {
    this.createRuleService.getAssetsJson(constants.RULE_QUERY_SPEC_JSON).subscribe((data) => {
      this.querySpecificationJson = data.sqlStructure;
      this.customSqlJson = data.customSQL;
      this.ruleLevelSelectionJson = data.sqlStructure;
      this.showQuerySpec = true;

      if (this.selectedProfileClientId != 59 &&
          this.querySpecificationJson &&
          this.querySpecificationJson[1] &&
          this.querySpecificationJson[1].groupControls) {
        const productControl = this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.PRODUCT)[0];
        if (productControl) {
          productControl.visible = false;
        }
      }
    });

    let tokenVal = localStorage?.getItem(constants.TOKEN);
    let _clientData = this.clientApiService.getAllClientsInPreferenceCenter();
    let _conceptData = this.conceptApiService.getProductConceptsId(tokenVal);

    forkJoin([_clientData, _conceptData]).subscribe(([clientData, conceptData]) => {
      if (clientData) {
        this.clientData = clientData.map(x => ({ value: x.clientId, name: x.clientName }));
      }
      if (conceptData) {
        this.conceptData = conceptData.executionConceptAnalyticResponse
          .filter(x => x.clientId === this.selectedProfileClientId || (this.selectedProfileClientId === ANTM_ID && x.clientId === 0))
          .map(x => ({ id: x.exConceptReferenceNumber, name: x.exConceptReferenceNumber }));

        if (this.ruleLevelSelectionJson &&
            this.ruleLevelSelectionJson[1] &&
            this.ruleLevelSelectionJson[1].groupControls) {
          const conceptControl = this.ruleLevelSelectionJson[1].groupControls.filter((x) => x.name == constants.CONCEPT_ID)[0];
          if (conceptControl) {
            conceptControl.options = this.conceptData;
          }
        }
      }
      this.showLoader = false;
    }, error => {
      this.clientData = [];
      this.showLoader = false;
    });

  }

  /**
    * triggers when chip is closed
   */
  closeStateChip(selectedState) {
    this.showQuerySpec = false;
    this.conceptIdSelectedForChips = this.conceptIdSelectedForChips.filter(x => x !== selectedState);
    this.conceptIdSelected = this.conceptIdSelectedForChips;
    this.querySpecificationJson[1].groupControls.filter((x) => x.name == constants.CONCEPT_ID)[0].selectedVal = this.conceptIdSelected;
    setTimeout(() => {
      this.showQuerySpec = true;
    }, 0);
  }

  /**
   * triggers on change of query spec dynamic form
  */
  mapValuesFromQuerySpecToJson(event): void {
    this.querySpecDetailsResponse = event.current;
    this.conceptIdSelected = [];
    let ruleLevel = event.current.group.rulesLevel;
    let selectedProduct = event.current.group.product;
    this.isRuleLevelPresent = ruleLevel == undefined ? false : true;

    switch (ruleLevel) {
      case constants.CONCEPT_LEVEL:
        this.conceptIdSelected = event.current.group?.conceptId;
        this.levelIndicator = constants.CONCEPT_LEVEL;
        this.querySpecificationJson[1].groupControls.find(c => c.name == constants.CLIENTID).selectedVal = undefined;
        this.conceptIdSelectedForChips = [...this.conceptIdSelected.map((c) => c)];
        this.conceptIdSelected = this.conceptIdSelectedForChips;
        this.levelIndicator = constants.CONCEPT_LEVEL;
        this.clientIdForECP = null;
        this.clientIdSelected = null;
        this.isRuleLevelPresent = true;
        break;
      case constants.GLOBAL_LEVEL:
        this.clientIdForECP = null;
        this.clientIdSelected = null;
        this.conceptIdSelected = [];
        this.conceptIdSelectedForChips = [];
        this.querySpecificationJson[1].groupControls.find(c => c.name == constants.CONCEPT_ID).selectedVal = undefined;
        this.querySpecificationJson[1].groupControls.find(c => c.name == constants.CLIENTID).selectedVal = undefined;
        this.levelIndicator = constants.GLOBAL_LEVEL;
        this.isRuleLevelPresent = true;
        break;
      case constants.CLIENT_LEVEL:
        this.clientIdSelected = this.clientData.find((clientObj) =>
          clientObj.value == event.current.group?.clientId
        )?.name
        this.querySpecificationJson[1].groupControls.find(c => c.name == constants.CONCEPT_ID).selectedVal = undefined;
        this.clientIdForECP = event.current.group?.clientId;
        this.levelIndicator = constants.CLIENT_LEVEL;
        this.conceptIdSelected = [];
        this.conceptIdSelectedForChips = [];
        this.isRuleLevelPresent = true;
        break;

      default:
        this.conceptIdSelectedForChips = [];
        break;
    }
    if (selectedProduct) {
      this.product = selectedProduct;
    }
    this.querySpecDetailsFormEvent = event;
    if (this.querySpecDetailsResponse.sqlType == constants.QUERY_BUILDER) {
      this.showQueryBuilderComponents = true;
    }
    else {
      this.levelIndicator = constants.GLOBAL_LEVEL;
      this.showQueryBuilderComponents = false;
    }
    this._onRuleLevelChange(event);
    setTimeout(() => (this.resetValidFields(), 100));

  }

  /**
   * Method will be called on custom SQL change
   */
  _onRuleLevelChange(event): void {
    this.levelIndicator = event.current.group.rulesLevel;
    let conceptFrmMaster = this.masterDataFromAPI[constants.QUERY_FIELDS]?.find(c => c.value == constants.CLNT_ID)?.options.find((item) => item.id === this.selectedProfileClientId);
    conceptFrmMaster = [conceptFrmMaster]?.map(data => {
      return {
        "name": data.name,
        "value": data.id
      }
    });

    if (event.current.group.rulesLevel == constants.CLIENT_LEVEL && event.current.group.clientId) {
      this.clientIdSelected = this.queryBuilderClientDataset?.find(c => c.id == event.current.group?.clientId)?.name ?? conceptFrmMaster[0]?.name;
      this.clientIdForECP = event.current.group.clientId;
      // this.conceptIdSelected = "";
    } else if (event.current.group.rulesLevel == constants.CONCEPT_LEVEL && event.current.group.conceptId) {
      // this.conceptIdSelected = event.current.group.conceptId;
      this.clientIdSelected = "";
    }


    if (event.current.group.rulesLevel == constants.CLIENT_LEVEL && !event.current.group.clientId) {
      this.querySpecificationJson[1].groupControls.find(c => c.name == constants.CLIENTID).options = this.queryBuilderClientDataset?.map(data => {
        return {
          "name": data.name,
          "value": data.id
        }
      }) ?? conceptFrmMaster;
    }
  }

  ruleLevelChange(event) {
    this.ruleLevelFormEvent = event;
  }

  /**
  * Method will be called on custom SQL change
  */
  _onSqlChange(event): void {
    this.customSql = event.current.customSql;
  }

}
