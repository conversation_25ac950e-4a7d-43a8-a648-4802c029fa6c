import { Component, OnInit, ElementRef, ViewEncapsulation } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ROUTING_LABELS } from 'src/app/_constants/menu.constant';
import { Router } from '@angular/router';
import { forkJoin } from 'rxjs';
import { environment } from 'src/environments/environment';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { UtilitiesService } from 'src/app/_services/utilities.service';
import { ClientApiService } from 'src/app/_services/client-preference-api.service';
import { ToastService } from 'src/app/_services/toast.service';
import { ROLES_CONSTANTS } from '../constants/roles-screen-constants';
import { AuthService } from 'src/app/_services/authentication.services';


@Component({
  selector: 'app-view-role',
  templateUrl: './view-role.component.html',
  styleUrls: ['./view-role.component.sass'],
  encapsulation: ViewEncapsulation.None
})
export class ViewRoleComponent implements OnInit {
  editRoleFormJSON: any;
  permissionsDS: any;
  editRoleScreenData: any;
  permissionsColumnConfig: any;
  permissionsPickListColumnConfig: any;
  isTableReady: boolean = false;
  isSkillsLoaded: boolean
  isStandard: boolean = true
  _formData: any;
  clientNames: any;
  inventoryTypeNames: any;
  productNames: any;
  breadcrumbDataset: any = [
    {
      label: 'Home',
      url: '/'
    },
    {
      label: 'Roles Management',
      url: '/settings/roles'
    }, {
      label: 'View Role'
    }]
  isCarelonAdmin: boolean = false;

  constructor(private router: Router, private userManagementSvc: UserManagementApiService,
    private utilityService: UtilitiesService, private alertService: ToastService,
    private clientApiService: ClientApiService, private authService: AuthService) { }

  /**
   * Method fires on form Value Change
   * @param event 
   */
  onRoleChange(event: Event): void {
    // emits on form value change
  }

  /**
   * Navigate to the home page
   * @param event 
   */
  selectedLink(event: any): void {
    this.router.navigate([event.selected.url]);
  }

  /**
   * Method to set the selected Form Values
   */
  setFormData(): void {
    this.editRoleFormJSON.forEach(e => {
      if (e['name'] == 'clientId' || e['name'] == 'productId' || e['name'] == 'inventoryId') {
        e['name'] == 'clientId' ? e['value'] = localStorage.getItem('selected-client-name') : "";
        e['name'] == 'productId' ? e['value'] = localStorage.getItem('selected-product-name') : "";
        e['name'] == 'businessDivision' ? e['value'] = localStorage.getItem('selected-business-division') : "";
        e['name'] == 'inventoryId' ? e['value'] = localStorage.getItem('selected-inventoryTypeName') : "";
        e['disabled'] = true;
      } else {
        if (e.name == ROUTING_LABELS.CLIENT_SITE) {
          this._formData[e.name] == ROUTING_LABELS.ONSHORE ? (e[ROUTING_LABELS.VALUE] = false) : (e[ROUTING_LABELS.VALUE] = true);
          this._formData[e.name] == ROUTING_LABELS.ONSHORE ? (e[ROUTING_LABELS.SELECTED_VALUE] = false) : (e[ROUTING_LABELS.SELECTED_VALUE] = true);
          e[ROUTING_LABELS.DISABLED] = true;
        } else {
          e[ROUTING_LABELS.VALUE] = this._formData[e.name];
          e[ROUTING_LABELS.SELECTED_VALUE] = this._formData[e.name];
          e[ROUTING_LABELS.DISABLED] = true;
          e['options']?.forEach(element => {
            element['enabled'] = false;
          });
        }
      }
    })
  }

  ngOnInit() {
    let selectedCientId = Number(localStorage.getItem('selected-row-clientID'));
    let selectedRoleId = Number(localStorage.getItem('selected-row-RoleID'));
    let selectedProdId = Number(localStorage.getItem('selected-row-ProdID'));
    let selectedBusinessDivision = localStorage.getItem('selected-business-division') || '';
    let _fetchUser = this.userManagementSvc.getAssetsJson('./assets/json/settings/role-management/role-form.json');
    let _fetchPage = this.userManagementSvc.getAssetsJson('./assets/json/settings/role-management/permission-role.json');
    let _getRoleData = this.userManagementSvc.getIndividualRole(selectedCientId, selectedRoleId, selectedProdId);
    let _getScreenList = this.userManagementSvc.getListOfScreensForAddRole();
    let _getAllClientNames = this.clientApiService.getAllClientsInPreferenceCenter();
    let _getAllTemplateData = this.clientApiService.getAllTemplates();
    let _allProductsData = this.clientApiService.getAllProducts();

    this.isCarelonAdmin = this.authService.isWriteOnly;

    forkJoin([_fetchUser, _fetchPage, _getRoleData, _getScreenList, _getAllClientNames, _allProductsData]).subscribe(
      ([team, pages, roleData, screenListForAddRoleTable, allClients, productData]) => {

        roleData["responseData"].forEach(roleDataObj => {
          roleDataObj['reminderDate'] = this.utilityService.getDbgDateFormat(roleDataObj['reminderDate']);
        });
        if (this.isCarelonAdmin) {
          team.find((x) => x.name == ROLES_CONSTANTS.TEAM_TYPE)[ROLES_CONSTANTS.DISABLED] = false;
        } else {
          team.find((x) => x.name == ROLES_CONSTANTS.TEAM_TYPE ? team.pop() : '')
        }
        this._formData = roleData["responseData"][0];
        this.editRoleScreenData = roleData["responseData"];
        this.clientNames = allClients.map(x => ({ "id": x.clientId, "name": x.clientName }));
        this.productNames = productData.map(x => ({ "id": x.prodId, "name": x.productName }));

        this.clientNames.forEach(clientNameList => {
          if (roleData[ROLES_CONSTANTS.RESPONSEDATA][0].clientId == clientNameList.id) {
            localStorage.setItem('selected-client-name', clientNameList.name);
          }
        });
        this.productNames.forEach(productNameList => {
          if (roleData[ROLES_CONSTANTS.RESPONSEDATA][0].prodId === productNameList.id) {
            localStorage.setItem('selected-product-name', productNameList.name);
          }
        });

        this.editRoleFormJSON = team
        this.setFormData();
        let clientId;
        let productId;
        let businessDivision
        this.editRoleFormJSON.forEach(e => {
          if (e['name'] == 'clientId') {
            clientId = e;
          }
          else if (e["name"] == 'productId') {
            productId = e;
          }
          else if (e["name"] == 'businessDivision') {
            businessDivision = e;
          }
        });

        if (clientId && clientId['value'] == ROLES_CONSTANTS.ANTHEM) {
          if (productId && productId['value'] == ROLES_CONSTANTS.PRODUCTIDVALUE) {
            if (businessDivision) {
              businessDivision['visible'] = true;
            }
          }
          else {
            if (businessDivision) {
              businessDivision['visible'] = false;
            }
          }
        }
        else {
          if (businessDivision) {
            businessDivision['visible'] = false;
          }
        }


        this.permissionsDS = []
        this.permissionsColumnConfig = JSON.parse(JSON.stringify(pages[ROLES_CONSTANTS.COLUMN_CONFIG]));
        this.permissionsColumnConfig.colDefs.filter(x => x.field == ROLES_CONSTANTS.CREATE).forEach(element => element.customFormatter = this.customFormatterFnCreate);
        this.permissionsColumnConfig.colDefs.filter(x => x.field == ROLES_CONSTANTS.VIEW).forEach(element => element.customFormatter = this.customFormatterFnRead);
        let navigationObjs: any = JSON.parse(JSON.stringify(screenListForAddRoleTable.responseData));
        for (let i = 0; i < navigationObjs.length; i++) {
          let matched = {};
          this.editRoleScreenData.forEach(element => {
            if (element.screenId == navigationObjs[i].masterId) {
              matched = element;
            }
          });

          this.permissionsDS.push({ "functional_page": navigationObjs[i].name, "roleScreenId": null, "roleId": null, "screenId": navigationObjs[i].masterId, "create": matched['create'], "view": matched['view'], "createUserId": navigationObjs[i].creatUserId, "createDateTime": null, "lastUpdateUserId": navigationObjs[i].lastUpdtUserId, "prodName": navigationObjs[i].prodName })
        }

        this.setFormData();
        this.isTableReady = true;
        this.isSkillsLoaded = true;
      },
      error => {
        this.alertService.setErrorNotification({
          notificationBody: error,
        });
      }
    )
  }

  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFn(event: any): string {
    return event?.value ? `<input type="checkbox" class="create-check" checked="true" disabled="true">` : `<input type="checkbox" class="create-check" disabled="true">`;
  }
  /**
   * 
   * Recursive function to get the children navigation links
   * @param parentItem 
   */
  recursiveChildElements(parentItem: any): void {
    if (parentItem.children) {
      for (let j = 0; j < parentItem.children?.length; j++) {
        this.permissionsDS.push({ "id": parentItem.children[j].name + j, "functional_page": parentItem.children[j].name, "create": this._formData?.create, "read": this._formData?.read })

        this.recursiveChildElements(parentItem.children[j]);
      }
    }
  }

  /**
   * Method fires on cell click
   * @param e 
   */
  onCellClick(e: Event): void {

  }
  /**
   * Method to show standard/pick list view
   * @param e 
   */
  changeView(e: any): void {
    e.toggle ? this.isStandard = true : this.isStandard = false
  }

  /**
  * Custom Formatter method to represent cell as checkBox
  * @param event 
  */
  customFormatterFnCreate(event: any): string {
    return event?.dataContext.create ? `<input type="checkbox" class="create-check" checked="${event?.dataContext.create}" disabled="true">` : `<input type="checkbox" class="create-check" disabled="true">`;
  }

  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFnRead(event: any): string {
    return event?.dataContext.view ? `<input type="checkbox" class="read-check" checked="${event?.dataContext.view}"disabled="true">` : `<input type="checkbox" class="read-check" disabled="true">`;
  }

  /**
  * Going Back to previous page
  */
  backToPreviousPage() {
    this.router.navigate(['/settings/roles']);
  }

}