{"name": "marketplace-file-parser", "version": "0.0.1", "peerDependencies": {"@ng-select/ng-select": "12.0.7", "xlsx": "^0.18.5"}, "dependencies": {"tslib": "^2.3.0"}, "sideEffects": false, "module": "fesm2022/marketplace-file-parser.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/marketplace-file-parser.mjs", "esm": "./esm2022/marketplace-file-parser.mjs", "default": "./fesm2022/marketplace-file-parser.mjs"}}}