<span class="title"><a (click)="backToListPage()"><i class="fa fa-chevron-circle-left"></i></a><span> {{type == 'edit'?
    'Edit': 'Terminate'}}</span> Fee
  Schedule of {{ editData['productName']}}</span>
<hr />
<div class="row">
  <div class="col-md-5 mar-0">
    <label for="name" class="label-title">Bundle Name: </label>
    <span class="label-value">{{editData['bundleName']}}</span>
  </div>

  <div class="col-md-3 mar-0">
    <label for="name" class="label-title">Created By: </label>
    <span class="label-value">{{editData['createdby']}}</span>
  </div>
  <div class="col-md-3 mar-0">
    <label for="name" class="label-title">Created Date: </label>
    <span class="label-value">{{editData['createdDate'] | date : 'MM-dd-YYYY'}}</span>
  </div>
</div>
<div class="row mar-top-20">
  <div class="col-md-5 mar-0">
    <label for="description" class="label-title">Bundle Description: </label>
    <span class="label-value">{{editData['bundleDescription']}}</span>
  </div>
  <div class="col-md-3 mar-0">
    <label for="name" class="label-title">Updated By: </label>
    <span class="label-value">{{editData['updatedBy']}}</span>
  </div>
  <div class="col-md-3 mar-0">
    <label for="name" class="label-title">Updated Date: </label>
    <span class="label-value">{{editData['updatedDate'] | date : 'MM-dd-YYYY'}}</span>
  </div>
</div>
<div class="mar-top-20">
  <marketplace-dynamic-form [formJSON]="feeScheduleJson" [isSubmitNeeded]="false" *ngIf="showForm"
    (onValueChange)="formValid($event,true)" #formRef>
  </marketplace-dynamic-form>
</div>

<span class="btn-span pad-btm-30">
  <marketplace-button [label]="'Cancel'" [type]="'secondary'" [name]="'secondary'" (onclick)="backToListPage()">
  </marketplace-button>

  <marketplace-button [enabled]="isEnabled" [label]="'Submit'" [type]="'primary'" [name]="'primary'"
    (onclick)="validateEditForm()">
  </marketplace-button>

</span>
<div class="modal" tabindex="-1" role="dialog" [ngStyle]="{'display':popupDisplayStyle}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-header-custom">
          <h4 class="modal-title custom-title">Attention !</h4>
        </div>
        <span class="btn-span"><a (click)="closePopup()"><i
              class="fa fa-times-circle-o fa-2x close-icon-color"></i></a></span>
      </div>
      <div class="modal-body custom-message">
        <p class="pad-30">You have chosen the <b>end date as {{endDate}}</b></p>
        <p class="pad-25">Please click continue if you wish to proceed</p>
      </div>
      <div class="modal-footer">
        <marketplace-button [label]="'EDIT'" [type]="'secondary'" [name]="'secondary'" (onclick)="closePopup()">
        </marketplace-button>

        <marketplace-button [label]="'Continue'" [type]="'primary'" [name]="'primary'" (onclick)="closePopup()">
        </marketplace-button>

      </div>
    </div>
  </div>
</div>
<marketplace-popup [open]="showRestrictPopUp" [size]="'small'" (onClose)="closePopup()">
  <div mpui-modal-header>
    <h5 class="modal-title custom-title">Attention !</h5>
  </div>
  <div mpui-modal-body class="custom-message">
    <p class="pad-35">Respective bundle is inactive, please activate and retry</p>
  </div>
  <div mpui-modal-footer>
    <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closePopup()">">
    </marketplace-button>

  </div>
</marketplace-popup>