/* app-bundle-list .card {
  border: 3px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
} */
app-bundle-list .btn-wrap-text {
  overflow: hidden;
  white-space: nowrap;
  display: inline-block;
  text-overflow: ellipsis;
}
app-bundle-list .table-title {
  float: left;
  width: 400px;
  left: 195px;
  top: 384px;
  
  font-style: normal;
  font-weight: bold;
  font-size: 24px;
  line-height: 34px;
  color: #000000;
  padding: 12px 0px 0px 17px;
}
app-bundle-list .btn-span {
  margin-left: 88%;
  margin-bottom: 5px;
}
app-bundle-list .table-container{
  margin-left: -3px;
}
app-bundle-list .btn-ruleadd {
  background: #5009B5;
  
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #ffffff;
}
app-bundle-list .add-new-rules-link {
  color: #ffffff;
  text-decoration: none;
}
app-bundle-list .quickaction-title {
  float: left;
  width: 200px;
  height: 24px;
  left: 195px;
  top: 148px;
  
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #2453a6;
  padding: 25px 0px 20px 30px;
}
app-bundle-list .mb-3 {
  margin-top: 1rem;
  margin-left: 1rem;
}
app-bundle-list .tp-bt-rem-1 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
/* app-bundle-list .dashbord-card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 2rem 1rem 2rem 1rem;
} */
app-bundle-list .fa-caret-right {
  font-size: 31px;
  color: #5009B5;
  float: right;
  padding-right: 20px;
}

app-bundle-list .card-body {
  clear: both;
}
/* app-bundle-list .card-title {
  
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 17px;
  color: #161616;
} */
app-bundle-list .setup-rule {
  margin-top: 5px;
}

app-bundle-list .fa-list,
app-bundle-list .fa-chevron-circle-left {
  font-size: 30px;
  color: #5009B5;
}
app-bundle-list .fa-plus:before {
  color: #ffffff;
}
app-bundle-list .pd-righ-10 {
  padding-right: 10px;
}
app-bundle-list .pd-righ-20 {
  padding-right: 20px;
}

/*Rules dashboard Table action menu dropdown*/
app-bundle-list .dropdown {
  position: absolute;
  background-color: gray;
  padding: 5px;
  outline: none;
  opacity: 0;
  min-width: 100%;
  overflow: auto;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  background: #ffffff;
  box-shadow: 0px 0px 20px rgb(0 0 0 / 40%);
  border-radius: 4px;
  right: 10px;
}
app-bundle-list .dropdown a {
  color: black;
  padding: 5px 10px;
  text-decoration: none;
  display: block;
}
app-bundle-list input:not(:checked) ~ .dropdown {
  display: none;
}
app-bundle-list input:checked ~ .dropdown {
  opacity: 1;
  z-index: 100;
  transition: opacity 0.2s;
  z-index: 1;
}
app-bundle-list .three-dots:after {
  cursor: pointer;
  color: #444;
  content: "\2807";
  font-size: 20px;
  z-index: 999;
}
app-bundle-list .table-action-menu .fa-eye,
app-bundle-list .table-action-menu .fa-edit,
app-bundle-list .table-action-menu .fa-trash,
app-bundle-list .table-action-menu .fa-plus {
  font-size: 20px;
  color: #5009B5 !important;
  padding-right: 15px;
}
app-bundle-list .table-action-menu .fa-plus:before {
  color: #5009B5 !important;
}
app-bundle-list .table-action-menu {
  border: 8.5px solid #ffffff;
}
app-bundle-list .fa-plus {
  width: 35px;
}

/* table overflow css overwrite*/
app-bundle-list .btn.bundle-dashboard {
  color: white;
  font-weight: 350;
}
app-bundle-list .btn.focus.bundle-dashboard,
app-bundle-list .btn:focus.bundle-dashboard {
  outline: 0;
  box-shadow: 0 0 0 0rem;
}
app-bundle-list .btn.bundle-dashboard-big {
  padding: 0.075rem 0.1rem !important;
  color: white;
  font-weight: 200;
}
app-bundle-list .btn.focus.bundle-dashboard-big,
app-bundle-list .btn:focus.bundle-dashboard-big {
  outline: 0;
  box-shadow: 0 0 0 0rem;
}
app-bundle-list .dropdown-container {
  padding-top: 5px;
}
app-bundle-list .search-filter .operator.input-group-addon {
  display: none !important;
}

app-bundle-list .btn-active {
  background: #D9F5F5;
  width: 100%;
  border: 1px solid #00BBBA;
}
app-bundle-list .btn-pending {
  background: #E1EDFF;
  width: 100%;
  border: 1px solid #44B8F3;
}
app-bundle-list .btn-inactive {
  background: #F5F5F5;
  width: 100%;
  border: 1px solid #231E33;
}
app-bundle-list .btn.bundle-list {
  /* padding: 0.075rem 0.45rem !important; */
  color: white;
  font-weight: 350;
}
app-bundle-list .bundles-text-field {
  color: #2d6fe1;
  margin-right: 30px;
}

app-bundle-list .fa-chevron-circle-left {
  font-size: 25px;
  color: #5009B5;
  margin-right: 5px;
}
