import { Component, OnInit, ElementRef, <PERSON>Encapsulation, On<PERSON>estroy, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { Subscription, forkJoin } from 'rxjs';
import { formatDate } from '@angular/common';
import { constants, emptyProduct, nullValueProduct, tableColumnConfig } from './users-constants';
import { CookieService } from 'ngx-cookie-service';
import { cloneDeep } from 'lodash';
import { UserManagementApiService } from './_services/user-management-api.service';
import { ToastService } from '../_services/toast.service';
import { AuthService } from '../_services/authentication.services';
import { ExternalSOAService } from '../_services/external-soa.service';
import { UtilitiesService } from '../_services/utilities.service';
import { RegistrationConstants } from '../_constants/app.constants';
import { environment } from '../../environments/environment';
import { AUTH_CONFIG } from '../_constants/menu.constant';
import { IExternalUserStatus } from '../_models/external/external-user-status';
import { externalAuthenticationConstants } from '../_helpers/helpers.constants';
import { EXTERNALUSER } from '../_models/external/external-user-constants';
import { LoaderService } from '../_services/loader.service';

const USERSLIST = 'Users List';
const AUDITLIST = 'Audit Trail';
const VERIFIER = 'Carelon Verifier';
const AUTHENTICATOR = 'Carelon Authenticator';

@Component({
  selector: 'app-user',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class UsersComponent implements OnInit, OnDestroy {
  internalFlagFromEvent: any;
  selectedClientSite: string = "";
  invalidCharPresent: boolean;
  userStatusPayload: IExternalUserStatus;
  constructor(private el: ElementRef, private http: HttpClient, private router: Router,
    private userManagementSvc: UserManagementApiService, private notificationService: ToastService,
    private cookieService: CookieService,
    private authService: AuthService, private soaService: ExternalSOAService, private dateService: UtilitiesService, private loaderService: LoaderService) { }

  isFormSubmitted: boolean = false;
  openAddNewUsersModel: any;
  openAuditUsersModel: any;
  userDataset: any = [];
  userConfig: any = {};
  userKebab: any = {
    "field": constants.REGISTRATION_STATUS,
    "dataset": [{
      "value": constants.AWAITING_APPROVAL,
      "options": [{
        "label": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View User`,

        "id": constants.VIEW_USER
      }]
    },
    {
      "value": constants.REQUEST_NOT_APPROVED,
      "options": [{
        "label": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View User`,
        "id": constants.VIEW_USER
      }]
    },
    {
      "value": "default__options",
      "options": [{
        "label": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View User`,
        "id": constants.VIEW_USER
      }, {
        "label": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.2475 4.13527C14.3108 4.026 14.2958 3.88362 14.2021 3.79L12.2104 1.80078C12.098 1.68854 11.9157 1.68949 11.8045 1.80288L3.29748 10.478L3.1828 10.6102C3.07664 10.749 2.99653 10.9065 2.94695 11.0751L2.12229 13.8761L4.92496 13.0528C5.14963 12.9868 5.35461 12.8664 5.52186 12.7025L14.2001 4.1962L14.2475 4.13527ZM6.72185 13.9268L6.55919 14.0753C6.22418 14.3605 5.83235 14.5728 5.40867 14.6974L1.09901 15.9649C0.798101 16.0535 0.472866 15.9705 0.251072 15.7487C0.0292778 15.527 -0.0536594 15.2017 0.0348549 14.9008L1.30232 10.5914L1.37189 10.3823C1.52539 9.96971 1.76416 9.59316 2.07349 9.27773L10.5805 0.602618L10.7228 0.470713C11.4612 -0.150374 12.5447 -0.153581 13.2869 0.464641L13.4218 0.587841L15.4135 2.57706C16.2009 3.36351 16.1949 4.64144 15.4001 5.42045L6.72185 13.9268ZM15.1429 14.2855C15.6163 14.2855 16 14.6693 16 15.1426C16 15.5766 15.6776 15.9352 15.2592 15.992L15.1429 15.9998H8.85716C8.38378 15.9998 8.00002 15.616 8.00002 15.1426C8.00002 14.7087 8.32248 14.3501 8.74085 14.2933L8.85716 14.2855H15.1429Z" fill="black"/>
</svg>  Edit User`,
        "id": constants.EDIT_USER
      }
      ]
    }
    ]
  };
  // to be re-instated later
  // { label: '<i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit User New', id: constants.EDIT_USER_MANAGEMENT }



  groupDataset: any = [];
  groupConfig: any = {};
  isTableGroupReady: boolean = false;

  userFormJSON: any;
  isUserJSONReady: boolean = false;

  newSkillJSON: any;
  previewSkillsJSON: any;
  isNewSkillsReady: boolean = false;

  breadcrumbDataset: any = [{
    label: 'Home',
    url: '/'
  }, {
    label: 'Users Management'
  }]

  openPanelIndex = "0";
  userStepperConfig: any = [];

  selectedRows: any;
  pageNum: number = 1;
  showEditButton: boolean = false;

  currentUserFormData: any;
  currentUserSkillsFormData = {};
  isUserListSelected: boolean = true;

  isShowMore = false;

  userAuditDataset: any = [];
  userAuditConfig: any = {};
  tabledraw: any
  selectedSegment: string = USERSLIST;

  sgDashboardDataset: any = [];

  maxTargetSelection: number = 20;
  redrawForms: any;
  userConfigFormValues: any = [];
  userConfigDataset: any = [];
  userConfigPreviewDataset: any = [];
  showPreviewRoleForm: boolean = false;
  showClientAndProdForPreviewSkill: boolean = false;
  errorMsg: string;
  isSegmentStepperReady: boolean = false;
  masterData: any;
  stateValues: any = [];
  userTypeOptions = { false: "External", true: "Internal" };

  matchedUsers: boolean = false;
  userSelectionMismatch: any;

  categoryData: any = [];
  isUserTableReady: boolean = false;
  invalidUserRole: boolean = false;
  clntRoleRelationList: any = [];
  prodRoleRelationList: any = [];
  clientRoleMasterData: any[] = [];
  rolesBasedOnUserSite: any[] = [];       // this is based on client site onshore or offshore
  isCarelonAdmin: boolean = false;
  isClientAdmin: boolean = false;
  isInternalUser: boolean = false;
  userInfoPreviewDtls: any;
  showFooterButtons: boolean = true;
  isFormRepeaterReady: boolean = false;
  isRejectUserPopupReady: boolean = false;
  tableDataJSON: any;
  rowData: any = {};
  tableColumnConfig: any = tableColumnConfig;

  distinctClients: object[] = [];
  distinctProducts: object[] = [];
  selectedClientForSkills: {
    name: string,
    id: number
  };
  selectedPIProductForSkills: {
    name: string,
    id: number
  };
  selectedClientForSkillsId: number;
  selectedPIProductForSkillsId: number;
  openPIProductWarning: any;
  openExistingRolePopup: any;
  showCobSubskill: boolean = false;
  currentMaxClaimAssignment: number = 250;
  @ViewChild('formRef') formRef: any;

  private skillDefSubscription: Subscription | undefined;
  private roleDefSubscription: Subscription | undefined;
  skillProductForChanged: boolean = false;
  clientAndProductWarningMessage: any = { product: false, client: false }
  ngOnInit(): void {
    this.isInternalUser = sessionStorage.getItem('isInternalUser') ? true : false
    //C2P this.isInternalUser = this.authService.getStoredUserProfile()?.responseData?.internalFlag;
    //C2P this.authService.currentUserProfile.subscribe(data => {
    //   this.isInternalUser = data?.responseData?.internalFlag;
    // });
    //this.isInternalUser = this.authService.isInternalUser;

    this.userManagementSvc.getAssetsJson(constants.USER_FORM_CONFIGURATION_JSON).subscribe((data) => {
      this.userStepperConfig = data["userStepperConfig"];
      this.sgDashboardDataset = data["sgDashboardDataset"];
      this.isSegmentStepperReady = true;
    });
    this.errorMsg = constants.ERROR_MSG;
    let _fetchUser = this.userManagementSvc.getAssetsJson(constants.USER_FORM_JSON);
    let _fetchUserlist = this.userManagementSvc.getAssetsJson(constants.USER_LIST_JSON);
    let _fetchAuditUsers = this.userManagementSvc.getAssetsJson(constants.USER_LIST_AUDIT_JSON);
    let _fetchUsersList = this.userManagementSvc.getUsersList();
    //C2P
    //let _fetchUserRoles = this.userManagementSvc.getuserRolesList();
    this.isCarelonAdmin = this.authService.isWriteOnly;
    this.loaderService.show();
    forkJoin([_fetchUser, _fetchUserlist, _fetchAuditUsers, _fetchUsersList]).subscribe(//C2P _fetchUserRoles 
      ([userForm, users, audit, usersList]) => {//C2P userRoles
        this.userFormJSON = userForm;
        this.userFormJSON = this.userFormJSON.filter(x => x.id != constants.MODIFIED_BY && x.id != constants.MODIFIED_DATE);
        this.userFormJSON.find(x => x.name == constants.STATUS).value = constants.ACTIVE;
        //C2P this.isClientAdmin = !!userRoles.roleDetails.find(roleObj => roleObj[1] == constants.CLIENT_ADMINISTRATOR);
        if (environment.loginType != AUTH_CONFIG.INTERNAL) {
          this.userFormJSON.find((x) => x.name == constants.INTERNAL_FLAG)[constants.DISABLED] = true;
          this.userFormJSON.find((x) => x.name == constants.INTERNAL_FLAG)["value"] = false;

          this.userFormJSON.find((x) => x.name == constants.CLIENT_SITE)[constants.DISABLED] = true;
          this.userFormJSON.find((x) => x.name == constants.CLIENT_SITE)["value"] = false;
        }
        else if (this.isCarelonAdmin) {
          this.userFormJSON.find((x) => x.name == constants.INTERNAL_FLAG)[constants.DISABLED] = false;
        } else {
          this.userFormJSON.find((x) => x.name == constants.INTERNAL_FLAG ? this.userFormJSON.pop() : '')
        }
        this.isUserJSONReady = true;
        this.userConfig = users['columnConfig'];
        this.userDataset = usersList[constants.RESPONSE_DATA];

        this.userDataset.forEach(item => {
          item.userType = item.internalFlag ? "Internal" : "External"
        })
        this.userAuditConfig = audit['columnConfig'];
        this.userAuditConfig.colDefs.filter(x => x.field == 'form' || x.field == 'skill' || x.field == 'team' || x.field == 'actionView').forEach(element => {
          element.field == 'form' || element.field == 'skill' || element.field == 'team' ? element.customFormatter = this.customFormatterFn : element.customFormatter = this.customFormatterFnAction
        });
        let isAwatAppReq = false;
        if (Array.isArray(this.userDataset)) {
          isAwatAppReq = this.userDataset && this.userDataset.some(i => i.registrationStatus === constants.AWAITING_APPROVAL);
        }

        this.userConfig.colDefs.forEach(e => {
          e.field === constants.USER_ID ? (e.customFormatter = this.renderCaseId) : '';
          /* This is to umcommented when we get permissions field from API */
          e.field === constants.PERMISSIONS ? (e.customFormatter = this.renderCaseId) : '';
          e.field == constants.REGISTRATION_STATUS ? e.customFormatter = this.customFormatterStatus : "";
          if (e.field == "action") {
            this.isClientAdmin && isAwatAppReq ? e.customFormatter = this.renderActionColumn : e.visible = false;
          }
        });

        this.userAuditDataset = audit['dataset'];
        this.isUserTableReady = true;
        this.loaderService.hide();
      }
    );
  }
  /**
   * custom formatter for display inventory button
   * @param event 
   */
  renderActionColumn(event) {
    let rStatus = event.dataContext.registrationStatus;
    if (rStatus === constants.AWAITING_APPROVAL) {
      if (event.columnDef.id === 'approve') {
        return `
            <button type='button' class='btn btn btn-approve' data-toggle='modal' id='approveBtn'  data-target='#inventory_model'>Approve</button>
           `;
      } else {
        return `
            <button type='button' class='btn btn btn-reject' data-toggle='modal' id='rejectBtn'  data-target='#inventory_model'>Reject</button>
           `;
      }
    } else {
      return ``;
    }

  }
  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFn(event): string {
    return event?.value ? `<i class="fa fa-check-circle permission-granted" aria-hidden="true"></i>` : `<i class="fa fa-times-circle permission-denied" aria-hidden="true"></i>`;
  }

  /**
  * Custom Formatter method to represent cell as view Details
   @param event 
  */
  customFormatterFnAction(event: Event): string {
    return `<i class="fa fa-external-link action-view-more" aria-hidden="true"></i>`;
  }

  /**
   * @function renderCaseId - Renders and styles priority column data
   */
  public renderCaseId(event): string {
    return `<div class="userId-as-hyperlink">${event.value}</div>`;
  }

  /**
  * @function onPaginationClick invokes when page changes
  * 
  */
  onPaginationClick(event) {
    this.pageNum = event.paging.pageNum;
    this.pageNum = this.pageNum + 1;
  }

  /**
 * Invoked when rows selected in landing table
 * @function landingTableRowSelection perform validations when rows selected in landing table
 * @param event selected rows data
 */
  landingTableRowSelection(event: any) {
    this.selectedRows = event.map(e => e);
    if (this.selectedRows !== undefined && this.selectedRows?.length > 0) {
      this.showEditButton = true;
    }
    else {
      this.showEditButton = false;
    }
  }

  onCellClicked(event) {
    this.rowData = event.currentRow;
    if (event.eventData.target.id === 'approveBtn') {
      localStorage.setItem('user-row-selected', JSON.stringify(event.currentRow));
      this.router.navigate(["product-catalog/security/users/" + constants.EDIT_USER], { queryParams: { mode: `Preview` } });

    } else if (event.eventData.target.id === 'rejectBtn') {
      this.isRejectUserPopupReady = true;
    }
  }
  onRejectClose() {
    this.isRejectUserPopupReady = false;
  }
  onRejectUser(event) {
    this.isRejectUserPopupReady = false;
    let userDetailsObj = {
      userId: this.rowData.userId,
      registrationStatus: constants.REQUEST_NOT_APPROVED,
      requestType: "reject",
      updatedBy: this._getLoggedInUserId()
    }
    this.userManagementSvc.rejectUserApproval(userDetailsObj)
      .subscribe((data) => {
        if (data.responseCode == 200) {
          this._showErrorNotification(constants.USER_REJECTED, data.responseData.userName + constants.USER_REJECTION_MSG);
          this.rejectExternalUser(data.responseData ? data.responseData.userId : this.rowData.userId);
        }
        else {
          this._showErrorNotification(constants.ERROR, data.responseData);
        }
      }, err => {
        this._showErrorNotification(constants.ERROR, "");
      });
  }
  /**
   * Method to refresh page after reject success
   */
  onRejectSuccess() {
    this.ngOnInit();
  }
  /**
   * Method to call Soa to update user when rejected
   */
  rejectExternalUser(userId) {

    this.soaService.searchExtUser(userId).subscribe((data: any) => {
      this.userStatusPayload = {
        comments: constants.DISABLE_COMMENTS,
        enableUser: false
      }
      if (data == externalAuthenticationConstants.NOT_FOUND || (data?.exceptions && data.exceptions[0].code == externalAuthenticationConstants.USER_IS_NOT_FOUND)) {
        this.onRejectSuccess();
      } else if (data?.exceptions) {
        this.notificationService.setErrorNotification({
          notificationHeader: externalAuthenticationConstants.ERROR,
          notificationBody: externalAuthenticationConstants.SEARCH_USER_ERRMSG
        });
      }
      if (data.user && data.user[0].userRoleEnum == EXTERNALUSER.APPLICATION) {
        this.updateUserStatusForReject(userId, this.userStatusPayload);
      } else {
        this.onRejectSuccess();
      }
    }, (error: any) => {
      if (error == externalAuthenticationConstants.NOT_FOUND) {
        this.onRejectSuccess();
      } else {
        this.notificationService.setErrorNotification({
          notificationHeader: externalAuthenticationConstants.ERROR,
          notificationBody: externalAuthenticationConstants.SEARCH_USER_ERRMSG
        });
      }
    })
  }
  /**
   * Update User Status
   * @param token 
   * @param status 
   * @param userStatusPayload 
   */
  updateUserStatusForReject(user: string, userStatusPayload: IExternalUserStatus) {
    this.soaService.updateUserStatus(user, userStatusPayload).subscribe((data: any) => {
      this.onRejectSuccess();
    }, (error: any) => {
      this.notificationService.setErrorNotification({
        notificationHeader: RegistrationConstants.ERROR,
        notificationBody: constants.ERR_MSG_SOA
      });
    });
  }

  /**
   * Method to show changed Audit values
   *  @param event 
   */
  rendererTableClicked(e: any): void {
    let _clickedElement: any = e.eventData.target;
    // On changed click
    if (_clickedElement.classList.contains('action-view-more')) {
      this.openAuditModalPopup();

      this.currentUserFormData = this.userDataset.find(x => x.userId == e.currentRow.userId)
      this.currentUserSkillsFormData = e.currentRow;
    }
  }

  /**
   * Method fires on add or delete a row in user role tab
   * @param event 
   */
  onAddOrDeleteRow(event: any) {
    if (event.action == 'delete') {
      this.checkUserRoleFormVaid();
    }
  }

  /**
   * method to route the page
   * @param event 
   */
  selectedLink(event: any): void {

    if (event.selected.label == 'Home') {
      // this.authService.onHomeClick();
    }
    this.router.navigate([event.selected.url]);
  }

  /**
   * Method to open modal dialog
   * @param event 
   */
  openModalPopup(event: Event): void {
    let _fetchSkills = this.userManagementSvc.getAssetsJson(constants.SKILL_HISTORY_JSON);
    let _fetchMasterData = this.userManagementSvc.getUserMasterData();
    let _rolesList = this.userManagementSvc.getRolesList();

    if (!this.isCarelonAdmin) return
    this.currentUserFormData = {};
    this.userConfigFormValues = [];
    this.userConfigDataset = {};
    this.isSegmentStepperReady = false;
    setTimeout(() => {
      this.isSegmentStepperReady = true;
    }, 50);
    forkJoin([_fetchSkills, _fetchMasterData, _rolesList]).subscribe(
      ([skill, masterData, rolesList]) => {


        skill['groupControls'].forEach(element => element.selectedVal = null);
        this.userFormJSON.forEach(element => {
          if (element.selectedVal)
            element.selectedVal = null;
        });

        this.newSkillJSON = skill;
        this.masterData = masterData;


        let internalFlag = sessionStorage.getItem(constants.INTERNAL_FLAG)
        this.internalFlagFromEvent = internalFlag == "true" ? "internal" : "external";
        if (this.isCarelonAdmin) {
          this.clientRoleMasterData = rolesList[constants.RESPONSE_DATA];   // BELOW COMMENTED LINES ARE NEEDED IN FUTURE
          // this.rolesBasedOnUserSite = rolesList[constants.RESPONSE_DATA]?.filter(c => c.teamType == this.internalFlagFromEvent)
        }
        else {
          this.clientRoleMasterData = rolesList[constants.RESPONSE_DATA].filter(c => c.clientId == sessionStorage.getItem(constants.CLIENT_ID));
          // this.rolesBasedOnUserSite = rolesList[constants.RESPONSE_DATA]?.filter(c => c.teamType == this.internalFlagFromEvent)
        }

        this.loadClientAndProductForSkills(); //Load clients and products to prepopulate the skills client and product selections as well as roles screen selections
        let clientId = sessionStorage.getItem("clientId");
        this.populateMasterDataOnForm(masterData); //By default loads Anthem client and CAD product.
        this.constructRelationForClientRole();
        this.skillProductForChanged = false
        this.clearSelectionDropdown("all");
        this.openAddNewUsersModel = Date.now();
        this.isTableGroupReady = true;

      });
  }

  //     BELOW COMMENTED LINES ARE NEEDED IN FUTURE
  /**
   * Method to show only roles based on client site
   */
  onClientSiteChange(event: any) {
    if (event.current.clientSite != event.previous.clientSite || event.current.internalFlag != event.previous.internalFlag) {
      this.userConfigFormValues = [];
      this.userConfigDataset = {};
      this.constructRelationForClientRole();
    }
  }


  /**
   * Method to close the modal dialog
   */
  closePopup(): void {
    this.skillProductForChanged = false;
    this.openAddNewUsersModel = false;
  }

  /**
 * Method to open Audit modal dialog
 */
  openAuditModalPopup(): void {
    this.openAuditUsersModel = Date.now();
  }

  /**
   * Method to close audit modal dialog
   */
  closeAuditPopup(): void {
    this.openAuditUsersModel = false;
  }

  validateSelecedUsers() {
    if (this.selectedRows?.length === 1) {
      this.matchedUsers = true;
      return;
    }
    const firstUser = this.selectedRows[0];
    const allMatch = this.selectedRows.every(user =>
      user.userType === firstUser.userType && user.clientSite === firstUser.clientSite && user.permissions === firstUser.permissions && user.status === firstUser.status
    );

    if (!allMatch) {
      this.matchedUsers = false;
      this.userSelectionMismatch = Date.now();

    }
    else {
      this.matchedUsers = true;
      this.userSelectionMismatch = false;
    }
  }

  navigateToEditUsers() {
    this.validateSelecedUsers();
    if (this.matchedUsers) {
      sessionStorage.setItem('selected-users-list', JSON.stringify(this.selectedRows));
      localStorage.setItem('user-row-selected', JSON.stringify(this.selectedRows[0]));
      this.router.navigate(["product-catalog/security/users/" + constants.EDIT_USER_MANAGEMENT, { 'bulkEdit': true }]);
    }
  }

  /************************ On Table Kebab Selection *****************************/
  onUserAction(event: any): void {
    //To be replaced with service
    localStorage.setItem('user-row-selected', JSON.stringify(event.currentRow));
    switch (event.text) {
      case "View User":
        //C2P this.router.navigate(["product-catalog/security/users/" + constants.VIEW_USER]);
        this.router.navigate(["users/" + constants.VIEW_USER]);
        break;
      case "Edit User":
        //C2P this.router.navigate(["product-catalog/security/users/" + constants.EDIT_USER]);
        this.router.navigate(["users/" + constants.EDIT_USER]);
        break;
      case "Edit User New":
        this.router.navigate(["product-catalog/security/users/" + constants.EDIT_USER_MANAGEMENT]);
        break;
      default:
        break;
    }
  }

  /**
   * Validate forms and consolidate before save API call
   */
  _validateUserForms(event: any): void {
    this.showFooterButtons = false;
    if (this.isCarelonAdmin) {
      let userInfo = this.currentUserFormData;
      userInfo[constants.STATUS] == constants.ACTIVE ? userInfo[constants.STATUS] = true : userInfo[constants.STATUS] = false;

      userInfo[constants.CLIENT_ROLE] = this.createRolesPayload();
      userInfo[constants.SKILLS] = this.selectedPIProductForSkillsId ? this.createSkillsPayload() : [];
      let newMarketSkillsValueInuserInfo = userInfo["skills"].find((e) => e.skillType == "Market Skill")?.values
      if (newMarketSkillsValueInuserInfo) {
        let uniqueMarketSkillsValueInUserInfo = [...new Set(newMarketSkillsValueInuserInfo)];
        userInfo["skills"].find((e) => e.skillType == "Market Skill").values = uniqueMarketSkillsValueInUserInfo;

      }
      userInfo[constants.MANAGER_NAME] = userInfo[constants.MANAGER_NAME] ? userInfo[constants.MANAGER_NAME] : null;
      userInfo[constants.CREATED_BY] = this._getLoggedInUserId();
      userInfo[constants.CREATED_DATE] = this._getFormatDate();
      userInfo[constants.UPDATED_BY] = this._getLoggedInUserId();
      userInfo[constants.UPDATED_DATE] = this._getFormatDate();
      userInfo[constants.REMINDER_DATE] = this.dateService.getECPDateFormat(userInfo[constants.REMINDER_DATE]);
      userInfo[constants.REQUEST_TYPE] = constants.CREATE_API;
      this._createUser(userInfo);
    }
  }

  /**
   * Add new user to list
   */
  _createUser(userInfo): void {
    //API method to post data
    this.userManagementSvc.createUpdateUser(userInfo)
      .subscribe((data) => {
        if (data.responseCode == 200) {
          this._showSuccessNotification(constants.SUCCESS, data.responseName);
          this.userManagementSvc.getUsersList().subscribe(response => {
            this.userDataset = response[constants.RESPONSE_DATA];
          });
          this.closePopup();
        }
        else {
          this._showErrorNotification(constants.ERROR, data.responseData);
        }
      }, err => {
        this._showErrorNotification(constants.ERROR, "");
      })
  }

  /**
   * Method to refresh and get Group list
   */
  refreshData(): void {
    this.isTableGroupReady = false
    this.userManagementSvc.getAssetsJson(constants.USER_LIST_AUDIT_JSON)
      .subscribe(
        (data: any) => {
          this.groupConfig = data['columnConfig'];
          this.groupConfig['colDefs'] = data['columnConfig']['colDefs'].filter(x => x.field == constants.GROUP_NAME || x.field == constants.DESCRIPTION || x.field == constants.ROLE);
          this.groupDataset = data['dataset'];
          setTimeout(() => this.isTableGroupReady = true, 50);
        }
      )
  }

  /**
   * Method Fires on user form change
   * @param event 
   */
  _onUserSelection(event: any, step: number): void {
    event.value.clientSite = event.value.clientSite ? constants.CLIENT_OFFSHORE : constants.CLIENT_ONSHORE;
    this.currentUserFormData = event.value;
    this.isFormSubmitted = false
    this.invalidCharPresent = false;
    this.userFormJSON[3].customErrMsg = '';
    let managerName = event.value.managerName;
    managerName ? this.userManagementSvc.checkManagerNameValidation(managerName) : '';
    if (this.userManagementSvc.validationOfName) {
      this.isFormSubmitted = true
      this.userFormJSON[3].customErrMsg = RegistrationConstants.INVALID_CHAR
      this.invalidCharPresent = true;
    }
    this.populatePreviewScreenData();
    this._checkFormisValid(event, step);
  }

  /**
   * Method will be called on the user form change to allow for the next step
   * @param event and @param step
   */
  _checkFormisValid(event: any, step: any): void {
    let valueToBeUpdated: boolean = false;
    if (event.status == constants.VALID) {
      valueToBeUpdated = true;
    }
    if (this.invalidCharPresent == true) {
      valueToBeUpdated = false;
    }
    if (this.invalidUserRole) {
      this.userStepperConfig[1].valid = valueToBeUpdated;
      this.userStepperConfig[1].enabled = valueToBeUpdated;
    }
    else {
      for (var i = step; i < this.userStepperConfig.length; i++) {
        this.userStepperConfig[i].valid = valueToBeUpdated;
        this.userStepperConfig[i].enabled = valueToBeUpdated;
      }
    }
  }

  /**
   * Method Fires on user form Skills change
   * @param event 
   */
  _onUserSkillSelection(event: any): void {
    this.isNewSkillsReady = false;
    if (event.current.groupSkillJson["Market Skill"] !== event.previous.groupSkillJson["Market Skill"]) {
      let isWestSelected = event.current.groupSkillJson["Market Skill"].includes(1001);
      let isNortheastSelected = event.current.groupSkillJson["Market Skill"].includes(1002);
      let isCentralSelected = event.current.groupSkillJson["Market Skill"].includes(1003);
      let california = this.stateValues.find(e => e.value === "California").id;
      let nevada = this.stateValues.find(e => e.value === "Nevada").id;
      let colorado = this.stateValues.find(e => e.value === "Colorado").id;
      let connecticut = this.stateValues.find(e => e.value === "Connecticut").id;
      let maine = this.stateValues.find(e => e.value === "Maine").id;
      let newHamsphire = this.stateValues.find(e => e.value === "New Hampshire").id;
      let kentucky = this.stateValues.find(e => e.value === "Kentucky").id;
      let ohio = this.stateValues.find(e => e.value === "Ohio").id;
      let wisconsin = this.stateValues.find(e => e.value === "Wisconsin").id;
      let missouri = this.stateValues.find(e => e.value === "Missouri").id;
      let indiana = this.stateValues.find(e => e.value === "Indiana").id;
      let all = this.stateValues.find(e => e.value === "ALL")?.id;

      if (isWestSelected || isNortheastSelected || isCentralSelected) {
        const indexOfAll = event.current.groupSkillJson["Market Skill"].indexOf(all);
        if (indexOfAll > -1) {
          event.current.groupSkillJson["Market Skill"].splice(indexOfAll, 1);
        }
        switch (isWestSelected || isNortheastSelected || isCentralSelected) {
          case isWestSelected:
            event.current.groupSkillJson["Market Skill"].push(california); // CA california
            event.current.groupSkillJson["Market Skill"].push(nevada); // CO NEVADA
            event.current.groupSkillJson["Market Skill"].push(colorado); // NV Colorado
            const index = event.current.groupSkillJson["Market Skill"].indexOf(1001);
            if (index > -1) {
              event.current.groupSkillJson["Market Skill"].splice(index, 1);
            }
            break;
          case isNortheastSelected:
            event.current.groupSkillJson["Market Skill"].push(connecticut); // CT Connecticut
            event.current.groupSkillJson["Market Skill"].push(maine); // ME Maine
            event.current.groupSkillJson["Market Skill"].push(newHamsphire); // NH New Hamsphire 
            const indexNorth = event.current.groupSkillJson["Market Skill"].indexOf(1002);
            if (indexNorth > -1) {
              event.current.groupSkillJson["Market Skill"].splice(indexNorth, 1);
            }
            break;
          case isCentralSelected:
            event.current.groupSkillJson["Market Skill"].push(kentucky); //KY Kentucky
            event.current.groupSkillJson["Market Skill"].push(ohio); // OH Ohio
            event.current.groupSkillJson["Market Skill"].push(wisconsin); // WI Wisconsin 
            event.current.groupSkillJson["Market Skill"].push(missouri); // MO Missouri
            event.current.groupSkillJson["Market Skill"].push(indiana); // IN INDIANA
            const indexCentral = event.current.groupSkillJson["Market Skill"].indexOf(1003);
            if (indexCentral > -1) {
              event.current.groupSkillJson["Market Skill"].splice(indexCentral, 1);
            }
            break;

          default:
            break;
        }
      }
      setTimeout(() => {
        this.isNewSkillsReady = true;
      }, 10);
    }
    this.currentUserSkillsFormData = event.current.groupSkillJson;
    let grpNumberSkills = event.current.groupSkillJson[constants.GROUP_NUMBER];
    let allId = this.masterData[constants.SKILLS].find(c => c.skillName == constants.GROUP_NUMBER)?.values?.find(c => c.value == constants.ALL)?.id;
    let nonKCId = this.masterData[constants.SKILLS].find(c => c.skillName == constants.GROUP_NUMBER)?.values?.find(c => c.value == constants.EXCLUDE_25)?.id;
    let groupIdMasterData = this.masterData[constants.SKILLS].find(c => c.skillName == constants.GROUP_NUMBER)?.values;

    if (grpNumberSkills?.length > 1) {
      if (this.masterData[constants.SKILLS].find(c => c.skillName == constants.GROUP_NUMBER)?.values?.find(c => c.id == grpNumberSkills[0])?.value == constants.ALL) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = this.currentUserSkillsFormData[constants.GROUP_NUMBER]?.filter(c => c != grpNumberSkills[0]);
      }
      else if (grpNumberSkills[grpNumberSkills.length - 1] == groupIdMasterData.find(c => c.value == constants.GROUP_ID_65).id
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(groupIdMasterData.find(c => c.value == constants.EXCLUDE_65).id)) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != groupIdMasterData.find(c => c.value == constants.EXCLUDE_65).id);
      }
      else if (grpNumberSkills[grpNumberSkills.length - 1] == groupIdMasterData.find(c => c.value == constants.EXCLUDE_65).id
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(groupIdMasterData.find(c => c.value == constants.GROUP_ID_65).id)) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != groupIdMasterData.find(c => c.value == constants.GROUP_ID_65).id);
      }
      else if (grpNumberSkills[grpNumberSkills.length - 1] == groupIdMasterData.find(c => c.value == constants.GROUP_ID_85).id
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(groupIdMasterData.find(c => c.value == constants.EXCLUDE_85).id)) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != groupIdMasterData.find(c => c.value == constants.EXCLUDE_85).id);
      }
      else if (grpNumberSkills[grpNumberSkills.length - 1] == groupIdMasterData.find(c => c.value == constants.EXCLUDE_85).id
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(groupIdMasterData.find(c => c.value == constants.GROUP_ID_85).id)) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != groupIdMasterData.find(c => c.value == constants.GROUP_ID_85).id);
      }
      else if (groupIdMasterData.filter(c => c.value.includes(constants.GROUP_ID_25)).map(c => c.id).includes(grpNumberSkills[grpNumberSkills.length - 1])
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(nonKCId)
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].toString() !== event.current.groupSkillJson[constants.GROUP_NUMBER].toString()) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != nonKCId);
      }
      else if (grpNumberSkills[grpNumberSkills.length - 1] == nonKCId &&
        groupIdMasterData.filter(value => event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(value.id)).map(c => c.value).find(c => c.includes(constants.GROUP_ID_25))) {
        event.current.groupSkillJson[constants.GROUP_NUMBER].forEach((element, index) => {
          if (groupIdMasterData.find(c => c.id == element).id != nonKCId && groupIdMasterData.find(c => c.id == element).value.includes(constants.GROUP_ID_25))
            this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != element)
        });
      }
      else {
        if (grpNumberSkills?.find(c => c == allId)) {
          this.currentUserSkillsFormData[constants.GROUP_NUMBER] = [allId];
        }
      }
    }

    if (this.currentUserSkillsFormData[constants.COB_SKILL].length > 0 && this.selectedPIProductForSkills.id === constants.COORDINATION_OF_BENEFITS_ID) {
      this.newSkillJSON[0][constants.GROUP_CONTROLS].map((skillItem) => (skillItem.name.toLowerCase() === constants.COB_SUB_SKILL.toLowerCase()) ? skillItem.visible = true : null);
    } else {
      this.newSkillJSON[0][constants.GROUP_CONTROLS].map((skillItem) => (skillItem.name.toLowerCase() === constants.COB_SUB_SKILL.toLowerCase()) ? (skillItem.visible = false, skillItem.selectedVal = []) : null);
    }

    if (event.current.groupSkillJson[constants.MAX_CLAIM_COUNT] != event.previous.groupSkillJson[constants.MAX_CLAIM_COUNT]) {
      this.currentMaxClaimAssignment = Number(event.current.groupSkillJson[constants.MAX_CLAIM_COUNT]);
    }

    this.isNewSkillsReady = false;
    this.newSkillJSON[0][constants.GROUP_CONTROLS].forEach(skill => {
      skill.name == constants.MARKET_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.MARKET_SKILL] : '';
      skill.name == constants.CLAIM_SYSTEM ? skill.selectedVal = this.currentUserSkillsFormData[constants.CLAIM_SYSTEM] : '';
      skill.name == constants.FUNDING_TYPE ? skill.selectedVal = this.currentUserSkillsFormData[constants.FUNDING_TYPE] : '';
      skill.name == constants.LOB_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.LOB_SKILL] : '';
      skill.name == constants.SERVICE_TYPE_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.SERVICE_TYPE_SKILL] : '';
      skill.name == constants.CONCEPT_CATEGORY_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.CONCEPT_CATEGORY_SKILL] : '';
      skill.name == constants.GROUP_NUMBER ? skill.selectedVal = this.currentUserSkillsFormData[constants.GROUP_NUMBER] : '';
      skill.name == constants.BLUE_CARD_ACCESS ? skill.selectedVal = this.currentUserSkillsFormData[constants.BLUE_CARD_ACCESS] : '';
      skill.name == constants.COB_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.COB_SKILL] : '';
      skill.name == constants.COB_SUB_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.COB_SUB_SKILL] : '';
      skill.name == constants.MAX_CLAIM_COUNT ? skill.value = this.currentMaxClaimAssignment : 250;
      skill.name == constants.MAX_CLAIM_COUNT ? this.currentUserSkillsFormData[constants.MAX_CLAIM_COUNT] = skill.selectedVal : [1003];
      skill.name == constants.CONCEPT_STATE ? skill.selectedVal = this.currentUserSkillsFormData[constants.CONCEPT_STATE] : '';
      skill.name == constants.MEMBER_BRAND_Label ? skill.selectedVal = this.currentUserSkillsFormData[constants.MEMBER_BRAND_Label] : '';
      skill.name == constants.SERVICE_PROVIDER_REGION_Label ? skill.selectedVal = this.currentUserSkillsFormData[constants.SERVICE_PROVIDER_REGION_Label] : '';
    });
    setTimeout(() => {
      this.isNewSkillsReady = true;
    }, 0);

  }

  /**
   * Method fires on segemented selection
   * @param event 
   */
  _onDashboardSGSelection(event: any): void {
    this.selectedSegment = event.selection.label
    switch (this.selectedSegment) {
      case AUDITLIST:
        this.isUserListSelected = false;
        this.tabledraw = Date.now()
        break;
      case USERSLIST:
        this.isUserListSelected = true;
        this.tabledraw = Date.now();
        break;
      default:
        break;
    }
  }

  /**
   * Method fires once stepper options are selected
   * @param event 
   */
  stepperNext(event) {
    if (event === 1) {
      this.checkUserRoleFormVaid();
    }
    if (event === 2) {
      //only proceed with data which has selections
      this.userConfigDataset = this.userConfigDataset.filter(c => { return c.formJSON.find(d => { return d.id === constants.ROLE_ID_CAMEL_CASE && d.selectedVal.length > 0 }) })

      if (Array.isArray(this.userConfigDataset) && !this.userConfigDataset.length) {
        this.populateNewUserRoleForm();
        this.showPreviewRoleForm = false;
      }
    }
    if (event === 3) {
      this.userInfoPreviewDtls.internalFlag = this.currentUserFormData.internalFlag ? true : false;

      if (this.selectedClientForSkills && this.selectedPIProductForSkills) {
        this.previewSkillsJSON = cloneDeep(this.newSkillJSON);
        this.previewSkillsJSON[0]['groupControls']?.forEach(element => element.disabled = true);
        this.showClientAndProdForPreviewSkill = true;
      } else {
        this.previewSkillsJSON = [];
        this.showClientAndProdForPreviewSkill = false;
      }

      this.populatepreviewUserRoleForm();

      let clientRoleObject = this.createRolesPayload();
      let clientData: Array<{ clientName: string, roleName: string }> = [];


      const uniqueClient = [...new Set(clientRoleObject.map(obj => obj.clientName))];
      uniqueClient.forEach(data => {
        let roleName = [];
        let role = clientRoleObject.filter(c => c.clientName != null && c.clientName == data);
        role.forEach(row => {
          roleName.push(row.roleName);
        })
        let name = roleName.join(', ');
        let client: string = data;
        clientData.push({ clientName: client, roleName: name });
      })
      this.tableDataJSON = clientData;
    }
  }

  /**
   * Method invoked when form values are changed
   * @param event 
   */
  _onRolesFormValueChange(event: any): void {
    //[0] since at any point of time only 1 stepper will be changed
    let selectedCl = (event.dataJSON.formJSON.filter((item) => item.id === constants.CLIENT_ID)[0].selectedVal);
    let selectedPr = (event.dataJSON.formJSON.filter((item) => item.id === constants.PROD_ID)[0].selectedVal);
    let selectedBD = (event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].selectedVal);

    if (selectedCl === undefined || selectedPr === undefined) {
      event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].disabled = true;
      event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].required = true;
      event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].selectedVal = null;
      event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].disabled = true;
      event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = null;
    }
    if (selectedCl && selectedCl != undefined && selectedPr && selectedPr != undefined) {
      if (selectedCl == constants.ANTHEM_CLIENT_ID && selectedPr == constants.CAD_PROD_ID) {
        event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].disabled = false;
        event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].required = true;
        event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].disabled = false;
        if (this.isClProdBDRoleAlreadyPresent(event, selectedCl, selectedPr, selectedBD)) {
          this.openExistingRolePopup = Date.now();
          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.CLIENT_ID)[0].selectedVal = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.PROD_ID)[0].selectedVal = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].selectedVal = [];
        }
      } else {
        event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].disabled = true;
        event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].required = false;
        event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].disabled = false;
        if (this.isClProdRoleAlreadyPresent(event, selectedCl, selectedPr)) {
          this.openExistingRolePopup = Date.now();
          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.CLIENT_ID)[0].selectedVal = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.PROD_ID)[0].selectedVal = [];
        }
      }
    }

    if (selectedCl && selectedCl !== '' && selectedPr && selectedPr !== '') {
      if (selectedCl == constants.ANTHEM_CLIENT_ID && selectedPr == constants.CAD_PROD_ID) {
        if (!selectedBD) {
          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].options = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];
        }
        if (selectedBD && selectedBD != '') {
          if (event.form.filter(item => item.event.current.clientName !== item.event.previous.clientName || item.event.current.prodName !== item.event.previous.prodName || item.event.current.busDivision !== item.event.previous.busDivision).length > 0) {

            event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];

          }
          selectedCl = parseInt(selectedCl);
          selectedPr = parseInt(selectedPr);

          let roleOptions = [].concat(this.clientRoleMasterData.filter(c => c.clientId === selectedCl && c.prodId === selectedPr && c.teamType === this.internalFlagFromEvent && c.clientSite === this.selectedClientSite && (c.businessDivision === selectedBD || !c.businessDivision)).map(c => {
            return {
              label: c.roleName,
              value: "" + c.roleId
            }
          }));
          roleOptions = this.filterRoles(event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal, roleOptions);
          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].options = roleOptions;
        }
      }
      else {
        if (event.form.filter(item => item.event.current.clientName !== item.event.previous.clientName || item.event.current.prodName !== item.event.previous.prodName).length > 0) {

          event.dataJSON.formJSON.filter((item) => item.id === "roleId")[0].selectedVal = [];

        }
        selectedCl = parseInt(selectedCl);
        selectedPr = parseInt(selectedPr);
        let roleOptions = [].concat(this.clientRoleMasterData.filter(c => c.clientId === selectedCl && c.prodId === selectedPr && c.teamType === this.internalFlagFromEvent && c.clientSite === this.selectedClientSite).map(c => {
          return {
            label: c.roleName,
            value: "" + c.roleId
          }
        }));
        roleOptions = this.filterRoles(event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal, roleOptions);
        event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].options = roleOptions;
      }
    } else {
      event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].options = [];
      event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];
      event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].selectedVal = null;
    }
    this.checkUserRoleFormVaid();
  }

  /* Restricting user to proceed further if user roles are not provided correctly */
  checkUserRoleFormVaid() {
    this.invalidUserRole = false;
    this.userConfigDataset?.some((dataSet, index) => {
      let eachRow = dataSet.ngModel?.value;
      let isRoleInValid: boolean = false;
      if (this.formRef) {
        isRoleInValid = this.formRef.dataset[index]["formJSON"].find(c => c.label == "Role").selectedVal.length == 0;
      }
      if (!eachRow || !eachRow['clientName'] || !eachRow['prodName'] || isRoleInValid) {
        this.userStepperConfig[2].valid = false;
        this.userStepperConfig[2].enabled = false;
        this.userStepperConfig[3].valid = false;
        this.userStepperConfig[3].enabled = false;
        this.invalidUserRole = true;
        return true;
      }
      else if (!this.invalidUserRole) {
        this.userStepperConfig[2].valid = true;
        this.userStepperConfig[2].enabled = true;
        this.userStepperConfig[3].valid = true;
        this.userStepperConfig[3].enabled = true;
      }
      return false;
    })
  }


  /**
  * Check if selected client and product is already present in the previous selections
  */
  isClProdRoleAlreadyPresent(event, selectedCl, selectedPr) {
    return this.userConfigDataset.filter(item => item.name !== event.dataJSON.name).map(item => item.formJSON).filter(item => item.find(f => f.id === 'prodId' && f.selectedVal === selectedPr)).filter(item => item.find(f => f.id === 'clientId' && f.selectedVal === selectedCl)).length > 0
  }

  isClProdBDRoleAlreadyPresent(event, selectedCl, selectedPr, selectedBd) {
    return this.userConfigDataset.filter(item => item.name !== event.dataJSON.name).map(item => item.formJSON).filter(item => item.find(f => f.id === constants.PROD_ID && f.selectedVal === selectedPr)).filter(item => item.find(f => f.id === constants.CLIENT_ID && f.selectedVal === selectedCl)).filter(item => item.find(f => f.id === constants.BUS_DIV_ID && f.selectedVal === selectedBd)).length > 0
  }


  /**
   * Method to get logged in userId from cookie
   * Returns string
   */
  _getLoggedInUserId(): string {
    return this.cookieService.get(constants.USER_ID).toUpperCase();
  }

  /**
  * Method to get formatted date
  * Returns string
  */
  _getFormatDate(): string {
    return formatDate(Date.now(), "yyyy-MM-dd", 'en-US');
  }

  /**
   * Common method called on showing success notofication on the top
   * @param header and @param message
   */
  _showSuccessNotification(header: string, message: string): void {
    this.notificationService.setSuccessNotification({
      notificationHeader: header,
      notificationBody: message,
    });
    this.showFooterButtons = true;
  }

  /**
   * Common method called on showing error notification on the top
   * @param header and @param message
   */
  _showErrorNotification(header: string, message: string): void {
    this.notificationService.setErrorNotification({
      notificationHeader: header,
      notificationBody: message,
    });
    this.showFooterButtons = true;
  }

  /**
   * Common method to load Add skills drop downs
   * @param masterData
   */
  loadSkillsDD(masterData) {
    this.stateValues = [];
    this.masterData.skills.forEach(e => {
      if (e.skillName == "Market Skill") {
        if (e.prodId === this.selectedPIProductForSkillsId && e.clientId === this.selectedClientForSkillsId) {
          this.stateValues = e.values;
        }
      }
    })
    if (this.selectedPIProductForSkillsId && this.selectedClientForSkillsId) {
      const skillJSON = this.newSkillJSON.length > 0 ? this.newSkillJSON[0]['groupControls'] : this.newSkillJSON['groupControls'];
      skillJSON.forEach(skill => {
        const clntProdSkill = masterData.skills.find(element => skill.name?.toLowerCase() === element.skillName?.toLowerCase() && element.prodId === this.selectedPIProductForSkillsId && element.clientId === this.selectedClientForSkillsId);
        if (skill.name == constants.MAX_CLAIM_COUNT) {
          if (clntProdSkill?.values[0]?.id) {
            skill.selectedVal = [clntProdSkill?.values[0]?.id];
          }
          skill.value = 250;
          this.currentMaxClaimAssignment = 250;
        }
        skill.options = [];
        skill.options = clntProdSkill?.values?.map(skillOptions => {
          return {
            label: skillOptions.value,
            value: skillOptions.id
          }
        }) ?? [];
      });
      this.newSkillJSON = this.newSkillJSON.length > 0 ? this.newSkillJSON : [this.newSkillJSON];
      this.userManagementSvc.addingAllOption(this.newSkillJSON, this.currentUserSkillsFormData);
      this.prePopulateSkillsBasedOnProductAndClient();
    }
  }

  /**
   * method to populate master data on all dropdowns
   * @param masterData 
   */
  populateMasterDataOnForm(masterData) {
    this.userFormJSON.find((x) => x.id == constants.EXPERIENCE_LVL_ID).options = masterData.experience;
    this.loadSkillsDD(masterData);
  }

  /**
   * method to populate user details on preview page   
   */
  populatePreviewScreenData() {
    this.userInfoPreviewDtls = {
      clientSite: this.currentUserFormData.clientSite,
      comments: this.currentUserFormData.comments,
      experienceLevelId: this.currentUserFormData.experienceLevelId ? this.masterData.experience.
        filter(c => c.id == this.currentUserFormData.experienceLevelId)[0]?.value : '',
      internalFlag: this.currentUserFormData.internalFlag,
      managerName: this.currentUserFormData.managerName?.length == 0 ? '' : this.currentUserFormData.managerName,
      reminderDate: this.currentUserFormData.reminderDate,
      status: this.currentUserFormData.status,
      userId: this.currentUserFormData.userId,
      userNm: this.currentUserFormData.userName,
    };
  }

  /**
   * method to populate clients and roles form details  
   */
  populateNewUserRoleForm() {
    this.isFormRepeaterReady = false;
    this.userConfigDataset = [];
    let roleProdConfig = new UsersClientRoleForm();
    let distinctMasterClnt = this.clientRoleMasterData.filter(c => c.clientName != null).filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    let distinctProducts = this.clientRoleMasterData.filter(c => c.prodName != null).filter(function (a) {
      var key = a.prodId + '|' + a.prodName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: "Client",
      type: constants.OPTION_TYPE_SELECT,
      multiple: false,
      closeOnSelect: true,
      name: "clientName",
      column: "4",
      disabled: false,
      hidden: false,
      required: true,
      allowUnique: false,
      id: "clientId",
      options: [].concat(distinctMasterClnt.map(mstClnt => {
        return {
          label: mstClnt.clientName,
          value: "" + mstClnt.clientId
        }
      })),
      relationship: this.clntRoleRelationList

    });

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: "Product",
      type: constants.OPTION_TYPE_SELECT,
      multiple: false,
      closeOnSelect: true,
      name: "prodName",
      column: "4",
      disabled: false,
      required: true,
      allowUnique: false,
      id: "prodId",
      options: [].concat(distinctProducts.map(mstClnt => {
        return {
          label: mstClnt.prodName,
          value: "" + mstClnt.prodId
        }
      })),
      relationship: this.prodRoleRelationList
    });

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: constants.BUSINESS_DIVISION,
      type: constants.OPTION_TYPE_SELECT,
      multiple: false,
      closeOnSelect: true,
      name: constants.BUS_DIV_NAME,
      id: constants.BUS_DIV_ID,
      column: "4",
      disabled: true,
      required: true,
      allowUnique: false,
      options: [
        {
          "label": constants.CSBD,
          "value": constants.CSBD
        },
        {
          "label": constants.GBD,
          "value": constants.GBD
        }
      ],
    });

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: "Role",
      type: constants.OPTION_TYPE_SELECT,
      multiple: true,
      closeOnSelect: false,
      allowUnique: false,
      name: "roleName",
      id: "roleId",
      column: "4",
      disabled: true,
      required: true,
      options: [],
      selectedVal: []
    });
    this.userConfigDataset = [].concat(roleProdConfig);
    setTimeout(() => {
      this.isFormRepeaterReady = true;
      this.checkUserRoleFormVaid();
    }, 50);
  }

  /**
  * customFormatterStatus funtion for status button in Role table
  * @param event 
  */
  customFormatterStatus(event): void {
    let btn,
      status = event[constants.DATACONTEXT].registrationStatus;
    if (status) {
      if (status === constants.ACTIVE) {
        btn = `<span class="headerbadge badge badge-pill badge-active">Active</span>`;
      } else if (status === constants.INACTIVE || status === constants.REQUEST_NOT_APPROVED) {
        btn = `<span class="headerbadge badge badge-pill badge-inactive">${status}</span>`;
      } else {
        btn = `<span class="headerbadge badge badge-pill badge-userStatus">${status}</span>`;
      }
    }
    return btn;
  }

  /**
   * To construct relationship among client and respective roles
   */
  constructRelationForClientRole() {
    this.clntRoleRelationList = [];
    this.internalFlagFromEvent = this.currentUserFormData.internalFlag ? "internal" : "external";
    this.selectedClientSite = this.currentUserFormData.clientSite ? this.currentUserFormData.clientSite : constants.CLIENT_ONSHORE;
    let relationshipNullObj = nullValueProduct;
    let relationshipSelectedValObj = emptyProduct;
    this.clntRoleRelationList.push(relationshipNullObj);
    this.clntRoleRelationList.push(relationshipSelectedValObj);
    let distinctClnts = this.clientRoleMasterData.filter(c => c.clientName != null).filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    let distinctProducts = this.clientRoleMasterData.filter(c => c.prodName != null).filter(function (a) {
      var key = a.prodId + '|' + a.prodName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    distinctClnts.forEach(client => {
      let relationshipProdObj = {
        updateDataset: [
          {
            id: "prodName", dataset: distinctProducts.filter(c => c.clientId === client.clientId).map(({ prodName, prodId }) => ({ label: prodName, value: prodId }))
          }],
        when: client.clientId
      }
      this.prodRoleRelationList.push(relationshipProdObj);

    });

    distinctProducts.forEach(client => {
      let relationshipObj = {
        updateDataset: [
          {
            id: "roleName", dataset: this.clientRoleMasterData.filter(c => c.clientId === client.clientId && c.prodId === client.prodId && c.teamType === this.internalFlagFromEvent && c.clientSite === this.selectedClientSite).map(({ roleName, roleId }) => ({ label: roleName, value: roleId }))
          }],
        when: client.clientId && client.prodId
      }
      this.clntRoleRelationList.push(relationshipObj);

    });

    this.populateNewUserRoleForm();
  }

  populatepreviewUserRoleForm() {
    this.userConfigPreviewDataset = [];

    this.userConfigPreviewDataset = this.userConfigDataset;

    if (this.userConfigDataset.filter(c => { return c.formJSON.find(d => { return d.id === constants.ROLE_ID_CAMEL_CASE && d.selectedVal.length > 0 }) })) {
      //All changes for this will be same as current edit form. Only that it will be disabled in Preview stepper.
      this.userConfigPreviewDataset.map(data => {
        return data.formJSON.map(item => {
          return item.disabled = true;
        });
      });
      this.showPreviewRoleForm = true;
    } else {
      this.userConfigPreviewDataset = [];
      this.showPreviewRoleForm = false;
    }
  }

  /**
  * @function createRolesPayload Creates request payload for roles stepper
  */
  createRolesPayload() {
    let clientObj = [];
    this.userConfigDataset?.forEach(data => {
      let rowValue = data.ngModel.value;
      rowValue.roleName?.forEach(role => {
        let clientData = this.clientRoleMasterData.filter(c => c.clientName !== null && c.clientId === parseInt(rowValue.clientName));
        clientObj.push({
          clientId: parseInt(rowValue.clientName),
          clientName: this.distinctClients.find(c => c[constants.ID] === parseInt(rowValue.clientName))[constants.NAME] ?? '',
          roleId: parseInt(role),
          roleName: this.clientRoleMasterData.find(c => c.roleId === parseInt(role))?.roleName ?? '',
          prodId: parseInt(rowValue.prodName),
          prodName: this.clientRoleMasterData.find(p => p[constants.PROD_ID] === parseInt(rowValue.prodName))[constants.PROD_NAME] ?? '',
          businessDivision: rowValue.busDivision,
        })
      });
    });

    return clientObj;
  }

  /**
  * @function createSkillsPayload Creates request payload for skills stepper
  */
  createSkillsPayload() {
    let skillObj = [],
      resultSkills = [];
    if (this.selectedClientForSkillsId && this.selectedPIProductForSkillsId) {
      Object.keys(this.currentUserSkillsFormData).forEach((key) => {
        let skillValue = key == constants.MAX_CLAIM_COUNT ? this.currentMaxClaimAssignment : null;
        if (this.currentUserSkillsFormData[key] != null) {
          (key === constants.COB_SUB_SKILL && this.selectedPIProductForSkills.id !== constants.COORDINATION_OF_BENEFITS_ID) ? this.currentUserSkillsFormData[key].values = [] : null;
          skillObj.push(this.userManagementSvc.loadingSkillValuesForProduct(key, this.currentUserSkillsFormData, this.masterData, this.selectedClientForSkills, this.selectedPIProductForSkills, skillValue));
        }
      });
    }
    resultSkills = [...skillObj];
    return resultSkills.filter(item => {
      return (item.prodId === 31) ? // if product is MAD then we will remove hidden fields from payload
        (item.skillType !== constants.BLUE_CARD_ACCESS && item.skillType !== constants.CONCEPT_CATEGORY_SKILL_1 && item.skillType !== constants.CONCEPT_CATEGORY_SKILL && item.skillType !== constants.CLAIM_SYSTEM) : item
    });
  }

  /**
  * @function loadClientAndProductForSkills Get unique clients and products to repopulate skills based on client and product selections
  */
  loadClientAndProductForSkills() {
    this.distinctClients = this.getDistinctClientsData(this.clientRoleMasterData);
    this.distinctProducts = this.getDistinctProductsData(this.clientRoleMasterData);
  }

  /**
  * @function getDistinctClientsData Gets distinct client data based on master data
  * @param clientRoleMasterData
  */
  getDistinctClientsData(clientRoleMasterData) {
    return clientRoleMasterData.filter(c => c.clientName != null).filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null)).map(({ clientName, clientId }) => ({ name: clientName, id: clientId }));
  }

  /**
   * @function getDistinctProductsData Gets distinct product data based on master data
   * @param clientRoleMasterData
   */
  getDistinctProductsData(clientRoleMasterData) {
    return clientRoleMasterData.filter(c => c.prodName != null).filter(function (a) {
      var key = a.prodId + '|' + a.prodName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null)).map(({ prodName, prodId }) => ({ name: prodName, id: prodId }));
  }

  /**
   * @function prePopulateSkillsBasedOnProductAndClient Default or parameterized prepopulation of skills based on client and product selection in skills stepper
   * @param selectedClientName
   * @param selectedProductName
   */
  prePopulateSkillsBasedOnProductAndClient(): void {
    /**
     * Mentioning all use case conditions here for any future reference.
     * 1. By default pre-populate client as Anthem and product as CAD
     * 2. if there is data for multiple clients and products, by default it will always pre-populate point1
     * 3. If user updates the client or product, the respective pre-population logic will happen in respective change events of client and product
     * 4. Hiding and showing the skill fields will also be updated on change event and by default it will be configurations for Anthem client and CAD product
     */

    this.isNewSkillsReady = (this.selectedClientForSkillsId && this.selectedPIProductForSkillsId) ? true : false;
    if (this.isNewSkillsReady) {
      const selectedClientInSkillStep = this.distinctClients.filter(c => c[constants.ID] === this.selectedClientForSkillsId);
      const selectedProductInSkillStep = this.distinctProducts.filter(c => c[constants.ID] === this.selectedPIProductForSkillsId);
      if (this.selectedClientForSkillsId) {
        let selecetdClient = selectedClientInSkillStep.filter(x => x[constants.ID] = this.selectedClientForSkillsId)[0]
        this.selectedClientForSkills = {
          name: selecetdClient[constants.NAME],
          id: selecetdClient[constants.ID],
        }
        this.currentUserFormData[constants.CLIENT_ID] = this.selectedClientForSkillsId;
        this.currentUserFormData[constants.CLIENT_NAME] = this.selectedClientForSkills.name;
      }

      if (this.selectedPIProductForSkillsId) {
        let selecetdClient = selectedProductInSkillStep.filter(x => x[constants.ID] = this.selectedPIProductForSkillsId)[0];
        this.selectedPIProductForSkills = {
          name: selecetdClient[constants.NAME],
          id: selecetdClient[constants.ID],
        }
        this.currentUserFormData[constants.PROD_ID] = this.selectedPIProductForSkillsId;
        this.currentUserFormData[constants.PROD_NAME] = this.selectedPIProductForSkills.name;
      }
      this.showAndHideSkillsBasedOnProductSelection();
      const filteredSkills = this.filterSkillsBasedOnClientAndProduct(this.selectedClientForSkills.id, this.selectedPIProductForSkills.id);
      filteredSkills.forEach(skill => {
        this.newSkillJSON[0].groupControls.forEach(e => {
          (e.name?.toLowerCase() === skill.skillType?.toLowerCase()) ? e[constants.SELECTED_VALUE] = skill.values : null;
        });
        (skill.skillType !== undefined) ? this.currentUserSkillsFormData[skill.skillType] = skill.values : null;
      });

    }
  }

  /**
   * @function showAndHideSkillsBasedOnProductSelection Shows and hides skill level fields based on product and client selections
   */
  showAndHideSkillsBasedOnProductSelection() {
    this.showCobSubskill = false;
    this.newSkillJSON[0].groupControls.forEach(e => {
      if (this.selectedPIProductForSkills.id === constants.COORDINATION_OF_BENEFITS_ID) {
        e.id === constants.COB_SKILL_ID ? e.visible = true : null;
        e.id === constants.CLAIM_SYSTEM_1 || e.id === constants.CONCEPT_CATEGORY_SKILL_2 || e.id === constants.BLUE_CARD_ID ? e.visible = false : null;
      } else {
        e.id === constants.COB_SKILL_ID || e.id === constants.COB_SUB_SKILL_ID ? e.visible = false : null;
        e.id === constants.CLAIM_SYSTEM_1 || e.id === constants.CONCEPT_CATEGORY_SKILL_2 || e.id === constants.BLUE_CARD_ID ? e.visible = true : null;
      }
      if (this.selectedClientForSkills.id == constants.ANTHEM_CLIENT_ID) {
        e.id === constants.MEMBER_BRAND ? e.visible = true : null;
        e.id === constants.SERVICE_PROVIDER_REGION ? e.visible = true : null;
      }
    });
  }

  /**
   * @function filterSkillsBasedOnClientAndProduct Filters skills based on client and product selection
   */
  filterSkillsBasedOnClientAndProduct(client, product) {
    return this.masterData.skills.filter(c => c.clientId === client && c.prodId === product);
  }

  /**
   * @function _onClientSelectionForSkills Triggers on client selection on skills stepper
   */
  _onClientSelectionForSkills(event) {
    this.selectedClientForSkills = event;
    if (this.clientAndProductWarningMessage.client) {
      this.openProductWarning();
    }
    else {
      this.clientAndProductWarningMessage.client = true;
      this.updatePIProductSelection()
    }
  }

  /**
   * @function _onPIProductSelectionForSkills Triggers on product selection on skills stepper
   */
  _onPIProductSelectionForSkills(event) {
    this.selectedPIProductForSkills = event;
    if (this.clientAndProductWarningMessage.product) {
      this.openProductWarning();
    }
    else {
      this.clientAndProductWarningMessage.product = true;
      this.updatePIProductSelection()
    }
  }

  /**
  * Method Fires warning popup on selection of a different product/client in skills stepper
  * @param event 
  */
  openProductWarning(): void {
    this.openPIProductWarning = Date.now();
  }

  /**
  * Closes warning poup making no changes to the selected value (ie; persisting previous selection)
  */
  closeProductWarning(): void {
    this.openPIProductWarning = false;

    this.selectedPIProductForSkills = {
      name: this.currentUserFormData[constants.PROD_NAME],
      id: this.currentUserFormData[constants.PROD_ID]
    }
    this.selectedClientForSkills = {
      name: this.currentUserFormData[constants.CLIENT_NAME],
      id: this.currentUserFormData[constants.CLIENT_ID]
    }
    this.selectedPIProductForSkillsId = this.currentUserFormData[constants.PROD_ID];
    this.selectedClientForSkillsId = this.currentUserFormData[constants.CLIENT_ID];

  }

  /**
  * @function updatePIProductSelection on click of yes in warning popup
  * Updates the product and client selection to the selected value (ie; persisting latest selection)
  */
  updatePIProductSelection(): void {
    this.openPIProductWarning = false;
    this.skillDefSubscription = this.userManagementSvc.getAssetsJson(constants.SKILL_HISTORY_JSON).subscribe(
      (skillDefinition) => {
        this.newSkillJSON = skillDefinition;
        this.skillProductForChanged = true;//hotfix
        this.userManagementSvc.getClientMasterData(this.selectedClientForSkills.id).subscribe((data) => {
          data.skills.forEach((e) => {
            if (e.skillName == "Market Skill") {
              e.values.push({
                id: 1001,
                value: "West"
              }, {
                id: 1002,
                value: "Northeast"
              }, {
                id: 1003,
                value: "Central"
              });
            }
          })
          this.masterData = data;
          this.loadSkillsDD(data);
        });
      }
    )
  }

  /** Cleaning memory leaks */
  ngOnDestroy() {
    (this.skillDefSubscription) ? this.skillDefSubscription.unsubscribe() : null;
    (this.roleDefSubscription) ? this.roleDefSubscription.unsubscribe() : null;
  }

  /**
   * filter role options based on the selection
   * Remove "authenticator" if "verifier" is selected and vice versa
   * @param selectedRoles 
   * @param roleOptions 
   */
  filterRoles(selectedRoles, roleOptions) {
    let verifierIdRole = roleOptions.find(x => x.label == VERIFIER);
    let authenticatorIdRole = roleOptions.find(x => x.label == AUTHENTICATOR);
    if (verifierIdRole && selectedRoles.find(x => x == verifierIdRole.value)) {
      let index = roleOptions.indexOf(authenticatorIdRole);
      if (index !== -1) {
        roleOptions.splice(index, 1);
      }
    }
    else if (selectedRoles.find(x => x == authenticatorIdRole?.value)) {
      let index = roleOptions.indexOf(verifierIdRole);
      if (index !== -1) {
        roleOptions.splice(index, 1);
      }
    }
    return roleOptions;
  }


  /**
* @function clearSelectionDropdown method to unselect the client and product on stepper 3
*/
  clearSelectionDropdown(whatToClear) {
    if (whatToClear === "all") {
      this.selectedPIProductForSkillsId = null;
      this.selectedClientForSkillsId = null;
      this.clientAndProductWarningMessage = { product: false, client: false }
    } else {
      this.selectedPIProductForSkillsId = null;
      this.clientAndProductWarningMessage.product = false;
    }
  }
}

class UsersClientRoleForm {
  name: string;
  formJSON: FormJsonClass[] = [];
}
class FormJsonClass {
  options?: any[] = [];
  optionName?: string;
  optionValue?: string;
  label?: string;
  type?: string;
  multiple?: boolean;
  closeOnSelect?: boolean;
  name?: string;
  column?: string;
  disabled?: boolean;
  hidden?: boolean;
  value?: string;
  key?: string;
  id?: string;
  selectedVal?: any;
  relationship?: any;
  relationOptions?: any[];
  required?: boolean;
  allowUnique: boolean;
  visible?: any;
}
class UsersClientPreviewRoleForm {
  name: string;
  formJSON: PreviewFormJsonClass[] = [];
}

class PreviewFormJsonClass {
  options?: any[] = [];
  optionName?: string;
  optionValue?: string;
  label?: string;
  type?: string;
  multiple?: boolean;
  closeOnSelect?: boolean;
  name?: string;
  column?: string;
  disabled?: boolean;
  hidden?: boolean;
  value?: string;
  key?: string;
  id?: string;
  selectedVal?: any;
}