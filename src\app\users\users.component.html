<div class="breadcrumb-holder">
    <marketplace-breadcrumb [id]="'breadcrumb-team'" [dataset]="breadcrumbDataset" (onSelection)="selectedLink($event)">
    </marketplace-breadcrumb>
</div>
<div class="page-wrapper">
    <div class="page-header">
        <h3>Users Management</h3>
    </div>

    <div>
        <div class="button-row">
            <div class="left-button-container left-button">
                <div *ngIf="false" (click)="navigateToEditUsers()"><!--showEditButton-->
                    <!-- (click)="navigateToEditUsers()" -->
                    <marketplace-button [size]="'medium'" [label]="'Edit'" [type]="'secondary'">
                    </marketplace-button>
                </div>
            </div>
            <!-- <div [hidden]="selectedSegment!='Users List' || !isInternalUser">
                <div class="right-button-container">
                    <marketplace-button [label]="'Add New User'" [type]="'primary'" (onclick)="openModalPopup($event)"
                        [enabled]="isCarelonAdmin">
                    </marketplace-button>
                </div>
            </div> -->
        </div>

        <div class="users-table_container">
            <marketplace-table [id]="'cad-users-table'" [dataset]="userDataset" [dropdownOptions]="userKebab"
                [columnDefinitions]="userConfig" [isRowSelectable]="false" [isExcelExportNeeded]="false"
                [isToggleColumnsNeeded]="false" (onCellClick)="onCellClicked($event)" [redraw]="tabledraw"
                (onDropdownOptionsClick)="onUserAction($event)" (onRowSelection)="landingTableRowSelection($event)"
                [isSingleSelectionRequired]="false" (onPaginationClick)="onPaginationClick($event)"
                [isUpdateNeeded]="false" *ngIf="isUserTableReady">
            </marketplace-table>
        </div>
    </div>
    <div [hidden]="selectedSegment!='Audit Trail'">
        <div class="users-audit-table_container">
            <marketplace-table [id]="'audit-users-table'" [dataset]="userAuditDataset"
                [columnDefinitions]="userAuditConfig" [isRowSelectable]="false" [isActionButtonsNeeded]='false'
                [redraw]="tabledraw" (onCellClick)="rendererTableClicked($event)">
            </marketplace-table>
        </div>
    </div>
</div>
<marketplace-popup [open]="openAddNewUsersModel" [size]="'xlarge'">
    <div mpui-modal-header>Add New User</div>
    <div mpui-modal-body *ngIf="isSegmentStepperReady">
        <marketplace-stepper [headerConfig]="userStepperConfig" [showFooterButtons]="showFooterButtons"
            (onStepChange)="stepperNext($event)" (onSubmit)="_validateUserForms($event)">
            <marketplace-step>
                <div class="skill-category">
                    <marketplace-dynamic-form *ngIf="isUserJSONReady" [isSubmitted]="isFormSubmitted"
                        [formJSON]="userFormJSON" [isSubmitNeeded]="false" (onValueChange)="_onUserSelection($event, 1)"
                        (onValueChanges)="onClientSiteChange($event)">
                    </marketplace-dynamic-form>
                </div>
            </marketplace-step>
            <marketplace-step>
                <ng-container *ngIf="userConfigDataset?.length > 0">
                    <!--C2P <span class="card-title">{{ item }}</span> -->
                    <marketplace-form-repeater *ngIf="isFormRepeaterReady" #formRef [(formModel)]="userConfigFormValues"
                        [dataset]="userConfigDataset" [maxItems]="maxTargetSelection" [recreate]="redrawForms"
                        (onValueChange)="_onRolesFormValueChange($event)" (onAddOrDeleteRow)="onAddOrDeleteRow($event)">
                    </marketplace-form-repeater>
                </ng-container>
            </marketplace-step>
            <marketplace-step>
                <div class="row">
                    <div class="col-2">
                        <div class="d-flex flex-column">
                            <marketplace-select [label]="'Client'" [type]="'single'" [placeholder]="'Select Client'"
                                [(ngModel)]="selectedClientForSkillsId" [dataset]="distinctClients"
                                (onSelection)="_onClientSelectionForSkills($event)"
                                (onClear)="clearSelectionDropdown('all')">
                            </marketplace-select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="d-flex flex-column">
                            <marketplace-select [label]="'Product'" [type]="'single'" [placeholder]="'Select Product'"
                                [(ngModel)]="selectedPIProductForSkillsId" [dataset]="distinctProducts"
                                (onSelection)="_onPIProductSelectionForSkills($event)"
                                (onClear)="clearSelectionDropdown('product')" [disabled]="!selectedClientForSkillsId">
                            </marketplace-select>
                        </div>
                    </div>
                    <div class="col-12 skill-container" *ngIf="selectedPIProductForSkillsId">
                        <div>
                            <marketplace-dynamic-form *ngIf="isNewSkillsReady" [formJSON]="newSkillJSON"
                                [isSubmitNeeded]="false" (onValueChanges)="_onUserSkillSelection($event)">
                            </marketplace-dynamic-form>
                        </div>
                    </div>
                </div>
            </marketplace-step>
            <marketplace-step>
                <h4 class="header-blue">Preview User details</h4>
                <div class="row" *ngIf="userInfoPreviewDtls">
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">User ID </div>
                        <div class="label-value">{{userInfoPreviewDtls['userId'] || "NA"}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">User Name </div>
                        <div class="label-value">{{userInfoPreviewDtls['userNm'] || "NA"}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">User Experience Level </div>
                        <div class="label-value">{{userInfoPreviewDtls['experienceLevelId'] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">Assigned Manager </div>
                        <div class="label-value">{{userInfoPreviewDtls['managerName'] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">Reminder date </div>
                        <div class="label-value">{{userInfoPreviewDtls['reminderDate'] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">Comments </div>
                        <div class="label-value">{{userInfoPreviewDtls['comments'] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">User Type </div>
                        <div class="label-value">{{userTypeOptions[userInfoPreviewDtls['internalFlag']] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">User Status </div>
                        <div class="label-value">{{userInfoPreviewDtls['status'] || 'NA'}}</div>
                    </div>
                    <div class="col-md-4 mar-0 uimp-key-values">
                        <div for="name" class="label-title">Client Site </div>
                        <div class="label-value">{{userInfoPreviewDtls['clientSite']}}</div>
                    </div>
                    <div class="col-12" *ngIf="showPreviewRoleForm">
                        <label class="header-blue-client">User Role</label>
                        <div class="col-12 plus-hide">
                            <marketplace-form-repeater *ngIf="userConfigDataset.length > 0"
                                [dataset]="userConfigPreviewDataset" #formRef [maxItems]="maxTargetSelection"
                                [recreate]="redrawForms">
                            </marketplace-form-repeater>
                        </div>
                    </div>
                    <!-- <div class="col-12" disabled="disabled">
                        <marketplace-table 
                        [id]="'sample-table-1'" 
                        [title]="'User Role'"
                        [dataset]="tableDataJSON" 
                        [columnDefinitions]="tableColumnConfig"
                        [isDraggableRowsNeeded]="false"
                        [isExcelExportNeeded] = "false"
                        [isToggleColumnsNeeded] = "false"></marketplace-table>
                    </div> -->
                    <div class="col-2">
                        <div class="d-flex flex-column">
                            <marketplace-select *ngIf="selectedClientForSkillsId" [label]="'Client'" [type]="'single'"
                                [placeholder]="'Select Client'" [(ngModel)]="selectedClientForSkillsId"
                                [dataset]="distinctClients" (onSelection)="_onClientSelectionForSkills($event)"
                                [disabled]="true">
                            </marketplace-select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="d-flex flex-column">
                            <marketplace-select *ngIf="selectedPIProductForSkillsId" [label]="'Product'"
                                [type]="'single'" [placeholder]="'Select Product'"
                                [(ngModel)]="selectedPIProductForSkillsId" [dataset]="distinctProducts"
                                (onSelection)="_onPIProductSelectionForSkills($event)" [disabled]="true">
                            </marketplace-select>
                        </div>
                    </div>
                    <div class="col-12" *ngIf="selectedPIProductForSkillsId">
                        <marketplace-dynamic-form *ngIf="previewSkillsJSON" [formJSON]="previewSkillsJSON"
                            [isSubmitNeeded]="false" (onValueChanges)="_onUserSkillSelection($event)">
                        </marketplace-dynamic-form>
                    </div>
                </div>
            </marketplace-step>
        </marketplace-stepper>
    </div>
</marketplace-popup>
<marketplace-popup [open]="openAuditUsersModel" [size]="'medium'">
    <div mpui-modal-header>Last Updates {{currentUserSkillsFormData['modified_date']}} by User ID:
        {{currentUserSkillsFormData['modified_by']}}
        <label class="status-success"> {{currentUserSkillsFormData['Status']}} </label>
    </div>
    <div mpui-modal-body>
        <div class="row" *ngIf="currentUserFormData">
            <div class="col-md-4 border-style">
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="name" class="label-title">User ID </div>
                    <div class="label-value">{{currentUserFormData['userId'] || 'NA'}}</div>
                </div>
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="name" class="label-title">System </div>
                    <div class="label-value">{{currentUserSkillsFormData['system'] || 'NA'}}</div>
                </div>
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="name" class="label-title">Client </div>
                    <div class="label-value">{{currentUserSkillsFormData['client'] || 'NA'}}</div>
                </div>
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="name" class="label-title">Role </div>
                    <div class="label-value">{{currentUserSkillsFormData['role'] || 'NA'}}</div>
                </div>
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="description" class="label-title">Team </div>
                    <div class="label-value">{{currentUserSkillsFormData['Team'] || 'NA'}}</div>
                </div>
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="name" class="label-title">User Experience Level </div>
                    <div class="label-value">{{currentUserSkillsFormData['experienceLevel'] || 'NA'}}</div>
                </div>
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="name" class="label-title">Assign Product </div>
                    <div class="label-value">{{currentUserSkillsFormData['product'] || 'NA'}}</div>
                </div>
            </div>
            <div class="col-md-4 border-style">
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="description" class="label-title">Market Skills </div>
                    <div class="label-value">{{currentUserSkillsFormData['Market Skills'] || 'NA'}}</div>
                </div>
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="name" class="label-title">Concept Skills </div>
                    <div class="label-value">{{currentUserSkillsFormData['Concept Skills'] || 'NA'}}</div>
                </div>
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="name" class="label-title">Funding Type </div>
                    <div class="label-value">{{currentUserSkillsFormData['Funding Type'] || 'NA'}}</div>
                </div>
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="name" class="label-title">LOB Skills </div>
                    <div class="label-value">{{currentUserSkillsFormData['Lob Skills'] || 'NA'}}</div>
                </div>

            </div>
            <div class="col-md-4 border-style">
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="description" class="label-title">Reminder Date </div>
                    <div class="label-value">{{currentUserSkillsFormData['reminderDate'] || 'NA'}}</div>
                </div>
                <div class="col-md mar-0 uimp-audit-key-values">
                    <div for="name" class="label-title">Comments </div>
                    <div class="label-value">{{currentUserSkillsFormData['comments'] || 'NA'}}</div>
                </div>
                <div class="col-md-4 mar-0 uimp-key-values">
                    <div for="name" class="label-title">Client Site </div>
                    <div class="label-value">{{currentUserFormData['clientSite'] || 'NA'}}</div>
                </div>
            </div>
        </div>
    </div>

</marketplace-popup>

<marketplace-popup [open]="openPIProductWarning" [size]="'small'">
    <div mpui-modal-header>
        <h3>Warning</h3>
    </div>
    <div mpui-modal-body><span style="text-align: center;">All altered data will be lost on changing this selection. Are
            you certain ? </span></div>
    <div mpui-modal-footer>
        <marketplace-button [label]="'No'" [type]="'ghost'" (onclick)="closeProductWarning()"></marketplace-button>
        <marketplace-button [label]="'Yes'" [type]="'primary'" (onclick)="updatePIProductSelection()">
        </marketplace-button>
    </div>
</marketplace-popup>
<marketplace-popup [open]="isRejectUserPopupReady" (onClose)="onRejectClose()" [size]="'medium'"
    [isFooterNeeded]="true">
    <div mpui-modal-header>Are you sure you want to reject this user?</div>
    <div mpui-modal-body>Please acknowledge that once rejected, this user cannot use or access this application.
    </div>
    <div mpui-modal-footer>
        <marketplace-button [label]="'Cancel'" [type]="'ghost'" (onclick)="onRejectClose()"></marketplace-button>
        <marketplace-button [label]="'Reject'" [type]="'primary'" (onclick)="onRejectUser($event)"></marketplace-button>
    </div>
</marketplace-popup>
<marketplace-popup [open]="openExistingRolePopup" [size]="'small'">
    <div mpui-modal-header>
        <h3>Warning</h3>
    </div>
    <div mpui-modal-body><span style="text-align: center;"> These selections are already present. Please add on to
            existing selections.</span></div>
</marketplace-popup>

<marketplace-popup [open]="userSelectionMismatch" [size]="'small'">
    <div mpui-modal-header>
        <h3>Warning</h3>
    </div>
    <div mpui-modal-body>
        <span style="text-align: center">
            Product Access, client, User Type, user status, Client Site does not match.
        </span>
    </div>
</marketplace-popup>