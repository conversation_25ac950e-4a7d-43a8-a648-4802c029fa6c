import { Injectable } from '@angular/core';
import { formatDate } from '@angular/common';

import moment from 'moment'

@Injectable({
   providedIn: 'root'
})
export class UtilitiesService {
   today: any = new Date();
   dd: any;
   mm: any;
   yyyy: any;
   currentDate: any;

   constructor() { }

   /**
    * formatDate function to get formated current date to be consistent
    */
   formatDate() {
      this.dd = String(this.today.getDate()).padStart(2, '0');
      this.mm = String(this.today.getMonth() + 1).padStart(2, '0');
      this.yyyy = this.today.getFullYear();
      return this.currentDate = this.mm + '-' + this.dd + '-' + this.yyyy;
   }

   /**
    * getDbgDateFormat function to get formated date to be consistent
    * @param date
    */
   getDbgDateFormat(date) {
      if (date != undefined && date != "" && date != null)
         return formatDate(date, 'MM-dd-yyyy', 'en-US');
      else return "";
   }

   /**
    * getHitRateDateFormat function to get formated date to be consistent
    * @param date
    */
   getHitRateDateFormat(date) {
      if (date != undefined && date != "" && date != null)
         return formatDate(date, 'MM/dd/yyyy', 'en-US');
      else return "";
   }

   /**
    * getECPDateFormat function to get formated date(yyyy-MM-dd) to be in sync with ECP
    * @param date
   */
   getECPDateFormat(date) {
      if (date != undefined && date != "" && date != null)
         return formatDate(date, 'yyyy-MM-dd', 'en-US');
      else return "";
   }

   /*
    method adds number of days passed as a parameter to the passed date value
   */
   getFutureDate(date, numberOfDaysToBeAdded, format) {
      let dateToBeSet = new Date(date);
      dateToBeSet.setDate(dateToBeSet.getDate() + numberOfDaysToBeAdded);
      return this.getFormatedFutureDate(dateToBeSet, format);
   }
   /**
    * Method returns data based on format 
    * @param date - date  
    * @param format  - on which format data to be returned
    */
   getFormatedFutureDate(date, format) {
      if (date != undefined && date != "" && date != null)
         return formatDate(date, format, 'en-US');
      else return "";
   }
   /**
   * startDate Validation with EndDate 
   * @param start -date
   * @param end -date
   */
   checkDate(start, end) {
      var mStart = moment(start, 'MM-DD-YYYY');
      var mEnd = moment(end, 'MM-DD-YYYY');
      return mStart.isBefore(mEnd);
   }

   /**
  * startDate Validation with EndDate with formatting
  * @param start -date
  * @param end -date
  */
   checkDateLatest(start, end) {
      var mStart = moment(start);
      var mEnd = moment(end);
      return mStart.isBefore(mEnd);
   }

   /**
* compare the dates
* @param start -date
* @param end -date
*/
   checkInputDatePastOrEqlCurrentDt(date): boolean {
      var inputDate = moment(date);
      var currentDt = moment().set('hour', 0).set('minute', 0).set('second', 0);
      if (inputDate.isSameOrBefore(currentDt)) {
         return true
      }
      return false;

   }

   /**
* compare the dates
* @param inpDate -date
*/
   checkInputDatePastCurrentDt(inpDate): boolean {
      var inputDate = moment(inpDate);
      var currentDt = moment().startOf('day');
      if (inputDate.isBefore(currentDt)) {
         return true
      }
      return false;

   }

   /**
* compare the dates
* @param start -date
* @param end -date
*/
   checkActiveFlag(startDate, endDate): boolean {
      var startDt = moment(startDate);
      var endDt = moment(endDate);
      var currentDt = moment().startOf('day');
      if (startDt.isSameOrBefore(currentDt) && endDt.isSameOrAfter(currentDt)) {
         return true;
      }
      return false;

   }
}
