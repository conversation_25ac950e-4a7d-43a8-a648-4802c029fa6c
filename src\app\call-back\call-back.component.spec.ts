import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CallBackComponent } from './call-back.component';
import { AuthService } from '../_services/authentication.services';
import { LoaderService } from '../_services/loader.service';
import { MPUIJwtVerifierService } from "marketplace-jwt-verifier";
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { APP_CONSTANTS, AUTH_CONFIG } from '../_constants/app.constants';
import { of, throwError } from 'rxjs';

describe('CallBackComponent', () => {
  let component: CallBackComponent;
  let fixture: ComponentFixture<CallBackComponent>;

  // Mock services
  const authServiceMock = {
    getToken: jasmine.createSpy('getToken'),
    piAuthorize: jasmine.createSpy('piAuthorize').and.returnValue(of({}))
  };
  const loaderServiceMock = {
    show: jasmine.createSpy('show'),
    hide: jasmine.createSpy('hide')
  };
  const mpuiJwtVerifierServiceMock = {
    verifyToken: jasmine.createSpy('verifyToken').and.returnValue(Promise.resolve({}))
  };
  const routerMock = {
    navigate: jasmine.createSpy('navigate')
  };
  const activatedRouteMock = {
    queryParams: of({ [AUTH_CONFIG.CODE]: 'testCode' })
  };
  const cookieServiceMock = {
    get: jasmine.createSpy('get'),
    set: jasmine.createSpy('set')
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ CallBackComponent ],
      providers: [
        { provide: AuthService, useValue: authServiceMock },
        { provide: LoaderService, useValue: loaderServiceMock },
        { provide: MPUIJwtVerifierService, useValue: mpuiJwtVerifierServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: ActivatedRoute, useValue: activatedRouteMock },
        { provide: CookieService, useValue: cookieServiceMock }
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CallBackComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should get auth code on init and call getToken', () => {
    spyOn(component, 'getToken');
    component.ngOnInit();
    expect(component.getToken).toHaveBeenCalledWith('testCode');
  });

  it('should show loader when getting token', () => {
    const authCode = 'testAuthCode';
    const codeVerifier = 'testCodeVerifier';
    localStorage.setItem(AUTH_CONFIG.CODE_VERIFIER, codeVerifier);
    
    authServiceMock.getToken.and.returnValue(of({ appToken: 'testAppToken' }));

    component.getToken(authCode);

    expect(loaderServiceMock.show).toHaveBeenCalled();
    expect(loaderServiceMock.hide).toHaveBeenCalled();
    expect(authServiceMock.getToken).toHaveBeenCalledWith(authCode, codeVerifier);
  });

  it('should handle token verification for deployment', async () => {
    const authCode = 'testAuthCode';
    const userToken = { [APP_CONSTANTS.FIRST_NAME]: 'John', [APP_CONSTANTS.LAST_NAME]: 'Doe' };
    const codeVerifier = 'testCodeVerifier';

    localStorage.setItem(AUTH_CONFIG.CODE_VERIFIER, codeVerifier);
    authServiceMock.getToken.and.returnValue(of({ appToken: 'testAppToken' }));
    mpuiJwtVerifierServiceMock.verifyToken.and.returnValue(Promise.resolve(userToken));

    await component.getToken(authCode);

    expect(cookieServiceMock.set).toHaveBeenCalledWith(APP_CONSTANTS.USER_NAME, 'John Doe');
    expect(cookieServiceMock.set).toHaveBeenCalledWith(APP_CONSTANTS.PORTAL_ACCESS, APP_CONSTANTS.TRUE);
    expect(routerMock.navigate).toHaveBeenCalledWith(['']);
  });

  it('should handle error when getting token fails', () => {
    const authCode = 'testAuthCode';
    const codeVerifier = 'testCodeVerifier';
    const errorResponse = { error: { error: { errorCode: '404', errorMessage: 'Not Found' } } };

    localStorage.setItem(AUTH_CONFIG.CODE_VERIFIER, codeVerifier);
    authServiceMock.getToken.and.returnValue(throwError(() => errorResponse));

    component.getToken(authCode);

    expect(component.notificationHeader).toBe(APP_CONSTANTS.ERROR);
    expect(component.notificationBody).toBe('404 - Not Found');
    expect(component.notificationOpen).toBeDefined();
  });
});
