import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { ROUTING_LABELS } from 'src/app/_constants/menu.constant';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { ExternalSOAService } from 'src/app/_services/external-soa.service';
import { ToastService } from 'src/app/_services/toast.service';
import { IRequestContext } from 'src/app/_models/external/external-request-context';
import { EXTERNALUSER } from 'src/app/_models/external-user-constants';
import { IExternalGeneratePassword } from 'src/app/_models/external/external-generate-password';
import { externalAuthenticationConstants } from 'src/app/_helpers/helpers.constants';
import { RegistrationConstants } from 'src/app/_constants/app.constants';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { MPUIButtonModule } from 'marketplace-button';
import { OtpComponent } from '../otp/otp.component';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule, 
    FormsModule, 
    RouterModule, 
    HttpClientModule,
    MPUIDynamicFormModule,
    MPUIButtonModule,
    OtpComponent
  ]
})
export class ForgotPasswordComponent implements OnInit {
  isForgotPassword: boolean = true;
  isSendEmail: boolean = false;
  isResendEmail: boolean = false;
  isChangePassword: boolean = false;
  isEmailNotFound: boolean = false;
  isPasswordMatch: boolean = false;
  isSecurityQuestion: boolean = false;
  userEmailId: any;
  userId: any;
  forgotPasswordFormJSON: any;
  forgotPasswordJSONPath: any = "./assets/json/external-signIn/forgot-password-form.json";
  securityQuestionsJSONPath: any = "./assets/json/external-signIn/securityQuestions.json";
  securityQuestionsJSON: any;
  emailIdListJSON: any = [];
  emailIdListJSONPath: any = "./assets/json/external-signIn/emailId-list.json";
  generatedPasscode: any;
  secretQuestion1: string;
  isGenerateButtonEnabled: boolean = false;
  secretQuestion2: string;
  secretAnswer1: string;
  secretAnswer2: string;
  isShowForm: boolean = false;
  userDnData: string;
  isLoading: boolean = false;
  isOtpScreen: boolean = false;
  contactUsUrl: string = externalAuthenticationConstants.CONTACT_US_URL;

  constructor(
    private userManagementSvc: UserManagementApiService, 
    private router: Router, 
    private soaService: ExternalSOAService, 
    private alertService: ToastService
  ) { }

  ngOnInit(): void {
    let _fetchPage = this.userManagementSvc.getAssetsJson(this.forgotPasswordJSONPath);
    _fetchPage.subscribe(data => {
      this.forgotPasswordFormJSON = data[ROUTING_LABELS.FORM_DETAILS];
    });
  }

  /**
  * This function is triggerd on click of next button on forgot password screen
  * @param event 
  */
  onClickOfNextButton() {
    this.isForgotPassword = false;
    this.isOtpScreen = true;
  }

  /**
   * This function is triggerd once OTP validation is done and emited value back
   * @param event 
   */
  actionAfterOTPValidation(event): void {
    if (event.validated) {
      this.isOtpScreen = false;
      this.onOTPValidation();
    } else if (event.message == "User Details Not Found.") {
      this.isForgotPassword = true;
      this.isOtpScreen = false;
      this.alertService.setErrorNotification({
        notificationHeader: externalAuthenticationConstants.ERROR,
        notificationBody: "User Details Not Found."
      });
    }
  }

  /**
  * This function is triggerd on click of next button on forgot password screen
  * @param event 
  */
  onOTPValidation() {
    this.isLoading = true;
    this.isForgotPassword = false;
    let requestContext: IRequestContext = {
      username: this.userId, // this has to be changed
      requestId: EXTERNALUSER.GENERATE_PASSWORD_REQUEST_ID,
      application: EXTERNALUSER.APPLICATION
    };
    let searchUserPayload = {
      requestContext: requestContext,
      searchUserFilter: {
        username: this.userId,
        repositoryEnum: [
          RegistrationConstants.REPOSITORY_ENUM
        ]
      }
    }

    this.soaService.searchExtUser(this.userId).subscribe(response => {
      this.secretQuestion1 = response[RegistrationConstants.USER][0]?.secretQuestionAnswers[0]?.question;
      this.secretQuestion2 = response[RegistrationConstants.USER][0]?.secretQuestionAnswers[1]?.question;
      this.userDnData = response[RegistrationConstants.USER][0].dn;
      let _fetchPage = this.userManagementSvc.getAssetsJson(this.securityQuestionsJSONPath);
      _fetchPage.subscribe(data => {
        this.securityQuestionsJSON = data[ROUTING_LABELS.FORM_DETAILS];
        this.securityQuestionsJSON[0].selectedVal = this.secretQuestion1;
        this.securityQuestionsJSON[2].selectedVal = this.secretQuestion2;
        this.isSecurityQuestion = true;
        this.isLoading = false;
      });
    })
  }

  /**
  * This function is triggered when any change happens in field values
  * @param event 
  */
  onForgotPasswordValueChange(event) {
    this.userId = event.current.userId;
    if (this.userId) {
      this.isGenerateButtonEnabled = true;
    } else {
      this.isGenerateButtonEnabled = false;
    }
  }

  /**
  * This function is triggered when any change happens in field values
  * @param event 
  */
  onSecurityQuestionsFormChange(event) {
    this.secretAnswer1 = event.current.answer1;
    this.secretAnswer2 = event.current.answer2;
  }

  /**
  * This function is triggered when we click on resend email button
  * @param event 
  */
  onResendEmailButtonClicked() {
    this.isSendEmail = false;
    this.isResendEmail = true;
  }

  /**
 * Generate Password
 */
  onGeneratePassword(): void {
    this.isLoading = true;
    let secretAnswersPayload = {
      dn: this.userDnData,
      secretAnswerText1: this.secretAnswer1,
      secretAnswerText2: this.secretAnswer2
    }

    this.soaService.validateSecretAnswers(this.userId, secretAnswersPayload).subscribe(response => {
      if (response.secretAnswerMatched) {
        this.soaService.generatePassword(this.userId).subscribe((data: any) => {
          // Below notification need to be changed
          this.alertService.setSuccessNotification({
            notificationHeader: externalAuthenticationConstants.SUCCESS,
            notificationBody: externalAuthenticationConstants.GENERATE_PASSWORD_MESSAGE
          });
          this.isSecurityQuestion = false;
          this.isLoading = false;
          this.generatedPasscode = data.password;
        }, (error: any) => {
          this.isLoading = false;
          this.alertService.setErrorNotification({
            notificationHeader: RegistrationConstants.ERROR,
            notificationBody: externalAuthenticationConstants.GENERATE_PASSWORD_ERRMSG
          });
        })
      }
      else if (response.secretAnswerMatched == false) {
        this.isLoading = false;
        this.alertService.setErrorNotification({
          notificationHeader: RegistrationConstants.ERROR,
          notificationBody: RegistrationConstants.SECURITY_ANS_INVALID_MSG
        });
      }
    }, (error: any) => {
      this.isLoading = false;
      if (error === RegistrationConstants.UNAUTHORIZED) {
        this.alertService.setErrorNotification({
          notificationHeader: RegistrationConstants.ERROR,
          notificationBody: RegistrationConstants.MAXIMUM_LIMIT_EXCEEDED_MSG
        });
      }
    });
  }
}
