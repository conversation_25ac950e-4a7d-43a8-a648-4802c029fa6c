export const ROLES_CONSTANTS = {
    FORM_AUDIT: 'form',
    ACTIONVIEW_AUDIT: 'actionView',

    TEAM_TYPE: 'teamType',
    COLUMN_CONFIG: 'columnConfig',
    DATACONTEXT: 'dataContext',
    RESPONSECODE: 'responseCode',
    RESPONSEDATA: 'responseData',
    STATUS: "activeFlag",
    BASIC_ROLE_FORM: 'BasicRoleDetails',
    CLIENT_NAME_JSON: 'clientNames',
    PRODUCT_NAME_JSON: 'productNames',
    BUSINESS_DIVISION_JSON: 'businessDivision',
    INVENTORY_TYPE_DETAILS_JSON: 'inventoryTypeDetails',
    CLIENT_SITE_JSON: 'clientSiteJson',
    DESCRIPTION: 'description',
    DISABLED: 'disabled',
    CREATE: 'create',
    PRODUCT: 'product',
    CLIENT: 'client',
    ANTHEM: 'Anthem',
    PRODUCTIDVALUE: 'Claim Anomaly Detection',
    BUSINESS_DIVISION: 'businessDivision',
    REMINDERDATE: 'reminderDate',
    CLIENTSITE: 'clientSite',
    TEAMSELECTION: 'teamSelection',
    VIEW: 'view',
    ROLEID: "roleId",
    PRODID: "prodId",
    CLIENTID: 'clientId',
    PRODUCTID: 'productId',
    ROLENAME: "roleName",
    VIEWROLE: "View Role",
    EDITROLE: "Edit Role",
    CREATE_SCREENS: "createscreens",
    VIEW_SCREENS: "readscreens",
    NAVIGATION_VIEW_ROLE: "settings/roles/view-role",
    NAVIGATION_EDIT_ROLE: "settings/roles/edit-role",
    LASTUPDATED_USERID: "lastUpdateUserId",
    LASTUPDATED_DTM: "lastUpdtDtm",
    ROLES_LIST_TABLE_COLUMNS: './assets/json/settings/role-management/role-list.json',
    ADD_ROLE_FORM: './assets/json/settings/role-management/add-role-form.json',
    PERMISSIONS_JSON: './assets/json/settings/role-management/permission-role.json',
    AUDIT_ROLE_JSON: './assets/json/settings/role-management/roles-list-audit.json',
    PICKERTOOLBARLEFT: 'pickertoolbarleftcont',
    INVDETAILS: 'invDetails',
    INVTYPEID: 'invTypeId',
    ACTION_VIEW_MORE: 'action-view-more',
    REDBORDER: 'redBorder',
    SUCCESS: "Success",
    FAIL: "Warning",
    ROLE_SUCCESS_MESSAGE: "Role Created Successfully",
    COMMENT_REQUIRED_WARNING: "Please enter comments if recoverable is NO",
    RECOVERABLE_YES_WARNING: "Please enter client estimated overpayment if recoverable is YES",

    CHANGES_SAVED_SUCCESS: "Changes saved successfully",
    CARELON_ADMINISTRATOR: 'Carelon Administrator',
    PORTAL_ADMINISTRATOR: 'Portal Administrator',
    SIU_ADMIN: 'SIU Admin',
    SESSION_ROLE_NAME: "RoleName",
    SESSION_ROLE_ID: "RoleId",
    SESSION_CLIENTNAME: "clientName",
    ROLE_NAME_FIELD: "Role Name",
    ROLE_DESC_FIELD: "Role Description",
    ASSIGN_PROD_ID_FIELD: "Assign Product ID",
    CLIENT_ID_FIELD: "ClientId",
    BUSINESS_DIVISION_FIELD: 'Business Division',
    REMINDER_DATE_FIELD: "Remainder Date",
    SELECTED_TEAM_FIELD: "Selected Team",
    CLIENT_SITE_FIELD: "Client Site",
    CREATE_SCREENS_FIELD: "Create Screens",
    READ_SCREENS_FIELD: "Read Screens",
    CREATE_CHECK: "create-check",
    READ_CHECK: "read-check",
    MANDATORY_ERROR_MSG: 'Please fill all the mandatory fields',
    MANDATORY_SCREEN_ACCESS_ERROR_MSG: 'Please select screens to the role',



}