import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AUTH_CONFIG } from '../_constants/menu.constant';
import { environment } from '../../environments/environment';
import { catchError, map } from 'rxjs/operators';
import { IExternalCookie } from '../_models/mfa/external-cookie';
import { EXTERNALUSER } from '../_models/external-user-constants';
import { IExternalLoginThreat } from '../_models/mfa/external-loginthreat';
import { IExternalSendOTP } from '../_models/mfa/external-send-otp';
import { IExternalValidateOTP } from '../_models/mfa/external.validate-otp';
import { ExternalSOAService } from '../_services/external-soa.service';
import { throwError } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class ExternalMFAService {

    constructor(private http: HttpClient, private soaService: ExternalSOAService) { }

    /**
     * Get MFA Cookie
     * @param cookiePayload 
     * @param token 
     * @param userId 
     */
    getMFACookieObselete(cookiePayload: IExternalCookie, token: string, userId: string){
        return this.http.post(environment.mfaUrl + '/cookie', cookiePayload, {
            headers: {
                'apikey': this.soaService.soaParam,
                'Authorization': AUTH_CONFIG.BEARER + token,
                'meta-senderapp': EXTERNALUSER.MFA_SENDER_APP,
                'usernm': userId
            }
        }).pipe(map(cookieDetails => {
                return cookieDetails;
        }));
    }

    /**
     * Login Threat MFA
     * @param loginThreatPayload 
     * @param token 
     * @param userId 
     */
 loginThreat(loginThreatPayload: IExternalLoginThreat, token: string, userId: string, ipAddress: string) {
        // if (loginThreatPayload && loginThreatPayload.cookieValue){
        return this.http.post(environment.identityUrl + '/api/v1/loginthreat', loginThreatPayload, {
            headers: {
                // 'apikey': this.soaService.soaParam,
                // 'Authorization': AUTH_CONFIG.BEARER + token,
                // 'webguid': userId,
                // 'meta-ipaddress': ipAddress,
                // 'meta-senderapp': EXTERNALUSER.MFA_SENDER_APP,
                'usernm': userId
            }
        }).pipe(map(loginThreatDetails => {
            return loginThreatDetails;
        }));
    }


    /**
     * Send OTP MFA
     * @param sendOtpPayload 
     * @param token 
     */
   sendOTP(sendOtpPayload: IExternalSendOTP, userId: string) {
        return this.http.post(environment.identityUrl + '/api/v1/otp/send', sendOtpPayload, {
            headers: {
                'usernm': userId
            }
        }).pipe(map(sendOTPDetails => {
            return sendOTPDetails;
        }));
    }

    /**
     * Validate OTP Payload
     * @param validateOtpPayload 
     * @param token 
     */
     validateOTPObselete(validateOtpPayload: IExternalValidateOTP, token: string, userId: string, pingRiskId: string, pingDeviceId: string, pingUserId: string) {
        return this.http.post(environment.identityUrl + '/api/v1/otp/validate', validateOtpPayload, {
            headers: {
                // 'apikey': this.soaService.soaParam,
                //'Authorization': AUTH_CONFIG.BEARER + token,
                //'meta-transid': '12345678',
                //'meta-senderapp': EXTERNALUSER.MFA_SENDER_APP,
                'usernm': userId,
                'meta-pingRiskId': pingRiskId,
                'meta-pingDeviceId': pingDeviceId,
                'meta-pingUserId': pingUserId
            }
        }).pipe(map(validateOTPDetails => {
            return validateOTPDetails;
        }));
    }

    
    /**
     * Returns users IPv4 Address.
     */
     userIpAddress(){
        return this.http.get(environment.authorizationUrl + '/api/dbg-authorization/ipAddress',{ responseType: 'text' }).pipe(            
            catchError(err => {
                return throwError(err);
            }));
    }

    /**
     * Login Threat MFA
     * @param loginThreatPayload 
     * @param token 
     * @param userId 
     */
   externalLoginThreatCall(loginThreatPayload: IExternalLoginThreat, userId: string) {
        return this.http.post(environment.identityUrl + "/api/v1/loginthreat", loginThreatPayload, {
            headers: {
                'usernm': userId
            }
        }).pipe(map(loginThreatDetails => {
            return loginThreatDetails;
        }));
    }

     /**
     * Validate OTP Payload
     * @param validateOtpPayload 
     * @param token 
     */
    validateOTP(validateOtpPayload: IExternalValidateOTP, userId: string, pingRiskId: string, pingDeviceId: string, pingUserId: string) {
        return this.http.post(environment.identityUrl + '/api/v1/otp/validate', validateOtpPayload, {
            headers: {
                'usernm': userId,
                'meta-pingRiskId': pingRiskId,
                'meta-pingDeviceId': pingDeviceId,
                'meta-pingUserId': pingUserId
            }
        }).pipe(map(validateOTPDetails => {
            return validateOTPDetails;
        }));
    }

    /**
     * Get MFA Cookie
     * @param cookiePayload 
     * @param token 
     * @param userId 
     */
  getMFACookie(cookiePayload: IExternalCookie, userId: string) {
        return this.http.post(environment.identityUrl + '/api/v1/rememberMe', cookiePayload, {
            headers: {
                'usernm': userId
            }
        }).pipe(map(cookieDetails => {
            return cookieDetails;
        }));
    }
}
