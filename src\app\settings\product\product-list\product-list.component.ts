import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from "@angular/router"
import { UtilitiesService } from 'src/app/_services/utilities.service';
import { ProductApiService } from 'src/app/_services/product-api.service';
import { Inventoryform } from './constant/inventory-form';
import { forkJoin } from 'rxjs';
import { ToastService } from 'src/app/_services/toast.service';
import { AuthService } from 'src/app/_services/authentication.services';
import { environment } from 'src/environments/environment';

export interface IProductInventory {
  productId?: number,
  invTypeIds?: number[],
  createdByUserId?: string,
  lastUpdatedByUserId?: string
}

@Component({
  selector: 'app-product-list',
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class ProductListComponent implements OnInit {
  public headerText = "Rules Engine Dashboard";
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  statusOptions = { 'true': 'Active', 'false': 'Inactive' };
  public totalEntries: number;
  public dataURL: any;
  showTable: boolean = false;
  public dataRoot = "src";
  public columnConfigForProductTableUrl: string = "./assets/json/settings/product/Product-list-config.json";
  public productColumnConfig: any;
  breadcrumbDataset: any = [{ label: 'Home', url: '/' }, { label: 'Product list' }];
  kebabOptions: any = [{ label: '<i class="fa fa-eye" aria-hidden="true"></i> View Bundles', id: 'View Bundles' }, { label: '<i class="fa fa-plus" aria-hidden="true"></i>  Add Bundle', id: 'Add Bundle' }]
  kebabOptions_ReadOnly: any = [{ label: '<i class="fa fa-eye" aria-hidden="true"></i> View Bundles', id: 'View Bundles' }]

  showInventoryOverlay = false;
  inventoryFormData: any;
  isFormReady: boolean = false;
  rowData: any;
  isFormValid: boolean = false;
  inventoryOptions: any = [];
  selectedInventoryTypes: [];
  availableOptions: any = [];
  /****************************** Notification ***************************************/
  formDetails: any;
  loggedInUser: any;
  isReadOnly: boolean = false;

  constructor(private router: Router, private productApiService: ProductApiService,
    private dateService: UtilitiesService, private alertService: ToastService, private authService: AuthService) { }


  ngOnInit(): void {
    let fetchCookie: any = document.cookie?.split(";");
    fetchCookie?.map(e => {
      if (e?.includes("userId")) {
        this.loggedInUser = e?.trim()?.slice(7);
      }
    })
    this.isReadOnly = !this.authService.isWriteOnly;
    this.kebabOptions = this.isReadOnly ? this.kebabOptions_ReadOnly : this.kebabOptions;
    this.productApiService.getTableColumn(this.columnConfigForProductTableUrl).subscribe((data) => {
      data.colDefs.forEach(e => {
        e.field == "activeFlag" ? e.customFormatter = this.customFormatterStatus : "";
        e.field == "bundleCount" ? e.customFormatter = this.customFormatterBundles : "";
        e.field == "action" ? e.customFormatter = this.renderActionColumn : "";
      });
      this.productColumnConfig = data;
    });

    this.fetchProductInventoryList()
  }

  /**
   * Method to fetch Product & Inventory List
   */
  fetchProductInventoryList(): void {
    let _productDetails = this.productApiService.getProductDetails()
    let _fetchInventory = this.productApiService.getInventoryList();

    forkJoin([_productDetails, _fetchInventory]).subscribe(
      ([product, inv]) => {
        this.inventoryOptions = inv.map(x => ({ "name": x.invTypeName, "id": x.invTypeId })).filter((v, i, a) => a.findIndex(v2 => (JSON.stringify(v2) === JSON.stringify(v))) === i)
        product.forEach(e => {
          e["activeFlag"] = e["activeFlag"] ? "Active" : "Inactive";
          e["createdDate"] = e["createdDate"] ? e["createdDate"] = this.dateService.getDbgDateFormat(e["createdDate"]) : "";
          e["lastUpdatedDate"] = e["lastUpdatedDate"] ? e["lastUpdatedDate"] = this.dateService.getDbgDateFormat(e["lastUpdatedDate"]) : "";
        });
        this.showTable = true;
        this.dataURL = product;
        this.totalEntries = product.length
      }, err => {
        this.showTable = false;
      });
  }
  /**
   * customFormatterStatus funtion for button in product table
   * @param event 
   */
  customFormatterStatus(event) {
    let btn;
    switch (event.value) {
      case "Active":
        btn = "<button type='button' title='Active' class='btn btn rule-dashboard btn-active btn-wrap-text'>Active</button>";
        break;
      case "Inactive":
        btn = "<button type='button' title='Inactive' class='btn btn rule-dashboard btn-inactive btn-wrap-text'>Inactive</button>";
        break;
    }
    return btn;
  }

  /**
   * custom formatter for display inventory button
   * @param event 
   */
  renderActionColumn(event) {
    return `
          <button type='button' class='btn btn btn-inventory' data-toggle='modal' id='inventory'  data-target='#inventory_model'>Inventory</button>
         `;
  }


  /**
  * customFormatterBundles function for Product Table Action
  * @param event 
  */
  customFormatterBundles(event) {
    if (event.value > 9) {
      return `<div class="product-dashboard dropdown-container" tabindex=“-1”>
      <span class="bundles-text-field"> &nbsp &nbsp &nbsp &nbsp${event.value} bundles</span>
      </div>`;
    } else {
      return `<div class="product-dashboard dropdown-container" tabindex=“-1”>
      <span class="bundles-text-field"> &nbsp &nbsp &nbsp &nbsp${event.value} &nbsp bundle</span>
      </div>`;
    }

  }

  /**
    * cell click event
    * @param event 
    */
  cellClicked(event): void {
    try {
      this.rowData = event.currentRow;
      this.isFormReady = false;
      if (event.eventData.target.id === 'inventory') {
        this.getInventoryTypeByProduct();
      }
    }
    catch (err) {
      console.log(err, "Error occured in On cell Clicked")
    }
  }


  /**
   * Method to get the inventory type by product
   */
  getInventoryTypeByProduct() {
    this.showInventoryOverlay = true;
    this.inventoryFormData = [JSON.parse(JSON.stringify(Inventoryform()))]
    this.inventoryFormData[0].groupControls[0].value = this.rowData.productName;
    this.productApiService.getInventoryTypeByProduct(this.rowData.productId).subscribe(data => {
      if (data) {
        let options = data.usedInvIds ? this.inventoryOptions.map(x => x.id).filter(d => !data.usedInvIds.includes(d)) : [];
        data.invDetails ? this.selectedInventoryTypes = data.invDetails.map(x => x.invTypeId) : '';
        if (!data.usedInvIds) {
          this.inventoryFormData[0].groupControls[1].options = this.getAvailableInventoryTypes(options, this.selectedInventoryTypes);
        }
        if (data.usedInvIds && data.usedInvIds.length != this.inventoryOptions.length) {
          this.inventoryFormData[0].groupControls[1].selectedVal = this.selectedInventoryTypes;
          this.inventoryFormData[0].groupControls[1].options = this.getAvailableInventoryTypes(options, this.selectedInventoryTypes);
        }
      }
      this.isFormReady = true;
    })
  }

  /**
   * Method to pull all the available Inventory types
   * @param options 
   * @param selectedInvTypes 
   */
  getAvailableInventoryTypes(options, selectedInvTypes) {
    try {
      this.availableOptions = [];
      options.forEach(element => {
        let _found = this.inventoryOptions.find(x => x.id == element);
        _found ? this.availableOptions.push(_found) : '';
      });
      selectedInvTypes.forEach(element => {
        let _found = this.inventoryOptions.find(x => x.id == element);
        if (_found)
          this.availableOptions.indexOf(_found) > -1 ? '' : this.availableOptions.push(_found);
      });
      options.length == 0 ? this.availableOptions = this.inventoryOptions : ''
      return this.availableOptions.sort()
    }
    catch (err) {
      console.log(err, "Error occured in Available Inventory Types")
    }
  }

  /**
    * cellValueChanged Function for Table
    * @param event 
    */
  cellValueChanged(event: Event): void {

  }

  /**
    * tableReady Funtion
    * @param event 
    */
  tableReady(event: Event): void {

  }


  /**
    * AddNewRulefun Funtion
    */
  AddNewBundlesfun(): void {
    if (this.isReadOnly) return
    this.router.navigate([`settings/product/add-bundle`]);
  }

  /**
    * breadcrumSelection Funtion
    */
  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }

  onDropdownOptionsClick(event) {
    this.productApiService.getProductName(event.currentRow);
    let productAction = event.text;
    switch (productAction) {
      case 'View Bundles':
        this.router.navigate([`settings/product/bundle/${event.currentRow.productId}`]);
        break;
      case 'Add Bundle':
        this.router.navigate([`settings/product/add-bundle/${event.currentRow.productId}`]);
        break;
    }
  }

  /**
   * Method on close of Inventory popup, refresh grid
   * @param event default event
   */
  onCloseInventory(event): void {
    this.showInventoryOverlay = false;
  }

  /**
   * Method to reset the Inventory fields
   */

  resetInventoryForm(): void {
    this.isFormReady = false;
    this.getInventoryTypeByProduct();
  }

  /**
   * Method to post the inventory mapped to the product
   */
  onSubmitForm(): void {
    try {
      if (this.isReadOnly) return
      let inventoryObj: IProductInventory = {};
      if (this.formDetails) {
        inventoryObj.productId = this.rowData.productId;
        inventoryObj.invTypeIds = this.formDetails.inventory;
        inventoryObj.createdByUserId = this.loggedInUser;
        inventoryObj.lastUpdatedByUserId = this.loggedInUser;
      }
      else {
        inventoryObj.productId = this.rowData.productId;
        inventoryObj.invTypeIds = this.inventoryFormData[0].groupControls[1].selectedVal;
        inventoryObj.lastUpdatedByUserId = this.loggedInUser;
      }

      this.productApiService.saveProductInventory(inventoryObj).pipe().subscribe(data => {
        if (data.responseCode == 200) {
          this.alertService.setSuccessNotification({
            notificationBody: "Inventory Type Mapping submitted successfully."
          });
        }
        else {
          this.alertService.setErrorNotification({
            notificationBody: data.responseData,
          });
        }
      },
        error => {
          this.alertService.setErrorNotification({
            notificationBody: error.responseData,
          });
        })
    }
    catch (err) {
      console.log("Error while submitting inventory type mapping", err)
    }
  }

  /**
   * Method fires on form value change
   * @param e 
   */
  onFormChange(e: any): void {
    this.formDetails = e.current['Inventory_Details']
    setTimeout(() => this.checkInventoryMappingValidation(), 10);
  }

  /**
   * Method to check inventory is mapped to product is valid or not
   */
  checkInventoryMappingValidation(): void {
    try {
      const invalidCollectionForm = document.querySelectorAll(
        `marketplace-dynamic-form form.ng-invalid`
      );

      if (invalidCollectionForm.length == 0) {
        this.isFormValid = true;
      } else {
        this.isFormValid = false;
      }
    }
    catch (e) {
      console.log(e, "inventory mapping validation")
    }

  }

}