{"switches": {"enableSorting": true, "enablePagination": false, "editable": false, "enableFiltering": false}, "colDefs": [{"name": "Rule Id", "field": "rule_id", "filterType": "", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Rule Name", "field": "rule_name", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Rule Type", "field": "rule_type", "filterType": "", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Rule SubType", "field": "rule_subtype", "filterType": "text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Rule Level", "field": "rule_level", "filterType": "text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Created By", "field": "created_by", "filterType": "text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Start Date", "field": "start_date", "filterType": "text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "End Date", "field": "end_date", "filterType": "text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}]}