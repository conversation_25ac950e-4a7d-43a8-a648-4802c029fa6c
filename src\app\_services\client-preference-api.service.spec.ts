import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { ClientApiService } from './client-preference-api.service';
import { environment } from 'src/environments/environment';
import { of, throwError } from 'rxjs';

describe('ClientApiService', () => {
  let service: ClientApiService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ClientApiService]
    });
    service = TestBed.inject(ClientApiService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Service Initialization', () => {
    it('should initialize with default values', () => {
      expect(service.tabOptions).toEqual({
        'View Products': 0,
        'View Fee Schedule': 1,
        'View Data Exchange': 2,
        'View File Exchange': 3,
        'View Sample Validation Percentage': 4,
        'View Tenant': 5
      });
      expect(service.selectedTabOption).toBe('');
      expect(service.selectedProductName).toBe('');
      expect(service.selectedProductId).toBe('');
    });
  });

  describe('getAllFileTemplates', () => {
    it('should get all file templates successfully', () => {
      const mockResponse = { templates: ['template1', 'template2'] };

      service.getAllFileTemplates().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.inventoryDomainUrl}/api/dbg-inventorydomain/inventory/inventoryTypes/list`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle error in getAllFileTemplates', () => {
      const errorMessage = 'Server error';

      service.getAllFileTemplates().subscribe(
        () => fail('Expected error'),
        error => expect(error).toBeTruthy()
      );

      const req = httpMock.expectOne(`${environment.inventoryDomainUrl}/api/dbg-inventorydomain/inventory/inventoryTypes/list`);
      req.error(new ErrorEvent('Network error', { message: errorMessage }));
    });
  });

  describe('getClientProducts', () => {
    it('should get client products successfully', () => {
      const clientId = '123';
      const mockResponse = { products: ['product1', 'product2'] };

      service.getClientProducts(clientId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.productDomainUrl}/client/clientproductList/${clientId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle error in getClientProducts', () => {
      const clientId = '123';

      service.getClientProducts(clientId).subscribe(
        () => fail('Expected error'),
        error => expect(error).toBeTruthy()
      );

      const req = httpMock.expectOne(`${environment.productDomainUrl}/client/clientproductList/${clientId}`);
      req.error(new ErrorEvent('Network error'));
    });
  });

  describe('getAllTenantsByClientId', () => {
    it('should get all tenants by client ID successfully', () => {
      const clientId = '456';
      const mockResponse = { tenants: ['tenant1', 'tenant2'] };

      service.getAllTenantsByClientId(clientId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.clientPreferenceDomainUrl}/tenant/getallbyclientid/${clientId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getTenantsAssetsJson', () => {
    it('should get tenants assets JSON successfully', () => {
      const url = 'assets/test.json';
      const mockResponse = { data: 'test' };

      service.getTenantsAssetsJson(url).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(url);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getTenantDetailsById', () => {
    it('should get tenant details by ID successfully', () => {
      const tenantId = '789';
      const mockResponse = { tenant: 'details' };

      service.getTenantDetailsById(tenantId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.clientPreferenceDomainUrl}/tenant/getbyid/${tenantId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('addEditTenantData', () => {
    it('should add/edit tenant data successfully', () => {
      const payload = { name: 'Test Tenant' };
      const mockResponse = { success: true };

      service.addEditTenantData(payload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.clientPreferenceDomainUrl}/tenant/save`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(payload);
      req.flush(mockResponse);
    });
  });

  describe('getTableColumnforClientsTable', () => {
    it('should get table columns for clients table', () => {
      const url = 'assets/columns.json';
      const mockResponse = { columns: [] };

      service.getTableColumnforClientsTable(url).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(url);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getproductFormJSON', () => {
    it('should get product form JSON', () => {
      const url = 'assets/form.json';
      const mockResponse = { form: [] };

      service.getproductFormJSON(url).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(url);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getInventoryTypeId', () => {
    it('should get inventory type ID by product ID', () => {
      const productId = 'prod123';
      const mockResponse = { inventoryTypeId: 'inv456' };

      service.getInventoryTypeId(productId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.productDomainUrl}/productInventoryType/getByProductId/${productId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('saveProduct', () => {
    it('should save product successfully', () => {
      const productObj = { name: 'Test Product' };
      const mockResponse = { success: true };

      service.saveProduct(productObj).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.productDomainUrl}/client/clntProdInvDetails/save`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(productObj);
      req.flush(mockResponse);
    });
  });

  describe('getClientDetails', () => {
    it('should get client details successfully', () => {
      const mockResponse = { clients: ['client1', 'client2'] };

      service.getClientDetails().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.productDomainUrl}/client/allclients`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getPrioritizationDetails', () => {
    it('should get prioritization details successfully', () => {
      const clientId = 'client1';
      const productId = 'product1';
      const conceptId = 'concept1';
      const mockResponse = { prioritization: 'data' };

      service.getPrioritizationDetails(clientId, productId, conceptId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.rulesDomainUrl}/hierarchy_weights?clientId=${clientId}&productId=${productId}&conceptId=${conceptId}`);
      expect(req.request.method).toBe('GET');
      expect(req.request.headers.get('x-api-key')).toBe('fb5b6828-a0a6-4e90-be31-de48ce4fba3f');
      req.flush(mockResponse);
    });
  });

  describe('savePrioritizationDetails', () => {
    it('should save prioritization details successfully', () => {
      const data = { prioritization: 'modified' };
      const mockResponse = { success: true };

      service.savePrioritizationDetails(data).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.rulesDomainUrl}/hierarchy_weights/save/`);
      expect(req.request.method).toBe('POST');
      expect(req.request.headers.get('x-api-key')).toBe('202b6f16-a66c-4d63-ad1f-98f24d678abb');
      expect(req.request.body).toEqual(data);
      req.flush(mockResponse);
    });
  });

  describe('addUpdateRoleData', () => {
    it('should add/update role data successfully', () => {
      const roleData = { role: 'admin' };
      const mockResponse = { success: true };

      service.addUpdateRoleData(roleData).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.authorizationUrl}/api/dbg-authorization/role/addOrUpdateRole`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(roleData);
      req.flush(mockResponse);
    });
  });

  describe('getProductBundleFeeSchedules', () => {
    it('should get product bundle fee schedules with client only', () => {
      const clientId = 'client123';
      const mockResponse = { bundles: [] };

      service.getProductBundleFeeSchedules(clientId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.productDomainUrl}/client/clientbundlefeesetup?clientId=${clientId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should get product bundle fee schedules with client and product', () => {
      const clientId = 'client123';
      const productId = 'product456';
      const mockResponse = { bundles: [] };

      service.getProductBundleFeeSchedules(clientId, productId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.productDomainUrl}/client/clientbundlefeesetup?clientId=${clientId}&productId=${productId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('Tab Options Management', () => {
    it('should set and get selected tab option', () => {
      const tabOption = 'View Products';
      service.setSelectedTabOption(tabOption);
      expect(service.selectedTabOption).toBe(tabOption);

      const result = service.getSelectedTabOption();
      expect(result as any).toBe(0);
    });

    it('should get tab option by name', () => {
      const option = 'View Fee Schedule';
      const result = service.getTabOptionSelected(option);
      expect(result as any).toBe(1);
    });

    it('should handle unknown tab option', () => {
      const option = 'Unknown Option';
      const result = service.getTabOptionSelected(option) as any;
      expect(result).toBeUndefined();
    });

    it('should handle empty selected tab option', () => {
      service.selectedTabOption = '';
      const result = service.getSelectedTabOption() as any;
      expect(result).toBeUndefined();
    });
  });

  describe('getClientPreferences', () => {
    it('should get client preferences successfully', () => {
      const url = 'client123/product456';
      const mockResponse = { preferences: [] };

      service.getClientPreferences(url).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.clientPreferenceDomainUrl}/clientPreference/getDataExchange/${url}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getClientPreferencesViewDataExchange', () => {
    it('should get client preferences view data exchange', () => {
      const preferenceId = 'pref123';
      const mockResponse = { dataExchange: [] };

      service.getClientPreferencesViewDataExchange(preferenceId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.clientPreferenceDomainUrl}/clientPreference/ViewDataExchange/${preferenceId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getClientFileHistory', () => {
    it('should get client file history successfully', () => {
      const exId = 'ex123';
      const mockResponse = { files: [] };

      service.getClientFileHistory(exId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.inventoryInsightUrl}api/dbg-inventoryinsight/clientacknowledgement/files/${exId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getConceptExecutionIds', () => {
    it('should get concept execution IDs successfully', () => {
      const clientSelected = 'client123';
      const clientName = 'Test Client';
      const mockResponse = { conceptIds: [] };

      service.getConceptExecutionIds(clientSelected, clientName).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.inventoryInsightUrl}api/dbg-inventoryinsight/invinsight/getConceptExecutionids/${clientSelected}/${clientName}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getConceptProdExecutionIds', () => {
    it('should get concept prod execution IDs successfully', () => {
      const clientSelected = 'client123';
      const mockResponse = { prodIds: [] };

      service.getConceptProdExecutionIds(clientSelected).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.inventoryInsightUrl}api/dbg-inventoryinsight/invinsight/getConceptProdExecutionids/${clientSelected}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getMasterData', () => {
    it('should get master data using forkJoin', () => {
      const clientId = 'client123';
      spyOn(service, 'getClientProducts').and.returnValue(of({ products: [] }));
      spyOn(service, 'getAllTemplates').and.returnValue(of({ templates: [] }));
      spyOn(service, 'getAllDBGUnits').and.returnValue(of({ units: [] }));
      spyOn(service, 'getCffFields').and.returnValue(of({ fields: [] }));

      service.getMasterData(clientId).subscribe(result => {
        expect(result).toEqual([
          { products: [] },
          { templates: [] },
          { units: [] },
          { fields: [] }
        ]);
      });

      expect(service.getClientProducts).toHaveBeenCalledWith(clientId);
      expect(service.getAllTemplates).toHaveBeenCalled();
      expect(service.getAllDBGUnits).toHaveBeenCalled();
      expect(service.getCffFields).toHaveBeenCalled();
    });
  });

  describe('getAllProducts', () => {
    it('should get all products successfully', () => {
      const mockResponse = { products: [] };

      service.getAllProducts().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.productDomainUrl}/products`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getAllTemplates', () => {
    it('should get all templates successfully', () => {
      const mockResponse = { templates: [] };

      service.getAllTemplates().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.inventoryDomainUrl}api/dbg-inventorydomain/filetemplate/list`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getSystemsAndInventoryTypes', () => {
    it('should get systems and inventory types successfully', () => {
      const mockResponse = { systems: [] };

      service.getSystemsAndInventoryTypes().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.inventoryDomainUrl}api/dbg-inventorydomain/system/list`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getAllDBGUnits', () => {
    it('should get all DBG units successfully', () => {
      const mockResponse = { units: [] };

      service.getAllDBGUnits().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.productDomainUrl}/DbgUnit/list`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getCffFields', () => {
    it('should get CFF fields successfully', () => {
      const mockResponse = { fields: [] };

      service.getCffFields().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.clientPreferenceDomainUrl}/clientPreference/getMasterColumns`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('Service Properties', () => {
    it('should allow setting and getting selectedProductName', () => {
      const productName = 'Test Product';
      service.selectedProductName = productName;
      expect(service.selectedProductName).toBe(productName);
    });

    it('should allow setting and getting selectedProductId', () => {
      const productId = 'prod123';
      service.selectedProductId = productId;
      expect(service.selectedProductId).toBe(productId);
    });

    it('should allow setting and getting selectedTenantId', () => {
      const tenantId = 456;
      service.selectedTenantId = tenantId;
      expect(service.selectedTenantId).toBe(tenantId);
    });
  });
});
