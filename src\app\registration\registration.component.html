<div class="page-header">
     <img class="image-holder" src="./assets/images/logo.png">
      <h4 class="page-text"> | Payment Integrity</h4>
  </div>
  <div class="carelon-product-theme">
    <div class="register-container">
      <div class="wd-30"></div>
      <div class="form-reg-container wd-30">
        <div class="header-name">Create your account</div>
            <marketplace-dynamic-form 
              [formJSON]="registrationDetailsJson" 
              [isSubmitNeeded]="false"
              [isSubmitted]="isFormSubmitted"
  
              (onValueChanges)="onChange($event)">
            </marketplace-dynamic-form>
        <div class="btn-footer">
            <marketplace-button                
                class="" 
                name="create-account" 
                [label]="'Create account'"
                [leadIconSVG]="createAccountSVG"
                [type]="'cta'"
                [enabled]="!isReadOnly"
                
                (onclick)="register()">
            </marketplace-button>
        </div>
        <div class="btn-link">
          <a routerLink="/signin">Already have an account?</a>
        </div>
      </div>
      <div class="wd-30"></div>
    </div>
  </div>
  <div class="page-footer">
    Need Help ?
    <a class="email-support" href="{{contactUsUrl}}" target="_blank">Email Support</a>
  </div>
  
  <marketplace-notification *ngIf="alertService?.showAlert" [open]="alertService.notification.notificationOpen"
          [header]="alertService.notification.notificationHeader" [bodyText]="alertService.notification.notificationBody"
          [type]="alertService.notification.notificationType" [duration]="alertService.notification.notificationDuration"
          [position]="alertService.notification.notificationPosition" (onClose)="clearNotification()">
  </marketplace-notification>
  <div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>