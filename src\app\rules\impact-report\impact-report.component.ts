import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { RulesApiService } from '../_services/rules-api.service';
import { BusinessDivisionService } from 'src/app/_services/business-division.service';
import { ToastService } from 'src/app/_services/toast.service';
import { Subscription } from 'rxjs';


@Component({
  selector: 'app-impact-report',
  templateUrl: './impact-report.component.html',
  styleUrls: ['./impact-report.component.scss'],
})
export class ImpactReportComponent implements OnInit {
  impactReportDrpdwnJSON: any;
  arrowSymbol: string = `<`;
  impactReportList: any;
  businessDivision: string;
  conceptExecutionRes: any[] = [];
  enablePerformAnalysisBtn: boolean = false;
  ruleId: number;
  isImpactReportReady: boolean = false;
  impactReportCardDataSet: any;
  showLoader = false;
  selectedConceptState: any;
  selectedExecutionId: any;
  selectedConceptId: any;
  ruleLevel: string = "";
  paramSub: Subscription;

  constructor(private router: Router, private rulesApiService: RulesApiService,
    private businessDivisionService: BusinessDivisionService, private alertService: ToastService, private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.businessDivision = this.businessDivisionService.getBusinessDivision()
    this.constructHeaderDetails();
    let routerParameters = this.router.url.slice(this.router.url.lastIndexOf('/') + 1);
    let extractRuleID: string = routerParameters;
    extractRuleID = routerParameters.substring(0, routerParameters.indexOf("?"));
    this.ruleId = Number(extractRuleID);
    this.paramSub = this.route.queryParams.subscribe(queryParams => {
      if (queryParams["level"]) {
        this.ruleLevel = queryParams["level"];
      }
      if (queryParams["impactModeId"]) {
        this.navigateToFinalReport();
      }
    });
  }

  /**
   * Gets called only when routed from notification
   */
  navigateToFinalReport(): void {
    // logic for navigation from notification
    if (this.router.url.indexOf("read-report") > -1) {
      this.impactReportCardDataSet = [{
        icon: '',
        title: "500 Distinct Insights",
        count: "Impacted by Rule"
      }, {
        icon: '',
        title: "1000 Distinct Insights",
        count: "Not Impacted by Rule"
      },]

      let requestPayload = {
        "data": {
          "rule_id": this.ruleId
        }
      }

      this.rulesApiService.getImpactReport(requestPayload).subscribe((data) => {
        this.impactReportList = data;
      });

    }
  }

  /**
 * Method to construct dropdowns
 */
  constructHeaderDetails() {
    this.impactReportDrpdwnJSON = createSearchConfig();
  }

  /**
 * This method triggers when the top down changes
 */
  onDropdownValueChange(event: any) {
    
    if (event.current.impactReportForm.conceptState != event.previous.impactReportForm.conceptState) {
      this.impactReportDrpdwnJSON[0].groupControls.forEach(element => {
        if(element.name == "concept"){
          element.selectedVal = null
          element.disabled = true
          element.options = []
        }
        else if(element.name == "executionId"){
          element.selectedVal = null
          element.disabled = true
          element.options = []
        }
      });
      
      if (event.current.impactReportForm.conceptState) {
        this.showLoader = true
        this.rulesApiService.getConceptExecutionByConceptState(event.current.impactReportForm.conceptState, this.businessDivision).subscribe(res => {
            this.conceptExecutionRes = res;
            this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "concept").disabled = false;

            this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "concept").options = res.map(c => { return { "name": c.conceptId, "id": c.conceptId } });
            this.showLoader = false
          },
          (error) => {
            this.showLoader = false;
          })
      }
      else {
        this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "concept").selectedVal = null;
        this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "concept").disabled = true;
        this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "concept").options = [];
      }

    }
    else if (event.current.impactReportForm.concept != event.previous.impactReportForm.concept) {

      if (event.current.impactReportForm.concept) {
        this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "executionId").disabled = false;
        const executionIds = this.conceptExecutionRes.filter(c => c.conceptId == event.current.impactReportForm.concept)?.map(c => c.executionIds)
        if (executionIds.length > 0)
          this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "executionId").options = executionIds[0]?.map(c => { return { "name": c, "id": c } });
      }
      else {
        this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "executionId").selectedVal = null;
        this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "executionId").disabled = true;
        this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "executionId").options = [];
      }

    }
    else if (event.current.impactReportForm.conceptState && event.current.impactReportForm.concept && event.current.impactReportForm.executionId) {
      this.enablePerformAnalysisBtn = true;

      //// API call
    }

    if (!event.current.impactReportForm.conceptState || !event.current.impactReportForm.concept || !event.current.impactReportForm.executionId) {
      this.enablePerformAnalysisBtn = false;
    }
  }

  /**
 * This method triggers when back to rules engine bread crumb is clicked
 */
  breadCrumbClick() {
    this.router.navigate(['/rules']);
  }

  /**
 * This method triggers when user clicks on Perform Analysis button
 */
  performAnalysisBtnClick() {
    const payload = {
      "data": {
        "rule_id": this.ruleId,
        "execution_id": this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "executionId")?.selectedVal,
        "concept_id": this.impactReportDrpdwnJSON[0].groupControls.find(c => c.name == "concept")?.selectedVal,
        "rule_level": this.ruleLevel
      }
    }
    this.rulesApiService.triggerPerformAnalysis(payload).subscribe(response => {
      this.alertService.setSuccessNotification({
        notificationHeader: "Success",
        notificationBody: "Processing Impact Report. We will notify you when it's ready"
      });
    });
  }

  ngOnDestroy(): void {
    if (this.paramSub) {
      this.paramSub.unsubscribe();
    }
  }


  columnConfig: any = {
    "switches": {
      "enableSorting": true,
      "enablePagination": true,
      "enableFiltering": true
    },
    "colDefs": [
      {
        "name": "Rule ID",
        "field": "ruleId",
        "filterType": "Text",
        "visible": "True",
      },
      {
        "name": "Rule Name",
        "width": 100,
        "field": "inventoryId",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Inventory ID",
        "width": 100,
        "field": "inventoryId",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Inventory Claim Type Indicator",
        "width": 200,
        "field": "invClmTypeInd",
        "visible": "True",
      },
      {
        "name": "Claim number",
        "width": 100,
        "field": "clmNumber",
        "visible": "True"
      },
      {
        "name": "Claim Adjustment Key",
        "width": 200,
        "field": "clmAdjKey",
        "visible": "True"
      },
      {
        "name": "Claim Line Number",
        "width": 100,
        "field": "clmLineNumber",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "XREF Claim Number",
        "width": 200,
        "field": "xrefClmNumber",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "XREF Claim Adjustment Key",
        "width": 200,
        "field": "xrefClmAdjKey",
        "visible": "True"
      },
      {
        "name": "ITS Indicator",
        "width": 100,
        "field": "itsIndicator",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Claim Type",
        "width": 100,
        "field": "clmType",
        "filterType": "Text",
        "visible": "False"
      },
      {
        "name": "Adjustment Date",
        "width": 100,
        "field": "adjDate",
        "filterType": "Calendar",
        "visible": "True",
        "dateFormat": "MM/DD/YYYY"
      },
      {
        "name": "Claim Line Service Start Date",
        "width": 300,
        "field": "clmLineServiceStartDate",
        "filterType": "Calendar",
        "visible": "True",
        "dateFormat": "MM/DD/YYYY"
      },
      {
        "name": "Claim Line Service End Date",
        "width": 300,
        "field": "clmLineServiceEndDate",
        "filterType": "Calendar",
        "visible": "True",
        "dateFormat": "MM/DD/YYYY"
      },
      {
        "name": "Billing Provider Tax ID",
        "width": 300,
        "field": "billingProvTaxId",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Rendering Provider National Provider Identifier",
        "width": 300,
        "field": "nationalProviderIden",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Rendering Provider Identifier",
        "width": 100,
        "field": "providerIden",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Rendering Primary Special 1 Code",
        "width": 300,
        "field": "rendPrimarySpec1Code",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Provider Status (Inn Code)",
        "width": 300,
        "field": "providerStatus",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Provider State",
        "width": 200,
        "field": "providerState",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Procedure Code",
        "width": 200,
        "field": "procedureCode",
        "filterType": "Text",
        "visible": "True"
      },


      {
        "name": "Procedure Modifier 1 Code",
        "width": 300,
        "field": "procedureMod1Code",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Procedure Modifier 2 Code",
        "width": 300,
        "field": "procedureMod2Code",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Procedure Modifier 3 Code",
        "width": 300,
        "field": "procedureMod3Code",
        "filterType": "Text",
        "visible": "True"
      },



      {
        "name": "Procedure Modifier 4 Code",
        "width": 300,
        "field": "procedureMod4Code",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Type of Bill Code",
        "width": 100,
        "field": "typeBillCode",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Diagnosis Pointer Code",
        "width": 100,
        "field": "daignosisPointerCode",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Revenue Code",
        "width": 100,
        "field": "revenueCode",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Place of Service",
        "width": 100,
        "field": "placeOfService",
        "filterType": "Text",
        "visible": "True"
      },

      {
        "name": "Claim Edit Text",
        "width": 100,
        "field": "clmExitTxt",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Paid Amount",
        "width": 100,
        "field": "paidAmount",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Total Paid Amount",
        "width": 100,
        "field": "totalPaidAmount",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Allowed Amount",
        "width": 100,
        "field": "allowedAmount",
        "filterType": "Text",
        "visible": "True"
      },

      {
        "name": "Billed Charge Amount",
        "width": 200,
        "field": "billedChargeAmount",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Total Billed Charge Amount",
        "width": 200,
        "field": "totBilledChargeAmount",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Paid Service Unit Count",
        "width": 100,
        "field": "paidServiceUntiCnt",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Billed Service Unit Count",
        "width": 200,
        "field": "billedServiceUnitCnt",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Member State",
        "width": 100,
        "field": "memberState",
        "filterType": "Text",
        "visible": "True"
      },



      {
        "name": "Member ID",
        "width": 100,
        "field": "memberId",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Member Sequence Number",
        "width": 200,
        "field": "memberSeqNumber",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Group Number",
        "width": 100,
        "field": "grpNumber",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Subgroup Number",
        "width": 100,
        "field": "subGrpNumber",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Benefit Package Identifier",
        "width": 100,
        "field": "benifitPackIden",
        "filterType": "Text",
        "visible": "True"
      },
      {
        "name": "Provider Contract Identifier",
        "width": 100,
        "field": "providerContractIden",
        "filterType": "Text",
        "visible": "True"
      },








    ]
  };

}

/**
 * @function createSearchConfig - json
 * @param data - data 
 * @returns final json
 */
const createSearchConfig = () => {
  const data = [
    {
      "type": "group",
      "name": "impactReportForm",
      "label": "",
      "column": "1",
      "groupControls": [
        {
          "label": "Concept State",
          "type": "select",
          "name": "conceptState",
          "id": "conceptState",
          "column": "3",
          "closeOnSelect": true,
          "disabled": false,
          "optionName": "name",
          "optionValue": "id",
          "required": true,
          "options": [{ name: "QA", id: "QA" }, { name: "Production", id: "Production" }],
          // "selectedVal": formData.clientName
        },
        {
          "options": [],
          "optionName": "name",
          "optionValue": "id",
          "label": "Concept",
          "type": "select",
          "name": "concept",
          "id": "concept",
          "column": "3",
          "disabled": true,
          "closeOnSelect": true,
          "required": true
        },
        {
          "options": [],
          "optionName": "name",
          "optionValue": "id",
          "label": "Execution Id",
          "type": "select",
          "name": "executionId",
          "id": "executionId",
          "column": "3",
          "closeOnSelect": true,
          "disabled": true,
          "required": true
        },
      ]
    }]
  return data;
}