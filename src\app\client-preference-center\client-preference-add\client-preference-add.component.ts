import {
  Component,
  EventEmitter,
  HostListener,
  OnInit,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ActivatedRoute, Router } from '@angular/router';
import { default as dataExchange } from './../../../assets/json/client-preference/dataExchange.json';

import { cloneDeep } from 'lodash';
import moment from 'moment';
import { UtilitiesService } from '../../_services/utilities.service';
import { list, QBOperators } from '../constant'
import { typeMapping, nullValueProduct, emptyProduct, operatorMap, rules, exchangeObj } from '../client-preference';
import { QueryBuildFormatterService } from '../../_services/query-build-formatter.service';
import { query } from '@angular/animations';
import { AbstractControl } from '@angular/forms';
import { ToastService } from 'src/app/_services/toast.service';
import { ProductApiService } from 'src/app/_services/product-api.service';

@Component({
  selector: 'app-client-preference-add',
  templateUrl: './client-preference-add.component.html',
  styleUrls: ['./client-preference-add.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class ClientPreferenceAddComponent implements OnInit {
  public dataExchangeJson: any;
  startDateValue: string | any = '';
  datePickerFormat: string = 'DD-MM-YYYY';
  datePickerName: string = 'test';
  notificationReady: boolean;
  notificationOpencp: any = false;
  notificationHeadercp: string;
  notificationBodycp: string;
  public popupDisplayStyle: any = 'none';
  notificationType: any;
  inventorySystem: string = "";
  @Output() DataEvent = new EventEmitter<string>();
  productName: any;
  preferenceName: any;
  DBGUnit: any;
  system: any;
  inventoryType: any;
  showLoader: boolean = false;
  products: any = [];
  templates: any = [];
  systems: any = [];
  DBGUnits: any = [];
  systemDependentFieldsData: any = [];
  enableForm: boolean = false;
  dataExchangeconfig = {
    fields: {}
  };
  dataExchangequery = {
    condition: 'and',
    rules: [],
  };
  clientObject: any;
  conceptStateOptions: any = [{ name: "QA", id: 1 }, { name: "Prod", id: 2 }];
  formValidation: any;
  multiTimeFrameValidation: boolean = false;
  enableFormButtton: boolean = true;
  clientId: any;
  clientName: any;
  enableSystemValidation = true;
  selectedTemplate: any;
  productId: any;
  productMappingFileTemplatesList: any = [];
  isTemplateReady: boolean = false;
  timeFrequency: any = "";
  isFrequencyDaily: boolean = false;
  maxTargetSelection: number = 5;
  timeFrameDataset: any;
  dataExchangeTimeFormJson: any = [{
    label: "Time",
    type: "time",
    name: "time",
    required: true,
    column: "1",
    disabled: false
  }];
  operators: any = QBOperators;

  constructor(
    private alertService: ToastService,
    private clientApiService: ClientApiService,
    private dateService: UtilitiesService,
    private route: ActivatedRoute,
    private router: Router,
    private productApiService: ProductApiService,
    private queryBuildServer: QueryBuildFormatterService
  ) { }

  ngOnInit(): void {
    this.clientId = Number(this.route.snapshot.paramMap.get('clientId'));
    this.clientName = this.route.snapshot.paramMap.get('clientName');
    this.getMasterData();
  }

  /**
   * Calls the service method to get master data from API
   */
  getMasterData(): void {
    this.showLoader = true;
    this.clientApiService.getMasterData(this.clientId).subscribe(
      ([products, templates, DBGUnits]) => {
        this.products = products;
        if (this.clientApiService.selectedProductId) {
          const item = products.find((elem) => { return elem.productId == this.clientApiService.selectedProductId });
          this.products = [item];
        }
        this.templates = templates.filter((elem) => { return elem.actvInd === true; });;
        this.DBGUnits = DBGUnits;
        this.populateMasterDateOnForm();
        this.showLoader = false;
      }
      ,
      (error) => {
        this.populateMasterDateOnForm();
        this.showLoader = false;
      }
    );
  }

  /**
* Changing the query builder structure to be sync with API
*/
  modifyQBuilderStructure(qbQuery) {
    let parsed: any = JSON.parse(JSON.stringify(qbQuery), function (k, v) {
      switch (k) {
        case "operator":
          return operatorMap[v] ? operatorMap[v] : v;;
          break;
        case "static":
          this.stat = v;
          break;
        case "config":
        case "operatorList":
        case "delete":
        case "fieldsList":
        case "fieldsMapList":
        case "customfieldsList":
        case "tabsList":
          delete qbQuery[k];
          break;
        default:
          return v;
      }
    });
    parsed = this.queryBuildServer.getQueryOutput(parsed, this.dataExchangeconfig.fields)
    return parsed;
  }

  /**
   * Populates the data on form
   */
  populateMasterDateOnForm(): void {
    this.dataExchangeJson = cloneDeep(dataExchange);
    this.bindDropdown();
    this.enableForm = true;
  }

  /* 
   * Method is used to bind dropdown for product,template,dbgunit,system columns 
   * And relationship of system with inventory  
   */
  bindDropdown(): void {
    if (this.clientId != list.ANTHEM_CLIENT_ID) {
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).disabled = true;
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).required = false;
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).placeholder = "";
    }
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.PRODUCT_NAME).options = this.products;
    if (this.clientApiService.selectedProductId && this.products.length) {
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.PRODUCT_NAME).selectedVal = this.products[0].productName;
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.PRODUCT_NAME).disabled = this.products.length == 1 ? true : false;
    }
    this.constructRelationForProductFileTemplate();
    let productId = this.clientApiService?.selectedProductId;
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.TEMPLATE_NAME).options = this.templates.filter(x => x.prodId == productId).map(template => ({ "fileTmplName": template.fileTmplName }));
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.PRODUCT_NAME).relationship = this.productMappingFileTemplatesList;
    this.dataExchangeJson[0].groupControls.find((x) => x.name == list.DBG_UNIT).options = this.DBGUnits;
  }

  /**
   * Method to construct related dropdown file templates for product
   */
  constructRelationForProductFileTemplate(): void {
    this.productMappingFileTemplatesList = [];
    let relationshipNullObj = nullValueProduct
    let relationshipSelectedValObj = emptyProduct;
    this.productMappingFileTemplatesList.push(relationshipSelectedValObj);
    this.productMappingFileTemplatesList.push(relationshipNullObj);
    this.products.forEach(product => {
      let relationshipObj = {
        updateDataset: [
          {
            id: list.TEMPLATE_NAME, dataset: this.templates.filter(x => x.prodId == product.productId).map(template => ({ "fileTmplName": template.fileTmplName }))
          }],
        when: product.productName
      }
      this.productMappingFileTemplatesList.push(relationshipObj);
    });
  }

  /**
   * Method to construct the query builder config
   * @param invType 
   * @param prodName 
   */

  constructQbConfigFields(invType: string, prodName: string): void {
    try {
      this.isTemplateReady = false;
      this.clientApiService.getCffFieldsByInventoryTypeProduct(invType, prodName).subscribe(data => {
        if (Array.isArray(data) && data.length) {
          this.dataExchangeconfig.fields = {};
          this.dataExchangeconfig.fields = this.modifyQBConfig(data);
          let rulesetObj =
          {
            "condition": "or",
            "rules": rules
          }
          rulesetObj.rules[0].field = data[0].clmnNm;
          this.dataExchangequery = cloneDeep(rulesetObj);
          this.isTemplateReady = true;
          this.alertService.setInfoNotification({
            notificationHeader: list.INFO,
            notificationBody: list.NOTIFICATION_MESSAGE,
          });
        }
      })
    }
    catch (err) {
      console.log("Error while constructing qbConfig", err)
    }

  }

  /**
   * Modify master data fields into the format query builder understands
  */
  modifyQBConfig(masterDataQBConfig): any {
    let QBfields = {};
    masterDataQBConfig.forEach(field => {
      switch (field.clmnType.toUpperCase()) {
        case list.DROPDOWN:
          QBfields[field.clmnNm] = { name: field.dsplyNm, type: list.SINGLE_SELECT, dataset: field.options, key: 'name', id: 'id', table: field.tableName };
          break;
        case list.VARCHAR, list.NUMBER, list.DATE_COLUMN:
          QBfields[field.clmnNm] = { name: field.dsplyNm, type: typeMapping[field.clmnType.toUpperCase()], table: field.tableName };
          if (field.clmnType == list.DATE_COLUMN) {
            QBfields[field.clmnNm].dateFormat = list.DATE_FORMAT;
          }
          if (field.clmnType == list.DECIMAL) {
            QBfields[field.clmnNm].regPattern = list.DECIMAL_REG;
            QBfields[field.clmnNm].forceRegex = true;
          }
          break;
        default:
          QBfields[field.clmnNm] = { name: field.dsplyNm, type: typeMapping[field.clmnType.toUpperCase()], table: field.tableName };
          break;
      }
    });
    return QBfields;
  }

  /**
   *  Method will be triggered on time field change
   * Need to validate this method if needed or not
   */
  onTimeChange(): void {
    let inputEle = (<HTMLInputElement>document.getElementById('timeInput'))
      .value;
    let timeSplit = inputEle.split(':'),
      hours,
      minutes,
      meridian;
    hours = timeSplit[0];
    minutes = timeSplit[1];
    if (hours > 12) {
      meridian = 'PM';
      hours -= 12;
    } else if (hours < 12) {
      meridian = 'AM';
      if (hours == 0) {
        hours = 12;
      }
    } else {
      meridian = 'PM';
    }
  }

  /**
   * Method will be triggered on timepicker form change
   * @param event 
   */
  onTimepickerChange(event) {
    this.timeFrequency = event.value.time;
    this.enableFormButtton = this.productName === list.CLAIM_ANOMALY_DETECTION && this.clientId == list.ANTHEM_CLIENT_ID ? this.formValidation[list.STATUS] == list.VALID ? false : true : !this.formValidationCheck();
  }

  /**
   * Method will be triggered on form repeater form change
   * @param event 
   */
  formRepeaterValueChange() {
    let count = 0;
    this.timeFrameDataset.forEach(element => {
      if (element.ngModel.value.time)
        count++;
    });
    if (this.timeFrameDataset.length == count) {
      this.multiTimeFrameValidation = true;
      this.enableFormButtton = this.productName === list.CLAIM_ANOMALY_DETECTION && this.clientId == list.ANTHEM_CLIENT_ID ? this.formValidation[list.STATUS] == list.VALID ? false : true : !this.formValidationCheck();
    }
    else {
      this.multiTimeFrameValidation = false;
      this.enableFormButtton = true
    }
  }

  /**
   * Method will be triggered on form change
   * @param event 
   */
  valuechange(event): void {
    this.enableSystemValidation = true;
    if (this.productName != event.controls.exchange.controls.productName.value) {
      this.productName = event.controls.exchange.controls.productName.value;
      event.controls.exchange.patchValue({ templateName: null, conceptState: null })
      this.dataExchangeconfig.fields = {};
      this.isTemplateReady = false;
    }
    if (this.clientId == list.ANTHEM_CLIENT_ID) {
      if (this.productName != undefined || this.productName != null) {
        if (this.productName === list.CLAIM_ANOMALY_DETECTION) {
          this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).disabled = false;
          this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).required = true;
          this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).placeholder = list.BUS_DIV_PLACEHOLDER;
        } else {
          this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).disabled = true;
          this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).required = false;
          this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).placeholder = null;
          this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).selectedVal = undefined;
        }
      } else {
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).disabled = false;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).required = true;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).placeholder = list.BUS_DIV_PLACEHOLDER;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.BUSINESS_DIVISION).selectedVal = undefined;
      }
    }

    if (!this.formValidation || this.formValidation.value.exchange.templateName != event.value.exchange.templateName) {
      this.selectedTemplate = this.templates.find((elem) => {
        return event.value.exchange.templateName == elem.fileTmplName
      });
      this.formValidation = cloneDeep(event);
      if (this.selectedTemplate) {
        event.controls.exchange.patchValue({
          inventoryType: this.selectedTemplate.invTypeName,
          system: this.selectedTemplate.systemName, conceptState: this.selectedTemplate?.tmpltType == list.PROD_UAT ? undefined : this.selectedTemplate?.tmpltType
        });

        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.INVENTORY_TYPE).value = this.selectedTemplate.invTypeName;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).selectedVal = this.selectedTemplate?.tmpltType == list.PROD_UAT ? undefined : this.selectedTemplate?.tmpltType;
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.SYSTEM).value = this.selectedTemplate.systemName;
      } else {
        event.controls.exchange.patchValue(exchangeObj);
      }

    } else {
      if (event.value.exchange.startDate >= this.formValidation.value.exchange.endDate && this.formValidation.value.exchange.startDate != event.value.exchange.startDate) {
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.END_DATE).value = null;
        event.value.exchange.endDate = null;
        event[list.STATUS] = list.INVALID;
      }
      else {
        this.dataExchangeJson[0].groupControls.find((x) => x.name == list.END_DATE).value = event.value.exchange.endDate;
      }
      this.formValidation = cloneDeep(event);
    }

    if (this.selectedTemplate?.tmpltType == list.UAT || this.selectedTemplate?.tmpltType == list.PROD) {

      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).disabled = true;
    }
    else {
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).disabled = false;
      this.dataExchangeJson[0].groupControls.find((x) => x.name == list.CONCEPT_STATE).options = [{ name: list.UAT, id: 1 }, { name: list.PROD, id: 2 }];
    }
    if (event.value.exchange.frequency != list.DAILY && this.timeFrequency != '')
      this.enableFormButtton = this.productName === list.CLAIM_ANOMALY_DETECTION && this.clientId == list.ANTHEM_CLIENT_ID ? this.formValidation[list.STATUS] == list.VALID ? false : true : !this.formValidationCheck();
    else if (event.value.exchange.frequency == list.DAILY && this.multiTimeFrameValidation == true)
      this.enableFormButtton = this.productName === list.CLAIM_ANOMALY_DETECTION && this.clientId == list.ANTHEM_CLIENT_ID ? this.formValidation[list.STATUS] == list.VALID ? false : true : !this.formValidationCheck();
    else
      this.enableFormButtton = true;
  }

  /**
 * Method to validate form when ClientId =59 and product != CAD
 * @param event 
 */

  formValidationCheck() {
    return Object.entries(this.formValidation.controls.exchange.controls).every(([controlName, control]) => {
      const abstractControl = control as AbstractControl;
      if (controlName !== 'businessDivision') {
        return abstractControl.valid;
      }
      return true;
    });

  }

  /**
   * Method to get the previous and current values to get cff fields
   * @param event 
   */
  getPreviousCurrentValues(event: any): void {
    if (event.current.exchange.frequency == list.DAILY && event.previous.exchange.frequency != event.current.exchange.frequency) {
      this.timeFrameDataset = [{
        formJSON: [{
          label: "Time",
          type: "time",
          name: "time",
          required: true,
          column: "1",
          disabled: false
        }]
      }];
      this.isFrequencyDaily = true;
      this.enableFormButtton = true;
    }
    else if (event.current.exchange.frequency != list.DAILY) {
      this.isFrequencyDaily = false;
    }
    if (event.previous.exchange.templateName != event.current.exchange.templateName) {
      this.constructQbConfigFields(this.selectedTemplate?.invTypeName, this.productName)
    }
  }


  /**
   *  method to get Product Id for sending to Data platform
  */
  getProductId(): any {
    this.products.forEach(e => {
      if (e.productName == this.formValidation.value.exchange[list.PRODUCT_NAME]) {
        return this.productId = e[list.PRODUCT_ID];
      }
    });
  }

  /**
   *  Method does service call to save the preference
   */
  savePreference(): void {
    /**The following lines will be needed in future, 
     * so commenting them down */

    //let timeList = [];
    this.getProductId();
    if (this.isFrequencyDaily == true) {
      this.timeFrequency = "";
      this.timeFrameDataset.forEach(data => {
        this.timeFrequency += data.ngModel.value.time + ";"
        //timeList.push(data.ngModel.value.time);
      });
      this.timeFrequency = this.timeFrequency.slice(0, -1);
    }
    //else if (this.timeFrequency) {
    //  timeList.push(this.timeFrequency);
    //}
    if (this.formValidation['status'] == "VALID" || (this.clientId === list.ANTHEM_CLIENT_ID && this.productName !== list.CLAIM_ANOMALY_DETECTION && this.formValidationCheck)) {
      let clientObject = this.formValidation.value.exchange;
      const startIsBeforeEnd = this.dateService.checkDateLatest(clientObject['startDate'], clientObject['endDate']);
      if (startIsBeforeEnd) {
        clientObject['clientId'] = this.clientId;
        clientObject['clientName'] = this.clientName;
        clientObject['activeInd'] = 1;
        clientObject['systemId'] = this.selectedTemplate.systemId;
        clientObject['productId'] = this.productId;
        clientObject['templateId'] = this.selectedTemplate.fileTmplId;
        clientObject['invTypeId'] = this.selectedTemplate.invTypeId;
        clientObject['startDate'] = moment(clientObject['startDate']).format('YYYY-MM-DD');
        clientObject['endDate'] = moment(clientObject['endDate']).format('YYYY-MM-DD');
        clientObject['conditions'] = this.modifyQBuilderStructure(this.dataExchangequery);
        clientObject[list.PRODUCT_NAME] = clientObject[list.PRODUCT_NAME] == list.DMS ? list.CAD : clientObject[list.PRODUCT_NAME];
        var timeValuepair = { time: this.timeFrequency };
        clientObject = { ...clientObject, ...timeValuepair };

        this.clientApiService.createEditAddDE(clientObject).subscribe((data: any) => {
          if (data.statusCodeValue == 200) {
            this.alertService.setSuccessNotification({
              notificationHeader: list.SUCCESS,
              notificationBody: list.SUCCESS_MESG,
            });
            this.showLoader = false;
            this.backToListPage();
          }
          else if (data.statusCodeValue == 500) {
            this.displayNotification(list.FAIL, data.body)
          }

        }, (err: any) => {
          this.displayNotification(list.FAIL, err)
        })
      } else { this.displayNotification(list.WARNING, list.WARNING_MESG) }
    } else { this.displayNotification(list.WARNING, list.WARNING_MESG) }
  }

  /**
   * Method to display notification on success or failure
   * @param header 
   * @param body 
   */
  displayNotification(header: string, body: string): void {
    this.alertService.setErrorNotification({
      notificationHeader: header,
      notificationBody: body,
    });
    this.showLoader = false;
  }
  /**
   *  Method takes back to preference list
   */
  backToListPage(): void {
    this.DataEvent.emit('back to list');
  }

  /**
   *  Method closes the validation popup
   */
  closePopup(): void {
    this.popupDisplayStyle = 'none';
  }
  /**
* event to disable submit button when adding a row on form repeater
*/
  @HostListener('document:click', ['$event'])
  documentClick(event) {

    /** Adjusting marketplace Timepicker element Ids in the DOM to make AM,PM selection works properly 
    * when we use Timepicker with Marketplace Formrepeater.
    */
    let InputIds = ['inputId0', 'inputId1', 'inputId2', 'inputId3', 'inputId4']
    let outerElementcount = 0;
    document.getElementsByName(list.INPUT_ELEMENT_CLASS).forEach(outerElement => {
      outerElement.id = InputIds[outerElementcount];
      let innerElementCount = 0;
      document.querySelectorAll(list.LABEL_ELEMENT_CLASS).forEach(innerElement => {
        if (outerElementcount == innerElementCount)
          innerElement.setAttribute(list.FOR_ATTRIBUTE, outerElement.id)
        innerElementCount++;
      })
      outerElementcount++;
    })

    if (event.srcElement?.parentElement?.parentElement?.className == list.CLASS_RIGHT_ICON ||
      event.srcElement?.parentElement?.title == list.ADD_NEW_ROW) {
      this.enableFormButtton = true;
      this.formRepeaterValueChange()
    }
    else if (event.srcElement?.parentElement?.parentElement?.className == list.CLASS_LEFT_ICON ||
      event.srcElement?.parentElement?.title == list.DELETE_ROW) {
      this.formRepeaterValueChange()
    }
  }


}
