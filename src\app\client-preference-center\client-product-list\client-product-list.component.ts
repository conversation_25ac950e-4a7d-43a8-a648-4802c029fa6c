import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ToastService } from 'src/app/_services/toast.service';
import moment from 'moment';
import { AuthService } from 'src/app/_services/authentication.services';
import { ProductLinkedToClient } from 'src/app/_models/ProductLinkedToClient';
import { SCREEN_NAME_CONSTANTS, STATUS_CONSTANTS } from 'src/app/_constants/generic.constants';
import { NOTIFICATION_CONSTANT } from 'src/app/_constants/notification_constants';


@Component({
  selector: 'app-client-product-list',
  templateUrl: './client-product-list.component.html',
  styleUrls: ['./client-product-list.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class ClientProductListComponent implements OnInit {
  public headerText = "Client Setup";
  public isPriviousRedirectPage = true;
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  public dataDate: any = "21/02/2022";
  public lastRegfreshDate: any = "21/02/2022";
  public totalEntries: number;
  public clientProductData: any;
  public productBundleFeeData: any;
  public clientProductColConfig: any;
  public productBundleFeeColConfig: any;
  showProductInventoryEditPopUp: boolean = false;
  productFormJSON: any;
  invTypeJSON: any;
  inventoryOptions: any = [];
  inventoryTypeIDsData: any = [];
  isInventoryTypeFormReady: boolean = false;
  public dataRoot = "src";
  public client: string = "";
  public tabSelected: any = 0;
  public tableRedraw: any;
  public clientselected: any;
  public showFeeList: boolean = true;
  public showFeeAdd: boolean = false;
  public showFeeEdit: boolean = false;
  public isFormValid: boolean = false;
  public breadcrumbDataset: any = [];
  public selectedProduct: string = "Product_1";
  public notificationOpen: any = false;
  public notificationHeader: string;
  public notificationBody: string;
  public notificationPosition: any;
  public notificationDuration: any;
  public isProductTypeFormReady: boolean = false;
  public notificationReady: boolean = false;
  public notificationType: any;
  public listView: boolean = true;
  public notificationMessage: any = { 'add': 'Fee Schedule added successfully.', 'edit': 'Fee Schedule edited successfully.' };
  public kebabOptions: any = [{ label: '<i class="fa fa-eye" aria-hidden="true"></i> View Fee Schedule', id: 'viewFeeSetup' }, { label: '<i class="fa fa-eye" aria-hidden="true"></i> View Data Exchange', id: 'viewDataExchange' }];
  isReadOnly: boolean = false;
  selectTabheader: any = "";
  selectedProductName: any = "";
  clientName: any = "";
  allProductNames: any = [];
  state = {
    nscreen: "client"
  };
  navigate: any;
  selectedTab: any;
  customExport: any = {
    enabled: true,
    fileName: 'Product.xls'
  };
  addProductObj = {} as ProductLinkedToClient;
  constructor(private router: Router, private route: ActivatedRoute, private clientApiService: ClientApiService, private notificationService: ToastService, private authService: AuthService) {
    this.client = this.route.snapshot.paramMap.get('clientId');
    this.clientName = this.route.snapshot.paramMap.get('clientName');
    this.clientselected = this.client;
    this.breadcrumbDataset = [{ label: SCREEN_NAME_CONSTANTS.HOME.toString(), url: '/' }, { label: `${this.clientName}`, url: '/clients' }, { label: SCREEN_NAME_CONSTANTS.PRODUCTS_LIST.toString() }];
  }

  ngOnInit(): void {
    this.isReadOnly = !this.authService.isWriteOnly;
    const InventoryNameFromTempalteApi = this.clientApiService.getAllFileTemplates();

    InventoryNameFromTempalteApi.subscribe(templateData => {
      this.inventoryOptions = templateData.map(x => ({ "name": x.invTypeName, "id": x.invTypeId })).filter((v, i, a) => a.findIndex(v2 => (JSON.stringify(v2) === JSON.stringify(v))) === i)

    });

    this.tabSelected = (this.clientApiService.getSelectedTabOption() != undefined) ? this.clientApiService.getSelectedTabOption() : 0;
    this.state['nscreen'] = 'client';
    this.serviceData();

  }
  /*
  fetch all products from Client 
  client-> client id 
  */
  serviceData() {
    this.clientApiService.getClientProducts(this.client)
      .subscribe(data => {
        if (data) {
          this.clientProductData = data;
          this.totalEntries = data.length;
        } else {
          this.clientProductData = [];
          this.totalEntries = 0;
        }
      }, err => {
        this.clientProductData = [];
        this.totalEntries = 0;
      });

    this.clientProductColConfig = {
      "switches": {
        "enableSorting": true,
        "enablePagination": true,
        "enableFiltering": true
      },
      "colDefs": [
        {
          "name": "PRODUCT NAME",
          "field": "productName",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "P&L UNIT",
          "field": "plunit",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "PRODUCT DIVISION",
          "field": "productDivision",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "STATUS",
          "field": "status",
          "visible": "True",
          "editorType": "",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "customFormatter": this.customFormatterStatus
        },
        {
          "name": "# of ACTIVE BUNDLES",
          "field": "activeBundles",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "# of FEE SCHEDULES",
          "field": "feeschedules",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
        }
      ]
    };
  }

  /**
   * customFormatterStatus funtion for button in Rule table
   * @param event 
   */
  customFormatterStatus(event) {
    let btn;
    switch (event.value) {
      case STATUS_CONSTANTS.ACTIVE.toString():
        btn = "<button type='button' class='btn btn product-list btn-active'>Active</button>";
        break;
      case STATUS_CONSTANTS.IN_ACTIVE.toString():
        btn = "<button type='button' class='btn btn product-list btn-inactive'>Inactive</button>";
        break;
      case STATUS_CONSTANTS.PENDING.toString():
        btn = "<button type='button' class='btn btn product-list btn-pending'>Pending</button>";
        break;
    }
    return btn;
  }

  /**
    * redrawTable Function for Table
    * 
    */
  redrawTable(event: any) {
    this.state.nscreen = 'client';
    this.selectedProductName = '';
    this.clientApiService.selectedProductName = '';
    this.clientApiService.selectedProductId = '';
    setTimeout(() => {
      this.tableRedraw = Date.now()
      this.manipulateBreadcrumb(event);
    }, 100);

  }
  /**
   * Function to open a Pop-up for editing product inventory type
   */
  showProductModal() {
    if (this.isReadOnly) return
    this.isInventoryTypeFormReady = true;
    this.showProductInventoryEditPopUp = true;
    this.isProductTypeFormReady = true;

    const productFormJsonHttp = this.clientApiService.getproductFormJSON("./assets/json/client-preference/addProduct.json");
    productFormJsonHttp.subscribe(FormData => {
      this.productFormJSON = FormData["product"];
      this.invTypeJSON = FormData["inventoryType"];
      this.isInventoryTypeFormReady = true;

      this.clientApiService.getAllProducts().subscribe(allProducts => {

        this.allProductNames = allProducts.map(val => ({ name: val.productName, id: val.productId }));

        this.productFormJSON[0].groupControls.filter(
          (x) => x.name == SCREEN_NAME_CONSTANTS.PRODUCT.toString().toLocaleLowerCase()
        )[0].options = this.allProductNames;
      });

    });
  }

  /**
   * for closing product inventory edit popup
   */
  onCloseOverlay() {
    this.isInventoryTypeFormReady = false;
    this.isProductTypeFormReady = false;
    this.showProductInventoryEditPopUp = false;
  }

  /**
   * for adding product to a client
   */
  onSubmit() {
    this.clientApiService.saveProduct(this.addProductObj).subscribe((data) => {
      if (data.responseCode == 200) {
        this.notificationPopUpMsg(NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER, `Product updated Successfully`);
        this.serviceData();
        this.onCloseOverlay();
      }
      else {
        this.notificationPopUpMsg(NOTIFICATION_CONSTANT.ERROR_NOTIFICATION_HEADER, data.responseData);
      }
    },
      error => {
        this.notificationPopUpMsg(NOTIFICATION_CONSTANT.WARNING_NOTIFICATION_HEADER, error.responseData);
      });
  }


  /**
   * checks for null value
  */
  isNull(fieldValue) {
    if (fieldValue == null || fieldValue == "") return true;
    else return false;
  }
  /**   * Method to check inventory is mapped to product is valid or not   */
  checkInventoryMappingValidation(): void {
    setTimeout(() => {
      const invalidProductCollectionForm = document.querySelectorAll(`marketplace-dynamic-form#productform form.ng-invalid , marketplace-date-picker.ng-invalid`);
      const invalidInventoryTypeCollectionForm = document.querySelectorAll(`marketplace-dynamic-form#inventoryTypeform form.ng-invalid`);
      if (invalidProductCollectionForm.length == 0 && invalidInventoryTypeCollectionForm.length == 0) {
        this.isFormValid = true;
      } else {
        setTimeout(() => { this.isFormValid = false; }, 100);
      }
    }, 0);
  }

  /**
   * fires when inventory type is selected
   */
  _onInventorySelection(event) {
    this.checkInventoryMappingValidation();
    this.addProductObj.prodInvIds = [].concat(event.current['General_3'].invType);
  }

  /**
   * Function to handle dynamic form value change events
   */
  _onproductSelection(event) {
    if (event.current['General_1'].product != event.previous['General_1'].product) {
      this.inventoryTypeIDsData = [];
      this.invTypeJSON[0].groupControls.filter(x => x.name == "invType")[0].options = this.inventoryTypeIDsData;
      this.isInventoryTypeFormReady = false;
      if (event.current['General_1'].product) {
        const inventoryTypeIdHttp = this.clientApiService.getInventoryTypeId(Number(event.current['General_1'].product));

        inventoryTypeIdHttp.subscribe(inventoryData => {
          inventoryData['invDetails'].forEach(invTypeIdField => {
            let inventoryTypeObject: any = {};
            inventoryTypeObject.name = this.fetchInventoryTypeName(invTypeIdField['invTypeId'])?.toString();
            inventoryTypeObject.id = invTypeIdField['prodInvId'];
            if (inventoryTypeObject.name) {
              this.inventoryTypeIDsData.push(inventoryTypeObject);
            }
          });
          this.invTypeJSON[0].groupControls.filter(x => x.name == "invType")[0].options = this.inventoryTypeIDsData;
          this.isInventoryTypeFormReady = true;
          this.checkInventoryMappingValidation();
        });
      }
      else {
        this.checkInventoryMappingValidation();
      }
    }
    else {
      this.checkInventoryMappingValidation();
    }

    this.addProductObj.clientId = Number(this.client);
    this.addProductObj.productId = Number(event.current['General_1'].product);
    this.addProductObj.clntProdEfctvDt = moment(event.current['General_2'].effective_date).format('YYYY-MM-DD');
    this.addProductObj.clntProdTrmntnDt = moment(event.current['General_2'].termination_date).format('YYYY-MM-DD');
    this.addProductObj.revenueAmnt = event.current['General_1'].revenue_amount;
    this.addProductObj.membershipCount = event.current['General_1'].membership_count;
    this.addProductObj.createdBy = event.current['General_2'].create_by;
    this.addProductObj.modifiedBy = event.current['General_2'].update_by;
    this.addProductObj.status = event.current['General_2'].Status == "true";
  }
  /**
   * function to compare filetemplate results with inventory type Ids to get Inventory name for particular product
   */
  fetchInventoryTypeName(invTypeId): string {
    const fileTemplateData = this.inventoryOptions.find(x => x.id == invTypeId);
    return fileTemplateData?.name;
  }

  /**
   * moveToSelectedTab function
   * @param event 
   */
  moveToSelectedTab(event: Event): void {
    this.state.nscreen = SCREEN_NAME_CONSTANTS.PRODUCT.toString().toLocaleLowerCase();
    const currentRow = event['currentRow'];
    this.selectedProduct = event['currentRow'];
    this.selectedProductName = currentRow['productName'];
    const selectedOption = event['text'];
    this.tabSelected = -1;
    this.clientApiService.selectedProductName = this.selectedProductName;
    this.clientApiService.selectedProductId = currentRow['productId'];
    setTimeout(() => {
      this.tabSelected = this.clientApiService.getTabOptionSelected(selectedOption);
      setTimeout(() => {
        this.state.nscreen = SCREEN_NAME_CONSTANTS.PRODUCT.toString().toLocaleLowerCase();
        this.selectedProductName = currentRow['productName'];
        this.selectedProduct = event['currentRow'];
        this.clientApiService.selectedProductName = this.selectedProductName;
        this.clientApiService.selectedProductId = currentRow['productId'];
      }, 10)

    }, 0);
  }

  /**
   * selected Tab get event function
   * @param event 
   */
  manipulateBreadcrumb(event) {
    this.selectTabheader = event.name;
    this.selectedTab = "";
    switch (event.name) {
      case 'Products':
        this.breadcrumbDataset = [{ label: SCREEN_NAME_CONSTANTS.HOME.toString(), url: '/' }, { label: `${this.clientName}`, url: '/clients' }, { label: SCREEN_NAME_CONSTANTS.PRODUCTS_LIST.toString() }];
        break;
      case SCREEN_NAME_CONSTANTS.FEE_SCHEDULE.toString():
        this.selectedTab = SCREEN_NAME_CONSTANTS.FEE_SCHEDULE.toString();
        this.breadcrumbDataset = [{ label: SCREEN_NAME_CONSTANTS.HOME.toString(), url: '/' },
        { label: `${this.clientName}`, url: '/clients' }];
        if (this.state.nscreen == SCREEN_NAME_CONSTANTS.PRODUCT.toString().toLocaleLowerCase() || this.selectedProductName != '') {
          this.breadcrumbDataset.push({ label: `${this.selectedProductName}`, url: `/product/${this.client}/${this.clientName}` });
          this.breadcrumbDataset.push({ label: SCREEN_NAME_CONSTANTS.FEE_SCHEDULE.toString(), url: 1 });
          if (event?.feePageName) {
            this.breadcrumbDataset.push({ label: `${event?.feePageName}` });
          }
        } else {
          this.breadcrumbDataset.push({ label: SCREEN_NAME_CONSTANTS.FEE_SCHEDULE.toString(), url: 1 });
          if (event.feePageName) {
            this.breadcrumbDataset.push({ label: event.feePageName + ` Schedule` })
          }
        }

        break;
      case SCREEN_NAME_CONSTANTS.DATA_EXCHANGE.toString():
        this.selectedTab = SCREEN_NAME_CONSTANTS.DATA_EXCHANGE.toString();
        if (this.state.nscreen == SCREEN_NAME_CONSTANTS.PRODUCT.toString().toLocaleLowerCase()) {
          this.breadcrumbDataset = [
            { label: SCREEN_NAME_CONSTANTS.HOME.toString(), url: '/' },
            { label: `${this.clientName}`, url: '/clients' },
            { label: `${this.selectedProductName}`, url: `/product/${this.client}/${this.clientName}`, navigate: true },
            { label: SCREEN_NAME_CONSTANTS.DATA_EXCHANGE.toString(), url: 2 }
          ];
          if (event.dataExchangePageName && event.dataExchangePageName !== SCREEN_NAME_CONSTANTS.DATA_EXCHANGE.toString()) {
            this.breadcrumbDataset.push({ label: event.dataExchangePageName })
          }
        } else {
          this.breadcrumbDataset = [{ label: SCREEN_NAME_CONSTANTS.HOME.toString(), url: '/' },
          { label: `${this.clientName}`, url: '/clients' },
          { label: SCREEN_NAME_CONSTANTS.DATA_EXCHANGE.toString(), url: 2 }];
          if (event.dataExchangePageName && event.dataExchangePageName !== SCREEN_NAME_CONSTANTS.DATA_EXCHANGE.toString()) {
            this.breadcrumbDataset.push({ label: event.dataExchangePageName })
          }
        }
        break;
      case SCREEN_NAME_CONSTANTS.FEE_SETUP.toString():
        this.breadcrumbDataset = [{ label: SCREEN_NAME_CONSTANTS.HOME.toString(), url: '/' }, { label: `${this.clientName}`, url: '/clients' }, { label: SCREEN_NAME_CONSTANTS.PRODUCTS_LIST.toString(), url: `/product/${this.client}` }, { label: SCREEN_NAME_CONSTANTS.FILE_EXCHANGE.toString() }];
        break;

      case SCREEN_NAME_CONSTANTS.FILE_EXCHANGE.toString():
        this.selectedTab = SCREEN_NAME_CONSTANTS.FILE_EXCHANGE.toString();
        this.breadcrumbDataset = [{ label: SCREEN_NAME_CONSTANTS.HOME.toString(), url: '/' }, { label: `${this.clientName}`, url: '/clients' }, { label: SCREEN_NAME_CONSTANTS.FILE_EXCHANGE.toString(), url: `/product/${this.client}` }];
        break;

      case SCREEN_NAME_CONSTANTS.SAMPLE_VALIDATION_PERCENTAGE.toString():
        this.selectedTab = SCREEN_NAME_CONSTANTS.SAMPLE_VALIDATION_PERCENTAGE.toString();
        this.breadcrumbDataset = [{ label: SCREEN_NAME_CONSTANTS.HOME.toString(), url: '/' }, { label: `${this.clientName}`, url: '/clients' }, { label: SCREEN_NAME_CONSTANTS.SAMPLE_VALIDATION_PERCENTAGE.toString(), url: `/product/${this.client}` }];
        break;

      case SCREEN_NAME_CONSTANTS.VIEW_TENANT.toString():
        this.selectedTab = SCREEN_NAME_CONSTANTS.VIEW_TENANT.toString();
        this.breadcrumbDataset = [{ label: SCREEN_NAME_CONSTANTS.HOME.toString(), url: '/' }, { label: `${this.clientName}`, url: '/clients' }, { label: SCREEN_NAME_CONSTANTS.VIEW_TENANT.toString(), url: `/product/${this.client}` }];
        break;

      default:
        break;
    }
  }

  /**
    * AddNewRulefun Funtion
    */
  AddNewBundlesfun(): void {
    this.router.navigate([`product-catalog/sales/product/add-bundle`]);
  }

  /**
   * breadcrumSelection
   * @param event 
   */
  breadcrumSelection(event) {
    this.selectTabheader = "";
    this.tabSelected = -1;
    switch (event.selected.label) {
      case SCREEN_NAME_CONSTANTS.PRODUCTS_LIST.toString():
        setTimeout(() => {
          this.tabSelected = 0;
        }, 0);
        break;
      case SCREEN_NAME_CONSTANTS.FEE_SCHEDULE.toString():
        setTimeout(() => {
          this.tabSelected = 1;
        }, 0);
        break;
      case SCREEN_NAME_CONSTANTS.DATA_EXCHANGE.toString():
        setTimeout(() => {
          this.tabSelected = 2;
        }, 0);
        break;
      case SCREEN_NAME_CONSTANTS.FILE_EXCHANGE.toString():
        setTimeout(() => {
          this.tabSelected = 3;
        }, 0);
        break;
      default:
        if (typeof event.selected.url == 'number') {
          this.navigate = event.selected.url;
          setTimeout(() => {
            this.tabSelected = event.selected.url;
            setTimeout(() => {
              this.state.nscreen = SCREEN_NAME_CONSTANTS.PRODUCT.toString().toLocaleLowerCase();
              this.selectedProductName = this.selectedProduct['productName'];
              this.clientApiService.selectedProductName = this.selectedProductName;
              this.clientApiService.selectedProductId = this.selectedProduct['productId'];
            }, 10)
          }, 0);
        } else {
          setTimeout(() => {
            if (event.selected.url.indexOf(this.clientName) > -1) {

              this.tabSelected = 0;
              this.state.nscreen = 'client';
              this.selectedProductName = ''
              this.clientApiService.selectedProductName = ''
              this.clientApiService.selectedProductId = ''
            } else {
              this.router.navigate([`${event.selected.url}`]);
            }
          }, 0);
        }
        break;
    }
  }
  /**
   * display notification
   * @param event 
   */
  enableNotification(event) {
    this.manipulateBreadcrumb(event)
  }
  /*
   change BreadCrum  based on your tab
  */
  activePageInfoEvent(event) {
    this.manipulateBreadcrumb(event)
  }

  /**
   * Generic method for showing notification message as per params
   * @param header - string, to show notification pop-up header
   * @param body - string, to show notification pop-up body
   */
  notificationPopUpMsg(header: string, body: string) {
    if (header === NOTIFICATION_CONSTANT.SUCCESS_NOTIFICATION_HEADER.toString()) {
      this.notificationService.setSuccessNotification({
        notificationHeader: header,
        notificationBody: body,
      });
    }
    else {
      this.notificationService.setErrorNotification({
        notificationHeader: header,
        notificationBody: body,
      });
    }
  }


}
