app-file-home .page-wrapper{
    padding: 0 1rem;
}

app-file-home .template-count {
    font-weight: 600;
}
app-file-home.elevated-card {
    box-shadow: 0 5px 5px -3px #0003, 0 8px 10px 1px #00000024, 0 3px 14px 2px #0000001f;
}
app-file-home .create-btn {
    margin-top: 50px;
}
app-file-home .btn.rule-dashboard {
    /* padding: 0.075rem 0.45rem !important; */
    color: white;
    font-weight:350;
}

app-file-home .btn-onhold {
    border: 1px solid #5009B5;
    width: 100%;
    background: #EBE4FF
}

app-file-home app-file-home .slick-headerrow-columns {
    display: none;
}

app-file-home .btn.rule-dashboard {
    /* padding: 0.075rem 0.45rem !important; */
    color: white;
    font-weight:350;
}
app-file-home .btn-onhold {
    background: #EBE4FF;
    width: 100%;
    border: 1px solid #5009B5;
    
}


app-file-home .loaderPosition {
    position: absolute;
    position: absolute;
    left: 50%;
    top: 80%;
    transform: translate(-50%, -50%);
    z-index: 999;
    background-color:'#FFF';
}

app-file-home .loader {
    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid #3498db;
    width: 120px;
    height: 120px;
    -webkit-animation: spin 2s linear infinite; /* Safari */
    animation: spin 2s linear infinite;
  }
  app-file-home .spanrem{
    margin-bottom: 0.5 rem;
        font-weight: 500;
        line-height: 1.2;
        font-size: 1.25rem;
  }
  
  /* Safari */
  @-webkit-keyframes spin {
    0% { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
app-file-homeapp-file-home .rule-dashboard .three-dots:after {
    cursor: pointer;
    color: #444;
    content: "\2807";
    font-size: 20px;
    padding: 0 5px;
    z-index: 999;
}
app-file-home .rule-dashboard input:checked ~ .dropdown {
    opacity: 1;
    z-index: 100;
    transition: opacity 0.2s;
    z-index: 1;
}
app-file-home .rule-dashboard .three-dots:after {
    cursor: pointer;
    color: #444;
    content: "\2807";
    font-size: 20px;
    padding: 0 5px;
    z-index: 999;
}
app-file-home .rule-dashboard .table-action-menu .fa-eye,
app-file-home .rule-dashboard .table-action-menu .fa-edit,
app-file-home .rule-dashboard .table-action-menu .fa-trash,
app-file-home .rule-dashboard .table-action-menu .fa-plus,
app-file-home .rule-dashboard .table-action-menu .fa-check {
    font-size: 20px;
    color: #5009B5;
    padding-right: 15px;
}
app-file-home .rule-dashboard .table-action-menu {
    border: 8.5px solid #ffffff;
}
app-file-home .rule-dashboard .dropdown a {
    color: black;
    padding: 5px 10px;
    text-decoration: none;
    display: block;
}
app-file-home .rule-dashboard .dropdown {
    position: absolute;
    background-color: gray;
    padding: 5px;
    outline: none;
    opacity: 0;
    min-width: 160px;
    overflow: auto;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    background: #ffffff;
    box-shadow: 0px 0px 20px rgb(0 0 0 / 40%);
    border-radius: 4px;
}

app-file-home .pad2
{
    padding-left: 2px;
}

app-file-home .card-headerfile {
    padding: 19px 0.25rem;
    margin-bottom: 0;
    background-color: rgba(0,0,0,.03);
    border-bottom: 1px solid rgba(0,0,0,.125);
}
app-file-home .float
{
    float:right;
}
app-file-home marketplace-breadcrumb span.breadcrumb__holder .current-page {
    font-size: 14px;
}

app-file-home .templateList{
    display: flex;
}

app-file-home .templateListTbl{
    width: 130%;
}

app-file-home .templateListCreateBtn{
    width: 30%;
    margin-top: 50px;
    text-align: center;
}

app-file-home .elemToCenter{
    text-align: center;
}

app-file-home .filesBreadcrumb .breadcrumb__holder{
    margin-left: 0;
}