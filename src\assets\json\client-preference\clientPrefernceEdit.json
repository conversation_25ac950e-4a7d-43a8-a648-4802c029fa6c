{"requestType": "add/edit", "preferenceId": 43, "system": "anthem", "clientId": 101, "clientName": "client_9", "activeInd": 1, "productName": "products_9", "preferenceName": "preference_43", "dbgUnit": "<PERSON><PERSON>", "inventoryType": "Program", "templateName": "Universal", "fileDestination": "sampleText", "frequency": "sampleText", "startDate": "2022-03-28", "endDate": "2022-03-29", "time": "30mins", "conditions": {"condition": "and", "rules": [{"field": "lob", "operator": "Equal", "value": "anthem", "stat": false, "active": true}, {"field": "lob", "operator": "Equal", "value": "12", "stat": false, "active": true}]}}