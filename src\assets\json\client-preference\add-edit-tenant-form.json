[{"type": "number", "name": "tenantId", "placeholder": "Tenant Id", "label": "Tenant Id", "id": "tenantId", "visible": true, "disabled": true}, {"type": "text", "name": "tenantName", "placeholder": "Please enter the tenant name", "label": "Tenant Name", "id": "tenantName", "disabled": false, "maxLength": 100, "required": true, "rows": 5}, {"label": "Tenant Code", "type": "text", "name": "tenantCode", "disabled": false, "hidden": false, "id": "tenantCode", "placeholder": "Please enter tenant code", "maxLength": 50, "required": true}, {"label": "Offshore Accessible Logic", "type": "text", "name": "offshoreAccLogic", "disabled": false, "hidden": false, "id": "offshoreAccLogic", "placeholder": "Please enter offshore accesible logic"}, {"label": "Security Level Code", "type": "text", "name": "SecuLvlCd", "disabled": false, "hidden": false, "id": "SecuLvlCd", "placeholder": "Please enter security level code", "maxLength": 10}, {"options": [{"name": "True", "value": "Y"}, {"name": "False", "value": "N"}], "optionName": "name", "optionValue": "value", "label": "Offshore Accessible", "type": "radio", "name": "offshoreAccess", "id": "offshoreAccess", "disabled": false, "selectedVal": true}, {"options": [{"name": "Active", "value": true}, {"name": "In Active", "value": false}], "optionName": "name", "optionValue": "value", "label": "Active Status", "type": "radio", "name": "active", "id": "active", "disabled": false, "selectedVal": true}, {"type": "textarea", "name": "tenantDesc", "placeholder": "Please enter the tenant Description", "label": "Tenant Description", "id": "tenantDesc", "disabled": false, "rows": 5, "maxLength": 250}]