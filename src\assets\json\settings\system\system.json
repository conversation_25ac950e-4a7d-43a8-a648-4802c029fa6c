[{"id": "1", "systemCode": "Pareo", "systemName": "Claris Health Pario", "inventoryType": "Appeals", "createdDate": "03/25/2022", "createdBy": "<PERSON><PERSON>", "lastModifiedDate": "03/26/2022", "lastModifiedBy": "<PERSON><PERSON>", "status": "Active", "noOfClients": 7, "finish": "Action"}, {"id": "2", "systemCode": "ACR", "systemName": "<PERSON><PERSON>", "inventoryType": "Provider <PERSON>", "createdDate": "03/25/2022", "createdBy": "<PERSON><PERSON>", "lastModifiedDate": "03/26/2022", "lastModifiedBy": "<PERSON><PERSON>", "status": "Active", "noOfClients": 10, "finish": "Action"}, {"id": "3", "systemCode": "EPA", "systemName": "Enterprise Provider Audit", "inventoryType": "Recovery", "createdDate": "03/25/2022", "createdBy": "<PERSON><PERSON>", "lastModifiedDate": "03/26/2022", "lastModifiedBy": "<PERSON><PERSON>", "status": "Inactive", "noOfClients": 5, "finish": "Action"}, {"id": "4", "systemCode": "CCERT", "systemName": "Cost Containment Enterprise Recovery Tool", "inventoryType": "Provider <PERSON>", "createdDate": "03/25/2022", "createdBy": "<PERSON><PERSON>", "lastModifiedDate": "03/26/2022", "lastModifiedBy": "<PERSON><PERSON>", "status": "Active", "noOfClients": 12, "finish": "Action"}]