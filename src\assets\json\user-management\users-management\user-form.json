[{"type": "text", "name": "userId", "label": "User ID", "column": 2, "closeOnSelect": true, "multiple": false, "customTags": true, "id": "userId", "placeholder": "Please Enter User ID", "required": true}, {"type": "text", "name": "userName", "label": "User Name", "column": 2, "closeOnSelect": true, "multiple": false, "customTags": true, "id": "userName", "placeholder": "Enter User Name", "required": true}, {"options": [], "optionName": "value", "optionValue": "id", "label": "User Experience Level", "type": "select", "multiple": false, "closeOnSelect": true, "name": "experienceLevelId", "column": "2", "disabled": false, "hidden": false, "id": "experienceLevelId", "placeholder": "Select Experience Level"}, {"type": "text", "name": "<PERSON><PERSON><PERSON>", "label": "Assigned Manager ID", "column": 2, "customTags": true, "id": "<PERSON><PERSON><PERSON>", "maxLength": 50, "placeholder": "Please Enter Assigned Manager ID"}, {"id": "reminderDate", "label": "Reminder Date", "type": "date", "name": "reminderDate", "column": "2", "disabled": false, "placeholder": "Please select Reminder Date", "pickerType": "single", "dateFormat": "MM-DD-YYYY"}, {"type": "textarea", "name": "comments", "label": "Comments", "column": 2, "id": "comments", "disabled": false, "placeholder": "Enter comments"}, {"label": "User Type", "text": "Internal", "type": "switch", "name": "internalFlag", "column": "2", "preLabel": "External", "disabled": false, "value": true}, {"label": "Client Site", "text": "Offshore", "type": "switch", "name": "clientSite", "column": "2", "preLabel": "Onshore", "disabled": false, "value": false}, {"options": [{"name": "Active", "value": "Active"}, {"name": "Inactive", "value": "Inactive"}], "optionName": "name", "optionValue": "value", "label": "User Status", "type": "radio", "name": "status", "id": "status", "column": "1", "disabled": false, "customClass": "form-radio-button"}, {"optionName": "name", "optionValue": "value", "label": "Deactivation Status", "type": "radio", "name": "deactivationStatus", "id": "deactivationStatus", "column": "2", "value": "Temporary", "visible": false, "options": [{"name": "Permanent", "value": "Permanent"}, {"name": "Temporary", "value": "Temporary"}], "customClass": "form-radio-button"}, {"type": "textarea", "name": "deactivationReason", "label": "Deactivation Reason", "column": "2", "id": "deactivationReason", "visible": false, "placeholder": "Enter Reason"}]