import { Component, OnInit, ViewEncapsulation, ChangeDetectorRef } from '@angular/core';
import { FILE_STATUS_COLUMNS, TEMPLATE_COLUMNS } from '../file.constants';
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';
import * as XLSX from 'xlsx'; 
import { FileService } from '../services/file.service';
import {get} from 'lodash';
import { AuthService } from 'src/app/_services/authentication.services';




@Component({
  selector: 'app-file-home',
  templateUrl: './file-home.component.html',
  styleUrls: ['./file-home.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class FileHomeComponent implements OnInit {
  data: any;
  src: any;
  constructor(private router: Router,private fileService:FileService, private cd: ChangeDetectorRef,private authService: AuthService, private route: ActivatedRoute) { }
  enableImportButton = false;
  templates: any = [];
  files: any = [];
  templateDataJSON:any=[];
  fileStatusDataJSON:any=[];
  fileDataAll:any=[];
  templateDateScreen:any;
  templateSelected:any;
   idData:any ={} ;
   showLoader:boolean = false;
   selectedFile:any;
   filesData:any[];
   isFileSelect:boolean=false;
   templateColumnConfigFilters: any;
   fileStatusColumnConfigFilters: any = FILE_STATUS_COLUMNS;
   showData:boolean = false;
   notificationHeader: string = "";
  notificationBody: string = "Template Deleted Successfully";
  notificationPosition: any = 'top-right';
  notificationDuration: number = 3000;
  notificationType: any = 'success';
  notificationOpen: string = "false";
  tableRecreate: any;
  confirmPopup:any;
  templateId:any;
  kebabOptions: any = [{ label: '<i  class="fa fa-eye"></i> View', id: 'View' },{ label: '<i  class="fa fa-edit"></i> Edit', id: 'Edit' },{ label: '<i  class="fa fa-trash"></i> Delete', id: 'Delete' }]
  kebabOptions_Readonly: any = [{ label: '<i  class="fa fa-eye"></i> View', id: 'View' }]

   public schedulerBreadCrumbData: any = [{ label: 'Home', url: '/' },{ label: 'Files', url: '/settings/files' }];

  isReadOnly: boolean= false;
  columnDefinitions: any = {

    "switches": {
      "enableSorting": true,
      "enableFiltering": true,
    },

    "colDefs": [
      {
        "name": "Template Name",
        "field": "name",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Long Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },

      {
        "name": "Created By",
        "field": "createdBy",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Long Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "Date",
        "field": "date",
        "filterType": "Calendar",
        "visible": "True",
        "editorType": "Long Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": "",
        "width": 110,
        "dateFormat": "MM/DD/YYYY"
      },
      {
        "name": "Status",
        "field": "status",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
      }

    ]

  }
  ngOnInit(): void {
    this.isReadOnly = !this.authService.isWriteOnly;
    this.isReadOnly ? this.kebabOptions = this.kebabOptions_Readonly : this.kebabOptions
    this.showData = false;
    this.templateColumnConfigFilters = this.columnDefinitions;
    this.getAllTemplatesApi()
      
  }

/**
 * API call to fetch all Templates 
 */
  getAllTemplatesApi(){
    this.fileService.getAllTemplates().subscribe((data:any)=>{
      if(data.length)
      {
        this.templateDataJSON=[]
      data.forEach(element => {
        element.createdDate = new Date(element?.createdDate) 
        const elem ={
          "id": get(element, 'fileTmplId'),
          "name": get(element, 'fileTmplName'),
          "createdBy": get(element, 'createdUserId'),
          "date": get(element, 'createdDate'),
          "status": get(element, 'actvInd') ? "Active":"InActive",
          "published":true

        };
        this.templateDataJSON.push(elem);
      });

      this.templateDataJSON.sort((a, b) => a.name.localeCompare(b.name));
      
      this.tableRecreate = Date.now();
      setTimeout(()=>this.showData = true,0);
    }
   
    },(err)=>{
      this.showData = true;
      });
  }

  /*
  Radio Button to each Row
  */
  _createRadio(event: any) {
    
    return "<input type='radio' class='radioselect' name='row-select' id='"+event.dataContext.id+"'>";

  }

/*
 read File selection based of row
  */
  onFileSelected(ev) {
        this.showLoader= true;
    let workBook = null;
    let jsonData = null;
    const reader = new FileReader();
    const file = ev.target.files[0];
    reader.onload = (event) => {
      const data = reader.result;

      workBook = XLSX.read(data, { type: 'binary' });
     
      jsonData = workBook.SheetNames.reduce((initial, name) => {
        const sheet = workBook.Sheets[name];
        initial[name] = XLSX.utils.sheet_to_json(sheet);
        return initial;
      }, {});
     
    
      const dataString = JSON.stringify(jsonData);
      localStorage.setItem('data', dataString);
      localStorage.setItem('templateSelected',JSON.stringify(this.templateSelected));
     
       setTimeout(()=>{
        this.showLoader= false;
        this.router.navigate(['/settings/files/importTemplate']);
      },2000);
    }
    reader.readAsBinaryString(file);
  }
/*
  Actions-Edit and Delete of each template
  */
  templateTableClicked(event) {
    if (event['eventData'].target.attributes) {
        this.enableImportButton = true;
        if (event['eventData'].target.attributes.dataaction && event['eventData'].target.attributes.datevalue) {
            let templateId = event['eventData'].target.attributes.datevalue.nodeValue;
            let action = event['eventData'].target.attributes.dataaction.nodeValue;
            switch (action) {
                case 'add':
                  this.router.navigate(['addEditTemplate'], { relativeTo: this.route });
                    break;
                case 'edit':
                    this.router.navigate(['addEditTemplate', templateId], { relativeTo: this.route });
                    break;
                case 'delete':
                    this.fileService.getSingleTemplateDelete(templateId);
                    break;
            }
        }
        if (event['eventData'].target.classList.contains('radioselect')) {
            this.templateSelected = Number(event['eventData'].target.id)
            this.enableImportButton = true;
        }
    }
}


  /*
  For UnPublished row,show unpublished button
  */
  customFormatter(){
    return "<button type='button' class='btn btn rule-dashboard btn-onhold'>UnPublished</button>"
  }

  
/*
On Upload file ,to check the format of file based on template
*/
  onFileClick(event:any)
  {
       this.fileDetails(event.target.value);
       this.isFileSelect=true;
  }
  /*
After uploading the file, filter Based on File Information on the behalf of selected template
*/
  fileDetails(fileid:any)
  {
      this.fileStatusDataJSON=this.fileDataAll;
      this.fileStatusDataJSON=this.fileStatusDataJSON.filter(x=>x.fileid==fileid);
      this.selectedFile=this.fileStatusDataJSON.filter(x=>x.fileid==fileid)[0].name+"."+ this.filesData.filter(x=>x.id==fileid)[0].extn;
  }

    /**
   * Method Invoked when popup is closed in Mapping Tab's Table's field Option is Selected as Constant Value 
   * @param event 
   */
  closePopup(event: any) {
    this.confirmPopup = false;
  }

/**
 * Method invoked when yes is clicked in popup while deleting the file template
 * @param event 
 */
  confimDelete(event:any) {
    this.confirmPopup = false;
    this.fileService.getSingleTemplateDelete(this.templateId).subscribe(data=>{
      this.getAllTemplatesApi()
      this.notificationBody = "Template Deleted Successfully";
      this.notificationDuration = 3000;
      this.notificationType = 'success';
      this.notificationOpen = "true";
    })
  }


  /*-- Template Screen Table Kebab options Navigate --*/
onDropdownOptionsClick(event) {
  let rulesAction = event.text;
  this.templateId = event.currentRow.id
  switch (rulesAction) {
    case 'View':
      this.router.navigate([`/settings/files/addEditTemplate/view/${this.templateId}`]);
      break;
    case 'Edit':
      this.router.navigate([`/settings/files/addEditTemplate/${this.templateId}`]);
      break;
    case 'Delete':
      this.notificationOpen = "false";

      this.fileService.checkBeforeDeletingFileTemplate(this.templateId).subscribe(data=>{
        if(data.templateInUse){
          this.notificationBody = "Cannot Delete Template, As it is associated with existing client preferences";
          this.notificationDuration = 10000;
          this.notificationType = 'warning';
          this.notificationOpen = "true";
        } else {
          this.confirmPopup = Date.now();
        }
      })

      
      break;
  }
}

createBtnClicked = () => {
  if (this.isReadOnly) return;
  this.router.navigate(['addEditTemplate'], { relativeTo: this.route });
}

}
