{"switches": {"enableSorting": false, "enablePagination": false, "enableFiltering": false}, "colDefs": [{"name": "File Name", "width": 170, "field": "fileName", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "Date & Time", "field": "sentDateTime", "width": 130, "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "sortable": false}, {"name": "<PERSON><PERSON>", "field": "sentBy", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "sortable": false}, {"name": "Status", "field": "status", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "sortable": false, "width": 40}, {"name": "", "field": "checkStatus", "width": 120, "filterType": "Multi Select", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}]}