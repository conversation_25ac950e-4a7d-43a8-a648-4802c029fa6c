import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { UserManagementApiService } from '../app/users/_services/user-management-api.service';

@Injectable({ providedIn: 'root' })
export class AppService {

    constructor(public rolesListService: UserManagementApiService) { }
    UsersScreenAccessList: any = [];
    listOfAccessibleScreens: any;
    permissionList: any;


    pushSubscriptionToPanel = new BehaviorSubject<any>('');
    pushSubscriptionPanel = this.pushSubscriptionToPanel.asObservable();

    public setSubscriptionToPanel(data): any {
        this.pushSubscriptionToPanel.next(data);
    }

    public getSubscriptionToPanel() {
        return this.pushSubscriptionToPanel;
    }

    pushSubscriptionToLandingScreen = new BehaviorSubject<any>('');
    pushSubscription = this.pushSubscriptionToLandingScreen.asObservable();

    public setSubscriptionToLandingScreen(data): any {
        this.pushSubscriptionToLandingScreen.next(data);
    }

    public getSubscriptionToLandingScreen() {
        return this.pushSubscriptionToLandingScreen;
    }

    navigateTo = new BehaviorSubject<any>('');

    public setNavigateTo(data): any {
        this.navigateTo.next(data);
    }

    public getNavigateTo() {
        return this.navigateTo;
    }

    /**
   * Method to get screen access permission list
   */
    getScreenAccessPermissionList(screenAccessListFromApi, screenListFromNavigationJson) {
        this.UsersScreenAccessList = [];
        let permissions = {}
        screenAccessListFromApi.forEach((screenListData) => {
            this.UsersScreenAccessList.push(screenListData);
        });
        this.UsersScreenAccessList = this.UsersScreenAccessList.filter((item, index) => this.UsersScreenAccessList.indexOf(item) == index);
        Object.keys(screenListFromNavigationJson).forEach(element => {
            let _foundElement = this.UsersScreenAccessList.find(x => x.label?.trim() == screenListFromNavigationJson[element]?.name?.trim());
            if (_foundElement) {
                let screen = screenListFromNavigationJson.find(element => element.name == _foundElement.label);
                _foundElement["value"] = screen?.value;
            }
            if (_foundElement?.subMenu && screenListFromNavigationJson[element]?.children) {
                let elemArr = [];
                screenListFromNavigationJson[element].children.forEach((subElement: any, index: number) => {
                    let _childElement = _foundElement.subMenu.find(x => x.label?.trim() == subElement.name?.trim());
                    if (_foundElement.subMenu.length == 1 && subElement.name == null) {
                        elemArr.push({ "name": _foundElement.label, "endpoint": subElement.endpoint })
                        permissions[_foundElement.label] = { "permission": _foundElement.subMenu[0].permission, "url": screenListFromNavigationJson[element].children[0].endpoint }
                    }
                    else if (_childElement) {
                        elemArr.push(screenListFromNavigationJson[element].children[index])
                        permissions[_childElement.label] = { "permission": _childElement.permission, "url": screenListFromNavigationJson[element].children[index].endpoint }
                    }
                });
                screenListFromNavigationJson[element].children = JSON.parse(JSON.stringify(elemArr));
            }
            else if (!_foundElement)
                screenAccessListFromApi = screenAccessListFromApi.filter((menuItem) => menuItem.label?.trim() !== screenListFromNavigationJson[element]?.name?.trim());
        })
        this.listOfAccessibleScreens = screenAccessListFromApi;
        this.permissionList = permissions;
        return [this.listOfAccessibleScreens, this.permissionList]
    }
}