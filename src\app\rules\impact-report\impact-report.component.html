
<div class="form-container">
    <div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <a class="brdcrumb" (click)="breadCrumbClick()">
        <span class="brdcrumbSymbol">{{arrowSymbol}}</span>Back to Rules Engine
    </a>
    <br>
    <div class="page-header">
        <h3>Rule {{ruleId}} Impact Report</h3>
    </div>
    <div class="card-container">
        <div class="dynamicForm">
            <marketplace-dynamic-form #dyForm [isSubmitNeeded]="false" [formJSON]="impactReportDrpdwnJSON"
                (onValueChanges)="onDropdownValueChange($event)">
            </marketplace-dynamic-form>
        </div>
        <div class="performAnalysisBtn">
            <marketplace-button [label]="'Perform Analysis'" [type]="'primary'" [name]="'primary'"
                (onclick)="performAnalysisBtnClick()" [enabled]="enablePerformAnalysisBtn">
            </marketplace-button>
        </div>
    </div>
    <br>
    <div class="impact-report-card">
        <marketplace-cards *ngIf="isImpactReportReady" id="asTile" [type]="'asTile'"
            [dataset]="impactReportCardDataSet">
        </marketplace-cards>
    </div>
    <div class="card-container">
        <marketplace-table [id]="'impact-report-table'" [dataset]="impactReportList" [columnDefinitions]="columnConfig"
            [isXScrollNeeded]="true" [isToggleColumnsNeeded]="false">
        </marketplace-table>
    </div>
</div>