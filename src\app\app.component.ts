import { Component, inject, OnInit, ViewEncapsulation } from '@angular/core';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import { MPUIFooterModule } from 'marketplace-footer';
import { MPUIHeaderModule } from 'marketplace-header';
import { MPUIQuickNavsModule } from 'marketplace-quick-navs';
import { MPUISlidePanelModule } from 'marketplace-slide-panel';
import { MPUITargetCardsModule } from "marketplace-target-cards";
import { AuthService } from './_services/authentication.services';
import { ILandingScreen, ILandingScreenCards, ILandingScreenQuickLinks } from './_models/landing-screen.model';
import { CookieService } from 'ngx-cookie-service';
import { APP_CONSTANTS, QUICK_LINKS } from './_constants/app.constants';
import { IFooterDataset } from './_models/_components/footer-model';
import { interval, Subscription, filter } from 'rxjs';
import { environment } from '../environments/environment';
import { LoaderService } from './_services/loader.service';
import { LoaderComponent } from './loader/loader.component';
import { CommonModule } from '@angular/common';
import { BnNgIdleService } from 'bn-ng-idle';
import { HttpClientModule } from '@angular/common/http';
import { AppService } from './app.service';
import { ToastService } from './_services/toast.service';
import { MPUINotificationModule } from 'marketplace-notification';
import { CONSTANTS, NAVIGATION } from './_constants/menu.constant';


@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, CommonModule, LoaderComponent, HttpClientModule, MPUIHeaderModule, MPUIFooterModule, MPUISlidePanelModule, MPUIQuickNavsModule, MPUITargetCardsModule, MPUINotificationModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.sass',
  providers: [LoaderService],
  encapsulation: ViewEncapsulation.None
})
export class AppComponent implements OnInit {
  title = 'pi-pf-portal-ui';
  appsSub: Subscription
  dataset: any = {};
  /**
   * Header Configurations
   */
  appLogo: string = APP_CONSTANTS.APP_LOGO;
  appName: string = APP_CONSTANTS.APP_TITLE;
  userName: string;
  isNotificationNeeded: boolean = true;

  isOverlayVisible: boolean = false;

  /**Footer */
  footerDataset: IFooterDataset[] = [
    { label: '&copy; 2024 Carelon. All Rights Reserved.' },
    { label: 'Privacy Policy', link: 'https://pulse.elevancehealth.com/' },
    { label: 'Terms Of Use', link: 'https://pulse.elevancehealth.com/' }
  ];

  /**
   * Interval
   */
  interval$: Subscription;
  private routerSubscription?: Subscription;
  /**
   * Landing Page
   */
  listOfAccessibleCards: ILandingScreenCards[];
  listOfAccessibleQuickLinks: ILandingScreenQuickLinks[];
  skipSignOn: boolean = false;

  authService = inject(AuthService);
  router = inject(Router);
  loaderService = inject(LoaderService);
  bnIdleService = inject(BnNgIdleService);
  cookieService = inject(CookieService);
  alertService = inject(ToastService);
  foundItem: any;
  selectedMenu: any;
  urlBackup: any;

  constructor(private appSrvc: AppService,) {
    this.routerSubscription = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      // Access the current route
      const currentRoute = event.url;
      if (currentRoute === '/') {
        this.dataset = { menuData: [] }
      }
    });
  }

  ngOnInit() {
    console.log("Environment", environment.name);
    if (!this.isLogoutUrl() && !this.skipSignOn) {
      this.authService.singleSignOn();
    }
    this.startIdleWatcher();
    this.startTokenRefresh();
    this.appSrvc.getNavigateTo().subscribe((data) => {
      if (data)
        this.menuSelectionSession();
    });

    this.constructDataSet();
  }


  isLogoutUrl(): boolean {
    return window.location.href.includes('logout');
  }

  handleSessionTimeout() {
    this.authService.logoutPortal().subscribe({
      next: () => {
        this.authService.isTimedout = true;
        this.clearSession()
      },
      error: () => this.clearSession(),
    });
  }

  startIdleWatcher() {
    this.bnIdleService.startWatching(environment.idleSeconds).subscribe((isTimedOut: boolean) => {
      if (isTimedOut) {
        this.handleSessionTimeout();
      }
    });
  }

  startTokenRefresh() {
    this.interval$ = interval(environment.refreshInterval).subscribe(() => {
      const userToken = this.cookieService.get('userToken');
      const appToken = sessionStorage.getItem('appToken');

      if (userToken && appToken && !environment.enableOkta) {
        this.authService.refreshToken().subscribe((tokenDetails) => {
          sessionStorage.setItem('appToken', tokenDetails['appToken']);
        });
      }
    });
  }


  readSessionVariables(panelObject: any) {
    if (panelObject) {
      this.dataset = panelObject.menuData;
      this.isNotificationNeeded = panelObject.isNotificationNeeded
    }
  }
  /**
   * Clear Session
   */
  clearSession() {
    this.cookieService.deleteAll('/');
    sessionStorage.clear();
    this.router.navigate(['logout']);
  }


  /**
  * on click of overlay
  */
  _onOverlayClick(): void {
    if (!this.listOfAccessibleCards) {
      const landingScreenDetails: ILandingScreen = JSON.parse(this.cookieService.get(APP_CONSTANTS.LANDING_SCREEN_DETAILS));
      this.listOfAccessibleCards = landingScreenDetails.cards;
      this.listOfAccessibleQuickLinks = landingScreenDetails.quickLinks;
    }

    this.isOverlayVisible = !this.isOverlayVisible;
  }

  /**
  * Logout Endpoint
  * @param event 
  */
  _onLogoutClick() {
    this.authService.logoutPortal().subscribe({
      next: () => {
        this.authService.isLogin = false;
        this.clearSession()
      },
      error: () => this.clearSession(),
    });
  }

  /**
 * This method is to construct the flow (landing user to same page) of already selected Screen/QuickLink/Menu on screen refresh.
 */
  constructDataSet() {
    //let sesToken = JSON.stringify(sessionStorage.getItem('appToken'));
    let sesAppToken = sessionStorage.getItem('appToken');
    if (sesAppToken) {
      let navigateTo = JSON.parse(sessionStorage.getItem(CONSTANTS.NAVIGATETO));
      let isHeaderMenu: Boolean = false;
      let _menuData: any;
      let _quickLinkData: any;
      let label: string = navigateTo.selection;

      const getScreenAccessLinks = JSON.parse(this.cookieService.get(APP_CONSTANTS.LANDING_SCREEN_DETAILS))

      if (!_menuData)
        _quickLinkData = getScreenAccessLinks.find(c => c.label == navigateTo.selection);


      if (!_menuData && !_quickLinkData) {
        _quickLinkData = getScreenAccessLinks.find(c => c.subMenu.find(d => d.label == navigateTo.selection));
        isHeaderMenu = _quickLinkData != undefined;
      }

      if (_menuData) {
        if (isHeaderMenu) {
          let object = { "selection": label };
          this.dataset = { menuData: JSON.parse(JSON.stringify(_menuData?.subMenu)) };
          this.onMenuSelection(object)
        }
        else {
          let object = { "selected": { "label": _menuData.label, "subMenu": _menuData?.subMenu } };
          this.onScreenSelection(object);
        }
      }
      else if (_quickLinkData) {
        if (isHeaderMenu) {
          let object = { "selection": label };
          this.dataset = { menuData: JSON.parse(JSON.stringify(_quickLinkData?.subMenu)) };
          this.onMenuSelection(object)
        }
        else {
          let object = { "selected": { "label": label, "subMenu": _quickLinkData?.subMenu } };
          this.quickLinkClick(object);
        }
      }
    }
  }


  /**
    * Method invoked on screen selection
    * @param event 
    */
  onScreenSelection(event) {
    const url = event.selected.url || `https://ui.portal-card.pi.${environment.name}.gcpdns.internal.das`;
    window.open(url);
  }

  menuSelectionSession() {
    let navigateTo = JSON.parse(sessionStorage.getItem(CONSTANTS.NAVIGATETO));
    this.onMenuSelection(navigateTo);
    this.appName = navigateTo.selection || CONSTANTS.APP_NAME;
  }

  /**
  * Method invoked on quick link click
  * @param event 
  */
  quickLinkClick(event) {
    const linkBase = environment.commonServicesUrl;
    const getScreenAccessLinks = JSON.parse(this.cookieService.get(APP_CONSTANTS.LANDING_SCREEN_DETAILS))
    let _menuData = getScreenAccessLinks.quickLinks.find(
      (x) => x.label == event.selected.label
    );
    this.dataset = { menuData: JSON.parse(JSON.stringify(_menuData?.subMenu)) };
    let selection = {
      "selection": event.selected.label,
      "path": true
    }
    sessionStorage.setItem(CONSTANTS.NAVIGATETO, JSON.stringify(selection));
    this.isOverlayVisible = false;
    this.onMenuSelection({ selection: event.selected.label });
    this.appName = event.selected.label || CONSTANTS.APP_NAME;
    const quickLinkPaths = {
      [QUICK_LINKS.RULES]: `${linkBase}rules`,
      [QUICK_LINKS.EXTERNAL_USER_REGISTRATION]: `${linkBase}registration`,
      [QUICK_LINKS.HELP_CENTER]: `${linkBase}help-center`,
      [QUICK_LINKS.SETTINGS]: `${linkBase}settings`,
      [QUICK_LINKS.CLIENTS]: `${linkBase}clients`,
      [QUICK_LINKS.USERS]: `${linkBase}users`,
    };

    switch (event.selected.label) {
      case QUICK_LINKS.USERS:
        this.router.navigate(["users"]);
        break;
      case QUICK_LINKS.EXTERNAL_USER_REGISTRATION:
        this.router.navigate(["signin/register"]);
        break;
      case QUICK_LINKS.RULES:
        this.router.navigate(["rules"]);
        break;
      case QUICK_LINKS.CLIENTS:
        this.router.navigate(["clients"]);
        break;
      case QUICK_LINKS.SETTINGS:
        this.router.navigate(["settings/product"]);
        break;
      default:
        window.open(quickLinkPaths[event.selected.label] || linkBase, "_self");
        break;
    }
  }




  onMenuSelection(event: any) {
    // Save selection in sessionStorage
    let selection = {
      "selection": event.selection,
      "path": false
    };
    sessionStorage.setItem(CONSTANTS.NAVIGATETO, JSON.stringify(selection));
    let _found = this.dataset.menuData.find((e) => e.label == event.selection);
    _found ? (_found['active'] = true) : '';
    if (_found == undefined) {
      if (this.dataset.menuData[0] != undefined && this.dataset.menuData.length == 1 && this.dataset.menuData[0].label == null) {
        this.foundItem = { active: false, label: event.selection }
      }
      else if (this.dataset.menuData[0] != undefined) {
        this.foundItem = this.dataset.menuData[0];
      }
    }
    this.foundItem ? (this.foundItem['active'] = true) : '';
    this.selectedMenu = _found ? event.selection : this.foundItem.label;



    // Route to the selected menu's path if available
    let selectedUrl = this.getNavigationUrlForSelectedScreen(this.selectedMenu);
    if (selectedUrl) {
      this.router.navigate([selectedUrl]);
      this.urlBackup = selectedUrl;
    }
  }

  /**
* Method to get url for selected screen
*/
  getNavigationUrlForSelectedScreen(selectedMenu) {
    return NAVIGATION[selectedMenu];
  }

  /**
   * Home Click
   */
  _onHomeClick(){
    this.router.navigate([''])
  }

  /**
   * Ng Destroy
   */
  ngOnDestroy(): void {
    this.bnIdleService.stopTimer();
    this.interval$.unsubscribe();
  }
}