import { Component, OnInit, ViewEncapsulation, AfterViewInit, Input, EventEmitter, Output } from '@angular/core';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { Router, ActivatedRoute } from '@angular/router';
import moment from 'moment';

@Component({
  selector: 'app-acknowledgement',
  templateUrl: './acknowledgement.component.html',
  styleUrls: ['./acknowledgement.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class AcknowledgementComponent implements OnInit, AfterViewInit {

  public filehistoryData: any;
  public totalEntries: number;
  public filehistoryColConfig: any;
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  public tableRedraw: any;
  selectedMonth: any
  public selectedRowData: any;
  @Input() clientSelected: string;
  @Input() isReadOnly: boolean = false;
  @Output() activePageInfoEvent = new EventEmitter();
  public conceptIdOptions: any = [];
  public executionIdOptions: any = [];
  conceptIdDropdownSelectedValue: any = 1;
  executionIdDropdownSelectedValue: any = 1;
  currantYear = new Date().getFullYear();
  isCurrentYear: boolean = true;
  conceptececutionid: any = []
  clientId: any;
  clientName: string;
  executionID: any;
  totalInsightsSent: any;
  totalFilesSent: any;
  rejectedFileReportsTblColumnConfigURL: string = "./assets/json/client-preference/client-file-history-rejected-config.json"
  recentfilelistcolumnconfigurl: string = "./assets/json/client-preference/client-preferance-recentfilelist.json"
  rejectedFileReportsTblColumnConfig: any
  fileReportTblDataset: any
  fileDetailsTableRedraw: any
  tblFlag: boolean = false
  showFileRptDetails: boolean = false
  selectedFileReportDetails: any
  tableRecreate: any;
  isScrollNeeded: boolean = true;
  openHTMLModal: any;
  recentFileListBool: boolean = true
  panelIndex: string = "0";
  conceptAndExecutionFromJsonConfig: any
  openAll: boolean = false
  insightDetails: any
  insightYearDropdownLabel: string = "Year Selected";
  drpdwnName: string = "simple_drp";
  drptype: string = "single";
  dropdownSelectedValue: any = 1
  insightYearDataset: any = []
  redrawInsightChart: any
  monthGroupdata: any;
  insightDetailsData: any
  isYearWise: boolean = false;
  insightSelectedYear: any;
  insightErrorMsg: string = "No Execution ID Selected"
  insightErrMsgBool: boolean = true
  insightNoDataErrMsg = "Year Not Selected"
  yearNtSelected: boolean = false
  showChart: boolean = false;
  currentYear: any;
  exeId: any
  numOfYrsReq: number = 5
  errMsgBold = false;
  showOnlyChart: boolean = false
  selectedConceptId: any
  selectedExecutionId: any
  barChartDataset: any = {
    xAxis: [],
    plotData: [{
      name: 'Accepted',
      color: '#1AB2A8',
      data: []
    }, {
      name: 'Rejected',
      color: '#E20000',
      data: []
    },
    {
      name: 'Missing',
      color: '#FFA726',
      data: []
    }]
  };

  sgTimeDataset: any = [
    { id: 'Day', label: 'Day' },
    { id: 'Week', label: 'Week' },
    { id: 'Month', label: 'Month' },
    { id: 'Year', label: 'Year', checked: true }
  ];
  public timeLineDataset: any = [{
    title: 'No File Selected',
    content: 'No File Selected',
    complete: true,
  }];

  chartType: string = "column";
  properties: any = {
    "exporting": {
      "enable": true,
      "fileName": "Custom Name"
    },
    "tooltip": {
      "enable": true,
      "shared": true
    },
    "view": "",
    "showLegend": true,
    "stacked": {
      "enable": true,
      "groupLabel": true,
      "individualLabel": false
    },
    "title": "",
    "YAxisTitle": "Insights Count",
    "XAxisTitle": "Date Range",
    legendProperties: { align: 'center', layout: "horizontal", verticalAlign: 'bottom' }
  };


  conceptId: any;
  filename: any;
  sentDateTime: any;
  filedate: any;
  clientNameFromCookie: any;
  renderAcknowledgeTable: boolean = false;

  constructor(private clientApiService: ClientApiService, private router: Router, private route: ActivatedRoute) { }

  ngOnInit(): void {
    let client = this.clientSelected;
    this.clientName = this.route.snapshot.paramMap.get('clientName');
    this.openHTMLModal = false;
    this.renderAcknowledgeTable = true;
    this.clientApiService.getTableColumn(this.rejectedFileReportsTblColumnConfigURL).subscribe((data) => {
      this.rejectedFileReportsTblColumnConfig = data['config'];
      this.fileReportTblDataset = data['dataset'];
      setTimeout(() => {
        this.fileDetailsTableRedraw = Date.now()
      }, 100);
    });

    this.clientApiService.getTableColumn(this.recentfilelistcolumnconfigurl).subscribe((data) => {
      data.colDefs.forEach(e => {
        e.field == "fileName" ? e.customFormatter = this._createFileName : "";
        e.field == "checkStatus" ? e.customFormatter = this._checkInventory : "";
      });
      this.filehistoryColConfig = data;
    });


    this.dynamicFromJsonConfig()

    setTimeout(() => this.tableRedraw = Date.now(), 2000);
    let fetchCookie: any = document.cookie?.split(";");

    fetchCookie?.map(e => {
      if (e?.includes("clientName") && !e?.includes("client_list")) {
        this.clientNameFromCookie = e?.trim()?.slice(11);
      }
    })
    this.clientApiService.getConceptProdExecutionIds(client).subscribe(data => {
      this.conceptececutionid = data;
      this.conceptIdOptions = data.map((v, i) => {
        return {
          name: v.conceptId,
          id: i++
        }
      });
      this.dynamicFromJsonConfig()
    });
    setTimeout(() => this.filehistoryData = [], 500);

    setTimeout(() => {
      let addClassToTimeLine = document.getElementsByClassName("center-line");
      addClassToTimeLine[0].classList.add("removeCentreLineFromTimeline")
    }, 100);

    this.currentYear = Number(new Date().getFullYear())
    this.insightSelectedYear = this.currentYear
    for (let i = 0; i < this.numOfYrsReq; i++) {
      let drpObj = {
        name: this.currentYear - i,
        id: i + 1
      }
      this.insightYearDataset.push(drpObj)
    }
  }

  /**
   * Add Or Remove data when there is any change in Dynamic From
   */
  manipulationsWhenChangeOnFrom = () => {
    let addClassToTimeLine = document.getElementsByClassName("center-line");

    this.filehistoryData = []
    this.barChartDataset.xAxis = []
    this.barChartDataset.plotData.forEach(e => {
      e.data = []
    });
    this.insightErrorMsg = "No Execution ID Selected"
    this.errMsgBold = false
    this.insightErrMsgBool = true
    this.showChart = false
    setTimeout(() => this.redrawInsightChart = Date.now(), 1);
    this.showFileRptDetails = false
    this.filename = null;
    this.filedate = null;
    addClassToTimeLine[0].classList.add("removeCentreLineFromTimeline")
    this.recentFileListBool = true
    this.timeLineDataset = [{
      title: 'No File Selected',
      content: 'No File Selected',
      complete: true,
    }];
  }

  /**
   * function Executed On Dynamic Form Event Change 
   * @param event 
   */
  formValue(event: any) {
    this.selectedConceptId = event.current.details.conceptId;
    this.selectedExecutionId = event.current.details.executionId;

    if (this.selectedConceptId) {
      this.conceptececutionid.map(x => {
        if (x.conceptId == this.selectedConceptId) {
          this.executionIdOptions = x.executionIds.map((e, i) => {
            return {
              name: e,
              id: i++
            }
          })
          this.dynamicFromJsonConfig()

          this.conceptAndExecutionFromJsonConfig.forEach(element => {
            if (element.name == "details") {
              element.groupControls.forEach(x => {
                if (x.name == "conceptId") {
                  x.selectedVal = this.selectedConceptId
                }
                if (x.name == "executionId") {
                  this.executionIdOptions.find(x => x.name == this.selectedExecutionId) ?
                    x.selectedVal = this.selectedExecutionId : x.selectedVal = '';
                }
              });
            }
          });
        }
      })
    }
    if (this.selectedConceptId && this.selectedExecutionId) {
      this.onexecutionvaluechange(this.selectedExecutionId)
    }

    if (!this.selectedExecutionId && event.previous.details.executionId) {
      this.manipulationsWhenChangeOnFrom();
    }

    if (!this.selectedConceptId && event.previous.details.conceptId) {
      this.manipulationsWhenChangeOnFrom();
      this.conceptAndExecutionFromJsonConfig.forEach(element => {
        if (element.name == "details") {
          element.groupControls.forEach(x => {
            if (x.name == "executionId") {
              x.selectedVal = this.selectedConceptId
            }
          });
        }
      });
      this.executionIdOptions = []
      this.dynamicFromJsonConfig()
    }

  }

  fileStatus: any

  dynamicFromJsonConfig() {
    this.conceptAndExecutionFromJsonConfig = [
      {
        "type": "group",
        "name": "details",
        "column": "2",
        "groupControls": [
          {
            "options": this.conceptIdOptions,
            "optionName": "name",
            "optionValue": "name",
            "label": "Concept ID",
            "type": "select",
            "multiple": false,
            "closeOnSelect": true,
            "name": "conceptId",
            "column": "2",
            "id": "conceptId",
            "placeholder": "Select Concept ID",

          },
          {
            "options": this.executionIdOptions,
            "optionName": "name",
            "optionValue": "name",
            "label": "Execution ID",
            "type": "select",
            "multiple": false,
            "closeOnSelect": true,
            "name": "executionId",
            "column": "2",
            "id": "executionId",
            "placeholder": "Select Execution ID",
          },
        ]
      },
    ];
  }

  /*
   Method to fetch the api for file report details
  */
  recentFileListApi(payload) {
    this.clientApiService.getRecentFileListDetails(payload).subscribe((data) => {
      this.fileReportTblDataset = data;
      this.showFileRptDetails = true
      this.fileDetailsTableRedraw = Date.now();
      this.openHTMLModal = Date.now;
      this.tblFlag = true;
    })
  }

  insightViewDetailsPopUp = (status) => {
    this.fileStatus = status
    this.tblFlag = false;
    let payload = {
      "fileName": this.filename,
      "exceID": this.executionIdDropdownSelectedValue,
      "status": ""
    }
    if (status == "Accepted") {
      payload.status = "success"
      this.recentFileListApi(payload)
    } else if (status == "Rejected") {
      payload.status = "rejected"
      this.recentFileListApi(payload)
    } else {
      payload.status = "error"
      this.recentFileListApi(payload)
    }


  }


  /*
  check inventory status to each Row
  */
  _checkInventory = (event: any) => {

    return `<a href='#/workflow-dashboard/dashboard/${this.selectedConceptId}/${this.clientSelected}/${this.selectedExecutionId}'> Check Inventory &#8594;</a>`;
  }

  /**
   * Function to Group By a key value from an Array
   * @param list 
   * @param key 
   * @param param2 
   * @returns 
   */
  groupByKey = (list, key, { omitKey = false }) => list.reduce((hash, { [key]: value, ...rest }) => (
    {
      ...hash, [value]: (hash[value] || []).concat(omitKey ? { ...rest } : { [key]: value, ...rest })
    }), {})



  /**
   * Populate Bar Chart Dataset By Grouping on Month OR Date Wise
   * @param dataToBeManipulated 
   * @param groupBy 
   */
  populateBarChartDataset = (dataToBeManipulated, groupBy) => {

    this.barChartDataset.xAxis = []
    this.barChartDataset.plotData.forEach(e => {
      e.data = []
    });

    let groupedData = this.groupByKey(dataToBeManipulated, groupBy, { omitKey: false })

    if (groupBy == "month") {
      this.properties.XAxisTitle = "Month Range"
      this.monthGroupdata = groupedData
      this.isYearWise = false
    } else {
      this.properties.XAxisTitle = "Date Range"
      this.isYearWise = true
    }

    for (const key in groupedData) {
      this.barChartDataset.xAxis.push(key)
      let rejectedCount = 0
      let successCount = 0
      let missingCount = 0
      groupedData[key].map(e => {
        switch (e.status) {
          case "Success":
            successCount++
            break;
          case "Rejected":
            rejectedCount++
            break;
          case "Error":
            missingCount++
            break;
        }
      })
      this.barChartDataset.plotData.forEach((e) => {
        switch (e.name) {
          case 'Accepted':
            e.data.push(successCount)
            break;
          case 'Rejected':
            e.data.push(rejectedCount)
            break;
          case 'Missing':
            e.data.push(missingCount)
            break;
        }
      })
    }
    setTimeout(() => this.redrawInsightChart = Date.now(), 1);

  }

  /**
   * Function to go back to Year wise BarChart Dataset
   */
  backToYear = () => {
    this.populateBarChartDataset(this.insightDetailsData, "month")
  }

  /**
   * Sorting a Array Based on Months
   * @param dataToSort 
   */
  sortDataByMonth = (dataToSort) => {
    let months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    dataToSort?.sort(function (a, b) {
      return months.indexOf(a.month)
        - months.indexOf(b.month);
    });
  }

  /**
   * Method to Call API to fetch Insight Details Chart Data
   * @param exeId 
   * @param insightSelectedYear 
   */
  getInsightDetailsApiCall = (exeId, insightSelectedYear) => {
    this.clientApiService.getInsightDetails(exeId, insightSelectedYear).subscribe((data) => {
      if (data.length == 0) {
        this.insightErrorMsg = "No Data Available";
        this.insightErrMsgBool = true;
        this.errMsgBold = true;
        this.showOnlyChart = false
        this.isYearWise = false
      } else {
        this.insightErrMsgBool = false
        this.showOnlyChart = true
        this.sortDataByMonth(data)
        this.insightDetailsData = data
        this.populateBarChartDataset(this.insightDetailsData, "month")
      }
    })
  }


  /*
     User change the executionid value change from the dropdown
    */
  onexecutionvaluechange(event: any) {
    this.exeId = event;
    this.insightErrMsgBool = false
    this.showChart = true
    this.clientApiService.getClientFileHistory(event)
      .subscribe(data => {
        if (!data) return;
        data.fileList.forEach(e => {
          e.sentDateTime = new Date(e.sentDateTime)
          e.sentDateTime = ((e.sentDateTime.getMonth() > 8) ? (e.sentDateTime.getMonth() + 1) : ('0' + (e.sentDateTime.getMonth() + 1))) + '-' + ((e.sentDateTime.getDate() > 9) ? e.sentDateTime.getDate() : ('0' + e.sentDateTime.getDate())) + '-' + e.sentDateTime.getFullYear() + " " + e.sentDateTime.toLocaleTimeString()
        });
        this.filehistoryData = data.fileList;
        this.executionIdDropdownSelectedValue = event;
        this.totalEntries = data.length
        this.recentFileListBool = false
        this.totalFilesSent = data.totalFilesSent;
        this.totalInsightsSent = data.totalInsightsSent;
      });

    this.insightSelectedYear = this.currentYear

    this.getInsightDetailsApiCall(this.exeId, this.insightSelectedYear)

  }


  /**
   * Function Triggered On values change for Select DropDown in Insight Chart
   */
  insightYearChange = (event) => {
    if (event) {
      this.insightSelectedYear = event.name;
      this.getInsightDetailsApiCall(this.exeId, this.insightSelectedYear)
    } else {
      this.barChartDataset.xAxis = []
      this.barChartDataset.plotData.forEach(e => {
        e.data = []
      });
      this.isYearWise = false
      this.errMsgBold = true
      setTimeout(() => this.redrawInsightChart = Date.now(), 1);
      this.insightErrorMsg = "Year Not Selected"
      this.insightErrMsgBool = true
      this.showOnlyChart = false
    }
  }

  /**
   * Function Executed when any column is clicked in Bar graph Chart.  
   * @param event 
   */
  onBarClick = (event) => {
    if (!this.isYearWise) {

      let selectedMonthData;
      for (const key in this.monthGroupdata) {
        if (event.xAxis == key) {
          selectedMonthData = this.monthGroupdata[key];
          this.selectedMonth = event.xAxis;
        }
      }

      selectedMonthData.forEach(e => {
        e.date = Number(e.date.slice(-2));
      });

      selectedMonthData.sort(function (a, b) {
        return a.date - b.date;
      });

      this.populateBarChartDataset(selectedMonthData, "date");
    }
  }



  /**
   * _createFileName function to display error symbole
   * @param event 
   */
  _createFileName(event: any) {
    if (event.dataContext.isError) {
      return `${event.dataContext.fileName} <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>`;
    } else {
      return event.dataContext.fileName;
    }

  }
  /*
 when the user select the radio button get the file report details 
 */
  rendererTableClicked = (event) => {

  }
  /*
   Method invoked when File Report Details checkbox is selected
   */
  recentFileListTblsel(event) {
    this.filename = event[0].fileName
    this.filedate = event[0].sentDateTime
    this.clientApiService.getFileReportDetails(this.filename, this.clientNameFromCookie).subscribe((data) => {
      if (data?.sentFileName == null) {
        this.showFileRptDetails = false
      } else {
        this.selectedFileReportDetails = data
        this.showFileRptDetails = true
        this.panelIndex = "0"
        this.openAll = true
      }
    })


    this.clientApiService.getFileTimeLineDetails(this.filename, this.clientNameFromCookie).subscribe((data) => {
      let addClassToTimeLine = document.getElementsByClassName("center-line");
      this.timeLineDataset = []
      if (data?.length > 0) {
        addClassToTimeLine[0].classList.remove("removeCentreLineFromTimeline")
        data.map(e => {
          let singleTimeLineOBJ = {
            title: e.status,
            content: e.date,
            complete: true
          }

          this.timeLineDataset.push(singleTimeLineOBJ)

        })
      } else {
        addClassToTimeLine[0].classList.add("removeCentreLineFromTimeline")
        this.timeLineDataset = [{
          title: 'File Not Sent',
          content: '-----',
          complete: true,
        }];

      }
    })

  }

  /**
   * ngAfterViewInit function
   */
  ngAfterViewInit(): void {
    setTimeout(() => this.tableRedraw = Date.now(), 2000);
  }

  /**
   * redrawTable table function
   */
  redrawTable() {
    setTimeout(() => this.tableRedraw = Date.now(), 100);
  }

  /**
   * yearSelected function to get data
   * @param data 
   * @param year 
   */
  yearSelected(data, year) {
    this.isCurrentYear = false;
    if (data == Number(new Date().getFullYear()) && year == 'nextYear') {
      this.isCurrentYear = true;
    } else {
      if (year == 'nextYear') {
        this.currantYear = data + 1
      } else {
        this.currantYear = data - 1
      }
    }
    if (this.currantYear == Number(new Date().getFullYear())) {
      this.isCurrentYear = true;
    }

  }

  closeModelPopup() {
    this.openHTMLModal = false
  }

}
