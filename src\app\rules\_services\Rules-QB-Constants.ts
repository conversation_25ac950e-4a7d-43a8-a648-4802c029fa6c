export const OperatorsRulesQB = {
    text: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }, { name: 'contains', id: 'contains' }, { name: 'Does Not Contain(s)', id: 'Does Not Contain(s)' }, { name: 'Begins With', id: 'Begins With' }, { name: 'Ends With', id: 'Ends With' }, { name: 'Does Not Begins With', id: 'Does Not Begins With' }, { name: 'Does Not End With', id: 'Does Not End With' }, { name: 'Is Null', id: 'Is Null' }, { name: 'Is Not Null', id: 'Is Not Null' }, { name: 'In', id: 'in' }],
    numeric: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }, { name: 'Greater Than', id: 'Greater Than' }, { name: 'Greater Than or Equal', id: 'Greater Than Or Equal' }, { name: 'Less Than', id: 'Less Than' }, { name: 'Less Than Or Equal', id: 'Less Than Or Equal' }, { name: 'Between', id: 'Between' }, { name: 'Not Between', id: 'Not Between' }, { name: 'Is Null', id: 'Is Null' }, { name: 'Is Not Null', id: 'Is Not Null' }],
    textarea: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }, { name: 'contains', id: 'contains' }, { name: 'Does Not Contain(s)', id: 'Does Not Contain(s)' }, { name: 'Begins With', id: 'Begins With' }, { name: 'Ends With', id: 'Ends With' }, { name: 'Does Not Begins With', id: 'Does Not Begins With' }, { name: 'Does Not End With', id: 'Does Not End With' }, { name: 'Is Null', id: 'Is Null' }, { name: 'Is Not Null', id: 'Is Not Null' }],
    time: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }, { name: 'Greater Than', id: 'Greater Than' }, { name: 'Greater Than or Equal', id: 'Greater Than Or Equal' }, { name: 'Less Than', id: 'Less Than' }, { name: 'Less Than Or Equal', id: 'Less Than Or Equal' }],
    calendar: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }, { name: 'Greater Than', id: 'Greater Than' }, { name: 'Greater Than or Equal', id: 'Greater Than Or Equal' }, { name: 'Less Than', id: 'Less Than' }, { name: 'Less Than Or Equal', id: 'Less Than Or Equal' }, { name: 'Between', id: 'Between' }, { name: 'Not Between', id: 'Not Between' }, { name: 'Is Null', id: 'Is Null' }, { name: 'Is Not Null', id: 'Is Not Null' }],
    singleselect: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }],
    checkbox: [{ name: 'Equal', id: 'Equal' }, { name: 'Not Equal', id: 'Not Equal' }],
    multipleselect: [{ name: 'Equal', id: 'Equal' }]
};

export const OperatorsMapForQb = {
    "Equal": "==", "Not Equal": "!=", "Greater Than": ">", "Greater Than Or Equal": ">=",
    "Less Than": "<", "Less Than Or Equal": "<=", "contains": "contains", "like": "like", "Does Not Contain(s)": "not contains", "Begins With": "startswith",
    "Ends With": "endswith", "Does Not Begins With": "not startswith", "Does Not End With": "not endswith", "Is Null": "isnull", "Is Not Null": "isnotnull", "Between": "between", "Not Between": "not between", "in": "in"
};


export const operatorsMapToShowInQb = {
    '==': 'Equal',
    '!=': 'Not Equal',
    '>': 'Greater Than',
    '>=': 'Greater Than Or Equal',
    '<': 'Less Than',
    '<=': 'Less Than Or Equal',
    'contains': 'contains',
    'like': 'like',
    'not contains': 'Does Not Contain(s)',
    'startswith': 'Begins With',
    'endswith': 'Ends With',
    'not startswith': 'Does Not Begins With',
    'not endswith': 'Does Not End With',
    'isnull': 'Is Null',
    'isnotnull': 'Is Not Null',
    "between": "Between",
    "not between": "Not Between",
    "in": "in"
};