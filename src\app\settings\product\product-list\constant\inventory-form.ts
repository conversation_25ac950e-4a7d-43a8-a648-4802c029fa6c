


export const Inventoryform = () => {
  const returnData = {
    "type": "group",
    "name": "Inventory_Details",
    "label": "",
    "column": "1",
    "groupControls": [
      {

        "label": "Product",
        "type": "text",
        "name": "product",
        "id": "product",
        "column": "1",
        "disabled": true

      },
      {
        "options": [],
        "optionName": "name",
        "optionValue": "id",
        "label": "Inventory Type",
        "type": "select",
        "multiple": true,
        "closeOnSelect": true,
        "name": "inventory",
        "id": "inventory",
        "column": "1",
        "disabled": false,
        "required":true,
        "selectedVal":[]

      }
    ]
  };
  return returnData;
};
