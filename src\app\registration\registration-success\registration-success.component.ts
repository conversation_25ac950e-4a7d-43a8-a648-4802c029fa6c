import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router'; 
import { MPUIButtonModule } from 'marketplace-button';

@Component({
  selector: 'app-registration-success',
  standalone: true,
  templateUrl: './registration-success.component.html',
  styleUrls: ['./registration-success.component.css'],
  imports: [CommonModule, RouterModule,MPUIButtonModule] 
})
export class RegistrationSuccessComponent {

  constructor(private router: Router) { }

  goToHome(): void {
    window.location.reload();
  }
}
