

app-acknowledgement .btn-spanpreference {
    float: right;
    padding: 3px 14px 16px 30px;
    padding-bottom: 11px;
}

app-acknowledgement .table-container .slickgrid-container .slick-column-name label:before {
    color: #fff;
    opacity: .4;
    display: none;
}  

/* app-acknowledgement marketplace-table .table-container .slickgrid-container .slick-cell-checkboxsel label:before {
    color: #000;
    display: none;
} */

app-acknowledgement .table-controls_features{
    display: none;
}

app-acknowledgement .mg-top-2{
    margin-top: 2%;
}

app-acknowledgement .v-line{
    border-right: 3px solid rgba(5,5,6,0.125);
    height: 100%;
}

app-acknowledgement .client-name, app-acknowledgement .product-name{
    margin-bottom: 8px;
    margin: 10px;
}
app-acknowledgement .file-name{
    font-size: 16px;
    
}
app-acknowledgement marketplace-select .select-holder label{
    font-weight:500;
    -webkit-margin-bottom-collapse: 5px;
}
app-acknowledgement .highcharts-exporting-group{
    display: none;
}
app-acknowledgement .card{
    border: 3px solid rgba(5,5,6,0.125);
    border-radius: .5rem;
    margin-bottom: 10px;
}
app-acknowledgement marketplace-table .table-container .table-controls{
    height: 0.5rem;
}

app-acknowledgement .span-lable-title{
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
}

app-acknowledgement .pd-righ-25{
    padding-right: 25px;
}
app-acknowledgement .mg-bottom-10{
    margin-bottom: 10px;
}

app-acknowledgement .mg-top-10{
    margin-top: 1rem;
}

app-acknowledgement .title-table-span{
    font-size: 20px;
    font-weight: 600;
    margin: 5px;
}
      
app-acknowledgement .timeline-title{
    margin-top: 1.5rem;
    font-weight:600;
    margin-bottom: 8px;
    font-size: 19px;
}

app-acknowledgement .panel-group .panel {
    border-radius: 0;
    box-shadow: none;
    border-color: #EEEEEE;
}

app-acknowledgement .panel-default > app-acknowledgement .panel-heading {
    padding: 0;
    border-radius: 0;
    color: #212121;
    background-color: #FAFAFA;
    border-color: #EEEEEE;
}

app-acknowledgement .panel-title{
    font-size: 14px;
}

app-acknowledgement .panel-title > a{
    padding-top: 10px;
    text-decoration: none;
}

app-acknowledgement .more-less {
    float: right;
    color: #212121;
}

app-acknowledgement .panel-default > app-acknowledgement .panel-heading + app-acknowledgement .panel-collapse > app-acknowledgement .panel-body {
    border-top-color: #EEEEEE;
}
app-acknowledgement .fa-plus:before{
    color: #000;
    float: right;
}

app-acknowledgement .horizontal-line{
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid #ccc;
}

marketplace-timeline .vertical-timeline__holder .row section .details .title{
    font-size: 18px !important;
    font-weight: 500 !important;
    width: 90%;
}

app-acknowledgement .panelcharts {
    justify-content: space-between;
    padding: 10px 20px 10px 20px;
}

app-acknowledgement .headZoom {
    float :left;
    display: flex;
    justify-content: center;
    width: 60%;
}
app-acknowledgement .zoomPanel{
    display: flex;
    justify-content: space-evenly;
    width: 55%;
}
app-acknowledgement .dateSetup {
    float: right;
    display: flex;
    justify-content: space-evenly;
}
app-acknowledgement .graph-timeline{
    color: blue;
    border-radius: 5px;
    border: 1px solid #d2d8fc;
    background-color: white;
    width: 90px;
}
app-acknowledgement .monthSelected{
    background:#286CE2;
    color: white;
}

app-acknowledgement .arrow {
    font-weight: bold;
}

app-acknowledgement .zoomPanel span{
    font-size: 14px;
}

app-acknowledgement .dateSetup span{
    font-size: 14px;
    color:blue;
    width: 40%;
}

app-acknowledgement marketplace-table .table-container .slickgrid-container .slick-column-name{
    font-size: 15px !important;
}


app-acknowledgement .panel-title > a:before{
    float: right !important;
    font-family: FontAwesome;
    content:"\f068";
    padding-right: 5px;
    color: #2D6FE2;
}
app-acknowledgement .panel-title > a.collapsed:before{
    float: right !important;
    content:"\f067";
    color: #2D6FE2;
}
app-acknowledgement .panel-title > a:hover, 
app-acknowledgement .panel-title > a:active, 
app-acknowledgement .panel-title > a:focus  {
    text-decoration:none;
}
app-acknowledgement .tabaccordion{
    display: flex;
    justify-content: space-around;
}
app-acknowledgement .tabaccordion .col-md-4{
    flex: 0 0 20%;
    max-width: 25%;
    border-radius: 10px;
    box-shadow:2px 2px 5px 1px grey;
    background: url("/assets/grey.PNG");
    background-repeat: no-repeat;
    background-size: 100rem 4rem;
}

.fouthCard{
    display: none;
}

app-acknowledgement .panel-title a{
    color: #000;
}

app-acknowledgement .section-heading{
    font-weight: 600;
    font-size: 20px;
    margin: 10px;
}
app-acknowledgement .sub-section-heading{
    font-weight: 500;
    font-size: 14px;
    margin: 10px;
}
app-acknowledgement marketplace-select .select-holder label{
    font-weight: 600;
}
app-acknowledgement .alert-card-box{
    display: flex;
    justify-content: center;
    font-weight: 600;
    font-size: 1rem;
    margin: 10px;
}
app-acknowledgement .panel-collapse{
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}
app-acknowledgement .circle{
    border-radius: 50%;
    width: 150px;
    height: 150px;
    padding: 30px;
    background: #fff;
    border-top: 3px solid #F2BC35;    
    border-bottom: 3px solid #00D795;    
    border-left: 3px solid #F2BC35;
    border-right: 3px solid #00D795;
    color: #000;
    text-align: center;
    justify-content: center;
    font: 32px Arial, sans-serif;
}
app-acknowledgement .circle-text{
    display: flex; width: 100%; justify-content: center; font-size: 14px;
}
app-acknowledgement .alert-card-box-subtitle{
    font-size: 13px;
    margin: 2px;
}

app-acknowledgement .widthForMissingBtns{
    width: 113%;
    margin-left: -13px;
    display: flex;
    justify-content: space-around;
}

app-acknowledgement .widthForMissingBtns marketplace-button .btn {
    margin: 0 0 0 0 !important;
}
app-acknowledgement .notification-btn{
    display: flex;
    justify-content: center;
    width: 90%;
    margin: 1rem;
}

app-acknowledgement .notification-btn1{
    border-radius: 7px;
    background: #286CE2;
    color: white;
    border: 1px solid white;
}

app-acknowledgement .notification-btn2{
    border-radius: 7px;
    background: white;
    color: #286CE2;
    border: 1px solid #d2d8fc;
}
app-acknowledgement marketplace-timeline p {
    font-weight: 500;
    font-size: 14px;
}

app-acknowledgement .fa-chevron-left{
    color: #286CE2;
    margin-right: 5px;
    padding-left: 8em;
}


app-acknowledgement .fa-chevron-right{
    color: #286CE2;
    margin-left: 5px;
}
app-acknowledgement .nextYearIcon{
    color: #b2caf3;
}
app-acknowledgement .fa-exclamation-triangle{
    color: #DB5F6A;
    padding-left: 1rem;
}
app-acknowledgement .container-alert{
    margin-top: 1rem;
}
app-acknowledgement .dateYearRange{
    display: flex;
    justify-content: center;
}

app-acknowledgement marketplace-dynamic-form .form-col-2 {
    max-width: 100% !important;
}

#insight-viewDetails marketplace-table .table-container .slickgrid-container .slick-column-name{
font-size: 12px !important;
}

#insight-viewDetails .custom-control-input:disabled~.custom-control-label, .custom-control-input[disabled]~.custom-control-label{
color: #000 !important;
}

#insight-viewDetails  .modal-header{
background-color: #5009B5 !important;
border-top-left-radius: 0 !important;
}

#insight-viewDetails .modal-content{
border: 0px !important;
}

#insight-viewDetails .close{
color: white !important;
}

#insight-viewDetails .close:not(:disabled):not(.disabled), #insight-viewDetails .close:not(:disabled):not(.disabled){
opacity: 1 !important;
}


#insight-viewDetails .slick-cell{
display: inline-block;
width: 11%;
white-space: nowrap;
overflow: hidden !important;
text-overflow: ellipsis;
}

.modal-dialog{
    max-width: 90vw;
}

app-acknowledgement marketplace-popup .modal .modal-dialog .modal-body {
    height: 74vh;
}

marketplace-popup button.close {
    display: none;
}

app-acknowledgement marketplace-panel .panel {
    padding: 0px !important;
    position: inherit !important;
    border: none !important;
    background: none !important;
    display: flex;
    flex-direction: column;
}

app-acknowledgement #vwInDtlsNoData {
    margin-left: 40px;
}

app-acknowledgement .viewInDtls{
    margin-left: 25px;
}

app-acknowledgement marketplace-panel .panel-section {
    border-top: none !important;
}

app-acknowledgement marketplace-timeline .vertical-timeline__holder .center-line .scroll-icon {
display: none !important;
}

app-acknowledgement marketplace-timeline .vertical-timeline__holder .wrapper .center-line {
    height: 73% !important;
    left: 26px;
}

app-acknowledgement .removeCentreLineFromTimeline{
    display: none !important;
}

app-acknowledgement .smbtBtn,
.smbtBtn:hover {
    border: none;
    background-color: #5009B5;
}

app-acknowledgement .yearDrpDwn{
    position: relative;
    left: 14%;
}

app-acknowledgement .monthInBarChart {
    position: absolute;
    right: 45%;
    margin-top: 22px;
    font-weight: 600;
    color: #3f3f3f;
}

app-acknowledgement .errMsgBold{
    font-weight: 700;
}