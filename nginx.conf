worker_processes auto;
 
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;
 
events {
worker_connections 1024;
}
 
http {
include /etc/nginx/mime.types;
default_type application/octet-stream;
ssl_verify_client off;
proxy_connect_timeout 10s;
root /usr/share/nginx/html;
 
log_format main '$remote_addr - $remote_user [$time_local] "$request" '
'$status $body_bytes_sent "$http_referer" '
'"$http_user_agent" "$http_x_forwarded_for"';
 
server {
listen 0.0.0.0:8080;
charset utf-8;
server_name  localhost;
root   /usr/share/nginx/html;
index  index.html index.htm;
include /etc/nginx/mime.types;
 
gzip on;
gzip_types text/css text/javascript application/x-javascript application/json;
 
# frontend
location / {
  try_files $uri $uri/ /index.html;
}
}

 
 
access_log /var/log/nginx/access.log main;
 
sendfile on;
#tcp_nopush on;
 
keepalive_timeout 65;
 
#gzip on;
}