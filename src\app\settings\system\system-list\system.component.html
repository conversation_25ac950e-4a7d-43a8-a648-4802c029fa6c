<div class="fixed-nav bg-gray" *ngIf="isView">
  <div class="content-wrapper">
    <div>
      <div class="card">
        <div>
          <marketplace-breadcrumb [dataset]="breadcrumbDataset"  (onSelection)="breadcrumSelection($event)">
          </marketplace-breadcrumb>
          <div>
            <span class="table-title">System List</span>


          </div>
          <div class="pd-left-30">
            
            <svg width="16" height="17" viewBox="0 0 16 17" class="pd-righ-10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M4.25 1.78223C4.66421 1.78223 5 2.11802 5 2.53223V3.78223H11V2.53223C11 2.11802 11.3358 1.78223 11.75 1.78223C12.1642 1.78223 12.5 2.11802 12.5 2.53223V3.79344C13.9016 3.91974 15 5.09771 15 6.53223V13.0322C15 14.551 13.7688 15.7822 12.25 15.7822H3.75C2.23121 15.7822 1 14.551 1 13.0322V6.53223C1 5.09772 2.09837 3.91974 3.5 3.79344V2.53223C3.5 2.11802 3.83579 1.78223 4.25 1.78223ZM12.25 5.28223H3.75C3.05964 5.28223 2.5 5.84187 2.5 6.53223V13.0322C2.5 13.7226 3.05963 14.2822 3.75 14.2822H12.25C12.9404 14.2822 13.5 13.7226 13.5 13.0322V6.53223C13.5 5.84186 12.9404 5.28223 12.25 5.28223Z" fill="#231E33"/>
              </svg>
              
            <lable class="pd-righ-20">{{dataDate}}</lable>
            
            <svg width="16" height="17" viewBox="0 0 16 17" class="pd-righ-10" (click)="refreshLstUpdted()" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.19621 1.92928C6.9026 1.71142 6.48594 1.73563 6.21967 2.0019L6.14705 2.08601C5.9292 2.37963 5.9534 2.79629 6.21967 3.06256L7.18915 4.03223H7L6.75547 4.03564C3.27825 4.13334 1 6.32498 1 10.0322C1 10.4464 1.33579 10.7822 1.75 10.7822C2.16421 10.7822 2.5 10.4464 2.5 10.0322C2.5 7.09444 4.18888 5.53223 7 5.53223H7.18915L6.21967 6.5019L6.14705 6.58601C5.9292 6.87963 5.9534 7.29629 6.21967 7.56256C6.51256 7.85545 6.98744 7.85545 7.28033 7.56256L9.53033 5.31256L9.60295 5.22844C9.8208 4.93483 9.7966 4.51816 9.53033 4.2519L7.28033 2.0019L7.19621 1.92928Z" fill="#231E33"/>
              <path d="M8.46967 10.0019C8.76256 9.709 9.23744 9.709 9.53033 10.0019C9.7966 10.2682 9.8208 10.6848 9.60295 10.9784L9.53033 11.0626L8.56085 12.0322H8.75C11.6347 12.0322 13.5 10.4146 13.5 7.53223C13.5 7.11801 13.8358 6.78223 14.25 6.78223C14.6642 6.78223 15 7.11801 15 7.53223C15 11.2107 12.5328 13.432 8.993 13.5289L8.75 13.5322H8.56085L9.53033 14.5019C9.7966 14.7682 9.8208 15.1848 9.60295 15.4784L9.53033 15.5626C9.26406 15.8288 8.8474 15.853 8.55379 15.6352L8.46967 15.5626L6.21967 13.3126C5.9534 13.0463 5.9292 12.6296 6.14705 12.336L6.21967 12.2519L8.46967 10.0019Z" fill="#231E33"/>
              </svg>
              
            <lable class="pd-righ-20">Last Updated: {{lastRegfreshDate}}</lable>
            <lable>Total Entries: {{totalEntries}}</lable>


          </div>
          <div class="pad-left-80">
            <span class='btn-span'>
              <marketplace-button [label]="'Add New System'" [type]="'primary'" [name]="'primary'" [enabled]="!isReadOnly"
                (onclick)="createSystem()">
              </marketplace-button>

            </span>
          </div>
        </div>
        <div class="card-body">
          <div class="card">
            <div class="pad-left-29"><b>NOTE:</b> Filter to find a specific system.</div>
            <marketplace-table [id]="viewSysListTbl" [dataset]="systemDataJSON" *ngIf="showData"
              [rowHeight]="ruleDashbordTableRowhg" [headerRowHeight]="ruleDashbordTableHeaderhg"
              [columnDefinitions]="columnConfig" (onCellValueChange)="cellValueChanged($event)"
              (onCellClick)="rendererTableClicked($event)" [dropdownOptions]="kebabOptions"
              (onDropdownOptionsClick)="onDropdownOptionsClick($event)">
            </marketplace-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Screen Drop Down -->
<div id="dropdownBtn" class="dropdown" style="display: none;">
  <div (click)="clickedInDropdown('Import')" class="" style="cursor:pointer;">
    
    <svg *ngIf="showTickText=='Import'" width="16" height="17" class="check-svg" viewBox="0 0 16 17" title="Import" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M11.5978 4.16216C11.8022 3.80192 12.26 3.67563 12.6202 3.88009C12.9505 4.06751 13.0841 4.46779 12.9466 4.81067L12.9023 4.90256L9.14429 11.5239C8.20299 13.1823 5.91892 13.3899 4.6877 11.9829L4.56818 11.8369L3.15428 9.98795C2.90266 9.65892 2.96541 9.18821 3.29444 8.93659C3.59356 8.70785 4.00977 8.73892 4.27202 8.99366L4.3458 9.07676L5.75971 10.9257C6.27516 11.5997 7.28404 11.5681 7.76472 10.9006L7.83977 10.7835L11.5978 4.16216Z" fill="#231E33"/>
      </svg>
      
    <b class="font bgcolor"> Import</b>
    <div class="drpDwnFont"></div>
  </div>
  <div class="dottedLine"></div>
  <div (click)="clickedInDropdown('Export')" class="" style="cursor:pointer;">
    
    <svg *ngIf="showTickText=='Export'" width="16" height="17" class="check-svg" viewBox="0 0 16 17" title="Export" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M11.5978 4.16216C11.8022 3.80192 12.26 3.67563 12.6202 3.88009C12.9505 4.06751 13.0841 4.46779 12.9466 4.81067L12.9023 4.90256L9.14429 11.5239C8.20299 13.1823 5.91892 13.3899 4.6877 11.9829L4.56818 11.8369L3.15428 9.98795C2.90266 9.65892 2.96541 9.18821 3.29444 8.93659C3.59356 8.70785 4.00977 8.73892 4.27202 8.99366L4.3458 9.07676L5.75971 10.9257C6.27516 11.5997 7.28404 11.5681 7.76472 10.9006L7.83977 10.7835L11.5978 4.16216Z" fill="#231E33"/>
      </svg>
      
    <b class="font bgcolor">Export</b>
    <div class="drpDwnFont"></div>
  </div>
  <div class="dottedLine"></div>
  <div (click)="clickedInDropdown('Import and Export')" class="" style="cursor:pointer;"> 
    
    <svg *ngIf="showTickText=='Import and Export'" width="16" height="17" class="check-svg" viewBox="0 0 16 17" title="Import and Export" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M11.5978 4.16216C11.8022 3.80192 12.26 3.67563 12.6202 3.88009C12.9505 4.06751 13.0841 4.46779 12.9466 4.81067L12.9023 4.90256L9.14429 11.5239C8.20299 13.1823 5.91892 13.3899 4.6877 11.9829L4.56818 11.8369L3.15428 9.98795C2.90266 9.65892 2.96541 9.18821 3.29444 8.93659C3.59356 8.70785 4.00977 8.73892 4.27202 8.99366L4.3458 9.07676L5.75971 10.9257C6.27516 11.5997 7.28404 11.5681 7.76472 10.9006L7.83977 10.7835L11.5978 4.16216Z" fill="#231E33"/>
      </svg>
      
    <b class="font bgcolor">Import and Export</b>
    <div class="drpDwnFont"></div>
  </div>
</div>


<div *ngIf="!isView" class="fixed-nav bg-gray">
  <div class="content-wrapper">
    <div class="container-fluid">
      <div class="card">
        <div class="pd-30">
          <span *ngIf="!isEdit" class="dashbord-title">
              <a (click)="backToPreviousPage()">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="backColor" aria-hidden="true" focusable="false" style="vertical-align: middle;">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                      <path d="M17 3.34a10 10 0 0 1 5 8.66c0 5.523 -4.477 10 -10 10s-10 -4.477 -10 -10a10 10 0 0 1 15 -8.66m-3.293 4.953a1 1 0 0 0 -1.414 0l-3 3a1 1 0 0 0 0 1.414l3 3a1 1 0 0 0 1.414 0l.083 -.094a1 1 0 0 0 -.083 -1.32l-2.292 -2.293l2.292 -2.293a1 1 0 0 0 0 -1.414"/>
                  </svg>
              </a> 
              {{headerTextNew}}
          </span>
          <span *ngIf="isEdit" class="dashbord-title">
              <a (click)="backToPreviousPage()">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="backColor" aria-hidden="true" focusable="false" style="vertical-align: middle;">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                      <path d="M17 3.34a10 10 0 0 1 5 8.66c0 5.523 -4.477 10 -10 10s-10 -4.477 -10 -10a10 10 0 0 1 15 -8.66m-3.293 4.953a1 1 0 0 0 -1.414 0l-3 3a1 1 0 0 0 0 1.414l3 3a1 1 0 0 0 1.414 0l.083 -.094a1 1 0 0 0 -.083 -1.32l-2.292 -2.293l2.292 -2.293a1 1 0 0 0 0 -1.414"/>
                  </svg>
              </a> 
              {{headerTextEdit}} <b class="font-size-13">{{editSystemName}}</b>
          </span>
      </div>
      

        <div class="addSys" *ngIf="!isEdit">
          <div>
            <marketplace-dynamic-form [formJSON]="releationSHJSON">
            </marketplace-dynamic-form>
            <svg width="16" height="17" viewBox="0 0 16 17" class="addEditIcon" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M10.2579 2.30988C10.939 1.61533 12.0558 1.60953 12.7441 2.29695L14.4868 4.03752C15.1758 4.72566 15.1705 5.84385 14.4751 6.52548L6.88162 13.9685C6.55967 14.284 6.16509 14.5156 5.73259 14.6428L1.96164 15.7519C1.69834 15.8294 1.41376 15.7568 1.21969 15.5627C1.02562 15.3687 0.953048 15.0841 1.0305 14.8208L2.13953 11.0501C2.26679 10.6174 2.49853 10.2226 2.81431 9.9006L10.2579 2.30988ZM3.57858 11.4733L2.8575 13.9249L5.30934 13.2038C5.50593 13.146 5.68529 13.0407 5.83163 12.8973L11.4991 7.34213L9.44164 5.28468L3.8853 10.9508C3.74176 11.0972 3.63643 11.2766 3.57858 11.4733ZM13.4251 5.45426L12.5704 6.29209L10.4919 4.21365L11.3289 3.36011C11.4262 3.26089 11.5857 3.26006 11.6841 3.35827L13.4268 5.09884C13.5252 5.19714 13.5244 5.35688 13.4251 5.45426Z"
                fill="#231E33" />
            </svg>
            <svg width="16" height="17" viewBox="0 0 16 17"  class="addEditIconDesc" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M10.2579 2.30988C10.939 1.61533 12.0558 1.60953 12.7441 2.29695L14.4868 4.03752C15.1758 4.72566 15.1705 5.84385 14.4751 6.52548L6.88162 13.9685C6.55967 14.284 6.16509 14.5156 5.73259 14.6428L1.96164 15.7519C1.69834 15.8294 1.41376 15.7568 1.21969 15.5627C1.02562 15.3687 0.953048 15.0841 1.0305 14.8208L2.13953 11.0501C2.26679 10.6174 2.49853 10.2226 2.81431 9.9006L10.2579 2.30988ZM3.57858 11.4733L2.8575 13.9249L5.30934 13.2038C5.50593 13.146 5.68529 13.0407 5.83163 12.8973L11.4991 7.34213L9.44164 5.28468L3.8853 10.9508C3.74176 11.0972 3.63643 11.2766 3.57858 11.4733ZM13.4251 5.45426L12.5704 6.29209L10.4919 4.21365L11.3289 3.36011C11.4262 3.26089 11.5857 3.26006 11.6841 3.35827L13.4268 5.09884C13.5252 5.19714 13.5244 5.35688 13.4251 5.45426Z"
                fill="#231E33" />
            </svg>
            <div class="addReadOnly">Read-Only Helper Text</div>
          </div>
          <div *ngIf="!isEdit" class="col-sm-7">
            <p class="pad-left-65"><b>Inventory Type</b></p>
            <marketplace-pagination [navigationConfig]="stepsConfig" [isCheckboxRequired]="checkboxneeded"
              [groupBy]="'name'" [dataset]="inventoryList" (onSelection)="selectedLinkCreate($event)">
            </marketplace-pagination>
          </div>
        </div>

        <div class="row">
          <div class="col-sm-5">
            
            <div *ngIf="isEdit">
              <marketplace-dynamic-form (onValueChange)="formValue($event,'details')" [formJSON]="releationSHJSONEdit">
              </marketplace-dynamic-form>
              <svg width="16" height="17" viewBox="0 0 16 17" class="editIcon" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M10.2579 2.30988C10.939 1.61533 12.0558 1.60953 12.7441 2.29695L14.4868 4.03752C15.1758 4.72566 15.1705 5.84385 14.4751 6.52548L6.88162 13.9685C6.55967 14.284 6.16509 14.5156 5.73259 14.6428L1.96164 15.7519C1.69834 15.8294 1.41376 15.7568 1.21969 15.5627C1.02562 15.3687 0.953048 15.0841 1.0305 14.8208L2.13953 11.0501C2.26679 10.6174 2.49853 10.2226 2.81431 9.9006L10.2579 2.30988ZM3.57858 11.4733L2.8575 13.9249L5.30934 13.2038C5.50593 13.146 5.68529 13.0407 5.83163 12.8973L11.4991 7.34213L9.44164 5.28468L3.8853 10.9508C3.74176 11.0972 3.63643 11.2766 3.57858 11.4733ZM13.4251 5.45426L12.5704 6.29209L10.4919 4.21365L11.3289 3.36011C11.4262 3.26089 11.5857 3.26006 11.6841 3.35827L13.4268 5.09884C13.5252 5.19714 13.5244 5.35688 13.4251 5.45426Z"
                  fill="#231E33" />
              </svg>
              <svg width="16" height="17" viewBox="0 0 16 17"  class="editIconDesc" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M10.2579 2.30988C10.939 1.61533 12.0558 1.60953 12.7441 2.29695L14.4868 4.03752C15.1758 4.72566 15.1705 5.84385 14.4751 6.52548L6.88162 13.9685C6.55967 14.284 6.16509 14.5156 5.73259 14.6428L1.96164 15.7519C1.69834 15.8294 1.41376 15.7568 1.21969 15.5627C1.02562 15.3687 0.953048 15.0841 1.0305 14.8208L2.13953 11.0501C2.26679 10.6174 2.49853 10.2226 2.81431 9.9006L10.2579 2.30988ZM3.57858 11.4733L2.8575 13.9249L5.30934 13.2038C5.50593 13.146 5.68529 13.0407 5.83163 12.8973L11.4991 7.34213L9.44164 5.28468L3.8853 10.9508C3.74176 11.0972 3.63643 11.2766 3.57858 11.4733ZM13.4251 5.45426L12.5704 6.29209L10.4919 4.21365L11.3289 3.36011C11.4262 3.26089 11.5857 3.26006 11.6841 3.35827L13.4268 5.09884C13.5252 5.19714 13.5244 5.35688 13.4251 5.45426Z"
                  fill="#231E33" />
              </svg>
              <div class="readOnly">Read-Only Helper Text</div>
              <div class="pad-top-73">
                <p class="pad-left-34"><b>Inventory Type</b></p>
                <marketplace-pagination [navigationConfig]="stepsConfig" [isCheckboxRequired]="checkboxneeded"
                  [groupBy]="'name'" [dataset]="editInventoryList" (onSelection)="selectedLinkEdit($event)">
                </marketplace-pagination>
              </div>
            </div>

          </div>
          <div *ngIf="isEdit" class="col-sm-7">
            <div (mouseleave)="displayNone($event)">
              <marketplace-table [dataset]="dataURLfileType" [rowHeight]="ruleDashbordTableRowhg"
                [datasetIDMapping]="'fileTmplId'"[isRowSelectable]="false" [headerRowHeight]="ruleDashbordTableHeaderhg"
                [columnDefinitions]="columnConfigSystemFiletype" (onCellClick)="rendererTableClicked($event)"
                [redraw]="editTblRedraw">
              </marketplace-table>


            </div>
            <div></div>
            <div class="row">
              <div class="col-6">
                <marketplace-input [label]="'Used By'" [id]="'usedById'" [name]="inputname" [groupText]="groupIcon"
                  [placeholder]="'Search Clients'" (input)="getClients($event)" (click)="changeUsedByColor()"
                  (onFocusOut)="removeUsedByColor($event)" ngDefaultControl>
                </marketplace-input>
              </div>
              <div class="col-6 clientText">
                <lable class="csearch">Total Clients: {{totalClients}}</lable>
              </div>
            </div>
            <hr>
            <b *ngIf="noRecordsEditSys">No Records Found</b>
            <marketplace-accordion *ngIf="!noRecordsEditSys">
              <marketplace-panel [header]="client.clientName" *ngFor="let client of clients">
                <div> {{client.clientName}}</div>

                <div class="pad-top-22">
                  On Boarding Date:{{client.createdDate | date:"MM/dd/YYYY"}}
                </div>
              </marketplace-panel>
            </marketplace-accordion>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>



<span class="btnsCloumn btn-span mt-3 mb-2" *ngIf="isVisible">
  <marketplace-button class="mr-30" [label]="'Cancel'" [type]="'secondary'" [name]="'secondary'"
    (onclick)="goToPrevious()">
  </marketplace-button>

  <marketplace-button class="mr-30" [label]="'Save'" [enabled]="!enableFormButtton" [type]="'primary'" [enabled]="!isReadOnly"
    [name]="'primary'" (onclick)="saveSystem()">
  </marketplace-button>
</span>

<marketplace-notification *ngIf="notificationReady" [open]="notificationOpen" [header]="notificationHeader"
  [bodyText]="notificationBody" [type]="notificationType" [duration]="notificationDuration"
  [position]="notificationPosition">

</marketplace-notification>