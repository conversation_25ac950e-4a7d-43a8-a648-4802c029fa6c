import { Component, OnInit, ViewEncapsulation } from "@angular/core";
import { Router } from "@angular/router";
import { EcmAuthenticationService } from "src/app/_services/ecm-authentication.service"

@Component({
  selector: "app-frequently-used-criteria",
  templateUrl: "./frequently-used-criteria.component.html",
  styleUrls: ["./frequently-used-criteria.component.css"],
  encapsulation: ViewEncapsulation.None,
})
export class FrequentlyUsedCriteriaComponent implements OnInit {
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  getTableData: boolean = false;
  public headerText: string = `Frequently Used Criteria Setup`;
  public tableRedraw: any;
  public isPriviousRedirectPage = true;
  breadcrumbDataset = [{ label: 'Home', url: '/' }, { label: 'Rules engine', url: '/rules' }, { label: 'Frequently used criteria setup' }];
  public kebabOptions: any = [{ label: '<i class="fa fa-eye" aria-hidden="true"></i> View Criteria', id: 'viewCriteria' }, { label: '<i class="fa fa-edit" aria-hidden="true"></i> Edit Criteria', id: 'editCriteria' }, { label: '<i class="fa fa-trash" aria-hidden="true"></i> Delete Criteria', id: 'deleteCriteria' }];
  dataURL: string = "./assets/json/table.json";
  dataRoot: string = "src";
  columnConfig: any = {
    "switches": {
      "enableSorting": true,
      "enablePagination": true,
      "enableFiltering": true
    },
    "colDefs": [
      {
        "name": "Name",
        "field": "criteria_name",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "Type",
        "field": "rule_type",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "Rule SubType",
        "field": "rule_sub_type",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "Count",
        "field": "criteria_count",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "Created by",
        "field": "created_by",
        "filterType": "Text",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": ""
      },
      {
        "name": "Created date",
        "field": "created",
        "filterType": "Calendar",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": ""
      }
    ]
  };


  ngOnInit(): void {
    this.getTableData = true;
  }

  /**
   * card component prm
   */
  cardsDataset: any =
    [
      {
        label: 'Setup Rule Type',
        url: '/rules/rule-type'
      },
      { label: 'Setup Frequently Used Criteria', url: '/rules/frequently-used-criteria' }
    ];

  constructor(private router: Router, private ecmAuthentication: EcmAuthenticationService,) { }




  /**
   * customFormatterStatus funtion for button in Rule table
   * @param event 
   */
  customFormatterReviewDate(event) {
    let btn;
    switch (event.value) {
      case 'Active':
        btn = "<button type='button' class='btn btn rule-dashboard btn-review-date-active'>Active</button>";
        break;
      case 'Expired':
        btn = "<button type='button' class='btn btn rule-dashboard btn-review-date-expired'>Expired</button>";
        break;
      case 'About to Expire':
        btn = "<button type='button' class='btn btn rule-dashboard-big btn-about-expire'>About to Expire</button>";
        break;
    }
    return btn;
  }


  /**
  * customFormatterAction function for Rule Table Action
  * @param event 
  */
  customFormatterAction(event) {

    return `<div class="rule-dashboard dropdown-container" tabindex=“-1”>
     <button type='button' class='btn btn btn-execute'>Execute</button>
     <input id=“selector-${event.dataContext.id}” type="checkbox" name="menu" style="display:none;" />
     <label  for=“selector-${event.dataContext.id}” class="three-dots"></label>
     <div class="dropdown">
     <div class="table-action-menu"><i class="fa fa-eye" title="View Rule" dataaction="view" datevalue=${event.dataContext.id}></i>View</div>
     <div class="table-action-menu"><i class="fa fa-edit" title="Edit Rule" dataaction="edit" datevalue=${event.dataContext.id}></i>Edit</div>
     <div class="table-action-menu"><i class="fa fa-trash" title="Delete Rule"  dataaction="delete" datevalue=${event.dataContext.id}></i>Delete</div>
     </div>
     </div>`
  }

  /**
  * Delete Rules funstion
  * @param ruleId 
  */
  rulesDelete(ruleId) {

    // call delete rules API
  }

  /**
    * cell click event
    * @param event 
    */
  cellClicked(event: any): void {

    if (event['eventData'].target.attributes) {
      if (event['eventData'].target.attributes.dataaction && event['eventData'].target.attributes.datevalue) {
        let ruleId = event['eventData'].target.attributes.datevalue.nodeValue;
        let rulesAction = event['eventData'].target.attributes.dataaction.nodeValue;
        switch (rulesAction) {
          case 'view':
            this.router.navigate(['/criteria/view'])
            break;
          case 'edit':
            this.router.navigate([`/criteria/edit/${ruleId}`])
            break;
          case 'delete':
            this.rulesDelete(ruleId);
            break;
        }
      }
    }
  }

  /**
    * cellValueChanged Function for Table
    * @param event 
    */
  cellValueChanged(event: Event): void {

  }
  dataJSON: any = [
    {
      id: "1",
      criteria_name: "ITS Exclusion",
      rule_type: "Global",
      rule_sub_type: "Exclusion",
      criteria_count: "4 Criteria",
      created_by: "Lakki Reddy",
      created: "2021-11-07",
      effortDriven: true,
    },
    {
      id: "2",
      criteria_name: "Exclude Ages Below 26",
      rule_type: "Global",
      rule_sub_type: "Exclusion",
      criteria_count: "3 Criteria",
      created_by: "Ajan Srinivas",
      created: "2021-11-09",
      effortDriven: true,
    },
    {
      id: "3",
      criteria_name: "Over 40 Clients",
      rule_type: "Global",
      rule_sub_type: "On Hold",
      criteria_count: "2 Criteria",
      created_by: "User 3",
      created: "2021-11-07",
      effortDriven: true,
    },
  ];

  // columnConfig: any = {
  //   "switches": {
  //     "enableSorting": true,
  //     "enablePagination": true,
  //     "enableFiltering": true,
  //     "editable": false,
  //   },
  //   "colDefs": [
  //     {
  //       "name": "Rule Id",
  //       "field": "id",
  //       "filterType": "Text",
  //       "visible": "True",
  //       "editorType": "Text",
  //       "editorTypeRoot": "",
  //       "editorTypeLabel": "",
  //       "editorTypeValue": ""
  //     },
  //     {
  //       "name": "Rule Name",
  //       "field": "rule_name",
  //       "filterType": "Text",
  //       "visible": "True",
  //       "editorType": "Text",
  //       "editorTypeRoot": "",
  //       "editorTypeLabel": "",
  //       "editorTypeValue": ""
  //     },
  //     {
  //       "name": "Rule Type",
  //       "field": "rule_type",
  //       "filterType": "Multi Select",
  //       "visible": "True",
  //       "editorType": "Text",
  //       "editorTypeRoot": "",
  //       "editorTypeLabel": "",
  //       "editorTypeValue": ""
  //     },
  //     {
  //       "name": "Rule SubType",
  //       "field": "rule_sub_type",
  //       "filterType": "Multi Select",
  //       "visible": "True",
  //       "editorType": "Text",
  //       "editorTypeRoot": "",
  //       "editorTypeLabel": "",
  //       "editorTypeValue": ""
  //     },
  //     {
  //       "name": "Created By",
  //       "field": "created_by",
  //       "filterType": "Text",
  //       "visible": "True",
  //       "editorType": "",
  //       "editorTypeRoot": "",
  //       "editorTypeLabel": ""
  //     },

  //     {
  //       "name": "Created Date",
  //       "field": "created",
  //       "filterType": "Calendar",
  //       "visible": "True",
  //       "editorType": "",
  //       "editorTypeRoot": "",
  //       "editorTypeLabel": ""
  //     },

  //   ]
  // };


  /**
   * customFormatterStatus funtion for button in Rule table
   * @param event 
   */
  customFormatterStatus(event) {
    let btn;
    switch (event.value) {
      case 'Active':
        btn = "<button type='button' class='btn btn rule-dashboard btn-active'>Active</button>";
        break;
      case 'Inactive':
        btn = "<button type='button' class='btn btn rule-dashboard btn-inactive'>Inactive</button>";
        break;
      case 'Draft':
        btn = "<button type='button' class='btn btn rule-dashboard btn-draft'>Draft</button>";
        break;
      case 'On Hold':
        btn = "<button type='button' class='btn btn rule-dashboard btn-onhold'>On Hold</button>";
        break;
    }
    return btn;
  }



  /**
    * tableReady Funtion
    * @param event 
    */
  tableReady(event: Event): void {
  }


  AddNewCriteriafun(): void {
    this.router.navigate(["/criteria/create"]);
  }

  httpRequestdata: any = {
    url: "./assets/form.json",
    dataRoot: "src",
  };

  simpleFormStaticJson: any = [
    {
      optionName: "name",
      optionValue: "value",
      multiple: false,
      closeOnSelect: true,
      label: "",
      group: "Criteria",
      type: "",
      name: "placeholder",
      column: "1",
      groupColumn: "2",
      disabled: true,
      selectedVal: "exp",
      options: [
        {
          name: "Expiration/Lookback Rule",
          value: "exp",
        },
        {
          name: "On Hold",
          value: "on_hold",
        },
        {
          name: "No Recovery",
          value: "no_recovery",
        },
        {
          name: "Exclusion",
          value: "exclusion",
        },
      ],
    },
    {
      label: "Criteria Name",
      group: " ",
      type: "select",
      name: "criteriaName",
      column: "1",
      groupColumn: "1",
      disabled: false,
      value: "",
    },
    {
      label: "Criteria Description",
      group: " ",
      type: "textarea",
      name: "criteriaDescription",
      column: "1",
      groupColumn: "1",
      disabled: false,
      value:
        "Rule " + (this.router?.url ? this.router.url.slice(this.router.url.lastIndexOf("/") + 1) : ""),
    },

    {
      optionName: "name",
      optionValue: "value",
      multiple: false,
      closeOnSelect: true,
      label: "Placeholder",
      group: " ",
      type: "",
      name: "placholder",
      column: "1",
      groupColumn: "1",
    },
  ];
  listPickData = [
    {
      label: "Expiration/Lookback",

      value: "exp",
    },
    {
      label: "Exclusion",

      value: "exclusion",
    },
    {
      label: "On Hold",

      value: "on_hold",
    },
    {
      label: "Recovery",

      value: "recovery",
    },
  ];

  _onListSelection(event) {
  }

  _onItemAddition(event) {
  }

  _onItemDeletion(event) {
  }

  public query = {
    condition: "and",
    rules: [
      {
        field: "age",
        operator: "Equal",
        value: "",
        static: true,
        active: true,
      },
    ],
  };
  exampleconfig: any = {
    fields: {
      client: { name: "Client", type: "numeric" },
      conceptID: { name: "ConceptID", type: "textarea" },
      memberID: { name: "MemberID", type: "text" },
      DOB: { name: "DOB", type: "calendar" },

      market: {
        name: "Market",
        type: "multipleselect",
        dataURL: "",
        dataRoot: "ddl3",
        key: "name",
        id: "value",
      },
      country: {
        name: "Country",
        type: "singleselect",
        dataURL: "",
        dataRoot: "ddl3",
        key: "name",
        id: "value",
      },

      age: { name: "Field One", type: "text" },
      age2: { name: "Field Two", type: "text" },
    },
  };

  addNewCriteriaOnClick(): void {
    this.router.navigate(["/rules/create-frequently-used-criteria"]);
  }
  returnHomeClick(): void {
    this.router.navigate(["/rules"]);
  }

  moveToOptionSelected(event: Event): void {

  }



}
