app-settings-rule-edit .container,
app-settings-rule-edit .container-fluid,
app-settings-rule-edit .container-lg,
app-settings-rule-edit .container-md,
app-settings-rule-edit .container-sm,
app-settings-rule-edit .container-xl {
  width: 100%;
  padding-right: 5px;
  padding-left: 5px;
  margin-right: auto;
  margin-left: auto;
}
app-settings-rule-edit .popUp-Header{
  font-family: elevance !important;
  font-weight: 600 !important;
}
app-settings-rule-edit .ruleDefinition {
	margin-top: 3%;
  width: 100%;
}
app-settings-rule-edit .selectedItemsInformation p{
    margin-left: 7px;
  }
app-settings-rule-edit .statusHeader{
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-align: left;
  }
app-settings-rule-edit .info {
    color: rgb(0, 0, 0);
    background-color:#fff;
    margin-top: -30px;
   }
app-settings-rule-edit .input-group.mb-3{
      margin-bottom: 0px !important;
    }
app-settings-rule-edit .learnMoreAboutRuleTag{
        color: #4e4e4e;
        font-size: 12px;
        cursor: pointer;
      }
app-settings-rule-edit .wrapper{
        max-width: 450px;
        margin: 47px auto;
      }
app-settings-rule-edit .DescriptionProvider{
        position: relative;
        top: 65%;
      }

app-settings-rule-edit .similarSuggestion{
  margin-left: 3px;
}
app-settings-rule-edit .fa-info-circle{
  margin-top: 3px;
  margin-left: 2px;
  margin-right: 3px;
}
app-settings-rule-edit .wrapper .search-input{
      margin-top: -13px;
      background-color: #fff;
      width: 100%;
    }
app-settings-rule-edit .selectedItemsInformation h6 {
  font-weight: bold;
}
 app-settings-rule-edit .selectedItemsInformation {
        font-size: 12px;
        border-style: ridge;
        margin-left: 2px;
        color: rgb(0, 0, 0);
        background-color:#ffffff;
      }
app-settings-rule-edit .searchResults {
        border-style: ridge;
        z-index: 999;
      }


app-settings-rule-edit .search-input .searchResults{
        padding: 0px 1px;
        max-height: 150px;
        overflow-y: scroll;
      }
app-settings-rule-edit .searchResults li{
        line-height: 1.2;
        list-style: none;
        padding: 2px 12px;
        width: 100%;
        cursor: pointer;
        border-radius: 3px;
        font-size: 20px;
        font-family: 'Times New Roman', Times, serif;
      }
app-settings-rule-edit .searchResults li:hover{
        background: #efefef;
      }
app-settings-rule-edit .card {
  border: 3px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
}
app-settings-rule-edit .card .card-no-border {
  border: none !important;
  padding: 0px 25px 0px 25px;
}
app-settings-rule-edit .pd-15 {
  padding: 15px;
}
app-settings-rule-edit .query-builder-title {
  float: left;
  width: 200px;
  height: 34px;
  left: 195px;
  top: 384px;
  
  font-style: normal;
  font-weight: 500;
  font-size: 28px;
  line-height: 34px;
  color: #000000;
}
app-settings-rule-edit .btn-span {
  float: right;
  margin-top:8px;
}
app-settings-rule-edit .btn-criteria {
  background: #5009B5;
  
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #ffffff;
}
app-settings-rule-edit .pd-bottom-15 {
  padding-bottom: 15px;
}
app-settings-rule-edit .pd-25 {
  padding: 25px 25px 25px 25px;
}
app-settings-rule-edit .custom-btn {
  padding: 0.375rem 3rem !important;
  margin-right: 20px;
}
app-settings-rule-edit .noResFound li{
  margin-left: 15px;
}
app-settings-rule-edit .noResFound {
  list-style-type: none;
  border-style: ridge;
  z-index: 999;
}
app-settings-rule-edit .pd-5 {
  padding: 5px;
}
app-settings-rule-edit .level-indicator {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 50px;
  left: 1228px;
  top: 130px;
  border-radius: 4px;
  padding: 10px, 50px, 10px, 50px;
  background: #fde2b9;
}
app-settings-rule-edit .dashbord-title {
  padding: 10px 0px 0px 14px !important;
}
app-settings-rule-edit .card-title {
  
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 29px;
  color: #794cff;
  margin-left: 3px;
}
app-settings-rule-edit .tabs-padding {
  padding: 0px 25px 25px 0px !important;
}
app-settings-rule-edit .notification-title {
  
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 22px;
  color: #161616;
  padding: 13px 0px 0px 15px;
}
app-settings-rule-edit .notification-font-wt {
  font-weight: 600 !important;
  padding: 13px 0px 0px 5px !important;
}

app-settings-rule-edit .breadcrumb-container {
  margin-top: -10px;
  margin-bottom: 49px;
}
app-settings-rule-edit .custom-switch {
  padding-left: 1em;
  margin-top: -4px;
}
app-settings-rule-edit .mar-10 {
  margin-top: 10px;
}
app-settings-rule-edit .modal-content {
  width: 140% !important;
  margin-top: 5% !important;
}
app-settings-rule-edit .custom-title {
  
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 29px;
  color: #794cff;
}
app-settings-rule-edit .custom-message {
  
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 19px;
  color: #4e4e4e;
}
app-settings-rule-edit .modal-header {
  justify-content: center !important;
}
app-settings-rule-edit .pad-1rem-cursor {
  padding: 1rem;
  cursor: not-allowed;
  pointer-events: none;
}
app-settings-rule-edit .pad-1rem {
  padding: 1rem;
}

.form-label {
  font-weight: 0 !important;
}

app-settings-rule-edit .attention-note {
  font-weight: normal;
  font-size: 17px;
}

app-settings-rule-edit .modal-header-custom {
  width: 100%;
  display: flex;
  justify-content: center;
}

app-settings-rule-edit .close-icon-color {
  color: #794cff;
}

app-settings-rule-edit .modal-footer {
  padding: 0px !important;
  margin-top: -20px !important;
}

app-settings-rule-edit hr.qb {
  margin-top: 3rem;
}

app-settings-rule-edit .custom-message {
  
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 19px;
  color: #4e4e4e;
}

app-settings-rule-edit .pad-30 {
  margin-left: 30%;
}

app-settings-rule-edit .pad-20 {
  padding-left: 20%;
}

@media (min-width: 576px) {
  app-settings-rule-edit .modal-dialog {
    max-width: 580px;
  }
}

app-settings-rule-edit .pad-35 {
  margin-left: 35%;
}

app-settings-rule-edit .redBorder {
  border-color: red !important;
}

app-settings-rule-edit .spinner-border {
  display: block;
  position: fixed;
  top: calc(50% - (58px / 2));
  right: calc(40% - (58px / 2));
  width: 5rem;
  height: 5rem;
}

app-settings-rule-edit
  marketplace-file-upload
  .file__upload-holder
  .file__inputBox
  .fileUpload__container-close-button {
  top: 0px !important;
}

app-settings-rule-edit marketplace-tab .tab__container {
  border: solid 1px #eee;
  box-shadow: 0 4px 4px #0a121e29, 0 0 4px #0a121e29;
  padding: 5px 1rem;
}

app-settings-rule-edit
  marketplace-file-upload
  .file__upload-holder
  .file__inputBox
  button {
  display: none;
}

app-settings-rule-edit .backdrop {
  position: fixed;
  top: 11%;
  left: 20%;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  background-color: rgb(0, 0, 0, 0.2);
}

app-settings-rule-edit .red-font {
  color: red;
  padding: 0px 0px 14px 14px;
}

app-settings-rule-edit .fileDetailsExcelPopup marketplace-popup .modal .modal-dialog.modal-sm .modal-content{
    height: 60vh !important;
}


app-settings-rule-edit .duplicatePopup marketplace-popup .modal .modal-dialog.modal-sm .modal-content{
  height: 80vh !important;
}

app-settings-rule-edit marketplace-popup .modal .modal-dialog .modal-footer{
  margin-top: 2% !important;
}

app-settings-rule-edit .rolefooterPopup{
  margin-top: 40px ;
}

app-settings-rule-edit .multi-criteria-btn-align {
  position: absolute;
  right: 21px;
  top: 81px;
}
app-settings-rule-edit .chip-Container{
  margin-top: 1%;
  margin-bottom: 1%;
}

app-settings-rule-edit .chips-Added-Concepts {
  height: 24px;
  padding: 15px 15px 15px 15px;
  border-radius: 17px;
  grid-gap: 4px;
  gap: 4px;
  display: inline-flex;
  justify-content: center;
  background: #E6F6ED;
  color: #fff;
  cursor: pointer;
  align-items: center;
  max-width: 220px;
  overflow: hidden;
  margin: 2px;
}

app-settings-rule-edit .chips-deleted-concepts {
  height: 24px;
  padding: 15px 15px 15px 15px;
  border-radius: 17px;
  grid-gap: 4px;
  gap: 4px;
  display: inline-flex;
  justify-content: center;
  background: #FFF1F2;
  color: #fff;
  cursor: pointer;
  align-items: center;
  max-width: 220px;
  overflow: hidden;
  margin: 2px;
}

app-settings-rule-edit  .chips {
  height: 24px;
  padding: 15px 15px 15px 15px;
  border-radius: 17px;
  grid-gap: 4px;
  gap: 4px;
  display: inline-flex;
  justify-content: center;
  background: #bab9b9;
  color: #fff;
  cursor: pointer;
  align-items: center;
  max-width: 220px;
  overflow: hidden;
  margin: 2px;
}
app-settings-rule-edit .conceptAddOrRemove {
  font-size: 16px;
  color: #353535;
  font-family: 'elevance-semi-bold';
}
app-settings-rule-edit img#imgIcon {
  vertical-align: text-bottom;
  border-style: none;
  margin-right: 1%;
}      

app-settings-rule-edit .chips-text {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  font: Elevance Sans;
  text-align: left;
  color: #000000;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
app-settings-rule-edit .chips-text-delete {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  font: Elevance Sans;
  text-align: left;
  color: #BD161F;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
app-settings-rule-edit .chips-text-add {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  font: Elevance Sans;
  text-align: left;
  color: #005228;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
                        

app-settings-rule-edit .close-button {
  cursor: pointer;
  margin-left: 5px;
  color: #fff;
  font-weight: bold;
  height: 24px;
  border-radius: 17px;
  grid-gap: 4px;
  gap: 4px;
  display: inline-flex;
  justify-content: center;
  background: #bab9b9;
  color: #000000;
  cursor: pointer;
  align-items: center;
  max-width: 190px;
  overflow: hidden;
  margin: 2px;
}
                            
app-settings-rule-edit .multi-criteria-div-align {
  position: relative;
  align-self: end;
  padding: 10px 20px 0px 10px;
}

app-settings-rule-edit .mar-20 {
  margin-top: 20px;
}
app-settings-rule-edit #impactReportPopup .modal-footer {
  margin-bottom: 3% !important;
  margin-right: 2% !important;
}

app-settings-rule-edit #impactReportPopup .modal-body {
  min-height: 55px !important;
}
app-settings-rule-edit .bypassmessgae {
  font-family: 'elevance-medium';
}

.disableQueryBuilder {
  padding: 1rem;
  cursor: not-allowed;
  pointer-events: none;
}