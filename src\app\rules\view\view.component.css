app-view .container,
app-view .container-fluid,
app-view .container-lg,
app-view .container-md,
app-view .container-sm,
app-view .container-xl {
  width: 100%;
  padding-right: 5px;
  padding-left: 5px;
  margin-right: auto;
  margin-left: auto;
}
app-view .ruleDefinition {
	margin-top: 3%;
  width: 100%;
}

app-view .breadcrumb-viewheading {
  margin-top: -27px; 
  margin-bottom: 45px;
}

app-view .dashbord-title{
  padding: 5px 0px 0px 14px !important;
}

app-view .info {
  color: rgb(0, 0, 0);
  background-color: #fff;
  margin-top: -30px;
}
app-view .wrapper .search-input{
  margin-top: -13px;
  background-color: #fff;
  width: 100%;
}

app-view .DescriptionProvider{
  position: relative;
  top: 65%;
}

app-view .wrapper{
  max-width: 450px;
  margin: 47px auto;
}

.card {
  border: 3px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
}
.card .card-no-border {
  border: none !important;
  padding: 0px 25px 0px 25px;
}
.pd-15 {
  padding: 15px;
}

.query-builder-title {
  float: left;
  width: 200px;
  height: 34px;
  left: 195px;
  top: 384px;
  
  font-style: normal;
  font-weight: 500;
  font-size: 28px;
  line-height: 34px;
  color: #000000;
}
.btn-span {
  float: right;
  margin-top:8px;
}
.btn-criteria {
  background: #794cff;
  
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #ffffff;
}
.pd-bottom-15 {
  padding-bottom: 15px;
}
.pd-25 {
  padding: 25px 25px 25px 25px;
}
.custom-btn {
  padding: 0.375rem 3rem !important;
  margin-right: 20px;
}
.pd-5 {
  padding: 5px;
}
.level-indicator {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 10px 50px;
  left: 1228px;
  top: 130px;
  border-radius: 4px;
  padding: 10px, 50px, 10px, 50px;
  background: #fde2b9;
}

.card-title {
  
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 29px;
  color: #794cff;
  margin-left: 2px;
}
.tabs-padding {
  padding: 0px 25px 25px 0px !important;
}
.notification-title {
  
  font-style: normal;
  font-weight: bold;
  font-size: 18px;
  line-height: 22px;
  color: #161616;
  padding: 13px 0px 0px 15px;
}
.notification-font-wt {
  font-weight: 600 !important;
  padding: 13px 0px 0px 0px !important;
}

.custom-control-label {
  margin-left: 5%;
}
.custom-switch {
  padding-left: 1em;
}
.mar-10 {
  margin-top: 10px;
}
.modal-content {
  width: 140% !important;
  margin-top: 30% !important;
}
.custom-title {
  
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 29px;
  color: #794cff;
}
.custom-message {
  
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 19px;
  color: #4e4e4e;
}
.modal-header {
  justify-content: center !important;
}
app-view .pad-1rem {
  padding: 1.5rem;
  
}
app-view .pad-1rem-pointer
{
  padding: 1.5rem;
  cursor: not-allowed;
  pointer-events: none;
}


@media (min-width: 576px) {
  app-view .modal-dialog {
    max-width : 580px;
  }
}

app-view .modal-footer {
  padding: 0px !important;
  margin-top: -20px !important;
}

app-view .p-align {
  margin-bottom: -10px;
}

app-view .pad-20 {
  padding-left: 20%}

app-view .pad-30 {
  margin-left: 30%;
}

app-view .form-label {
  font-weight: 0 !important;
}


app-view hr.qb{
  margin-top: 3rem;
}

app-view .custom-message {
  
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 19px;
  color: #4e4e4e;
}

app-view .attention-note {
  font-weight: normal;
  font-size: 17px;
}

app-view .spinner-border {
  display: block;
  position: fixed;
  top: calc(50% - (58px / 2));
  right: calc(40% - (58px / 2));
  width: 5rem;
  height: 5rem;
}

app-view .custom-switch .custom-control-label::before {
  border-radius: 1rem !important;
  height: 1.5rem;
  width: 2.5rem;
}

app-view .custom-switch .custom-control-label::after {
  border-radius: 0.65rem;
  height: calc(1.5rem - 4px);
  width: calc(1.5rem - 4px);
}

app-view
  .custom-control-input:checked
  ~ .custom-control-label::after {
  transform: translateX(1rem) !important;
}

app-view .custom-switch .custom-control-input:disabled:checked~.custom-control-label::before {
  color: #fff;
  background: #7fbf89;
}

app-view .multi-criteria-btn-align {
  position: absolute;
  right: 29px;
  top: 81px;
}

app-view .chip-Container{
  margin-top: 1%;
  margin-bottom: 1%;
}

app-view  .chips {
  height: 24px;
  padding: 15px 15px 15px 15px;
  border-radius: 17px;
  grid-gap: 4px;
  gap: 4px;
  display: inline-flex;
  justify-content: center;
  background: #bab9b9;
  color: #fff;
  cursor: pointer;
  align-items: center;
  max-width: 220px;
  overflow: hidden;
  margin: 2px;
}
                    

app-view  .chips-text {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  font: Elevance Sans;
  text-align: left;
  color: #000000;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
                        

app-view .close-button {
  display: none !important;
}


app-view .multi-criteria-div-align {
  position: relative;
  align-self: end;
  padding: 10px 20px 0px 10px;
}

app-view .mar-20 {
  margin-top: 20px;
}

app-view .pad-1rem-pointer
{
  padding: 1.5rem;
  cursor: not-allowed;
  pointer-events: none;
}

app-view .row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: 6px;
  margin-left: 7px;
}


app-view .tab__container {
  border: solid 1px #eee;
  box-shadow: 0 4px 4px #0a121e29, 0 0 4px #0a121e29;
  padding: 5px 1rem;
}