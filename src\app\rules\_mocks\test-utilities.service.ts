import { ComponentFixture } from '@angular/core/testing';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { of, throwError } from 'rxjs';

export class TestUtilities {
  
  // Common test setup utilities
  static setupComponent<T>(fixture: ComponentFixture<T>): {
    component: T;
    element: HTMLElement;
    debugElement: DebugElement;
  } {
    const component = fixture.componentInstance;
    const element = fixture.nativeElement;
    const debugElement = fixture.debugElement;
    
    fixture.detectChanges();
    
    return { component, element, debugElement };
  }

  // Router testing utilities
  static createMockRouter(): jasmine.SpyObj<Router> {
    return jasmine.createSpyObj('Router', [
      'navigate',
      'navigateByUrl',
      'createUrlTree',
      'serializeUrl'
    ]);
  }

  static createMockLocation(): jasmine.SpyObj<Location> {
    return jasmine.createSpyObj('Location', [
      'back',
      'forward',
      'go',
      'replaceState',
      'getState'
    ]);
  }

  // Form testing utilities
  static fillFormField(fixture: ComponentFixture<any>, selector: string, value: string): void {
    const input = fixture.debugElement.query(By.css(selector));
    if (input) {
      input.nativeElement.value = value;
      input.nativeElement.dispatchEvent(new Event('input'));
      fixture.detectChanges();
    }
  }

  static clickButton(fixture: ComponentFixture<any>, selector: string): void {
    const button = fixture.debugElement.query(By.css(selector));
    if (button) {
      button.nativeElement.click();
      fixture.detectChanges();
    }
  }

  static selectDropdownOption(fixture: ComponentFixture<any>, selector: string, value: string): void {
    const select = fixture.debugElement.query(By.css(selector));
    if (select) {
      select.nativeElement.value = value;
      select.nativeElement.dispatchEvent(new Event('change'));
      fixture.detectChanges();
    }
  }

  // Service mock utilities
  static createMockRulesApiService(): jasmine.SpyObj<any> {
    const spy = jasmine.createSpyObj('RulesApiService', [
      'getListOfRules',
      'createEditRule',
      'deleteRule',
      'getMasterData',
      'getAllViewEditRuleAPIs',
      'getFileDetailsOfRules',
      'uploadFileAndQBCriteria',
      'getMultipleCriteriaFile',
      'addFilesToRules',
      'getRuleHistoryData',
      'getUserNameForClient',
      'getConceptExecutionByConceptState',
      'triggerPerformAnalysis',
      'getImpactReport',
      'getInventoryStatusData'
    ]);

    // Default return values
    spy.getListOfRules.and.returnValue(of({ rules: [] }));
    spy.createEditRule.and.returnValue(of({ success: true, rule_id: 123 }));
    spy.deleteRule.and.returnValue(of({ success: true }));
    spy.getMasterData.and.returnValue(of({ fields: [], operators: [] }));
    spy.getAllViewEditRuleAPIs.and.returnValue(of({ rule_data: {} }));
    spy.getFileDetailsOfRules.and.returnValue(of({ files: [] }));
    spy.uploadFileAndQBCriteria.and.returnValue(of({ success: true }));
    spy.getMultipleCriteriaFile.and.returnValue(of({ files: [] }));
    spy.addFilesToRules.and.returnValue(of({ success: true }));
    spy.getRuleHistoryData.and.returnValue(of({ history: [] }));
    spy.getUserNameForClient.and.returnValue(of({ users: [] }));
    spy.getConceptExecutionByConceptState.and.returnValue(of({ data: [] }));
    spy.triggerPerformAnalysis.and.returnValue(of({ success: true }));
    spy.getImpactReport.and.returnValue(of({ impact_data: {} }));
    spy.getInventoryStatusData.and.returnValue(of({ inventory: [] }));

    return spy;
  }

  // HTTP testing utilities
  static createMockHttpResponse(data: any, status: number = 200): any {
    return {
      status,
      statusText: status === 200 ? 'OK' : 'Error',
      body: data,
      ok: status >= 200 && status < 300,
      headers: new Map(),
      url: 'http://test.com/api'
    };
  }

  static createMockHttpError(status: number = 500, message: string = 'Server Error'): any {
    return {
      status,
      statusText: message,
      error: { message },
      name: 'HttpErrorResponse',
      ok: false
    };
  }

  // Observable testing utilities
  static createSuccessObservable<T>(data: T) {
    return of(data);
  }

  static createErrorObservable(error: any) {
    return throwError(error);
  }

  // DOM testing utilities
  static getElementByTestId(fixture: ComponentFixture<any>, testId: string): HTMLElement | null {
    return fixture.nativeElement.querySelector(`[data-testid="${testId}"]`);
  }

  static getAllElementsByTestId(fixture: ComponentFixture<any>, testId: string): HTMLElement[] {
    return Array.from(fixture.nativeElement.querySelectorAll(`[data-testid="${testId}"]`));
  }

  static getElementText(fixture: ComponentFixture<any>, selector: string): string {
    const element = fixture.debugElement.query(By.css(selector));
    return element ? element.nativeElement.textContent.trim() : '';
  }

  static isElementVisible(fixture: ComponentFixture<any>, selector: string): boolean {
    const element = fixture.debugElement.query(By.css(selector));
    return element && element.nativeElement.offsetParent !== null;
  }

  // Async testing utilities
  static async waitForAsync(fixture: ComponentFixture<any>, condition: () => boolean, timeout: number = 5000): Promise<void> {
    const startTime = Date.now();
    
    while (!condition() && (Date.now() - startTime) < timeout) {
      fixture.detectChanges();
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    
    if (!condition()) {
      throw new Error(`Condition not met within ${timeout}ms`);
    }
  }

  // Data validation utilities
  static validateFormData(formData: any, expectedFields: string[]): boolean {
    return expectedFields.every(field => formData.hasOwnProperty(field));
  }

  static validateApiResponse(response: any, requiredFields: string[]): boolean {
    return requiredFields.every(field => response.hasOwnProperty(field));
  }

  // Mock data generators
  static generateMockRule(overrides: any = {}): any {
    return {
      rule_id: 123,
      rule_name: 'Test Rule',
      rule_level: 'Global',
      status: 'Active',
      description: 'Test description',
      created_date: '2024-01-01T10:00:00Z',
      created_by: 'test-user',
      ...overrides
    };
  }

  static generateMockRuleList(count: number = 5, overrides: any = {}): any[] {
    return Array.from({ length: count }, (_, index) => 
      this.generateMockRule({ 
        rule_id: index + 1, 
        rule_name: `Test Rule ${index + 1}`,
        ...overrides 
      })
    );
  }

  static generateMockCriteria(overrides: any = {}): any {
    return {
      criteria_id: 1,
      field_name: 'test_field',
      operator: 'Equal',
      value: 'test_value',
      data_type: 'text',
      ...overrides
    };
  }

  // Error simulation utilities
  static simulateNetworkError(): any {
    return this.createMockHttpError(0, 'Network Error');
  }

  static simulateServerError(): any {
    return this.createMockHttpError(500, 'Internal Server Error');
  }

  static simulateUnauthorizedError(): any {
    return this.createMockHttpError(401, 'Unauthorized');
  }

  static simulateForbiddenError(): any {
    return this.createMockHttpError(403, 'Forbidden');
  }

  static simulateNotFoundError(): any {
    return this.createMockHttpError(404, 'Not Found');
  }

  // Component state testing utilities
  static setComponentProperty<T>(component: T, property: string, value: any): void {
    (component as any)[property] = value;
  }

  static getComponentProperty<T>(component: T, property: string): any {
    return (component as any)[property];
  }

  static callComponentMethod<T>(component: T, methodName: string, ...args: any[]): any {
    return (component as any)[methodName](...args);
  }

  // Loading state utilities
  static simulateLoadingState<T>(component: T, isLoading: boolean = true): void {
    this.setComponentProperty(component, 'loading', isLoading);
    this.setComponentProperty(component, 'isLoading', isLoading);
  }

  // Pagination testing utilities
  static createMockPaginationData(currentPage: number = 1, totalPages: number = 5): any {
    return {
      current_page: currentPage,
      total_pages: totalPages,
      page_size: 10,
      total_records: totalPages * 10,
      has_next: currentPage < totalPages,
      has_previous: currentPage > 1
    };
  }

  // Chart/Graph testing utilities
  static createMockChartData(): any {
    return {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
      datasets: [{
        label: 'Test Data',
        data: [10, 20, 30, 40, 50],
        backgroundColor: ['#ff6384', '#36a2eb', '#cc65fe', '#ffce56', '#4bc0c0']
      }]
    };
  }

  // File upload testing utilities
  static createMockFile(name: string = 'test.txt', content: string = 'test content', type: string = 'text/plain'): File {
    const blob = new Blob([content], { type });
    return new File([blob], name, { type });
  }

  static createMockFileList(files: File[]): FileList {
    const fileList = {
      length: files.length,
      item: (index: number) => files[index] || null,
      [Symbol.iterator]: function* () {
        for (let i = 0; i < files.length; i++) {
          yield files[i];
        }
      }
    };
    
    // Add files as indexed properties
    files.forEach((file, index) => {
      (fileList as any)[index] = file;
    });
    
    return fileList as FileList;
  }

  // Date/Time testing utilities
  static createMockDate(dateString: string = '2024-01-01T10:00:00Z'): Date {
    return new Date(dateString);
  }

  static formatMockDate(date: Date): string {
    return date.toISOString();
  }

  // Validation utilities
  static expectElementToExist(fixture: ComponentFixture<any>, selector: string): void {
    const element = fixture.debugElement.query(By.css(selector));
    expect(element).toBeTruthy();
  }

  static expectElementNotToExist(fixture: ComponentFixture<any>, selector: string): void {
    const element = fixture.debugElement.query(By.css(selector));
    expect(element).toBeFalsy();
  }

  static expectElementToHaveText(fixture: ComponentFixture<any>, selector: string, expectedText: string): void {
    const text = this.getElementText(fixture, selector);
    expect(text).toBe(expectedText);
  }

  static expectElementToContainText(fixture: ComponentFixture<any>, selector: string, expectedText: string): void {
    const text = this.getElementText(fixture, selector);
    expect(text).toContain(expectedText);
  }
}
