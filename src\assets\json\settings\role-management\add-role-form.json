{"BasicRoleDetails": [{"type": "text", "name": "<PERSON><PERSON><PERSON>", "label": "Role Name", "column": 1, "id": "<PERSON><PERSON><PERSON>", "disabled": false, "required": true, "value": "", "placeholder": "Enter Role Name"}, {"type": "textarea", "name": "description", "label": "Role Description", "column": 1, "id": "description", "disabled": false, "required": true, "value": "", "placeholder": "Enter description"}], "clientNames": [{"options": [], "optionName": "name", "optionValue": "id", "label": "Client", "type": "select", "multiple": false, "closeOnSelect": true, "name": "clientId", "column": "4", "disabled": false, "hidden": false, "required": true, "id": "client", "selectedVal": null, "value": "", "placeholder": "Select Client Here.."}], "productNames": [{"options": [], "optionName": "name", "optionValue": "id", "label": "Assign Product", "type": "select", "multiple": false, "closeOnSelect": true, "name": "productId", "required": true, "column": "4", "disabled": false, "hidden": false, "id": "product", "selectedVal": "", "value": "", "placeholder": "Select a Product Here..."}], "businessDivision": [{"options": [{"name": "CSBD", "value": "CSBD"}, {"name": "GBD", "value": "GBD"}], "optionName": "name", "optionValue": "value", "label": "Business Division", "type": "select", "multiple": false, "closeOnSelect": true, "name": "businessDivision", "required": true, "column": "4", "disabled": false, "hidden": false, "id": "businessDivision", "selectedVal": null, "value": "", "placeholder": "Select Business Division"}], "inventoryTypeDetails": [{"options": [], "optionName": "name", "optionValue": "id", "label": "Inventory Type", "type": "select", "multiple": false, "closeOnSelect": true, "name": "invType", "id": "invType", "column": "4", "disabled": false, "required": false, "selectedVal": "", "value": "", "placeholder": "Please Select an Inventory Type"}], "clientSiteJson": [{"id": "reminderDate", "label": "Reminder Date", "type": "date", "name": "reminderDate", "column": "4", "disabled": false, "required": true, "dateFormat": "MM-DD-YYYY", "value": "", "placeholder": "Please select Reminder Date", "pickerType": "single"}, {"label": "Client Site", "id": "clientSiteRow", "text": "Offshore", "type": "switch", "name": "clientSite", "column": "4", "preLabel": "Onshore", "disabled": false, "required": true, "selectedVal": "Onshore", "value": ""}, {"options": [{"name": "Internal", "value": "internal"}, {"name": "External", "value": "external"}], "id": "teamSelect", "optionName": "name", "optionValue": "value", "label": "Select Team", "type": "radio", "name": "teamType", "column": "4", "disabled": false, "required": true, "value": "", "customClass": "form-radio-button"}]}