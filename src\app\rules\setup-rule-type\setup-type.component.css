app-setup-type .title-container {
    margin: 0px 0px 15px 0px;
    clear: both;
    margin-left: 17px; 
    margin-top: -45px;
}

app-setup-type marketplace-button#addNewRuleSubType {
    float: right;
    padding: 15px 30px 0px 30px;
}
app-setup-type .add-new-rules-link{
    color: #FFFFFF;
    text-decoration: none;
}
app-breadcrumbs-nav {
    margin-left: 5px;
}
app-setup-type .quickaction-title {
    float: left;
    width: 200px;
    height: 24px;
    left: 195px;
    top: 148px;
    
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 24px;
    color: #2453A6;
    padding: 25px 0px 20px 30px;
}
app-setup-type .mb-3{
    margin-top: 1rem;
    margin-left: 1rem;
}

app-setup-type marketplace-table {
    clear: both;
}

app-setup-type .table-header-title{
    margin-top: 25px;
    margin-bottom: 0rem;
    font-size: larger;
    font-weight: bold;
}