import { IRequestContext } from './external-request-context';


export interface IExternalRegistration {
  newUserInfo: INewUserInfo;
  internalUserInfo: INewExtUserInfo;
  newUser: boolean;
}

export class INewUserInfo {
  username: string;
  password: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
  dataOfBirth!: string;
  secretQuestionAnswers: [];
  userRoleEnum: string;
  repositoryEnum: string;
  memberOf: [];
}

export class INewExtUserInfo {
  userId: string;
  userName: string;
  createdUserId: string;
  phoneNumber: string;
  emailId: string;
}