app-roles {
    .redBorder {
       border-color: red !important;
     }
     marketplace-table .btn{
       display: flex;
       align-items: center;
       justify-content: center;
       margin-top: -7px;
       font-size: 14px;
     }
     .btn-active {
       background: #D9F5F5;
       width: 100%;
       border: 1px solid #00BBBA;
     }
     .btn-inactive {
       background: #F5F5F5;
       width: 100%;
       border: 1px solid #231E33;
     }
     .btn-wrap-text {
       overflow: hidden;
       white-space: nowrap;
       display: inline-block;
       text-overflow: ellipsis;
     }
   .spinner-border {
       display: block;
       position: fixed;
       top: calc(50% - (58px / 2));
       right: calc(40% - (58px / 2));
       width: 5rem;
       height: 5rem;
   }
   .backdrop {
       position: fixed;
       width: 100vw;
       height: 100vh;
       z-index: 999;
       background-color: rgb(0, 0, 0, 0.2);
   }
   
   font-family: "elevance-medium";


   marketplace-pick-list .pick-list-holder .pickertoolbar .pickertoolbarrightcont {
       font-size: 12px;
   }

   .page-wrapper {
       padding: 1rem 1rem;

       .page-header {

           h3 {
               color: #5009B5;
               margin-top: 8px;
               margin-bottom: 1rem;
               font-family: 'elevance-medium';
           }
       }

       .btn-holder {
           display: flex;
           justify-content: flex-end;
       }

       .roles-audit-table_container {
           marketplace-table .permission-denied {
               color: #da1e28;
               font-size: 1rem;
               display: flex;
               align-items: center;
           }

           .permission-granted {
               color: #00a551;
               font-size: 1rem;
               display: flex;


           }

           .slickgrid-container .grid-canvas .slick-row {

               .slick-cell:nth-child(2),
               .slick-cell:nth-child(6) {
                   display: flex;
                   justify-content: center;
               }
           }
       }
   }

   marketplace-popup {
       .row-container{
           display:flex;
       }

       .first-form{
           width: 50%;
       }

       .second-form{
           width: 25%;
       }

       .third-form{
           width: 25%;
       }

       .modal-header {
           position: relative;
       }

       .status-success {
           background-color: #00a551;
           border: 1px solid #ffffff;
           color: #fff;
           padding: 0 8px;
           position: absolute;
           right: 3rem;
       }

       .skill-category-switch-view-box {
           position: relative;
           margin-bottom: 50px;
           .switch-view {
               position: absolute;
               right: 1rem;
               top: 1rem;
               z-index: 2;

               marketplace-switch {
                   .switch-holder {
                       flex-direction: row !important;
                       align-items: baseline !important;
                       justify-content: end !important;

                       span {
                           padding: 0 5px;
                       }
                   }
               }
           }
       }
       marketplace-dynamic-form .form-row #parentclient{
           width: 300px;
       }
       marketplace-dynamic-form .form-row #parentproduct{
           width: 300px;
       }
       marketplace-dynamic-form .form-row #parentbusinessDivision{
           width: 300px;
       }
       marketplace-dynamic-form .form-row #parentinvType{
           width: 300px;
       }
       marketplace-dynamic-form .form-row #parentreminderDate{
           width: 300px;
       }
       marketplace-dynamic-form .form-row #parentclientSiteRow{
           margin-left: 20em;
           width: 200px;
       }
       marketplace-dynamic-form .form-row #parentteamSelect{
           margin-left: 30em;
           width: 250px;
       }

       marketplace-dynamic-form {
           .form-radio-button {
               .custom-control-label {
                   margin-top: 5px;
               }
           }

           .form-group{
               marketplace-switch .switch-holder {
                   flex-direction: row;
                   float: left;
                   align-items: inherit;
                   
                   .switch {
                       margin-right: 1rem;
                       margin-left: 1rem;
                   }
               }
           }
       }


       .picklist-table-container {
           display: flex;
           justify-content: space-between;

           .picklist-box {
               width: 90%;

               marketplace-pick-list .pick-list-holder {

                   .pickertoolbar input[name=filtername] {
                       width: 75%;
                   }

                   .fa.fa-chevron-circle-left {
                       color: #fff;
                   }

                   label.custom-control.custom-checkbox {
                       margin: 0;
                   }
               }

           }

           .table-box {
               width: 10%;
               display: flex;
               align-items: center;
               justify-content: center;

           }
       }

       .label-title {
           color: #666666 !important;
           font-family: 'elevance-medium';
       }

       .label-value {
           font-weight: 500;
           font-size: 16px;
           color: #000000;
           padding-left: 8px;
           word-break: break-word;
           font-family: 'elevance';

       }

       .border-style {
           border-right: 1px solid #ccc;
       }
   }
}