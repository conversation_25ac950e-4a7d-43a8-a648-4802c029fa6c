[{"id": "1", "productName": "Product_1", "bundleName": "Bundle_1", "bundleDescription": "Sample Description of Bundle_1", "productDivision": "<PERSON><PERSON>", "standardFee": "10%", "feeMethod": "actualSavings", "feeType": "Recoupment", "status": "Active", "startDate": "2022-02-02", "endDate": "2025-02-02", "createdBy": "<PERSON><PERSON><PERSON>", "createdDate": "2022-02-02", "updatedBy": "<PERSON><PERSON>", "updatedDate": "2022-02-25"}, {"id": "2", "productName": "Product_2", "bundleName": "Bundle_2", "bundleDescription": "Sample Description of Bundle_2", "productDivision": "<PERSON><PERSON>", "standardFee": "10%", "feeMethod": "identifiedValue", "feeType": "Recoupment", "status": "Active", "startDate": "2022-02-02", "endDate": "2025-02-02", "createdBy": "<PERSON><PERSON><PERSON>", "createdDate": "2022-02-02", "updatedBy": "<PERSON>", "updatedDate": "2022-02-25"}, {"id": "3", "productName": "Product_3", "bundleName": "Bundle_3", "bundleDescription": "Sample Description of Bundle_3", "productDivision": "<PERSON><PERSON>", "standardFee": "10%", "feeMethod": "perMember", "feeType": "Recoupment", "status": "Inactive", "startDate": "2022-02-02", "endDate": "2025-02-02", "createdBy": "<PERSON><PERSON><PERSON>", "createdDate": "2022-02-02", "updatedBy": "<PERSON><PERSON>", "updatedDate": "2022-02-25"}, {"id": "4", "productName": "Product_4", "bundleName": "Bundle_4", "bundleDescription": "Sample Description of Bundle_4", "productDivision": "<PERSON><PERSON>", "standardFee": "10%", "feeMethod": "perClaim", "feeType": "Recoupment", "status": "Active", "startDate": "2022-02-02", "endDate": "2025-02-02", "createdBy": "<PERSON><PERSON>", "createdDate": "2022-02-02", "updatedBy": "<PERSON><PERSON>", "updatedDate": "2022-02-25"}, {"id": "5", "productName": "Product_5", "bundleName": "Bundle_5", "bundleDescription": "Sample Description of Bundle_5", "productDivision": "<PERSON><PERSON>", "standardFee": "10%", "feeMethod": "actualSavings", "feeType": "Recoupment", "status": "Active", "startDate": "2022-02-02", "endDate": "2025-02-02", "createdBy": "<PERSON><PERSON><PERSON>", "createdDate": "2022-02-02", "updatedBy": "<PERSON><PERSON>", "updatedDate": "2022-02-25"}]