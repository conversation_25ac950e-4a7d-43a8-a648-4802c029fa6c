import { Injectable } from '@angular/core';
import { of, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class MockDatasetService {
  
  // Mock dataset for template binding resolution
  public mockDataset = {
    ruleId: 'test-rule-123',
    ruleName: 'Test Rule Name',
    ruleLevel: 'Global',
    status: 'Active',
    description: 'Test rule description',
    createdBy: 'test-user',
    createdDate: '2024-01-01',
    modifiedBy: 'test-user',
    modifiedDate: '2024-01-15',
    version: 1,
    businessDivision: 'test-division',
    clientId: 'test-client-123',
    conceptId: 'test-concept-456'
  };

  // Mock rule data for components
  public mockRuleData = {
    rule_id: 123,
    rule_name: 'Mock Test Rule',
    rule_level: 'Global',
    rule_description: 'Mock rule for testing',
    status: 'Active',
    created_date: '2024-01-01T10:00:00Z',
    modified_date: '2024-01-15T15:30:00Z',
    created_by: 'test-user',
    modified_by: 'test-user',
    version_seq: 1,
    business_division: 'test-division',
    client_id: 'test-client',
    concept_id: 'test-concept',
    rule_criteria: [
      {
        field_name: 'test_field',
        operator: 'Equal',
        value: 'test_value',
        data_type: 'text'
      }
    ],
    rule_outcomes: [
      {
        outcome_type: 'action',
        outcome_value: 'approve',
        priority: 1
      }
    ]
  };

  // Mock master data
  public mockMasterData = {
    fields: [
      { field_name: 'customer_id', display_name: 'Customer ID', data_type: 'text' },
      { field_name: 'amount', display_name: 'Amount', data_type: 'numeric' },
      { field_name: 'date_created', display_name: 'Date Created', data_type: 'date' }
    ],
    operators: [
      { name: 'Equal', id: 'Equal' },
      { name: 'Not Equal', id: 'Not Equal' },
      { name: 'Greater Than', id: 'Greater Than' }
    ],
    clients: [
      { client_id: 'client1', client_name: 'Test Client 1' },
      { client_id: 'client2', client_name: 'Test Client 2' }
    ],
    rule_levels: ['Global', 'Client Level', 'Concept Level'],
    statuses: ['Draft', 'Active', 'Inactive', 'Archived']
  };

  // Mock impact report data
  public mockImpactData = {
    rule_id: 123,
    total_records: 10000,
    affected_records: 2500,
    impact_percentage: 25.0,
    analysis_date: '2024-01-20T12:00:00Z',
    impact_details: [
      { category: 'Approved', count: 1500, percentage: 60 },
      { category: 'Rejected', count: 800, percentage: 32 },
      { category: 'Pending', count: 200, percentage: 8 }
    ],
    chart_data: {
      labels: ['Approved', 'Rejected', 'Pending'],
      datasets: [{
        data: [1500, 800, 200],
        backgroundColor: ['#28a745', '#dc3545', '#ffc107']
      }]
    }
  };

  // Mock history data
  public mockHistoryData = [
    {
      rule_id: 123,
      action: 'Created',
      user: 'test-user1',
      timestamp: '2024-01-01T10:00:00Z',
      changes: { status: 'Draft' },
      version: 1
    },
    {
      rule_id: 123,
      action: 'Modified',
      user: 'test-user2',
      timestamp: '2024-01-02T11:00:00Z',
      changes: { status: 'Active', rule_name: 'Updated Rule Name' },
      version: 2
    },
    {
      rule_id: 123,
      action: 'Approved',
      user: 'test-approver',
      timestamp: '2024-01-03T09:00:00Z',
      changes: { status: 'Active' },
      version: 2
    }
  ];

  // Mock criteria data
  public mockCriteriaData = [
    {
      criteria_id: 1,
      criteria_name: 'Amount Check',
      field_name: 'amount',
      operator: 'Greater Than',
      value: '1000',
      data_type: 'numeric',
      is_frequently_used: true
    },
    {
      criteria_id: 2,
      criteria_name: 'Customer Type',
      field_name: 'customer_type',
      operator: 'Equal',
      value: 'Premium',
      data_type: 'text',
      is_frequently_used: true
    }
  ];

  // Mock file data
  public mockFileData = [
    {
      file_id: 1,
      file_name: 'test-criteria.xlsx',
      file_size: 1024,
      upload_date: '2024-01-01T10:00:00Z',
      uploaded_by: 'test-user',
      file_type: 'excel'
    },
    {
      file_id: 2,
      file_name: 'rule-documentation.pdf',
      file_size: 2048,
      upload_date: '2024-01-02T11:00:00Z',
      uploaded_by: 'test-user',
      file_type: 'pdf'
    }
  ];

  // Mock users data
  public mockUsersData = [
    { user_id: 'user1', user_name: 'Test User 1', email: '<EMAIL>' },
    { user_id: 'user2', user_name: 'Test User 2', email: '<EMAIL>' },
    { user_id: 'user3', user_name: 'Test User 3', email: '<EMAIL>' }
  ];

  // Methods to get mock data
  getMockDataset(): any {
    return this.mockDataset;
  }

  getMockRuleData(): Observable<any> {
    return of(this.mockRuleData);
  }

  getMockMasterData(): Observable<any> {
    return of(this.mockMasterData);
  }

  getMockImpactData(): Observable<any> {
    return of(this.mockImpactData);
  }

  getMockHistoryData(): Observable<any> {
    return of({ history: this.mockHistoryData });
  }

  getMockCriteriaData(): Observable<any> {
    return of(this.mockCriteriaData);
  }

  getMockFileData(): Observable<any> {
    return of(this.mockFileData);
  }

  getMockUsersData(): Observable<any> {
    return of(this.mockUsersData);
  }

  // Utility methods for testing
  createMockFormData(): FormData {
    const formData = new FormData();
    formData.append('file', new Blob(['test content'], { type: 'text/plain' }), 'test.txt');
    formData.append('rule_id', '123');
    formData.append('rule_level', 'Global');
    return formData;
  }

  createMockHttpResponse(data: any, status: number = 200): any {
    return {
      status,
      statusText: status === 200 ? 'OK' : 'Error',
      body: data,
      headers: new Map(),
      url: 'http://test.com/api'
    };
  }

  // Error simulation methods
  createMockError(status: number = 500, message: string = 'Server Error'): any {
    return {
      status,
      statusText: message,
      error: { message },
      name: 'HttpErrorResponse'
    };
  }

  // Dynamic mock data generation
  generateMockRuleList(count: number = 10): any[] {
    const rules = [];
    for (let i = 1; i <= count; i++) {
      rules.push({
        rule_id: i,
        rule_name: `Test Rule ${i}`,
        rule_level: i % 3 === 0 ? 'Global' : i % 2 === 0 ? 'Client Level' : 'Concept Level',
        status: i % 4 === 0 ? 'Inactive' : 'Active',
        created_date: `2024-01-${String(i).padStart(2, '0')}T10:00:00Z`,
        created_by: `user${i % 3 + 1}`
      });
    }
    return rules;
  }

  // Pagination mock
  getMockPaginatedData(page: number = 1, pageSize: number = 10): any {
    const allData = this.generateMockRuleList(100);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    return {
      data: allData.slice(startIndex, endIndex),
      pagination: {
        current_page: page,
        page_size: pageSize,
        total_records: allData.length,
        total_pages: Math.ceil(allData.length / pageSize)
      }
    };
  }
}
