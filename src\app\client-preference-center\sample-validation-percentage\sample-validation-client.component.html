<div class="sample-validation-container">
    <div class="top-row-headers">
        <div class="left">
            <div class="last-updates-by">
                Last Updated by: <span class="bold-name">{{ lastUpdatedBy }}</span>
            </div>
            <div class="updates-info">
                <img src='./assets/images/icons/InfoCircle.svg'>
                Updates apply to next scheduled run.
            </div>
        </div>
        <div class="right">
            <div class="past-updates-link" (click)="openPastUpdates()">
                <img src="./assets/images/icons/history.svg">
                Past Updates
            </div>
            <div class="update-button">
                <marketplace-button [label]="'Update'" [type]="'primary'" [enabled]="updateButtonReady"
                    (onclick)="clickUpdateButton($event)">
                </marketplace-button>
            </div>
        </div>
    </div>
    <div class="input-container">
        <div *ngIf="isClientAnthem" class="anthem-inputs">
            <marketplace-dynamic-form [isSubmitNeeded]="false" [formJSON]="sampleValGBDAnthemJSON"
                (onValueChanges)="onGBDPercentageChange($event)">
            </marketplace-dynamic-form>
            <marketplace-dynamic-form [isSubmitNeeded]="false" [formJSON]="sampleValCSBDAnthemJSON"
                (onValueChanges)="onCSBDPercentageChange($event)">
            </marketplace-dynamic-form>
        </div>
        <div *ngIf="!isClientAnthem" class="non-anthem-inputs">
            <marketplace-dynamic-form [isSubmitNeeded]="false" [formJSON]="sampleValJSON"
                (onValueChanges)="onNonAnthemPercentageChange($event)">
            </marketplace-dynamic-form>
        </div>
    </div>
</div>

<div *ngIf="showPastUpdatesPopUp" class="past-updates-modal">
    <marketplace-popup id="viewPastUpdates" [open]="openPastUpdates" [size]="large" (onClose)="closeViewUpdatesPopup()">
        <div mpui-modal-header>
            <div class="modal-updates-header">Past Updates</div>
        </div>
        <div mpui-modal-body>
            <hr class="line-past-updates" />
            <div class="card">
                <marketplace-table *ngIf="isPastUpdateTblReady" [isExcelExportNeeded]="true" [id]="'past-updates'"
                    [dataset]="pastUpdatesDataSet" [columnDefinitions]="columnConfigs" [isToggleColumnsNeeded]="false">
                </marketplace-table>
            </div>
        </div>
    </marketplace-popup>
</div>