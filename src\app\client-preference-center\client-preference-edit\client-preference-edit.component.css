app-client-preference-edit .container,
app-client-preference-edit .container-fluid,
app-client-preference-edit .container-lg,
app-client-preference-edit .container-md,
app-client-preference-edit .container-sm,
app-client-preference-edit .container-xl {
  width: 100%;
  padding-right: 5px;
  padding-left: 5px;
  margin-right: auto;
  margin-left: auto;
}
app-client-preference-edit .pd-left-30 {
  padding-left: 30px;
}
app-client-preference-edit .pd-righ-10 {
  padding-right: 10px;
}
app-client-preference-edit .footer-btns {
  display: flex;
  justify-content: right;
}
app-client-preference-edit .form-pad {
  padding: 5px 0px 15px 15px;
}

app-client-preference-edit .qb-mar-2 {
  margin-left: -2px;
}

app-client-preference-edit .card.card-no-border {
  padding-bottom: 0px !important;
}

app-client-preference-edit .custom-title {
  
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 29px;
  color: #5009B5;
}

app-client-preference-edit .modal-header-custom {
  width: 100%;
  display: flex;
  justify-content: center;
}

app-client-preference-edit .custom-message {
  
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 19px;
  color: #4e4e4e;
}

app-client-preference-edit .pad-30 {
  margin-left: 35%;
}

app-client-preference-edit .close-icon-color {
  color: #5009B5;
}

app-client-preference-edit .pd-30 {
  padding-bottom: 30px;
}

app-client-preference-edit .pd-25 {
  padding: 11px 25px 25px 25px;
}
app-client-preference-edit .custom-btn {
  padding: 0.375rem 3rem !important;
  margin-right: 20px;
}

app-client-preference-edit .btn-span {
  float: right;
  padding: 0px 30px 25px 30px;
}
app-client-preference-edit .dashbord-title {
  
  font-style: normal;
  font-weight: 900;
  font-size: 24px;
  line-height: 34px;
  color: #000000;
  padding: 0px;
}

app-client-preference-edit .fa-chevron-circle-left {
  font-size: 25px;
  color: #5009B5;
  margin-right: 5px;
}


app-client-preference-edit .rounded {
  border-radius: 1.25rem !important;
}

app-client-preference-edit .btn-span {
  float: right;
  padding: 25px 0px 0px 30px;
}

app-client-preference-edit .cardqb {
  border: 3px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
}

app-client-preference-edit .cardqbdisabled {
  border: 3px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
  pointer-events: none;
}
app-client-preference-edit .freq {
  width: 466px;
  margin: -15px;
  padding-left: 36px;
  height: 180px;
  position: absolute;
  box-shadow: rgb(0 0 0 / 35%) 0px 5px 15px;
}
app-client-preference-edit .disabled {
  pointer-events: none;
}

app-client-preference-edit .card {
  border: 0px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
}

app-client-preference-edit .dashbord-card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 2rem 1rem 2rem 1rem;
}

app-client-preference-edit.card-title {
  
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 17px;
  color: #161616;
}
app-client-preference-edit .pad-10 {
  padding-left: 10px;
}

app-client-preference-edit .timebtn {
  width: 193%;
  border: 1px solid #c4ceff;
  outline: none;
  border-radius: 5px;
  color: #818080;
  width: 173%;
  height: 36px;
  padding: 0.5rem;
}

app-client-preference-edit .col-6 {
  padding-left: 102px;
  width: 53%;
}
app-client-preference-edit .pd-13 {
  padding-top: 8px;
}

app-client-preference-edit .pdg-13 {
  padding-top: 13px;
}

app-client-preference-edit .displayNone {
  display: none;
}

app-client-preference-edit .spinner-border {
  display: block;
  position: fixed;
  top: calc(50% - (58px / 2));
  right: calc(40% - (58px / 2));
  width: 5rem;
  height: 5rem;
}

app-client-preference-edit .backdrop {
  position: fixed;
  top: 11%;
  left: 20%;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  background-color: rgb(0, 0, 0, 0.2);
}

app-client-preference-edit .form-repeater{
  width: 30%
}