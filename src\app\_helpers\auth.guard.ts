import { inject, Injectable } from "@angular/core";
import { ActivatedRouteSnapshot, CanActivateFn, RouterStateSnapshot } from "@angular/router";
import { CookieService } from "ngx-cookie-service";
import { AUTH_CONFIG } from "../_constants/app.constants";
import { AuthService } from "../_services/authentication.services";

@Injectable({ providedIn: 'root' })
export class PermissionService  {

    constructor(private authService: AuthService, private cookieService: CookieService){
    }

    canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean{
        let portalAccess = this.cookieService.get(AUTH_CONFIG.USER_TOKEN);
        if(portalAccess){
            this.authService.isLogin = true;
            return true;
        }
            

        return false;
    }
}

export const AuthGuard: CanActivateFn = (next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean =>{
    return inject(PermissionService).canActivate(next, state);
}