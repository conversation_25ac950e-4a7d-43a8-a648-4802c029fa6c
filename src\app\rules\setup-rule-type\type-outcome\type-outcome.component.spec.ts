import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { TypeOutcomeComponent } from './type-outcome.component';

describe('TypeOutcomeComponent', () => {
  let component: TypeOutcomeComponent;
  let fixture: ComponentFixture<TypeOutcomeComponent>;
  let mockRouter: any;

  beforeEach(async () => {
    mockRouter = { navigate: jasmine.createSpy('navigate') };
    await TestBed.configureTestingModule({
      declarations: [ TypeOutcomeComponent ],
      providers: [ { provide: Router, useValue: mockRouter } ],
      schemas: [ CUSTOM_ELEMENTS_SCHEMA ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TypeOutcomeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should have default properties set correctly', () => {
      expect(component.headerText).toBe('Add New Rule Type');
      expect(component.isPriviousRedirectPage).toBeTrue();
      expect(component.dataURL).toBe('./assets/json/tableOutcome.json');
      expect(component.dataRoot).toBe('src');
    });

    it('should initialize with ngOnInit without errors', () => {
      expect(() => component.ngOnInit()).not.toThrow();
    });
  });

  describe('Column Configuration Validation', () => {
    it('should have valid columnConfigInlineEdit structure', () => {
      expect(component.columnConfigInlineEdit).toBeDefined();
      expect(component.columnConfigInlineEdit.switches).toBeDefined();
      expect(Array.isArray(component.columnConfigInlineEdit.colDefs)).toBeTrue();
    });

    it('should have correct switches configuration', () => {
      const switches = component.columnConfigInlineEdit.switches;
      expect(switches.enableSorting).toBeTrue();
      expect(switches.enablePagination).toBeTrue();
      expect(switches.editable).toBeTrue();
      expect(switches.enableFiltering).toBeTrue();
    });

    it('should have correct number of column definitions', () => {
      expect(component.columnConfigInlineEdit.colDefs.length).toBe(3);
    });

    it('should have valid first column definition', () => {
      const firstCol = component.columnConfigInlineEdit.colDefs[0];
      expect(firstCol.name).toBe('Field Name');
      expect(firstCol.field).toBe('Org Name');
      expect(firstCol.filterType).toBe('Text');
      expect(firstCol.visible).toBe('True');
      expect(firstCol.editorType).toBe('Single Select');
      expect(firstCol.editorTypeRoot).toBe('duplicateTitleSrc');
      expect(firstCol.editorTypeLabel).toBe('label');
      expect(firstCol.editorTypeValue).toBe('id');
      expect(firstCol.id).toBe('dup-name');
    });

    it('should have valid second column definition with custom formatter', () => {
      const secondCol = component.columnConfigInlineEdit.colDefs[1];
      expect(secondCol.name).toBe('Static');
      expect(secondCol.field).toBe('title');
      expect(secondCol.filterType).toBe('Text');
      expect(secondCol.visible).toBe('True');
      expect(secondCol.editorType).toBe('');
      expect(secondCol.customFormatter).toBe(component.customFormatterSwitch);
    });

    it('should have valid third column definition', () => {
      const thirdCol = component.columnConfigInlineEdit.colDefs[2];
      expect(thirdCol.name).toBe('Value');
      expect(thirdCol.field).toBe('percentComplete');
      expect(thirdCol.filterType).toBe('Text');
      expect(thirdCol.visible).toBe('True');
      expect(thirdCol.editorType).toBe('Text');
      expect(thirdCol.editorTypeRoot).toBe('');
      expect(thirdCol.editorTypeLabel).toBe('');
      expect(thirdCol.editorTypeValue).toBe('');
    });
  });

  describe('Column Configuration Validation', () => {
    it('should have valid columnConfigInlineEdit structure', () => {
      expect(component.columnConfigInlineEdit).toBeDefined();
      expect(component.columnConfigInlineEdit.switches).toBeDefined();
      expect(Array.isArray(component.columnConfigInlineEdit.colDefs)).toBeTrue();
      expect(component.columnConfigInlineEdit.colDefs.length).toBe(3);
    });

    it('should have correct switches configuration', () => {
      const switches = component.columnConfigInlineEdit.switches;
      expect(switches.enableSorting).toBeTrue();
      expect(switches.enablePagination).toBeTrue();
      expect(switches.editable).toBeTrue();
      expect(switches.enableFiltering).toBeTrue();
    });

    it('should have valid column definitions with required properties', () => {
      component.columnConfigInlineEdit.colDefs.forEach((col: any) => {
        expect(col.name).toBeDefined();
        expect(col.field).toBeDefined();
        expect(col.filterType).toBeDefined();
        expect(col.visible).toBeDefined();
        expect(typeof col.name).toBe('string');
        expect(typeof col.field).toBe('string');
      });
    });
  });

  describe('rendererTableClicked Method', () => {
    it('should not throw when called with valid event', () => {
      const mockEvent = { target: { value: 'test' } } as any;
      expect(() => component.rendererTableClicked(mockEvent)).not.toThrow();
    });

    it('should not throw when called with empty event', () => {
      expect(() => component.rendererTableClicked({} as any)).not.toThrow();
    });

    it('should handle null event gracefully', () => {
      expect(() => component.rendererTableClicked(null as any)).not.toThrow();
    });
  });

  describe('customFormatterSwitch Method', () => {
    it('should return toggle switch HTML string', () => {
      const result = component.customFormatterSwitch({});
      expect(typeof result).toBe('string');
      expect(result).toContain('custom-switch');
      expect(result).toContain('custom-control');
      expect(result).toContain('checkbox');
      expect(result).toContain('customSwitches');
    });

    it('should return consistent HTML structure', () => {
      const result1 = component.customFormatterSwitch({});
      const result2 = component.customFormatterSwitch({ data: 'test' });
      expect(result1).toBe(result2); // Should return same HTML regardless of input
    });

    it('should handle different event types', () => {
      expect(() => component.customFormatterSwitch(null)).not.toThrow();
      expect(() => component.customFormatterSwitch(undefined)).not.toThrow();
      expect(() => component.customFormatterSwitch('string')).not.toThrow();
    });
  });

  describe('tableReady Method', () => {
    it('should log event to console with correct message', () => {
      spyOn(console, 'log');
      const testEvent = { data: 'test-data' };
      component.tableReady(testEvent);
      expect(console.log).toHaveBeenCalledWith('Captured Table Ready event:::', testEvent);
    });

    it('should handle different event types', () => {
      spyOn(console, 'log');

      component.tableReady('string-event');
      expect(console.log).toHaveBeenCalledWith('Captured Table Ready event:::', 'string-event');

      component.tableReady(null);
      expect(console.log).toHaveBeenCalledWith('Captured Table Ready event:::', null);

      component.tableReady({ complex: { nested: 'object' } });
      expect(console.log).toHaveBeenCalledWith('Captured Table Ready event:::', { complex: { nested: 'object' } });
    });
  });

  describe('Navigation Methods', () => {
    beforeEach(() => {
      mockRouter.navigate.calls.reset();
    });

    describe('OnSubmit', () => {
      it('should navigate to rule-type page', () => {
        component.OnSubmit();
        expect(mockRouter.navigate).toHaveBeenCalledWith(['product-catalog/rules/rule-type']);
        expect(mockRouter.navigate).toHaveBeenCalledTimes(1);
      });
    });

    describe('OnCancel', () => {
      it('should navigate to rule-type details page', () => {
        component.OnCancel();
        expect(mockRouter.navigate).toHaveBeenCalledWith(['product-catalog/rules/rule-type/details']);
        expect(mockRouter.navigate).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete workflow', () => {
      // Test table ready
      spyOn(console, 'log');
      component.tableReady({ ready: true });
      expect(console.log).toHaveBeenCalled();

      // Test custom formatter
      const htmlResult = component.customFormatterSwitch({});
      expect(htmlResult).toContain('custom-switch');

      // Test table click
      expect(() => component.rendererTableClicked({} as any)).not.toThrow();

      // Test navigation
      mockRouter.navigate.calls.reset();
      component.OnSubmit();
      component.OnCancel();
      expect(mockRouter.navigate).toHaveBeenCalledTimes(2);
    });

    it('should maintain component state throughout interactions', () => {
      expect(component.headerText).toBe('Add New Rule Type');
      expect(component.isPriviousRedirectPage).toBeTrue();

      // Interact with methods
      component.tableReady({});
      component.customFormatterSwitch({});
      component.rendererTableClicked({} as any);

      // State should remain unchanged
      expect(component.headerText).toBe('Add New Rule Type');
      expect(component.isPriviousRedirectPage).toBeTrue();
      expect(component.dataURL).toBe('./assets/json/tableOutcome.json');
      expect(component.dataRoot).toBe('src');
    });
  });
});
