{"type": "group", "name": "groupSkillJson", "label": "Skills", "column": 1, "groupControls": [{"optionName": "label", "optionValue": "value", "label": "Market Skills", "type": "select", "multiple": true, "closeOnSelect": false, "name": "Market Skill", "placeholder": "Select One or More Skills Here...", "column": "2", "id": "marketSkills", "disabled": false, "isSelectAllRequired": true, "selectAllLabel": "Select All", "deselectAllLabel": "Clear", "options": [{"label": "Alabama", "value": 1}, {"label": "Alaska", "value": 2}, {"label": "American Samoa", "value": 3}, {"label": "Arizona", "value": 4}, {"label": "Arkansas", "value": 5}, {"label": "California", "value": 6}, {"label": "Colorado", "value": 7}, {"label": "Connecticut", "value": 8}, {"label": "Delaware", "value": 9}, {"label": "District Of Columbia", "value": 11}]}, {"optionName": "label", "optionValue": "value", "label": "Claim System", "type": "select", "multiple": true, "closeOnSelect": false, "name": "Claim System", "placeholder": "Select One or More Skills Here...", "column": "2", "id": "claimSystem", "disabled": false, "isSelectAllRequired": true, "selectAllLabel": "Select All", "deselectAllLabel": "Clear", "options": [{"label": "C11-S288-101-A-X1", "value": 1}, {"label": "C11-S284-101-A-X1", "value": 2}, {"label": "C6-S28-101-A-X1", "value": 3}, {"label": "C6-S266-101-A-X1", "value": 4}, {"label": "C20-S220-101-A-X1", "value": 5}]}, {"optionName": "label", "optionValue": "value", "label": "Funding Type", "type": "select", "multiple": true, "closeOnSelect": false, "name": "Funding Type", "placeholder": "Select One or More Skills Here...", "column": "2", "id": "fundingType", "disabled": false, "isSelectAllRequired": true, "selectAllLabel": "Select All", "deselectAllLabel": "Clear", "options": [{"label": "ASO", "value": 1}, {"label": "All Other Funding Model", "value": 2}, {"label": "FI", "value": 8}]}, {"optionName": "label", "optionValue": "value", "label": "LOB Skills", "type": "select", "multiple": true, "closeOnSelect": false, "name": "<PERSON><PERSON>", "placeholder": "Select One or More Skills Here...", "column": "2", "id": "lOBSkills", "disabled": false, "isSelectAllRequired": true, "selectAllLabel": "Select All", "deselectAllLabel": "Clear", "options": [{"label": "Medicare", "value": 1}, {"label": "Medicaid", "value": 2}, {"label": "Commercial", "value": 3}]}, {"optionName": "label", "optionValue": "value", "label": "Service Type", "type": "select", "multiple": true, "closeOnSelect": false, "name": "Service Type", "placeholder": "Select One or More Skills Here...", "column": "2", "id": "serviceType", "disabled": false, "isSelectAllRequired": true, "selectAllLabel": "Select All", "deselectAllLabel": "Clear", "options": [{"label": "Medicare", "value": 1}, {"label": "Medicaid", "value": 2}, {"label": "Commercial", "value": 10}]}, {"optionName": "label", "optionValue": "value", "label": "Concept Category", "type": "select", "multiple": true, "closeOnSelect": false, "name": "Concept Category", "placeholder": "Select One or More Skills Here...", "column": "2", "id": "conceptCategory", "disabled": false, "isSelectAllRequired": true, "selectAllLabel": "Select All", "deselectAllLabel": "Clear", "options": [{"label": "C11-S288-101-A-X1", "value": 1}, {"label": "C11-S284-101-A-X1", "value": 2}, {"label": "C6-S28-101-A-X1", "value": 3}, {"label": "C6-S266-101-A-X1", "value": 4}, {"label": "C20-S220-101-A-X1", "value": 5}]}, {"optionName": "label", "optionValue": "value", "label": "Group Number", "type": "select", "multiple": true, "closeOnSelect": false, "name": "Group Number", "placeholder": "Select One or More Skills Here...", "column": "2", "id": "groupNumber", "disabled": false, "deselectAllLabel": "Clear", "options": []}, {"type": "select", "name": "Blue Card Access", "label": "Blue Card Access", "options": [], "optionName": "label", "optionValue": "value", "column": 2, "closeOnSelect": false, "multiple": true, "customTags": true, "id": "blueCardId", "disabled": false, "deselectAllLabel": "Clear", "placeholder": "Select Blue Card Access"}, {"optionName": "label", "optionValue": "value", "label": "Skill", "type": "select", "multiple": true, "closeOnSelect": false, "name": "Skill", "placeholder": "Select a Skill", "column": "2", "id": "cobSkill", "disabled": false, "isSelectAllRequired": false, "selectAllLabel": "All", "deselectAllLabel": "Clear", "options": [{"label": "HEW", "value": 1}]}, {"optionName": "label", "optionValue": "value", "label": "Sub Skill", "type": "select", "multiple": true, "closeOnSelect": false, "name": "Subskill", "placeholder": "Select Sub skills", "column": "2", "id": "cobSubSkill", "disabled": false, "isSelectAllRequired": false, "selectAllLabel": "All", "deselectAllLabel": "Clear", "visible": false, "options": [{"label": "ALL", "value": 1}, {"label": "ESRD", "value": 2}, {"label": "Age", "value": 3}, {"label": "Disability", "value": 4}]}, {"type": "number", "label": "<PERSON> Assignment", "name": "<PERSON>", "column": "2", "id": "maxClaimAssignment", "placeholder": "Enter a <PERSON> Assignment", "selectedVal": [1003], "visible": true}, {"optionName": "label", "optionValue": "value", "label": "Concept State", "type": "select", "multiple": true, "closeOnSelect": true, "name": "Concept State", "placeholder": "Select Concept State", "column": "2", "id": "conceptStateSelect", "disabled": false, "selectedVal": "", "isSelectAllRequired": true, "selectAllLabel": "Select All", "deselectAllLabel": "Clear", "visible": true, "options": []}, {"optionName": "label", "optionValue": "value", "label": "Member Brand", "type": "select", "multiple": true, "closeOnSelect": false, "name": "Member Brand", "placeholder": "Select One or More Skills Here...", "column": "2", "id": "member<PERSON><PERSON>", "disabled": true, "visible": false, "isSelectAllRequired": true, "selectAllLabel": "Select All", "deselectAllLabel": "Clear", "options": []}, {"optionName": "label", "optionValue": "value", "label": "Service Provider Region", "type": "select", "multiple": true, "closeOnSelect": false, "name": "Service Provider Region", "placeholder": "Select One or More Skills Here...", "column": "2", "id": "serviceProviderRegion", "disabled": true, "visible": false, "isSelectAllRequired": true, "selectAllLabel": "Select All", "deselectAllLabel": "Clear", "options": []}], "border": true}