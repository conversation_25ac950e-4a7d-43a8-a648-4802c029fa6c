app-sample-validation-client {

    .sample-validation-container {
        height: calc(100vh - 19rem);
        padding: 11px 0;

        .top-row-headers {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
    
            .left {
                display: flex;
                flex-direction: row;
                align-items: center;
                margin-left: 5px;
            }
    
            .right {
                display: flex;
                flex-direction: row;
                align-items: center;
            }
        }
    }
    .past-updates-modal {
        .modal-title {
            color: #353535 !important;
            font-size: 24px;
            font-family: elevance-medium;
    
        }
        .modal-updates-header {
            font-family: var(--font-bold) !important;
            color: #353535 !important;
            font-size: 24px !important;
        }
    
        .modal-body{
            margin-top: -20px;
        }

        .modal-footer{
            margin-left: 0px;
        }
        
        .modal-header,.modal-body,.modal-footer {
            background-color: #f6f6f6 !important;
        }

        .card{
            padding-left: 25px;
            border-radius: 0.5rem;
        }

    }

    .top-button-holder-prod{
        display: flex;
        justify-content: end;  
        margin-top: 10px;     
        margin-right: 20px;         
    }
    
    marketplace-table .table-container .table-container-section .table-container__icons .table-controls {
        margin-top: 2px !important;
        margin-right: 20px; 
    }
    
   
    .last-updates-by {
        font-size: 13px;
        font-family: 'elevance';
        font-weight: 500;
        color: #353535;

        .bold-name {
            font-size: 13px;
            font-family: 'elevance';
            font-weight: 700;
            color: #353535;
        }
    }

    .updates-info {
        margin-left: 1rem;
        display: flex;
        align-items: center;
        color: #1C4DA0;
        font-family: 'elevance';
        font-size: 15px;
        font-weight: 500;
        background-color: #E1EDFF;
        padding: 2px 8px 2px 8px;
        border-radius: 17px;
        gap: 4px;
    }


    .past-updates-link {
        color: #286CE2;
        font-family: 'elevance';
        font-size: 15px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 4px;
        cursor: pointer;
    }

    .update-button {
        margin-left: 1rem;
    }

    .non-anthem-inputs {
        margin-top: 0.75rem;
    }

    .anthem-inputs {
        margin-top: 0.75rem;
    }
}
