export class UserProfile {
    responseCode: number
    responseName: string;
    responseData: ResponseData;
}

class ResponseData {
    userId: string;
    userNm: string;
    internalFlag: boolean;
    clntRoleDTOList: ClientRoleDTO[];
    clntDTOList: ClientDTO[];
}

class ClientRoleDTO {
    clntId: number;
    clntNm: string;
    roleDTO: RoleDTO;
}

export class RoleDTO {
    roleId: number;
    roleName: string;
    priority: string;
}

class ClientDTO {
    clntId: number;
    clntNm: string;
}