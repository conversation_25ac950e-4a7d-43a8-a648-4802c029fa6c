<div class="fixed-nav bg-gray" [hidden]="!isListing">
    <!-- <marketplace-breadcrumb [dataset]="breadcrumbDataset" (onSelection)="breadcrumSelection($event)">
    </marketplace-breadcrumb> -->
    <div class="card-body">
        <div class="clientsHeader">
            <span>Tenant List</span>
            <span class='.btn-spanpreference' style="float: right;">
                <marketplace-button [label]="'Add Tenant'" [type]="'primary'" [name]="'primary'"
                    (onclick)="createTenant()">
                </marketplace-button>
            </span>
        </div>
        <marketplace-table [id]="'view-client-table'" [dataset]="dataset" *ngIf="dataset"
            [columnDefinitions]="tenantListColumnConfig" [isRowSelectable]="false" [dropdownOptions]="kebabOptions"
            (onDropdownOptionsClick)="moveToClientTenant($event)">
        </marketplace-table>
    </div>
</div>

<app-add-tenant *ngIf="enablePage =='Add'" (DataEvent)="receiveFromAddEdit($event)">
</app-add-tenant>
<app-edit-tenant *ngIf="enablePage == 'Edit'" [editDataFromList]=selectedRowData
    (DataEvent)="receiveFromAddEdit($event)"></app-edit-tenant>
<app-view-tenant *ngIf="enablePage== 'View'" [editDataFromList]=selectedRowData
    (DataEvent)="receiveFromAddEdit($event)"></app-view-tenant>