app-product-list .btn-wrap-text {
  overflow: hidden;
  white-space: nowrap;
  display: inline-block;
  text-overflow: ellipsis;
}
app-product-list .table-container{
  clear: both;
  margin-left: 6px;
}
app-product-list .table-title {
  float: left;
  width: 200px;
  left: 195px;
  top: 384px;
  
  font-style: normal;
  font-weight: bold;
  font-size: 24px;
  line-height: 34px;
  color: #000000;
  padding: 20px 0px 0px 15px;
}
app-product-list .btn-span {
  float: right;
  padding: 25px 30px 0px 30px;
}
app-product-list .btn-ruleadd {
  background: #5009B5;
  
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #ffffff;
}
app-product-list .add-new-rules-link {
  color: #ffffff;
  text-decoration: none;
}
app-product-list .quickaction-title {
  float: left;
  width: 200px;
  height: 24px;
  left: 195px;
  top: 148px;
  
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #2453a6;
  padding: 25px 0px 20px 30px;
}
app-product-list .mb-3 {
  margin-top: 1rem;
  margin-left: 1rem;
}
app-product-list .tp-bt-rem-1 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
app-product-list .dashbord-card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 2rem 1rem 2rem 1rem;
}
app-product-list .fa-caret-right {
  font-size: 31px;
  color: #5009B5;
  float: right;
  padding-right: 20px;
}
app-product-list .card-title {
  
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 17px;
  color: #161616;
}
app-product-list .setup-rule {
  margin-top: 5px;
}

app-product-list .fa-list,
app-product-list .fa-chevron-circle-left {
  font-size: 30px;
  color: #5009B5;
}
app-product-list .fa-plus:before {
  color: #ffffff;
}
/* app-product-list .pd-left-30 {
  padding-left: 30px;
} */
app-product-list .pd-righ-10 {
  padding-right: 10px;
}
app-product-list .pd-righ-20 {
  padding-right: 20px;
}

/*Rules dashboard Table action menu dropdown*/
app-product-list .dropdown {
  position: absolute;
  background-color: gray;
  padding: 5px;
  outline: none;
  opacity: 0;
  min-width: 100%;
  overflow: auto;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  background: #ffffff;
  box-shadow: 0px 0px 20px rgb(0 0 0 / 40%);
  border-radius: 4px;
  right: 10px;
}
app-product-list .dropdown a {
  color: black;
  padding: 5px 10px;
  text-decoration: none;
  display: block;
}
app-product-list input:not(:checked) ~ .dropdown {
  display: none;
}
app-product-list input:checked ~ .dropdown {
  opacity: 1;
  z-index: 100;
  transition: opacity 0.2s;
  z-index: 1;
}
app-product-list .three-dots:after {
  cursor: pointer;
  color: #444;
  content: "\2807";
  font-size: 20px;
  z-index: 999;
}
app-product-list .table-action-menu .fa-eye,
app-product-list .table-action-menu .fa-edit,
app-product-list .table-action-menu .fa-trash,
app-product-list .table-action-menu .fa-plus {
  font-size: 20px;
  color: #5009B5 !important;
  padding-right: 15px;
}
app-product-list .table-action-menu .fa-plus:before {
  color: #5009B5 !important;
}
app-product-list .table-action-menu {
  border: 8.5px solid #ffffff;
}
app-product-list .fa-plus {
  width: 35px;
}

app-product-list .btn.product-dashboard {
  color: white;
  font-weight: 350;
}
app-product-list .btn.focus.product-dashboard,
app-product-list .btn:focus.product-dashboard {
  outline: 0;
  box-shadow: 0 0 0 0rem;
}
app-product-list .btn.product-dashboard-big {
  padding: 0.075rem 0.1rem !important;
  color: white;
  font-weight: 200;
}
app-product-list .btn.focus.product-dashboard-big,
app-product-list .btn:focus.product-dashboard-big {
  outline: 0;
  box-shadow: 0 0 0 0rem;
}
app-product-list .dropdown-container {
  padding-top: 5px;
}
app-product-list .search-filter .operator.input-group-addon {
  display: none !important;
}

app-product-list .btn-active {
  background: #D9F5F5;
  width: 100%;
  border: 1px solid #00BBBA;
}
app-product-list .btn-pending {
  background: #E1EDFF;
  width: 100%;
  border: 1px solid #44B8F3;
}
app-product-list .btn-inactive {
  background: #F5F5F5;
  width: 100%;
  border: 1px solid #231E33;
}
app-product-list .btn.product-list {
  color: white;
  font-weight: 350;
}
app-product-list .bundles-text-field {
  color: #2d6fe1;
  margin-right: 50px;
}
app-product-list .table-note {
  position: absolute;
  margin: 0px 10px;
}



app-product-list .inventory-overlay-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: grid;
  justify-content: flex-end;
  backdrop-filter: brightness(0.5);
}

app-product-list .inventory-overlay-wrapper .inventory-overlay {
      height: 100vh;
      width: 400px;
      background-color: #fff;
      box-shadow: 0px 0px 32px rgb(0 0 0);
      border-radius: 16px 0px 0px 16px;
      overflow-y: auto;
  }

app-product-list .inventory-overlay-wrapper .inventory-overlay-header {
      margin: 0.8rem;
      display: flex;
      gap: 1rem;
  }

app-product-list .inventory-overlay-wrapper .inventory-overlay-header-title {
      color: #383e93;
  }

app-product-list .inventory-overlay-close-icon {
  color: #383e93;
  background-color: #E8EBFF;
  border-radius: 50%;
  outline: none;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 25px;
  height: 25px;
}

app-product-list .btn-inventory,
app-product-list .btn-inventory:hover {
  background: #5009b5;
  font-family: 'elevance';
  font-weight: 500;
  font-size: 15px;
  line-height: 17px;
  color: #fff;
  width: 100px;
  outline: 0;
  box-shadow: 0 0 0 0rem;
}


app-product-list .inventory-options-wrapper {
  min-height: 80vh;
  justify-content: space-between;
  border: none !important;
}

 

app-product-list .inventory-options-wrapper .inventory-options-footer {
      display: flex;
      justify-content: flex-end;
      margin: 0px 11px 10px 0px;
      gap: 10px;
}

app-product-list .inventory-options-wrapper .inventory-options-footer .btn-submit {
          background: #383e93;
          color: #fff;
}

app-product-list .inventory-options-wrapper .inventory-options-footer .btn-cancel {
          border-color: #383e93;
          color: #383e93;
}

app-product-list .inventory-options-wrapper .inventory-options-footer .btn-cancel:hover {
          background: none;
}

app-product-list .inventory-options-wrapper .inventory-options-footer .form-row.form-element-group {
          border: none !important;
}

