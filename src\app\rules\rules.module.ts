import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

import { RulesRoutingModule } from './rules-routing.module';
import { DashboardComponent } from './dashboard/dashboard.component';
import { CreateComponent } from './create/create.component';
import { EditComponent } from './edit/edit.component';
import { BreadcrumbsNavComponent } from './shared/breadcrumbs-nav/breadcrumbs-nav.component';
import { ViewComponent } from './view/view.component';
import { MPUITableModule } from 'marketplace-table';

import { MPUINotificationModule } from 'marketplace-notification';
import { MPUIModalDialogModule } from 'marketplace-modal';
import { MPUIButtonModule } from 'marketplace-button';
import { MPUIFileParserModule } from 'marketplace-file-parser';
import { MPUIProgressBarModule } from 'marketplace-progressbar';


import { MPUIQueryBuilderModule } from 'marketplace-query-builder';
import { MPUIFileUploadModule } from 'marketplace-file-upload';
import { MPUICardsModule } from 'marketplace-cards';
import { MPUITabsModule } from 'marketplace-tabs';
import { MPUISelectModule } from 'marketplace-select';
import { MPUIBreadcrumbModule } from 'marketplace-breadcrumb';
import { FrequentlyUsedCriteriaComponent } from './frequently-used-criteria/frequently-used-criteria.component';
import { CreateNewCriteriaComponent } from './create-new-criteria/create-new-criteria.component';
import { SetupTypeComponent } from './setup-rule-type/setup-type.component';
import { TypeDetailsComponent } from './setup-rule-type/type-details/type-details.component';
import { TypeOutcomeComponent } from './setup-rule-type/type-outcome/type-outcome.component';
import { MPUIStepperModule } from 'marketplace-stepper';
import { MPUIPanelGroup } from 'marketplace-accordion';
import { MPUIInputModule } from 'marketplace-input';
import { MPUIDatePickerModule } from 'marketplace-date-picker';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { MPUITextareaModule } from 'marketplace-textarea';
import { MPUISwitchModule } from 'marketplace-switch';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { CommonHeader } from 'src/app/_helpers/commonheader.interceptor';
import { RulesApiService } from './_services/rules-api.service';
import { ClientApiService } from '../_services/client-preference-api.service';
import { MPUISegmentedControlModule } from 'marketplace-segmented-control';
import { CopyComponent } from './copy/copy.component';
import { RuleHistoryComponent } from './rule-history/rule-history.component';
import { ImpactReportComponent } from './impact-report/impact-report.component';

@NgModule({
  declarations: [
    DashboardComponent,
    CreateComponent,
    EditComponent,
    ViewComponent,
    BreadcrumbsNavComponent,
    FrequentlyUsedCriteriaComponent,
    CreateNewCriteriaComponent,
    SetupTypeComponent,
    TypeDetailsComponent,
    TypeOutcomeComponent,
    CopyComponent,
    RuleHistoryComponent,
    ImpactReportComponent
  ],
  imports: [
    CommonModule,
    RulesRoutingModule,
    MPUITableModule,
    MPUITableModule,
    MPUIFileUploadModule,
    MPUIButtonModule,
    MPUICardsModule,
    MPUITabsModule,
    MPUISelectModule,
    MPUIStepperModule,
    MPUIBreadcrumbModule,
    MPUIPanelGroup,
    MPUIInputModule,
    MPUINotificationModule,
    MPUIModalDialogModule,
    MPUITabsModule,
    MPUITableModule,
    MPUIFileUploadModule,
    MPUIFileParserModule,
    MPUIProgressBarModule,

    MPUIQueryBuilderModule,

    MPUITextareaModule,
    MPUISwitchModule,
    MPUIDatePickerModule,

    MPUIDynamicFormModule,
    MPUISegmentedControlModule
  ],
  bootstrap: [DashboardComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
  providers: [RulesApiService, ClientApiService]
})
export class RulesModule { }
