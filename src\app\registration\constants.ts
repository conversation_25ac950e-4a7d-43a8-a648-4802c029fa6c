
export const RegistrationConstants: any = {
    ENTER_VALID_EMAIL: "Please Enter a Valid Email address",
    USERID_NUMBER_ERRMSG: "User ID cannot start with number",
    USERID_ELEVANCE_ERRMSG: "User ID cannot start with 2 letters followed by all numbers",
    USERID_MIMMAX_ERRMSG: "User ID should be min 6 and max 20 with valid characters",
    INVALID_CHAR: "Invalid characters",
    PWD_USR_CONTAINS_ERRMSG: "Password contains 3 characters of user ID",
    PWD_CONSECUTIVE_ERRMSG: "Consecutively character repeated more than twice",
    SUCCESS: "Success",
    SUCCESS_MESG: "Registration is Sucessfull !!",
    EXTERNAL_USER_ERRMSG: "Error occurred while adding user to CAD application",
    ERROR: "Error",
    SECURITY_ANS_INVALID_MSG: "securtiy answer is not valid",
    MAXIMUM_LIMIT_EXCEEDED_MSG: "You have exceeded the maximum number of attempts",
    UNAUTHORIZED: "Unauthorized",
    REP<PERSON>ITORY_ENUM: "IAM",
    <PERSON><PERSON>: "user",
    NO_USER_FOUND: "No user found.",
    NOTI_BODY_ERROR: "Some error occurred",
    WRONG_ONE_TIME_ERRMSG: "Wrong One Time Password Entered! Please enter correct information",
    OTP_INFO_MSG: "Verify your identity by entering OTP sent to your email.",
    EMAIL_ADDRESS: "emailAddress",
    PASSWORD_FIELD: "password",
    PASSWORD_TOOL_TIP: `Password cannot contain User ID or first three characters of the User ID. No character can be repeated more than twice.
    Characters allowed: ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_!#$%^*()+,.?;:]}[{|@`,
    SOA_INVALID_PASSWORD: "PASSWORDINVALID",
    SOA_INVALID_PASSWORD_NOTIFICATION_HRD: "Invalid password",
    SOA_INVALID_PASSWORD_NOTIFICATION_BDY: "Please choose allowed characters"
}