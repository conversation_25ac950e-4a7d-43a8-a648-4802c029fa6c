import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router, ActivatedRoute } from "@angular/router"
import { UtilitiesService } from 'src/app/_services/utilities.service';
import { AuthService } from 'src/app/_services/authentication.services';
import { ProductApiService } from 'src/app/_services/product-api.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-bundle-list',
  templateUrl: './bundle-list.component.html',
  styleUrls: ['./bundle-list.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class BundleListComponent implements OnInit {
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  public totalEntries: number;
  public dataURL: any;
  public dataRoot = "src";
  public prodId: any;
  public productName: any;
  breadcrumbDataset: any;
  public columnConfigDetailsUrl: string = "./assets/json/settings/product/bundle.json"
  public bundleTableColumnConfig: any;
  isReadOnly: boolean = false;

  constructor(
    private router: Router,
    private productApiService: ProductApiService,
    private dateService: UtilitiesService,
    private route: ActivatedRoute,
    private authService: AuthService
  ) {
    this.prodId = Number(this.router.url.slice(this.router.url.lastIndexOf('/') + 1));
  }
  kebabOptions: any = [{ label: '<i class="fa fa-eye" aria-hidden="true"></i> View Bundle', id: 'View Bundle' }, { label: '<i class="fa fa-edit" aria-hidden="true"></i>  Edit Bundle', id: 'Edit Bundle' }]
  kebabOptions_ReadOnly: any = [{ label: '<i class="fa fa-eye" aria-hidden="true"></i> View Bundle', id: 'View Bundle' }]

  ngOnInit(): void {
    this.isReadOnly = !this.authService.isWriteOnly;
    this.kebabOptions = this.isReadOnly ? this.kebabOptions_ReadOnly : this.kebabOptions;

    this.productApiService.getProductDetails().subscribe((data) => {
      let matchedProduct = data.find((x) => x.productId == this.prodId)
      this.productName = matchedProduct?.productName;
      this.breadcrumbDataset = [{ label: 'Home', url: '/' }, { label: `${this.productName}`, url: 'settings/product' }, { label: `Bundle list` }];
    })

    this.productApiService.getTableColumnForBundleList(this.columnConfigDetailsUrl).subscribe((columnConfig) => {
      columnConfig.colDefs.forEach(e => {
        e.field == "activeFlag" ? e.customFormatter = this.customFormatterStatus : "";
      });
      this.bundleTableColumnConfig = columnConfig;
    })

    this.productApiService.getProductBundleDetails(this.prodId).subscribe((apidata) => {
      if (apidata) {
        apidata.forEach(e => {
          e["activeFlag"] = e["activeFlag"] ? "Active" : "Inactive";
          e["effStartDate"] = e["effStartDate"] ? e["effStartDate"] = this.dateService.getDbgDateFormat(e["effStartDate"]) : "";
          e["effEndDate"] = e["effEndDate"] ? e["effEndDate"] = this.dateService.getDbgDateFormat(e["effEndDate"]) : "";
        });
        this.dataURL = apidata;
        this.totalEntries = apidata.length;
      }
    }, err => {
      this.dataURL = [];
      this.totalEntries = 0;
    });

  }

  /**
   * customFormatterStatus funtion for button in bundle table
   * @param event 
   */
  customFormatterStatus(event) {
    let btn;
    switch (event.value) {
      case "Active":
        btn = "<button type='button' title='Active' class='btn btn rule-dashboard btn-active btn-wrap-text'>Active</button>";
        break;
      case "Inactive":
        btn = "<button type='button' title='Inactive' class='btn btn rule-dashboard btn-inactive btn-wrap-text'>InActive</button>";
        break;
    }
    return btn;
  }


  /**
    * cell click event
    * @param event 
    */
  cellClicked(event: Event): void {
    if (event['eventData'].target.attributes) {
      if (event['eventData'].target.attributes.dataaction && event['eventData'].target.attributes.datevalue) {
        let bundleId = event['eventData'].target.attributes.datevalue.nodeValue;
        let rulesAction = event['eventData'].target.attributes.dataaction.nodeValue;
        switch (rulesAction) {
          case 'view':
            this.router.navigate([`settings/product/view-bundle/${bundleId}`]);
            break;
          case 'edit':
            this.router.navigate([`settings/product/edit-bundle/${bundleId}`]);
            break;

        }
      }
    }
  }

  /**
    * cellValueChanged Function for Table
    * @param event 
    */
  cellValueChanged(event: Event): void {

  }

  /**
    * tableReady Funtion
    * @param event 
    */
  tableReady(event: Event): void {

  }


  /**
    * AddNewRulefun Funtion
    */
  AddNewBundlesfun(): void {
    if (this.isReadOnly) return
    this.router.navigate([`settings/product/add-bundle/${this.prodId}`]);
  }

  /**
    * breadcrumSelection Funtion
    */
  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }

  onDropdownOptionsClick(event) {
    let rulesAction = event.text;
    switch (rulesAction) {
      case 'View Bundle':
        this.router.navigate([`settings/product/view-bundle/${event.currentRow['prodId']}/${event.currentRow['bundleId']}`]);
        break;
      case 'Edit Bundle':
        this.router.navigate([`settings/product/edit-bundle/${event.currentRow['prodId']}/${event.currentRow['bundleId']}`]);
        break;
    }
  }
}

