  <marketplace-breadcrumb
    [dataset]="breadcrumbDataset"
    (onSelection)="breadcrumSelection($event)"
  >
  </marketplace-breadcrumb>
  
  <div class="title-container">
    <div style="margin-left: -25px;">
        <app-breadcrumbs-nav
    [headerText]="headerText"
    [isPriviousRedirectPage]="isPriviousRedirectPage"
    >
    </app-breadcrumbs-nav>
    </div>
    <marketplace-button
        id="addNewRuleSubType"
        [label]="'Add New Rule Type'"
        [type]="'primary'"
        [name]="'primary'"
        [enabled]="!isReadOnly"
        (onclick)="AddNewRuleSubTypefun()"
      >
      </marketplace-button>
      
    <p class="table-header-title">List of Rule Types</p>
    <marketplace-table
      [dataURL]="dataURL"   
      [rowHeight]="ruleDashbordTableRowhg"
      [headerRowHeight]="ruleDashbordTableHeaderhg"
      [isRowSelectable]="false"
      [dataRoot]="dataRoot"
      [columnDefinitions]="columnConfig"
      (onCellValueChange)="cellValueChanged($event)"
      [dropdownOptions]="kebabOptions"
      (onCellClick)="cellClicked($event)"
      (onTableReady)="tableReady($event)"
      (onDropdownOptionsClick)="onKebabOptionsClick($event)"
  *ngIf=true
    >
    </marketplace-table>
  </div>

<marketplace-popup [open]="openConfirmationModal" [size]="'small'">
  <div mpui-modal-header>
    <h3>Delete Rule Type</h3>
  </div>
  <div mpui-modal-body>
    <span style="text-align: center">Please confirm to delete the record.</span>
  </div>
  <div mpui-modal-footer>
    <marketplace-button
      [label]="'Cancel'"
      [type]="'ghost'"
      [name]="'primary'"
      (onclick)="closeModelPopup()"
    >
    </marketplace-button>
    <marketplace-button
      [label]="'Delete'"
      [type]="'primary'"
      [name]="'primary'"
      (onclick)="deleteRuletype()"
    >
    </marketplace-button>
  </div>
</marketplace-popup>
