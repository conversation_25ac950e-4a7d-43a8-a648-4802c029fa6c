import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { externalAuthenticationConstants } from 'src/app/_helpers/helpers.constants';
import { AuthService } from 'src/app/_services/authentication.services';
import { ExternalMFAService } from 'src/app/_services/external-mfa.service';
import { ExternalSOAService } from 'src/app/_services/external-soa.service';
import { ToastService } from 'src/app/_services/toast.service';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { OtpComponent } from '../otp/otp.component';

const jsonPath = "./assets/json/external-signIn/forgot-userid-form.json";
const emailFormName = "emailFormDetails";
const otpFormName = "otpFormDetails";
@Component({
  selector: 'app-forgot-userid',
  standalone: true,
  templateUrl: './forgot-userid.component.html',
  styleUrls: ['./forgot-userid.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule, 
    FormsModule, 
    RouterModule, 
    HttpClientModule,
    MPUIDynamicFormModule ,
    OtpComponent
  ]
})
export class ForgotUseridComponent implements OnInit {
  emailIdFormJSON: any;
  emailAddress: any;
  isSendEmail: boolean = false;
  isResendEmail: boolean = false;
  mfaToken: any;
  pingDeviceId: any;
  pingUserId: any;
  isOtpScreen: boolean = false;
  isFoundUserId: boolean = false;
  errorMsg: string = "";
  isLoading: boolean = false;
  otpFormJSON: any;
  otpValue: any;
  isOtpEntered: boolean = false;
  pingRiskId: any;
  userId: string = "";
  fetchedUserId: string;
  contactUsUrl: string = externalAuthenticationConstants.CONTACT_US_URL;

  constructor(
    private userManagementSvc: UserManagementApiService,
    private router: Router,
    private mfaService: ExternalMFAService,
    private authenticateService: AuthService,
    private alertService: ToastService,
    private soaService: ExternalSOAService) { }


  ngOnInit(): void {
    let _fetchPage = this.userManagementSvc.getAssetsJson(jsonPath);
    _fetchPage.subscribe(data => {
      this.emailIdFormJSON = data[0][emailFormName];
      this.otpFormJSON = data[1][otpFormName];
    });
  }

  /**
  * This function is triggered when any change happens in field values
  * @param event 
  */
  onForgotUserIdFormValueChange(event) {

    this.emailAddress = event.current.emailId;
  }

  /**
  * This function is triggered when we click on send email button
  * @param event 
  */
  onSendOtpButtonClicked() {
    this.isLoading = true;
    this.isOtpScreen = true;
  }

  /**
   * This function is triggerd once OTP validation is done and emited value back
   * @param event 
   */
  actionAfterOTPValidation(event): void {
    if (event.validated) {
      this.fetchedUserId = event.message;
      this.isOtpScreen = false;
      this.isFoundUserId = true;
    }
    else {
      this.isOtpScreen = false;
      this.alertService.setErrorNotification({
        notificationHeader: externalAuthenticationConstants.FAIL,
        notificationBody: event.message,
      });
    }
  }
}
