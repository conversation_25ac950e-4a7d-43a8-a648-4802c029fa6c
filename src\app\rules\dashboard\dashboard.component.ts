import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from "@angular/router"
import { EcmAuthenticationService } from 'src/app/_services/ecm-authentication.service';
import { RulesApiService } from '../_services/rules-api.service';
import { ToastService } from 'src/app/_services/toast.service';
import { UtilitiesService } from '../../_services/utilities.service';
import { constants } from '../rules-constants';
import { AuthService } from 'src/app/_services/authentication.services';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class DashboardComponent implements OnInit {
  public headerText = "Rules Engine";
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  statusOptions = { 'true': 'Active', 'false': 'Inactive', 'draft': 'Draft', 'edit' : 'Edit' };
  rulesList: any;
  dataRoot: string = "src";
  static isEditedDashBoard = false;
  showLoader: boolean = false;
  breadcrumbDataset = [{ label: 'Home', url: '/' }, { label: 'Rules engine' }];
  kebabOptions: any = {
    "field": "status",
    "dataset": [
      {
        "value": "Draft",
        "options": [{ label: '<i class="fa fa-eye" aria-hidden="true"></i> View Rule', id: 'View Rule' }, { label: '<i class="fa fa-edit" aria-hidden="true"></i>  Edit Rule', id: 'Edit Rule' }]
      },
      {
        "value": "default__options",
        "options": [{ label: '<i class="fa fa-eye" aria-hidden="true"></i> View Rule', id: 'View Rule' }, { label: '<i class="fa fa-edit" aria-hidden="true"></i>  Edit Rule', id: 'Edit Rule' }, { label: '<i class="fa fa-copy" aria-hidden="true"></i> Copy Rule' }]
      }
    ]
  }
  kebabOptions_Readonly: any = [{ label: '<i class="fa fa-eye" aria-hidden="true"></i> View Rule', id: 'View Rule' }]
  customExportAll: any 
  columnConfig: any = {
    "switches": {
      "enableSorting": true,
      "enablePagination": true,
      "enableFiltering": true,
      "excelExportOptions": {
        filename: 'export_rules',
        format: 'xlsx'
      }
    },
    "colDefs": [
      {
        "name": "RULE ID",
        "field": "rule_id",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "RULE NAME",
        "width": 200,
        "field": "rule_name",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "RULE TYPE",
        "width": 100,
        "field": "rule_type",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "RULE SUBTYPE",
        "width": 150,
        "field": "rule_subtype",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      },
      {
        "name": "RULE LEVEL",
        "width": 100,
        "field": "rule_level",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": ""
      },
      {
        "name": "CLIENT",
        "width": 100,
        "field": "client",
        "filterType": "Text",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "exportWithFormatter": true,
        "customFormatter": this.customFormatterClient
      },
      {
        "name": "CONCEPT",
        "width": 100,
        "field": "concept",
        "filterType": "Text",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": ""
      },
      {
        "name": "STATUS",
        "width": 110,
        "field": "status",
        "filterType": "Multi Select",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "customFormatter": this.customFormatterStatus
      },
      {
        "name": "BUSINESS OWNER",
        "width": 130,
        "field": "business_owner",
        "filterType": "Text",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": ""
      },
      {
        "name": "REVIEW DATE",
        "width": 100,
        "field": "review_remainder_date",
        "filterType": "Calendar",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "dateFormat": "MM/DD/YYYY"
      },
      {
        "name": "START DATE",
        "width": 100,
        "field": "start_date",
        "filterType": "Calendar",
        "visible": "False",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "dateFormat": "MM/DD/YYYY"
      },
      {
        "name": "END DATE",
        "width": 100,
        "field": "end_date",
        "filterType": "Calendar",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "dateFormat": "MM/DD/YYYY"
      },
      {
        "name": "CREATED DATE",
        "width": 100,
        "field": "created_ts",
        "filterType": "Calendar",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "dateFormat": "MM/DD/YYYY"
      },
      {
        "name": "UPDATED DATE",
        "width": 100,
        "field": "updated_ts",
        "filterType": "Calendar",
        "visible": "True",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "dateFormat": "MM/DD/YYYY"
      },
      {
        "name": "",
        "width": 100,
        "field": "action",
        "filterType": "",
        "visible": "False",
        "editorType": "",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "excludeFromExport": 'true',
        "customFormatter": this.customFormatterAction
      }
    ]
  };

  /**
   * card component prm
   */
  cardsDataset: any =
    [
      {
        label: 'Setup Rule Sub Type',
        url: '/rules/rule-type'
      },
      { label: 'Setup Frequently Used Criteria', url: '/rules/frequently-used-criteria' }
    ];
  isReadOnly: boolean = false;
  isUserTableReady: boolean = false;

  constructor(private router: Router, private ecmAuthentication: EcmAuthenticationService,
    private RulesApiService: RulesApiService, private alertService: ToastService,
    private dateService: UtilitiesService, private authService: AuthService) { 

        this.isUserTableReady = true;
    }

  /**
   * Function to Sort Rules in descending order according to created date
   */
  sortData(data: any) {
    return data.sort((a, b) => {
      if (a.updated_ts && b.updated_ts) {
        return <any>new Date(b.updated_ts) - <any>new Date(a.updated_ts);
      } else if (b.updated_ts) {
        return <any>new Date(b.updated_ts) - <any>new Date(a.created_ts);
      }
      else {
        return <any>new Date(b.created_ts) - <any>new Date(a.created_ts);
      }
    });
  }

  ngOnInit(): void {
    this.showLoader = true;
    this.isUserTableReady = true;
    this.isReadOnly = !this.authService.isWriteOnly;
    this.isReadOnly ? this.kebabOptions = this.kebabOptions_Readonly : this.kebabOptions;
    this.RulesApiService.getListOfRules({})
      .subscribe(
        data => {
          if (data.status.code == 200) {
            this.showLoader = false;
            this.isUserTableReady = true;
            if (data.result.metadata.rules.length == 0) return;
            //Remove this adding id key part once its implemented ECP side
            let ruleListBackUp = [];
            this.rulesList = [];
            ruleListBackUp = this.sortData(data.result.metadata.rules);
            let letterRuleSubtype = data.result.metadata.rules.filter(s => s.ltr_rule_sub_type != null);
            if (letterRuleSubtype.length > 0) {
              letterRuleSubtype.forEach(entry => {
                entry.rule_subtype = entry.ltr_rule_sub_type;
              });
            }
            ruleListBackUp.map((s, i) => {
              s.id = i + 1;
              s.status = s.is_draft ? this.statusOptions["draft"] : this.statusOptions[s.status];
              //25.1 - if(s.is_edited){
              //   s.status = this.statusOptions["edit"]
              // }
              // else if(s.is_draft){
              //   s.status = this.statusOptions["draft"]
              // }
              // else{
              //   s.status =  this.statusOptions[s.status]
              // }
              s.review_remainder_date = this.dateService.getECPDateFormat(s.review_remainder_date);
              s.start_date = this.dateService.getECPDateFormat(s.start_date);
              s.end_date = this.dateService.getECPDateFormat(s.end_date);
              s.created_ts = this.dateService.getECPDateFormat(s.created_ts);
              s.updated_ts = this.dateService.getECPDateFormat(s[constants.UPDATED_DATE]);
              s.concept = s.concept == "[]" ? null : s.concept;
            });
            this.rulesList = ruleListBackUp;
          }
          else if (data.status.code == 500) {
            this.alertService.setErrorNotification({
              notificationBody: data.status.message,
            });
          }
          this.showLoader = false;
        },
        error => {
          this.alertService.setErrorNotification({
            notificationBody: error,
          });
          this.showLoader = false;
        });

  }

  /**
   * customFormatterStatus funtion for button in Rule table
   * @param event 
   */
  customFormatterStatus(event) {
    let btn;
    switch (event.value) {
      case 'Active':
        btn = "<button type='button' title='Active' class='btn btn rule-dashboard btn-active btn-wrap-text'>Active</button>";
        break;
      case 'Inactive':
        btn = "<button type='button' title='Inactive' class='btn btn rule-dashboard btn-inactive btn-wrap-text'>Inactive</button>";
        break;
      case 'Draft':
        btn = "<button type='button' class='btn btn rule-dashboard btn-draft'>Draft</button>";
        break;
      case 'On Hold':
        btn = "<button type='button' class='btn btn rule-dashboard btn-onhold'>On Hold</button>";
        break;
      case 'Edit':
        btn = "<button type='button' class='btn btn rule-dashboard btn-onhold'>Edit</button>";
        break;
    }
    return btn;
  }

  /**
   * customFormatterStatus funtion for button in Rule table
   * @param event 
   */
  customFormatterReviewDate(event) {
    let btn;
    switch (event.value) {
      case 'Active':
        btn = "<button type='button' class='btn btn rule-dashboard btn-review-date-active'>Active</button>";
        break;
      case 'Expired':
        btn = "<button type='button' class='btn btn rule-dashboard btn-review-date-expired'>Expired</button>";
        break;
      case 'About to Expire':
        btn = "<button type='button' class='btn btn rule-dashboard-big btn-about-expire'>About to Expire</button>";
        break;
    }
    return btn;
  }


  /**
  * customFormatterAction function for Rule Table Action
  * @param event 
  */
  customFormatterAction(event) {

    return `<button type='button' class='btn rule-dashboard btn-execute' disabled>Execute</button>`
  }

  /**
  * customFormatterClient function to format client column 
  * @param event 
  */
   customFormatterClient(event) {
     if(event.dataContext.client == constants.ANTM){
       return constants.ANTHEM
     }
     else{
      return event.dataContext.client
     }
  }

  /**
  * Delete Rules funstion
  * @param ruleId 
  */
  rulesDelete(ruleId) {

    // call delete rules API
  }

  /**
    * cell click event
    * @param event 
    */
  cellClicked(event: any): void {
    event['currentRow']?.is_edited ? DashboardComponent.isEditedDashBoard = true : DashboardComponent.isEditedDashBoard = false
    if (event['eventData'].target.attributes) {
      if (event['eventData'].target.attributes.dataaction && event['eventData'].target.attributes.datevalue) {
        let ruleId = event['eventData'].target.attributes.datevalue.nodeValue;
        let rulesAction = event['eventData'].target.attributes.dataaction.nodeValue;
        switch (rulesAction) {
          case 'view':
            this.router.navigate([`/rules/view/${ruleId}`])//, { queryParams: {id:ruleId, mode: 'view', ruleName: event.currentRow.rule_name } });
            break;   //request_type
          case 'edit':
            this.router.navigate([`/rules/edit/${ruleId}`])
            break;
          case 'delete':
            this.rulesDelete(ruleId);
            break;
          case 'copy':
            this.router.navigate([`/rules/copy/${ruleId}`]);
            break;
        }
      }
    }
  }

  /**
    * cellValueChanged Function for Table
    * @param event 
    */
  cellValueChanged(event: Event): void {

  }

  /**
    * tableReady Funtion
    * @param event 
    */
  tableReady(event: Event): void {

  }


  /**
    * AddNewRulefun Funtion
    */
  AddNewRulefun(): void {
    if (!this.isReadOnly)
      this.router.navigate(['/rules/create'])
  }

  /**
   * card component callback
   * @param event 
   */
  selectedLink(event: any): void {
    if (event.selected == 'rule-type') {
      alert('coming soon functionality')
    } else {
      this.router.navigate([event.selected])
    }

  }

  /**
    * breadcrumSelection Funtion
    */
  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }

  onDropdownOptionsClick(event) {
    let ation = event.text;
    switch (ation) {
      case 'View Rule':
        this.RulesApiService.ruleLevelToBeOpened = event.currentRow.rule_level;
        this.router.navigate([`/rules/view/${event.currentRow.rule_id}`])
        break;
      case 'Edit Rule':
        this.RulesApiService.ruleLevelToBeOpened = event.currentRow.rule_level;
        this.router.navigate([`/rules/edit/${event.currentRow.rule_id}`])
        break;
      case 'Delete Rule':
        this.rulesDelete(event.currentRow.id);
        break;
      case 'Copy Rule':
        this.RulesApiService.ruleLevelToBeOpened = event.currentRow.rule_level;
        this.router.navigate([`/rules/copy/${event.currentRow.rule_id}`])
        break;
    }
  }
}
