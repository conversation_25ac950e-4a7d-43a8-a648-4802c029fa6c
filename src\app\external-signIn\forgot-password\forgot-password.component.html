<div class="d-flex justify-content-center backdrop" *ngIf="isLoading">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
    </div>
</div>
<div class="body-content">
    <div class="page-header-container">
        <img src="./assets/images/logo.png">
        <span class="seperator"></span>
        <div class="page-header-text">Payment Integrity</div>
    </div>

    <div class="card elevated-card-forgotpassword" *ngIf="isForgotPassword">
        <div class="page-header-forgot-password">
            <h3>Forgot your password?</h3>
            <p class="reset-password">Enter your email address below and we'll send you an email to</p>
            <p class="reset-password center-password-text">reset your password.</p>
        </div>
        <marketplace-dynamic-form [formJSON]="forgotPasswordFormJSON" [isSubmitNeeded]="false"
            (onValueChanges)="onForgotPasswordValueChange($event)">
        </marketplace-dynamic-form>
        <div class="red-font" *ngIf="isEmailNotFound">Email not found in our system</div>
        <div class="btn-holder">
            <!--Below implementation will be changed while implementing MFA-->
            <!-- <marketplace-button [label]="'Send email >'" [name]="'primary'" [type]="'primary'"
                (onclick)="onSendEmailButtonClicked()">
            </marketplace-button> -->
            <marketplace-button [enabled]="isGenerateButtonEnabled" [label]="'Generate Password'" [name]="'primary'"
                [type]="'primary'" (onclick)="onClickOfNextButton()">
            </marketplace-button>
        </div>
        <a routerLink="../">Return to log in</a>
    </div>

    <div class="card elevated-card-securityquestion" *ngIf="isSecurityQuestion">
        <div class="page-header">
            <h3>Forgot your password?</h3>
        </div>
        <marketplace-dynamic-form [formJSON]="securityQuestionsJSON" [isSubmitNeeded]="false"
            (onValueChanges)="onSecurityQuestionsFormChange($event)">
        </marketplace-dynamic-form>
        <div class="red-font" *ngIf="isEmailNotFound">Email not found in our system</div>
        <div class="btn-holder">
            <!--Below implementation will be changed while implementing MFA-->
            <!-- <marketplace-button [label]="'Send email >'" [name]="'primary'" [type]="'primary'"
                (onclick)="onSendEmailButtonClicked()">
            </marketplace-button> -->
            <marketplace-button [label]="'Generate Password'" [name]="'primary'" [type]="'primary'"
                (onclick)="onGeneratePassword()">
            </marketplace-button>
        </div>
        <a routerLink="../">Return to log in</a>
    </div>

    <div class="card elevated-card-securityquestion" *ngIf="generatedPasscode">
        <!--Below implementation will be changed while implementing MFA-->
        <!-- <div class="page-header">
            <h3>Check your email</h3>
            <span class="text-content">We have sent you an <NAME_EMAIL> with a link to reset
                your password.If it has not arrived within the next 15 minutes click resend email below.</span>
        </div>
        <div class="btn-holder">
            <marketplace-button [label]="'Resend email >'" [name]="'primary'" [type]="'primary'"
                (onclick)="onResendEmailButtonClicked()">
            </marketplace-button>
        </div> -->
        <div class="page-header">
            <h3>Generated password for the user: {{userId}}</h3>
            <h3>{{generatedPasscode}}</h3>
        </div>
        <a routerLink="../">Return to log in</a>
    </div>

    <div class="card elevated-card-resendemail" *ngIf="isResendEmail">
        <div class="page-header">
            <h3>Email resent</h3>
            <span class="text-content">We have resent the password recovery email to</span>
            <!-- <span class="text-content"><EMAIL>. If you still have not received the email contact support.</span> -->
        </div>
        <div class="btn-holder">
            <marketplace-button [label]="'Resend email >'" [name]="'primary'" [type]="'primary'"
                (onclick)="onGeneratePassword()">
            </marketplace-button>
        </div>
        <a routerLink="../">Return to log in</a>
    </div>
    <div *ngIf="isOtpScreen">
        <app-otp [userId]="userId" (isValidationSuccessful)="actionAfterOTPValidation($event)"></app-otp>
    </div>
    <div class="page-footer">
        Need Help ?
        <a href="{{contactUsUrl}}" target="_blank">Email Support</a>
    </div>
</div>