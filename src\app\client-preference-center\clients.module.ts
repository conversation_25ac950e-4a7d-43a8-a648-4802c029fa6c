import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ClientPreferenceCenterRoutingModule } from './client-preference-center-routing.module';
import { ClientListComponent } from './client-list/client-list.component';
import { ClientProductListComponent } from './client-product-list/client-product-list.component';
import { ProductBundleFeeEditComponent } from './product-bundle-fee-edit/product-bundle-fee-edit.component';
import { ClientPreferenceListComponent } from './client-preference-list/client-preference-list.component';
import { AcknowledgementComponent } from './acknowledgement/acknowledgement.component';
import { MPUITableModule } from 'marketplace-table';
import { MPUITabsModule } from 'marketplace-tabs';
import { ProductBundleFeeListComponent } from './product-bundle-fee-list/product-bundle-fee-list.component';
import { ProductBundleFeeAddComponent } from './product-bundle-fee-add/product-bundle-fee-add.component';
import { MPUIBreadcrumbModule } from 'marketplace-breadcrumb';
import { ClientPreferenceAddComponent } from './client-preference-add/client-preference-add.component';
import { ClientPreferenceEditComponent } from './client-preference-edit/client-preference-edit.component';
import { ClientPreferenceViewComponent } from './client-preference-view/client-preference-view.component';
import { SampleValidationClientComponent } from './sample-validation-percentage/sample-validation-client.component';
import { BreadcrumbsNavComponent } from './breadcrumbs-nav/breadcrumbs-nav.component';
import { MPUIDatePickerModule } from 'marketplace-date-picker';
import { MPUISelectModule } from 'marketplace-select';

import { MPUIInputModule } from 'marketplace-input';
import { MPUIButtonModule } from 'marketplace-button';
import { MPUIFileUploadModule } from 'marketplace-file-upload';
import { MPUIFileParserModule } from 'marketplace-file-parser';
import { MPUIProgressBarModule } from 'marketplace-progressbar';

import { MPUIQueryBuilderModule } from 'marketplace-query-builder';
import { MPUINotificationModule } from 'marketplace-notification';
import { MPUIChartsModule } from 'marketplace-charts';
import { MPUITimelineModule } from 'marketplace-timeline';
import { MPUISegmentedControlModule } from 'marketplace-segmented-control';
import { MPUIModalDialogModule } from 'marketplace-modal';
import { MPUIPanelGroup } from 'marketplace-accordion';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { MPUITextareaModule } from 'marketplace-textarea';
import { MPUISwitchModule } from 'marketplace-switch';
import { MPUIFormRepeaterModule } from 'marketplace-form-repeater';
import { ViewTenantComponent } from './view-tenant/view-tenant.component';
import { TenantListComponent } from './tenant-list/tenant-list.component';
import { EditTenantComponent } from './edit-tenant/edit-tenant.component';
import { AddTenantComponent } from './add-tenant/add-tenant.component';

@NgModule({
  declarations: [
    ClientListComponent,
    ClientProductListComponent,
    ProductBundleFeeEditComponent,
    ClientPreferenceListComponent,
    ProductBundleFeeListComponent,
    ProductBundleFeeAddComponent,
    ClientPreferenceAddComponent,
    ClientPreferenceEditComponent,
    ClientPreferenceViewComponent,
    BreadcrumbsNavComponent,
    AcknowledgementComponent,
    SampleValidationClientComponent,
    ViewTenantComponent,
    TenantListComponent,
    EditTenantComponent,
    AddTenantComponent
  ],
  imports: [
    CommonModule,
    ClientPreferenceCenterRoutingModule,
    MPUITableModule,
    MPUIFormRepeaterModule,
    MPUITextareaModule,
    MPUISwitchModule,

    MPUITabsModule,
    MPUIBreadcrumbModule,
    MPUIDatePickerModule,
    MPUISelectModule,

    MPUIInputModule,
    MPUIButtonModule,
    MPUIFileUploadModule,
    MPUIFileParserModule,
    MPUIProgressBarModule,

    MPUINotificationModule,
    MPUIChartsModule,
    MPUITimelineModule,
    MPUISegmentedControlModule,
    MPUIPanelGroup,
    MPUIModalDialogModule,
    MPUIQueryBuilderModule,
    MPUIDynamicFormModule,
  ]
})
export class ClientPreferenceCenterModule { }
