app-reset-password {
    .body-content {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      height: 100vh;
  
      .page-header-container {
        display: flex;
        width: 400px;
        justify-content: center;
        align-items: center;

        .page-header-text{
          font-family: var(--font)!important;
          font-size: 20px;
          font-style: normal;
        }
      

        img {
          width: 100px;
          display: flex;
          align-items: center;
        }

        .seperator{
          width: 1px;
          background: black;
          display: flex;
          align-items: center;
          margin: 6px;
          border-radius: 8px;
          height: 1rem;
        }
  
      }
  
    .elevated-card-changepassword{
      box-shadow: 0 5px 5px -3px #0003, 0 8px 10px 1px #00000024, 0 3px 14px 2px #0000001f;
      height: fit-content;
      width: 400px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 50px 25px 60px 25px;
      margin: 10px 0px;
  
      .page-header {
        h3 {
          font-weight: bold;
          display: flex;
          justify-content: center;
          font-size: 1.5rem;
        }
      }

      a {
        margin: 20px 0px;
        font-size: 12px;
        text-align: center;
      }
  
      marketplace-dynamic-form {
        margin: 10px 0px;
      }
  
      .red-font {
        color: #FF0000;
        padding: 0px 0px 14px 14px;
        font-size: 12px;
      }
    }
  
    .elevated-card-passwordsuccess{
      box-shadow: 0 5px 5px -3px #0003, 0 8px 10px 1px #00000024, 0 3px 14px 2px #0000001f;
      height: fit-content;
      width: 400px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 50px 25px 60px 25px;
      margin: 10px 0px;
  
      .page-header {
        h3 {
          font-weight: bold;
          display: flex;
          justify-content: center;
          font-size: 1.5rem;
        }
      }
  
      .btn-holder{
        margin-top: 30px;
      }
    }
  }

  .page-footer {
    display: flex;
    justify-content: space-around;
    width: 400px;
    margin: 10px 10px;
    font-size: 12px;
  }
}