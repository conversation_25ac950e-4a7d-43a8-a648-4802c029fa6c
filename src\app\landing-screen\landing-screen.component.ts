import { CommonModule } from "@angular/common";
import { Component, OnInit, ViewEncapsulation } from "@angular/core";
import { MPUIQuickNavsModule } from "marketplace-quick-navs";
import { MPUITargetCardsModule } from "marketplace-target-cards";
import { CookieService } from "ngx-cookie-service";
import { AuthService } from "../_services/authentication.services";
import { ILandingScreen, ILandingScreenCards, ILandingScreenQuickLinks } from "../_models/landing-screen.model";
import { APP_CONSTANTS, AUTH_CONFIG, LOB_CARD_NAME, QUICK_LINKS } from "../_constants/app.constants";
import { environment } from "../../environments/environment";
import { LoaderService } from "../_services/loader.service";
import { MPUIJwtVerifierService } from "marketplace-jwt-verifier";
import { Router } from "@angular/router";

@Component({
  selector: 'marketplace-landing-screen',
  standalone: true,
  templateUrl: './landing-screen.component.html',
  styleUrls: ['./landing-screen.component.sass'],
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule, MPUITargetCardsModule, MPUIQuickNavsModule],
  providers: [AuthService]
})

export class LandingComponent implements OnInit {
  userName: string;
  listOfAccessibleCards: ILandingScreenCards[];
  isInternal: boolean = true;
  selectedClient: any = null;
  listOfAccessibleQuickLinks: ILandingScreenQuickLinks[];
  landingScreen: ILandingScreen;
  constructor(private cookieService: CookieService, public jwtService: MPUIJwtVerifierService, public loaderService: LoaderService, public authService: AuthService,private router:Router) {
  }

  ngOnInit() {
    this.loadScreenDetails();
  }

  /**
   * Load Screen Details
   */
  loadScreenDetails() {
    let appToken = sessionStorage.getItem('appToken');
    this.loaderService.show();
    this.jwtService.verifyToken(appToken, environment.publicKey).then((appToken) => {
      this.loaderService.hide();
      this.listOfAccessibleCards = appToken[APP_CONSTANTS.SCREEN_ACCESS_CARDS];
      this.listOfAccessibleQuickLinks = appToken[APP_CONSTANTS.SCREEN_ACCESS_LINKS];
      this.listOfAccessibleCards.forEach(cardDetails => {
        cardDetails.value = `<img src='./assets/icons/landing-screen/${cardDetails.label}-icon.svg'>`;
        cardDetails.label = cardDetails.name
      });

      this.landingScreen = {
        cards: this.listOfAccessibleCards,
        quickLinks: this.listOfAccessibleQuickLinks
      }

      let clientDetails = {
        clientId: appToken['appScope']['clients'][0].clientId,
        clientName: appToken['appScope']['clients'][0].clientName
      }
      sessionStorage.setItem("clientDetails", JSON.stringify(clientDetails));

      this.cookieService.set(APP_CONSTANTS.LANDING_SCREEN_DETAILS, JSON.stringify(this.landingScreen));
      this.userName = this.cookieService.get(APP_CONSTANTS.USER_NAME);
      // Need to revisit
      if (this.userName == "") {
        this.jwtService.verifyToken(this.cookieService.get(AUTH_CONFIG.USER_TOKEN), environment.publicKey).then((userToken) => {
          this.userName = `${userToken[APP_CONSTANTS.FIRST_NAME]} ${userToken[APP_CONSTANTS.LAST_NAME]}`
          this.cookieService.set(APP_CONSTANTS.USER_NAME, this.userName);
        }).catch(error => {
          this.authService.clearSessions();
        });
      }
    });
  }


  /**
  * Method invoked on screen selection
  * @param event 
  */
  onScreenSelection(event) {
    const url = event.selected.url || `https://ui.portal-card.pi.${environment.name}.gcpdns.internal.das`;
    window.open(url);
  }

  /**
  * Method invoked on quick link click
  * @param event 
  */
  quickLinkClick(event) {
    let commonServiceNavigation = '';
    switch (event.selected.label) {
      case QUICK_LINKS.RULES:
        commonServiceNavigation = `${environment.commonServicesUrl}rules`;
        break;
      case QUICK_LINKS.EXTERNAL_USER_REGISTRATION:
        commonServiceNavigation = `${environment.commonServicesUrl}registration`;
        break;
      case QUICK_LINKS.HELP_CENTER:
        commonServiceNavigation = `${environment.commonServicesUrl}help-center`;
        break;
      case QUICK_LINKS.SETTINGS:
        commonServiceNavigation = `${environment.commonServicesUrl}settings`;
        break;
      case QUICK_LINKS.CLIENTS:
            commonServiceNavigation = `${environment.commonServicesUrl}clients`;
        break;
      case QUICK_LINKS.USERS:
        commonServiceNavigation = `${environment.commonServicesUrl}users`;
        break;
      default:
        commonServiceNavigation = environment.commonServicesUrl
        break;
    }

    window.open(commonServiceNavigation, "_self");
  }
}