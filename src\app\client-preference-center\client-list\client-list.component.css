app-client-list marketplace-select .ng-select.ng-select-single .ng-select-container {
    width: 300px;
    margin-left: 2rem;
}
app-client-list marketplace-select .ng-dropdown-panel.ng-select-bottom {
    width: 300px;
    margin-top: -1px;
    margin-left: 2rem;
}

app-client-list marketplace-select .select-holder label {
    margin-left: 2rem;
    margin-top: 1rem;
}
app-client-list .modal-footer{
    margin-left: auto;
}
app-client-list .btn-wrap-text {
    overflow: hidden;
    white-space: nowrap;
    display: inline-block;
    text-overflow: ellipsis;
}
app-client-list .card {
    border: 3px solid rgba(5,5,6,0.125);
    border-radius: 0.95rem;
    padding-bottom: 30px;
}
.clientsHeader{
    font-size: larger;
    font-weight: bold;
}
app-client-list .btn-span {
    float: right;
    padding: 25px 30px 0px 30px;
}
app-client-list .btn-ruleadd{
    background: #5009B5;
    
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    color: #FFFFFF;
}
app-client-list .add-new-rules-link{
    color: #FFFFFF;
    text-decoration: none;
}
app-client-list .quickaction-title {
    float: left;
    width: 200px;
    height: 24px;
    left: 195px;
    top: 148px;
    
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 24px;
    color: #2453A6;
    padding: 25px 0px 20px 30px;
}
app-client-list .mb-3{
    margin-top: 1rem;
    margin-left: 1rem;
}
app-client-list .tp-bt-rem-1{
    margin-top: 1rem;
    margin-bottom: 1rem;
}
app-client-list .card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 2rem 1rem 2rem 1rem;
}
app-client-list .fa-caret-right{
    font-size: 31px;
    color: #5009B5;
    float: right;
    padding-right: 20px;
}
app-client-list .card-title{
    
    font-style: normal;
    font-weight: normal;
    font-size: 18px;
    line-height: 17px;
    color: #161616;
}
app-client-list .setup-rule{
    margin-top: 5px;
}

app-client-list .fa-list, app-client-list .fa-chevron-circle-left{
    font-size: 30px;
    color: #5009B5;
}
app-client-list .fa-plus:before{
    color: #FFFFFF;
}
app-client-list .pd-left-30{
    padding-left: 30px;
}
app-client-list .pd-righ-10{
    padding-right: 10px;
}
app-client-list .pd-righ-20{
    padding-right: 20px;
}

/*Rules dashboard Table action menu dropdown*/
app-client-list .dropdown {
    position: absolute;
    background-color: gray;
    padding: 5px;
    outline: none;
    opacity: 0;
    min-width: 100%;
    overflow: auto;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    background: #ffffff;
    box-shadow: 0px 0px 20px rgb(0 0 0 / 40%);
    border-radius: 4px;
    right: 10px;

}
app-client-list .dropdown a {
    color: black;
    padding: 5px 10px;
    text-decoration: none;
    display: block;
}
app-client-list input:not(:checked) ~ .dropdown {
    display: none;
}
app-client-list input:checked ~ .dropdown {
    opacity: 1;
    z-index: 100;
    transition: opacity 0.2s;
    z-index: 1;
}
app-client-list .three-dots:after {
    cursor: pointer;
    color: #444;
    content: "\2807";
    font-size: 20px;
    z-index: 999;
}
app-client-list .table-action-menu .fa-eye, app-client-list .table-action-menu .fa-edit, app-client-list .table-action-menu .fa-trash, app-client-list .table-action-menu .fa-plus {
    font-size: 20px;
    color: #5009B5 !important;
    padding-right: 15px;
}
app-client-list .table-action-menu .fa-plus:before {
    color: #5009B5 !important;
}
app-client-list .table-action-menu {
    border: 8.5px solid #ffffff;
}
app-client-list .fa-plus {
    width: 35px;
}



/* table overflow css overwrite*/
app-client-list .btn.product-dashboard {
    color: white;
    font-weight:350;
}
app-client-list .btn.focus.product-dashboard, app-client-list .btn:focus.product-dashboard {
    outline: 0;
    box-shadow: 0 0 0 0rem;
}
app-client-list .btn.product-dashboard-big {
    padding: 0.075rem 0.1rem !important;
    color: white;
    font-weight:200;
}
app-client-list .btn.focus.product-dashboard-big, app-client-list .btn:focus.product-dashboard-big {
    outline: 0;
    box-shadow: 0 0 0 0rem;
}
app-client-list .dropdown-container{
    padding-top: 5px;
}
app-client-list .search-filter .operator.input-group-addon{
    display: none !important;
}

app-client-list .btn-active{
    background:#D9F5F5;
    width: 100%;
    border: 1px solid #00BBBA;
}
app-client-list .btn-pending {
    background:#E1EDFF;
    width: 100%;
    border: 1px solid #44B8F3;
}
app-client-list .btn-inactive {
    background: #F5F5F5;
    width: 100%;
    border: 1px solid #231E33;
}
app-client-list .btn.product-list {
    color: white;
    font-weight: 350;
}
app-client-list .btn.product-list {
    padding: 0.075rem 0.1rem !important;
    color: white;
    font-weight: 350;
}
app-client-list .bundles-text-field{
    color: #2D6FE1;
    margin-right: 50px;
}
/* dashboard css end */

/* table overflow css overwrite*/
app-client-list .mar-10 {
    margin-top: 10px;
}
/* ==============================Product Dashboard end ================================================== */

/* ==============================Client-overlay starts ================================================== */
app-client-list .btn-client-site-primary,
app-client-list .btn-client-site-primary:hover,
app-client-list .btn-client-site-primary:focus {
    background-color: primary;
    border-radius: 4px;
    color: white;
    width: full-landscape;
    overflow: hidden;
    outline: 0;
    box-shadow: 0 0 0 0rem transparent;
    font-family: text-font;
    font-weight: weight;

}

app-client-list .btn-client-site-warning,
app-client-list .btn-client-site-warning:hover,
app-client-list .btn-client-site-warning:focus {
    background-color: warning;
    border-radius: 4px;
    color: white;
    width: full-landscape;
    overflow: hidden;
    outline: 0;
    box-shadow: 0 0 0 0rem transparent;
    font-family: text-font;
    font-weight: weight;
}
app-client-list .font-bold{
    font-weight: bold;
}
app-client-list .font-normal{
    font-weight: normal;
}
app-client-list .btn-outline-secondary{
    color: #2196f3;
    border-color: #2196f3;
}
app-client-list .overlay-header-title{
    font-weight: bold;
    font-size: 2rem;
    
}
app-client-list .togglebuttonOnshore{
    margin-right: 1rem;
    margin-left: 1rem;
}

app-client-list .overlayHeadings{
    margin-left: 2rem;
    margin-top: 20px;
    margin-bottom: 0;
    
}
app-client-list .overlayData{
    margin-left: 2rem;
    margin-bottom: 10px;    
}

app-client-list .switch{
    margin-left: 1.5rem;    
}

app-client-list .client-site-overlay-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: grid;
    justify-content: flex-end;
    backdrop-filter: brightness(0.5);
}
app-client-list .client-site-overlay {
        height: 100vh;
        width: 420px;
        background-color: white;
        box-shadow: 0px 0px 32px rgb(0 0 0);
        border-radius: 16px 0px 0px 16px;
        overflow-y: auto;
        overflow-x: hidden;
    }

    app-client-list .client-site-overlay-header {
        margin: 0.8rem;
        display: flex;
        gap: 1rem;
        grid-gap:0em;
    }

    app-client-list .ClientEdit-options-footer {
        display: flex;
        justify-content: flex-end;
        margin-right: 11px;
        gap: size;
      }
        app-client-list .btn-submit {
            /* background: title-text; */
            color: red;
        }

        app-client-list .btn-cancel {
            border-color: title-text;
            color: title-text;
        }

        app-client-list .btn-cancel:hover {
            /* background: none; */
            color: green;
        }

        app-client-list .form-row.form-element-group {
            border: none !important;
        }

app-client-list marketplace-button .btn.btn-secondary.large{
    color: #5009b5;
    background: #fff;
    border: none;
    margin-left: -0.1rem;
}

app-client-list marketplace-button .btn.btn-secondary.large .btn-text .fa-arrow-right{
    font-size: 30px;
}

app-client-list .hit-rate-heading {
    font-weight: 600;
    padding: 0;
    font-size: 15px;
    font-family: var(--font-bold)!important;
    margin-left: 2rem;
    margin-top: 5px;
    margin-bottom: 10px;
}

.hitRateForm {
    width: 8rem;
    margin-left: 2rem;
    margin-bottom: 15px;
    display: flex;
    flex-direction: row;
    height: 40px;
    align-items: center;
}

.hitRateForm marketplace-dynamic-form .form-col {
    padding: 0;
    max-width: none;
}

.hit-rate-error {
    margin-left: 2rem;
    color: #dd4548;
    font-size: .875em;
    font-weight: 400;
}

/* ==============================client-overlay end ================================================== */
marketplace-table .table-container .table-container-section .table-container__table_holder angular-slickgrid .gridPane .slickgrid-container .slick-pane .slick-viewport .grid-canvas .slick-row .slick-cell button
 {
    padding: 2px 8px !important;
}
