marketplace-landing-screen
    .landing-screen-wrapper
        margin-top: -10px
        #select-landing .carelon-theme .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected .ng-option-label, .carelon-theme marketplace-dynamic-form .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked .ng-option-label
            color: #333 !important

        display: flex
        justify-content: center
        align-items: center
        min-height: calc(85vh - 100px)

        .section-container 
            width: 40%

            h3 
                text-align: center
                font-family: 'elevance-semi-bold'
                margin-bottom: 1rem

            .client-selection-holder.applied-style
                background: #F6F6F6
                padding: 40px

            .client-selection-holder 
                .client-selection-text 
                    text-align: center
                    font-family: 'elevance-medium'

                .client-dropdown 
                    display: flex
                    justify-content: center

                .client-dropdown 
                    marketplace-select 
                        display: block
                        width: 40%

            .subscribed-apps
                marketplace-target-cards 
                    display: contents
                    .target-cards__container 
                        justify-content: center

                        .target-cards_holder
                            min-width: 300px 
                            .target-cards__container-target 
                                .fa
                                    font-size: 1.2rem
        .disableCard
            cursor: not-allowed
            pointer-events: none