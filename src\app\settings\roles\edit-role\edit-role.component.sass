app-edit-role

    font-family: "elevance-medium"

    .backdrop 
        position: fixed
        width: 100vw
        height: 100vh
        z-index: 999
        background-color: rgb(0, 0, 0, 0.2)

    .spinner-border 
        display: block
        position: fixed
        top: calc(50% - (58px / 2))
        right: calc(40% - (58px / 2))
        width: 5rem
        height: 5rem

    .redBorder 
        border-color: red !important

    marketplace-dynamic-form .form-row #parentclient
        margin-left: -1em
        width: 300px
        
    marketplace-dynamic-form .form-row #parentproduct
        width: 300px

    marketplace-dynamic-form .form-row #parentbusinessDivision
        width: 300px       

    marketplace-dynamic-form .form-row #parentinvType
        width: 300px

    marketplace-dynamic-form .form-row #parentreminderDate
        width: 300px

    marketplace-dynamic-form .form-row #parentclientSiteRow
        width: 200px

    marketplace-dynamic-form .form-row #parentteamSelect
        margin-left: 3em
        width: 250px

    .editBtns
        float: right
        margin-bottom: 2rem

    .page-wrapper
        padding: 1rem 1rem

        .elemInColumns
            display: flex
            flex-direction: column

        .page-header
            h3
                color: #5009B5
                font-family: 'elevance-medium'
                
        .button-container
            float: right
            margin-bottom: 10px

        .form-container
            marketplace-dynamic-form 
                .form-row
                    .form-row.form-radio.disabled
                        align-items: center
                        height: 38px
                        background-color: white !important

                label.form-label 
                    margin-top: 1rem

                .form-radio-button
                    .custom-control-label
                        margin-top: 5px  

                .form-group
                    marketplace-switch 
                        .switch-holder 
                            flex-direction: row
                            float: left
                            align-items: inherit

                            .switch 
                                margin-right: 1rem
                                margin-left: 1rem
                              
            .switch-view
                position: absolute
                right: 4rem
                z-index: 2

                marketplace-switch
                    .switch-holder 
                        flex-direction: row !important
                        align-items: baseline !important
                        justify-content: end !important

                        span 
                            padding: 0 5px
            
            .picklist-table-container
                display: flex
                justify-content: space-between
                
                .picklist-box
                    width: 80%

                    marketplace-pick-list .pick-list-holder 
                        .pickertoolbar input[name=filtername] 
                            width: 70%
                        
                        .fa.fa-chevron-circle-left
                            color: #fff
                .table-box
                    width: 20%
                    display: flex
                    align-items: center
                    justify-content: center
                    

        .user-list-container
            width: 20%
            height: 60vh

            .search-list__holder
                max-height: 60vh
                overflow-y: auto
                height: 60vh

                .search-lists_container
                    marketplace-simple-table 
                        .matrix-table__container 
                            table 
                                tbody 
                                    tr
                                        td
                                            padding: 1rem 0

                                        td:nth-child(1)                                            
                                            display: flex
                                            justify-content: center

        .label-title 
            font-family: 'elevance-medium'
        
          
        .label-value 
            font-weight: 500
            font-size: 16px
            color: #000000
            word-break: break-word
            font-family: 'elevance'  
            margin-bottom: 20px      


        
