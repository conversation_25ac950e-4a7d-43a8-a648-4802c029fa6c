<div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>
<span class="title"><a (click)="backToListPage()"><i class="fa fa-chevron-circle-left"></i></a>Add Fee
  Schedule</span>
<hr />

<div class="mar-top-20">
  <marketplace-dynamic-form *ngIf="showForm" [isSubmitNeeded]="false" [formJSON]="feeScheduleJson"
    (onValueChange)="formValid($event)">
  </marketplace-dynamic-form>
  <span class="btn-span pad-btm-30">
    <marketplace-button [label]="'Cancel'" [type]="'secondary'" [name]="'secondary'" (onclick)="backToListPage()">
    </marketplace-button>

    <marketplace-button [enabled]="enableSumbitButton" [label]="'Submit'" [type]="'primary'" [name]="'primary'" (onclick)="validateCreateForm()">
    </marketplace-button>

  </span>
</div>




<div class="modal" tabindex="-1" role="dialog" [ngStyle]="{'display':popupDisplayStyle}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-header-custom">
          <h4 class="modal-title custom-title">Attention !</h4>
        </div>
        <span class="btn-span"><a (click)="closePopup()"><i
              class="fa fa-times-circle-o fa-2x close-icon-color"></i></a></span>
      </div>
      <div class="modal-body custom-message">
        <p class="pad-30">Please fill all the fields</p>
      </div>
      <div class="modal-footer">
        <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closePopup()">
        </marketplace-button>

      </div>
    </div>
  </div>
</div>