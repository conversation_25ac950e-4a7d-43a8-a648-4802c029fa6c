{"switches": {"enableSorting": true, "enablePagination": true, "enableFiltering": true}, "colDefs": [{"name": "LIST OF PRODUCTS", "width": 100, "field": "productName", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "UNIT", "width": 100, "field": "dbgUnitName", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "DIVISIONS", "width": 100, "field": "productDivisionName", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "CREATED DATE", "width": 100, "field": "createdDate", "filterType": "Calendar", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "dateFormat": "MM/DD/YYYY"}, {"name": "CREATED BY", "width": 100, "field": "createdByUserId", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "LAST MODIFIED DATE", "width": 100, "field": "lastUpdatedDate", "filterType": "Calendar", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": "", "dateFormat": "MM/DD/YYYY"}, {"name": "LAST MODIFIED BY", "width": 100, "field": "lastUpdatedByUserId", "filterType": "Text", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "STATUS", "width": 100, "field": "activeFlag", "filterType": "Multi Select", "visible": "True", "editorType": "", "editorTypeRoot": "", "editorTypeLabel": ""}, {"name": "BUNDLES", "field": "bundleCount", "visible": "True", "editorType": "Text", "editorTypeRoot": "", "editorTypeLabel": "", "editorTypeValue": ""}, {"name": "", "field": "action", "filterType": "", "visible": "True", "editorType": "", "editorTypeRoot": "", "editorTypeLabel": "", "excludeFromExport": "true"}]}