
export const environment = {
  production: false,
  name: 'sit',

  // Auth Endpoints
  iamManagerAuth: 'https://api.portal.pi.sit.gcpdns.internal.das/pf-pi-iam-manager/api/v1/auth',
  iamManagerUser: 'https://api.portal.pi.sit.gcpdns.internal.das/pf-pi-iam-manager/api/v1/user',
  authService: 'https://api.portal.pi.sit.gcpdns.internal.das/pf-pi-auth-svc',
  //callBackUrl: 'http://localhost:4200/login/callback',
  callBackUrl: 'https://ui.portal.pi.sit.gcpdns.internal.das/login/callback',
  oktaCallBackUrl: 'https://ui.portal.pi.sit.gcpdns.internal.das/login/auth/callback',
  enableOkta: true,
  commonServicesUrl:"https://ui.common-services.portal.pi.sit.gcpdns.internal.das/callback?navigateTo=",
  refreshInterval: 60000, // 1 minute
  idleSeconds: 600, // 10 minutes

  publicKey: `{
      "kty": "RSA",
      "e": "AQAB",
      "use": "sig",
      "kid": "WxcIMfKFAQ9d045V04sOPHDBr9FIzaGJnEsHehLH51c",
      "alg": "RS256",
      "n": "oeDesitXD-kgQavO-t9kj-DUHd_alfaMQzBLtjdG4QHLMeAZS2aFxYlmZPLA_GQpEs4Iy8kIoUtaenN2S8UdqQcCOzwvsMpHT5ojwlocqTMH5ofcdAcVUIBXkjuJ-c9sfMYUk3sd0ie4CwuTpN_LMVsygvvToX1n1LBww-dt1zwUxM-o0zbtq-cbRGbaJsrO5F2Uax4Y2Ftn9RtJPYZ4iBn6yPJyrLx_XYXhRfDJBV-nGMJtZ6oXJ2UIkfwmX_rV31y0zabvFAQMXayb2BmfSO_nVNKDJZ152ILhEsQh_la7vUgv6nqvCoiXji2V00a4vX4kEJq77kylYqxffYnPkw"
  }`,
  //Identity Service
identityUrl: 'https://api.portal.pi.sit.gcpdns.internal.das/pf-pi-identity-svc'
  
}
