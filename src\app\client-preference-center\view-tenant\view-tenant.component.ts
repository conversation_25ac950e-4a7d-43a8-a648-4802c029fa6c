import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ClientApiService } from '../../_services/client-preference-api.service';

@Component({
  selector: 'app-view-tenant',
  templateUrl: './view-tenant.component.html',
  styleUrls: ['./view-tenant.component.css']
})
export class ViewTenantComponent implements OnInit {
  ViewTenantJson: any;
  TENANTS_JSON: any = "./assets/json/client-preference/add-edit-tenant-form.json";
  enableForm: any = false;
  @Output() DataEvent = new EventEmitter<string>();
  editData: any = {};
  tenantId: number;
  constructor(private clientApiService: ClientApiService) { }

  ngOnInit(): void {
    this.tenantId = this.clientApiService.selectedTenantId;
    this.clientApiService.getTenantsAssetsJson(this.TENANTS_JSON).subscribe((jsonData) => {
      this.ViewTenantJson = jsonData;
      this.getFormDetails();
    })
  }

  /**
   * Back to list Data exchange view
   */
  backToListPage(): void {
    this.DataEvent.emit('back to list');
  }

  getFormDetails(): void {
    this.clientApiService.getTenantDetailsById(this.tenantId).subscribe((data) => {
      this.editData = data['body'];
      if (this.editData) {
        this.ViewTenantJson.forEach((x) => {

          switch (x.name) {
            case 'tenantId':
              x.name == 'tenantId' ? x.selectedVal = data['body']?.tenantId : x.selectedVal = null;
              x.name == 'tenantId' ? x.value = data['body']?.tenantId : x.value = null
              x.required = true
              break;

            case 'tenantName':
              x.name == 'tenantName' ? x.selectedVal = data['body']?.tenantName : x.selectedVal = null;
              x.name == 'tenantName' ? x.value = data['body']?.tenantName : x.value = null
              break;

            case 'tenantCode':
              x.name == 'tenantCode' ? x.selectedVal = data['body']?.tenantCode : x.selectedVal = null;
              x.name == 'tenantCode' ? x.value = data['body']?.tenantCode : x.value = null
              break;

            case 'tenantDesc':
              x.name == 'tenantDesc' ? x.selectedVal = data['body']?.tenantDesc : x.selectedVal = null;
              x.name == 'tenantDesc' ? x.value = data['body']?.tenantDesc : x.value = null
              x.placeholder = 'No tenant Description'
              break;

            case 'offshoreAccess':
              x.name == 'offshoreAccess' ? x.selectedVal = data['body']?.offshoreAccessibleIndicator : x.selectedVal = null;
              x.name == 'offshoreAccess' ? x.value = data['body']?.offshoreAccessibleIndicator : x.value = null
              break;

            case 'active':
              x.name == 'active' ? x.selectedVal = data['body']?.active : x.selectedVal = null;
              x.name == 'active' ? x.value = data['body']?.active : x.value = null
              break;

            case 'offshoreAccLogic':
              x.name == 'offshoreAccLogic' ? x.selectedVal = data['body']?.offshoreAccessibleIndicatorLogicText : x.selectedVal = null;
              x.name == 'offshoreAccLogic' ? x.value = data['body']?.offshoreAccessibleIndicatorLogicText : x.value = null
              x.placeholder = 'No Offshore Access Logic'
              break;

            case 'SecuLvlCd':
              x.name == 'SecuLvlCd' ? x.selectedVal = data['body']?.securityLevelCode : x.selectedVal = null;
              x.name == 'SecuLvlCd' ? x.value = data['body']?.securityLevelCode : x.value = null
              x.placeholder = 'No Security Level code'
              break;

            default:
              break;
          }
          x.disabled = true;
        })
        this.enableForm = true;
      }
    })
  }

}
