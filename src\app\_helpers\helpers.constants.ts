export const externalAuthenticationConstants = {
     ACCOUN_DEACTIVATED: "logged in user is deactivated",
     message: 'Please Fill All The Fields',
     messageNotMatchingPasswords: "Those passwords didn't match. Try again.",
     jsonPath: './assets/json/change-password/change-password-form.json',
     answer: 'answer',
     securityQuestion: 'securityQuestion',
     RED_BORDER: 'redBorder',
     FORM_DETAILS: 'formDetails',
     YES: 'yes',
     FAIL: "Fail",
     SUCCESS: "Success",
     WARNING: "Warning",
     SUCCESS_MESSAGE: "Your password has been changed successfully!",
     NO_ROLES: "No roles assigned to user",
     INSUFFICIENT_PRIVILEGE_MESSAGE: "You have insufficient privileges to access this page. Please check with administrator.",
     TOKEN_EXPIRED: "token expired",
     ERROR: "Error",
     USER_EXIST_ERRMSG: "User ID already exists, Please enter different user id",
     ACCESS_TOKEN_ERRMSG: "Error while receiving access token from SOA endpoint",
     CREATE_USER_ERRMSG: "Error occurred while creating new user",
     CHANGE_PASSWORD_ERRMSG: "Error occurred while changing password",
     SIGN_IN_ERROR_MSG: "Invalid User Id/Password",
     USER_NOT_FOUND: "Invalid User Id",
     OTP_ERROR_MSG: "Invalid OTP",
     GENERATE_PASSWORD_MESSAGE: "Password generated successfully",
     GENERATE_PASSWORD_ERRMSG: "Error occurred while generating password",
     NOT_FOUND: "Not Found",
     SEARCH_USER_ERRMSG: "Error occurred while searching user in SOA",
     USER_ID: "userId",
     USER_NAME: "userName",
     CHANGE_PASSWORD_NAVIGATION: "/change",
     SECRET_QUESTION2: "SECRET_QUESTION2",
     SECRET_QUESTION3: "SECRET_QUESTION3",
     SECRET_ANSWER2: "SECRET_ANSWER2",
     SECRET_ANSWER3: "SECRET_ANSWER3",
     REPLACE: "REPLACE",
     GET_SECURITY_QUESTION_ERRMSG: "Unable to fetch security questions",
     SAVE_SECURITY_QUEST_ERRMSG: "Error while saving security questions",
     MFA_COOKIE_ERRMSG: "Error occurred while calling Cookie MFA",
     EMAIL: "email",
     CONTINUE: "Continue",
     TWOFACTOR: "TwoFactor",
     COOKIE_VERIFIER: "verified/found",
     OTP_DURATION: "5",
     AUTHENTICATED: "authenticated",
     CONTACT_US_URL: "https://www.careloninsights.com/payment-integrity/claims-anomaly",
     USER_IS_NOT_FOUND: "USERNOTFOUND",
     USER_NOT_FOUND_IN_DB: "logged in user not found in user table"
}