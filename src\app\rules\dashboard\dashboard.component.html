<div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>
<div class="fixed-nav bg-gray">
  <div class="content-wrapper">
    <div class="container-fluid">
      <div class="breadcrumb-container">
        <app-breadcrumbs-nav [headerText]="headerText" [breadcrumbDataset]="breadcrumbDataset"></app-breadcrumbs-nav>
        <div class="pad-1p pad-tp-2p">
          <div class="pad-1p">
            <marketplace-cards [dataset]="cardsDataset" [groupLabel]="'Quick Actions'"
              (onSelection)="selectedLink($event)">
            </marketplace-cards>
          </div>
        </div>
      </div>
      <div class="tp-bt-rem-1">
        <div>
          <span class="table-title">View Rules</span>
          <span class='btn-span'>
            <marketplace-button [label]="'Add New Rule'" [type]="'primary'" [name]="'primary'" [enabled]="!isReadOnly"
              (onclick)="AddNewRulefun()">
            </marketplace-button>

          </span>
        </div>
        <div class="card-body">
          <div class="rule-table">
            <marketplace-table [id]="'rulelist-table'" [dataset]="rulesList" [rowHeight]="ruleDashbordTableRowhg"
              [headerRowHeight]="ruleDashbordTableHeaderhg" [isRowSelectable]="false" [columnDefinitions]="columnConfig"
              (onCellValueChange)="cellValueChanged($event)" (onCellClick)="cellClicked($event)"
              (onTableReady)="tableReady($event)" [dropdownOptions]="kebabOptions"
              (onDropdownOptionsClick)="onDropdownOptionsClick($event)" [customExportConfig]="customExportAll" *ngIf="isUserTableReady">
            </marketplace-table>
          </div>
        </div>
      </div>

    </div>
  </div>