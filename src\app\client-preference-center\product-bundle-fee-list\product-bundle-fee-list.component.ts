import { Component, OnInit, AfterViewInit, AfterViewChecked, ViewEncapsulation, Output, EventEmitter, Input } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { AuthService } from 'src/app/_services/authentication.services';

@Component({
  selector: 'app-product-bundle-fee-list',
  templateUrl: './product-bundle-fee-list.component.html',
  styleUrls: ['./product-bundle-fee-list.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class ProductBundleFeeListComponent implements OnInit {
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  public dataDate: any = "21/02/2022";
  public lastRegfreshDate: any = "21/02/2022";
  public totalEntries: number;
  public clientProductData: any;
  public productBundleFeeData: any;
  public clientPreferenceData: any;
  public clientProductColConfig: any;
  public productBundleFeeColConfig: any;
  public clientPreferenceColConfig: any;
  public dataRoot = "src";
  public client;
  public tabSelected: any = 0;
  public tableRedraw: any;
  public showFeeAdd: boolean = false;
  public showFeeEdit: boolean = false;
  public showFeeList: boolean = true;
  public selectedRowData: any;
  public breadcrumbDataset: any = [];
  type: any;
  customExport: any = {
    enabled : true,
    fileName: 'feeSchedule.xls'
};
  public kebabOptions: any = [{ label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.5H2.75C2.61193 2.5 2.5 2.61193 2.5 2.75V13.25C2.5 13.3881 2.61193 13.5 2.75 13.5H7.25C7.66421 13.5 8 13.8358 8 14.25C8 14.6642 7.66421 15 7.25 15H2.75C1.7835 15 1 14.2165 1 13.25V2.75C1 1.7835 1.7835 1 2.75 1H8.25C10.8734 1 13 3.12665 13 5.75V7H8.75C7.7835 7 7 6.2165 7 5.25V2.5ZM8.5 2.50947V5.25C8.5 5.38807 8.6119 5.5 8.75 5.5H11.4905C11.3691 3.9044 10.0956 2.63085 8.5 2.50947Z" fill="black"/>
<path d="M12.7432 9.64823C12.6935 9.28215 12.3797 9 12 9C11.5858 9 11.25 9.33579 11.25 9.75V11.25H9.75L9.64823 11.2568C9.28215 11.3065 9 11.6203 9 12C9 12.4142 9.33579 12.75 9.75 12.75H11.25V14.25L11.2568 14.3518C11.3065 14.7178 11.6203 15 12 15C12.4142 15 12.75 14.6642 12.75 14.25V12.75H14.25L14.3518 12.7432C14.7178 12.6935 15 12.3797 15 12C15 11.5858 14.6642 11.25 14.25 11.25H12.75V9.75L12.7432 9.64823Z" fill="black"/>
</svg> Edit Fee Schedule`, id: 'editFeeSchedule' }, { label: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.00014 1.78223C4.20427 1.78223 1.00014 2.69146 1.00014 4.28223C1.00014 4.32884 1.00289 4.37488 1.00833 4.42032C0.99602 4.5019 0.99707 4.58692 1.01338 4.67256L2.39404 11.921L2.44008 12.1358C2.94747 14.2643 4.85306 15.7822 7.06015 15.7822H8.94014L9.1598 15.7772C11.3456 15.677 13.1933 14.0891 13.6062 11.9209L14.9869 4.67257L14.9992 4.57131C15.0019 4.52014 14.9994 4.4696 14.9919 4.42032C14.9974 4.37488 15.0001 4.32884 15.0001 4.28223C15.0001 2.73123 11.9542 1.82811 8.28368 1.78393L8.00014 1.78223ZM13.2014 6.02994C11.9087 6.51969 10.0398 6.78223 8.00014 6.78223C5.96042 6.78223 4.09156 6.51969 2.7989 6.02994L3.86755 11.6403C4.14784 13.1119 5.39416 14.1928 6.874 14.2769L7.06015 14.2822H8.94014C10.4382 14.2822 11.7332 13.2602 12.0927 11.8222L12.1327 11.6403L13.2014 6.02994ZM8.00014 3.28223C9.56668 3.28223 11.0737 3.46997 12.1889 3.78644L12.3996 3.84932C12.8072 3.9772 13.1299 4.11709 13.3436 4.25453L13.3861 4.28223L13.3436 4.30993C13.0943 4.47027 12.6966 4.63395 12.1889 4.77801C11.0737 5.09449 9.56668 5.28223 8.00014 5.28223C6.43358 5.28223 4.9266 5.09449 3.81135 4.77801L3.60062 4.71514C3.261 4.60857 2.9803 4.49366 2.77249 4.37878L2.61414 4.28223L2.65666 4.25452C2.906 4.09418 3.30367 3.9305 3.81135 3.78644C4.9266 3.46997 6.43358 3.28223 8.00014 3.28223Z" fill="#231E33"></path></svg> Terminate Fee Schedule`, id: 'terminateFeeSchedule' }];
  @Input()
  set navigate(val) {
    if (val){
    this.receiveFromAddEdit()
    }
  }
  @Input('isReadOnly') isReadOnly : boolean
  @Output() feeSetupEvent = new EventEmitter<any>();
  public tableIsready = false;
  constructor(private router: Router, private clientApiService: ClientApiService, private route: ActivatedRoute,private authService: AuthService) {
   this.client = Number(this.route.snapshot.paramMap.get('clientId'));
  }

  ngOnInit() {
    this.isReadOnly = !this.authService.isWriteOnly;
    this.isReadOnly ? this.kebabOptions = [] : this.kebabOptions
    this.serviceData();
  }
  /*
  get all Product list based on client id and product id
  product id is optional
  */
  serviceData(): void {
    this.tableIsready= false;
    this.productBundleFeeColConfig = {
      "switches": {
        "enableSorting": true,
        "enablePagination": true,
        "enableFiltering": true
      },
      "colDefs": [
        {
          "name": "PRODUCT NAME",
          "field": "productName",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "BUNDLE NAME",
          "field": "bundleName",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "BUNDLE DESCRIPTION",
          "field": "bundleDescription",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "STANDARD FEE",
          "field": "standardFee",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
           "customFormatter":this.standarform
        },
        {
          "name": "FEE METHOD",
          "field": "feeMethod",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "FEE TYPE",
          "field": "feeType",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "STATUS",
          "field": "status",
          "visible": "True",
          "editorType": "",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "customFormatter": this.customFormatterStatus,
          "width": 100
        },
        {
          "name": "START DATE",
          "filterType": "Calendar",
          "field": "startDate",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
          "dateFormat": 'MM/DD/YYYY'
        },
        {
          "name": "END DATE",
          "filterType": "Calendar",
          "field": "endDate",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
          "dateFormat": 'MM/DD/YYYY'
        }
      ]
    };
    this.clientApiService.getProductBundleFeeSchedules(this.client, this.clientApiService.selectedProductId)
      .subscribe(data => {
        if (!data) return;
        this.productBundleFeeData = data;
        this.totalEntries = data.length;
        this.tableIsready = true;
      });
   this.tableIsready = true;
  }

   /**
   * customFormatterStatus funtion for button in Rule table
   * @param event 
   */
  customFormatterStatus(event) {
    let btn;
    switch (event.value) {
      case 'Active':
        btn = "<button type='button' title='Active' class='btn btn product-list btn-active btn-wrap-text'>Active</button>";
        break;
      case 'Inactive':
        btn = "<button type='button' title='Inactive' class='btn btn product-list btn-inactive btn-wrap-text'>Inactive</button>";
        break;
      case 'Pending':
        btn = "<button type='button' class='btn btn product-list btn-pending btn-wrap-text'>Pending</button>";
        break;
    }
    return btn;
  }

  /**
   * standarform funtion sending data in percentage
   * @param event 
   */
  standarform(event) {
    return  Number(event.value) +' %';
    
  }

  /**
  * cell click event
  * @param event 
  */
  cellClicked(event: Event): void {
    if (event['eventData'].target.attributes) {
      if (event['eventData'].target.attributes.dataaction && event['eventData'].target.attributes.datevalue) {
        console.log('event', event);
        let rulesAction = event['eventData'].target.attributes.dataaction.nodeValue;
        this.showFeeList = false;
        switch (rulesAction) {
          case 'edit':
            this.selectedRowData = event['currentRow'];
            this.showFeeAdd = false;
            this.showFeeEdit = true;
            break;
          case 'terminate':
            this.showFeeEdit = true;
            this.showFeeAdd = false;
            break;
        }
      }
    }
  }

/*
Navigate tabs Product through Kebab menu
type-> defines view screen or edit screen
*/
  moveToSelectedTab(event: Event): void {
    this.selectedRowData = event['currentRow'];
    let selectedOption = event['text'];
    if (selectedOption == "Edit Fee Schedule" || selectedOption == "Terminate Fee Schedule") {
      if (selectedOption === "Terminate Fee Schedule") {
        this.type = "view"
        this.activePageInfoFunction('Terminate Fee');
      } else {
        this.type = "edit"
        this.activePageInfoFunction('Edit Fee');
      }
      this.showFeeList = false;
      this.showFeeAdd = false;
      this.showFeeEdit = true;
    }
  }

  /*
  Navigate to add fee setup screen and hide all edit and list screen
  */ 
  AddFeeSchedule(): void {
    this.showFeeList = false;
    this.showFeeEdit = false;
    this.showFeeAdd = true;
    this.activePageInfoFunction('Add Fee');
  }

  /**
   * Navigate and update data in table by call services and show list screen
   * @param event 
   */

  receiveFromAddEdit() {
    this.serviceData();
    this.showFeeEdit = false;
    this.showFeeAdd = false;
    this.showFeeList = true;
    this.activePageInfoFunction('');
  }
/**
 * Table  redraw
 */
  redrawTable() {
    setTimeout(() => this.tableRedraw = Date.now(), 100);
  }

   /**
   * Method is used get data based on table screen on view,edit,add
   * 'info' carries data of page,row 
   */
  activePageInfoFunction(name){
    let info = {
      feePageName :name, 
      selectedRowData : this.selectedRowData,
      name: 'Fee Schedule'
    }
    this.feeSetupEvent.emit(info);
  } 
}
