import {
  Component,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { ProductApiService } from 'src/app/_services/product-api.service';
import { Router, ActivatedRoute } from '@angular/router';
import { UtilitiesService } from 'src/app/_services/utilities.service';
import { ToastService } from 'src/app/_services/toast.service';
import { forkJoin } from 'rxjs';
import { CookieService } from 'ngx-cookie-service';
import { ROUTING_LABELS } from 'src/app/_constants/menu.constant';
import { environment } from 'src/environments/environment';
import { AuthService } from 'src/app/_services/authentication.services';
@Component({
  selector: 'app-edit-bundle',
  templateUrl: './edit-bundle.component.html',
  styleUrls: ['./edit-bundle.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class EditBundleComponent implements OnInit {
  public generalDetailsJson: any[];
  public isFormready: boolean = false;
  public conceptsName: any[];
  public showLoader: boolean = true;
  public product: any[];
  public bundle: any[];
  public prodId: any;
  public bundleId: any;
  public prodIdBundles: any;
  public dataForDynamicForm: any = [];
  public postDataJson: any;
  public postDataJsonForVaildation: any;
  public isEnabled: any = false;
  public isLoading: any = false;
  public productName: any;
  public breadcrumbDataset: any;
  public inActivationPopup: boolean = false;
  public activationPopup: boolean = false;
  public alreadyActive: boolean = false;

  popupDisplayStyle: any = 'none';
  generalDetailsFormEvent: any;
  editErrOpenModel: boolean = false;
  public userId: String = "";
  constructor(
    private router: Router,
    private productApiService: ProductApiService,
    private route: ActivatedRoute,
    private dateService: UtilitiesService,
    private alertService: ToastService,
    private cookieService: CookieService,
    public authService: AuthService
  ) {
    this.bundleId = Number(this.router.url.slice(this.router.url.lastIndexOf('/') + 1));
    this.prodId = Number(this.router.url.split('/').slice(-2)[0]);
    this.userId = this.cookieService.get(ROUTING_LABELS.USER_ID).toUpperCase();
  }
  /**
   * calling both API's altogether to set data in dynamic form
   */
  getProductandConceptdetails() {
    let tokenVal = localStorage.getItem("token")
    return forkJoin([
      this.productApiService.getProductConceptsId(tokenVal),

      this.productApiService.getparticularBundleDetails(this.bundleId)]);

  }

  ngOnInit(): void {
    this.productApiService.getProductDetails().subscribe((data) => {
      let matchedProduct = data.find((x) => x.productId == this.prodId)
      this.productName = matchedProduct?.productName;
      this.breadcrumbDataset = [{ label: 'Home', url: '/' }, { label: `${this.productName}`, url: 'settings/product' }, { label: 'Bundle list', url: `settings/product/bundle/${this.prodId}` }, { label: `Edit Bundle` }];
    })

    this.getProductandConceptdetails().subscribe(([conceptdata, apidata]) => {
      if (apidata && conceptdata) {
        this.alreadyActive = apidata.activeFlag;
        apidata.concepts.forEach(element => {
          this.dataForDynamicForm.push(element.cncptId);
        });
        this.conceptsName = conceptdata['executionConceptAnalyticResponse'].map(e => ({ name: e.exConceptReferenceNumber, description: e.busConceptDesc }));
        this.generalDetailsJson = [
          {
            "type": "group",
            "name": "General_1",
            "label": "",
            "column": "2",
            "groupControls": [
              {
                "label": "Bundle Name",
                "type": "text",
                "name": "bundle_name",
                "column": "1",
                "disabled": false,
                "value": apidata?.bundleName,
                required: true,
                "placeholder": "Type Bundle Name"
              },
              {
                "label": "Description",
                "type": "textarea",
                "name": "bundle_desc",
                "column": "1",
                "disabled": false,
                "value": apidata?.bundleDesc,
                required: true,
                "maxLimit": "200",
                "placeholder": "Type Description"
              },
            ]
          },
          {
            "type": "group",
            "name": "General_2",
            "label": "",
            "column": "2",
            "groupControls": [
              {
                "label": "Created By",
                "type": "text",
                "name": "create_by",
                "column": "2",
                "disabled": true,
                "value": apidata?.createdUserId
              },
              {
                "label": "Modified By",
                "type": "text",
                "name": "modified_by",
                "column": "2",
                "disabled": true,
                "value": apidata?.lastModifiedUserId
              },
              {
                "id": "effstart_date",
                "label": "Effective Start Date",
                "type": "date",
                "name": "effstart_date",
                "column": "2",
                "disabled": false,
                "pickerType": "single",
                required: true,
                "value": this.dateService.getDbgDateFormat(apidata?.effStartDate),
                "dateFormat": 'MM-DD-YYYY',
                "placeholder": "Select Effective Start Date",
                "relatedDateControls": [{
                  "target": 'effend_date'
                }]
              },
              {
                "id": "effend_date",
                "label": "Effective End Date",
                "type": "date",
                "name": "effend_date",
                "column": "2",
                "disabled": false,
                required: false,
                "closeOnSelect": true,
                "pickerType": "single",
                "value": this.dateService.getDbgDateFormat(apidata?.effEndDate),
                "minDate": this.dateService.getFutureDate(apidata.effStartDate, 1, 'MM-dd-YYYY'),
                "dateFormat": 'MM-DD-YYYY',
                "placeholder": "Select Effective End Date"
              },
              {
                label: 'Add to a Product',
                group: '',
                type: 'text',
                name: 'product_id',
                column: '2',
                groupColumn: '2',
                disabled: true,
                value: this.productName ? this.productName : ""
              },
              {
                options: this.conceptsName,
                optionName: 'name',
                optionValue: 'name',
                label: 'Add a Concept',
                group: '',
                type: 'select',
                multiple: true,
                closeOnSelect: true,
                required: true,
                name: 'concept_name',
                column: '2',
                groupColumn: '2',
                disabled: false,
                selectedVal: this.dataForDynamicForm,
                placeholder: "Select Concept Name"
              }
            ]
          }
        ];
        setTimeout(() => (this.isFormready = true, 1000));
        this.showLoader = false;
      }
    }, err => {
    });

  }

  /**
   * checks for undefined value
  */
  isDefined(fieldValue) {
    if (fieldValue != undefined) return true;
    else return false;
  }

  /**
   * To highlight fieds which failed validation
  */
  showAllInvalidFields() {
    this.editErrOpenModel = true;
    this.resetValidFields();
    const invalidCollection = document.querySelectorAll(
      `marketplace-dynamic-form input.form-control.ng-invalid ,
      marketplace-textarea.ng-invalid .textarea-holder textarea,
      marketplace-dynamic-form .ng-select.ng-invalid .ng-select-container`
    );
    for (let i = 0; i < invalidCollection.length; i++) {
      invalidCollection[i].classList.add('redBorder');
    }
    this.popupDisplayStyle = 'block';
  }
  /**
   * closing the pop-up
  */
  editErrClosePopup = () => {
    this.editErrOpenModel = false;
  }
  /**
   * closing the pop-up
  */
  closePopup() {
    this.popupDisplayStyle = 'none';
  }

  /**
   * To remove highlighting from fieds which passed validation
  */
  resetValidFields() {
    const collection = document.querySelectorAll(
      `marketplace-dynamic-form input.form-control.ng-valid ,
      marketplace-textarea.ng-valid .textarea-holder textarea,
      marketplace-dynamic-form .ng-select.ng-valid .ng-select-container`
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].classList.remove('redBorder');
    }
  }
  /**
   * Validating all the dynamic forms for the mandatory fields 
   * and highlighting the failed fields by calling showAllInvalidFields()
  */
  validateEditDynamicForms() {
    if (this.isDefined(this.generalDetailsFormEvent) && this.generalDetailsFormEvent['status'] == 'INVALID') {
      this.showAllInvalidFields();
      return;
    } else {
      this.resetValidFields();
    }
    this.updateBundle();
  }

  /**
   * Method to map the values in Dynamic form Json
   */
  mapValuesToJson(event: any) {
    this.generalDetailsFormEvent = event;
    let conceptObj = [];
    event.value['General_2'].concept_name.forEach(element => {
      conceptObj.push({
        "conceptId": element,
        "conceptDescription": this.conceptsName.find(e => e.name == element)?.description ? this.conceptsName.find(e => e.name == element).description : ""
      })
    });
    this.isEnabled = true;
    if (event.value['General_2'].activate_bundle == "") {
      event.value['General_2'].activate_bundle = false;
    }
    let effectiveStartDate = event.value['General_2'].effstart_date.split("-");
    let effectiveEndDate = event.value['General_2'].effend_date.split("-");

    this.postDataJson =
    {
      "bundleName": event.value['General_1'].bundle_name,
      "bundleDesc": event.value['General_1'].bundle_desc,
      "prodId": this.prodId,
      "bundleId": this.bundleId,
      "activeFlag": effectiveStartDate > effectiveEndDate ? true : false,
      "createdByUserId": event.value['General_2'].create_by,
      "effStartDate": effectiveStartDate[2] + '-' + effectiveStartDate[0] + '-' + effectiveStartDate[1],
      "lastUpdatedByUserId": this.userId,
      "effEndDate": effectiveEndDate[2] + '-' + effectiveEndDate[0] + '-' + effectiveEndDate[1],
      "concepts": conceptObj
    };
    this.postDataJsonForVaildation =
    {
      "bundleName": event.value['General_1'].bundle_name,
      "bundleDesc": event.value['General_1'].bundle_desc,
      "prodId": event.value['General_2'].product_id,
      "activeFlag": effectiveStartDate > effectiveEndDate ? true : false,
      "createdByUserId": event.value['General_2'].create_by,
      "effStartDate": event.value['General_2'].effstart_date,
      "lastUpdatedByUserId": "string",
      "effEndDate": event.value['General_2'].effend_date,
      "concepts": conceptObj
    }
    this.checkValidation();
  }

  checkBundleActive(startDate, endDate): boolean {
    if (startDate != undefined && startDate != "" && endDate != undefined && endDate != "") {
      this.dateService.checkActiveFlag(startDate, endDate);
    }
    return false;


  }

  checkValidation() {
    if (this.postDataJson.bundleName == undefined || this.postDataJson.bundleName == "" ||
      this.postDataJson.bundleDesc == undefined || this.postDataJson.bundleDesc == "" ||
      this.postDataJson.prodId == undefined || this.postDataJson.prodId == "" ||
      this.postDataJson.concepts == undefined || this.postDataJson.concepts == "" ||
      this.postDataJsonForVaildation.effStartDate == undefined || this.postDataJsonForVaildation.effStartDate == "" ||
      this.postDataJsonForVaildation.effEndDate == undefined || this.postDataJsonForVaildation.effEndDate == "" ||
      this.postDataJsonForVaildation.concepts == undefined || this.postDataJsonForVaildation.concepts === 0) { 
      this.isEnabled = true;
    }

  }
  /**
   * Show alert popup if user try to inactivate bundle
   */
  updateBundle() {
    if (this.postDataJson.effEndDate != undefined && this.postDataJson.effEndDate != "") {
      if (this.alreadyActive && this.dateService.checkInputDatePastCurrentDt(this.postDataJson.effEndDate)) {
        this.inActivationPopup = true;
        return;
      }
      if (!this.alreadyActive && this.dateService.checkActiveFlag(this.postDataJson.effStartDate, this.postDataJson.effEndDate)) {
        this.activationPopup = true;
        return;
      }
      this.updateBundleData(false);
    }

  }

  /**
   * Method for Editing the Bundle 
   */
  updateBundleData(inActive: boolean) {
    this.isLoading = true;
    this.checkValidation();
    this.postDataJson["feeSchdlInactive"] = inActive;
    this.productApiService.addandEditProductBundle(this.postDataJson).subscribe(
      (data) => {
        if (data) {
          this.backToPreviousPage();
          this.alertService.setSuccessNotification({
            notificationHeader: "Success",
            notificationBody: `Bundle Edited Successfully`,
          });
        }
      },
      error => {
        this.alertService.setErrorNotification({
          notificationHeader: "Warning",
          notificationBody: error,
        });
        this.isLoading = false;
      });
  }

  /**
      * breadcrumSelection Funtion
      */
  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }

  backToPreviousPage() {
    window.history.back()
  }

  ngAfterViewInit(): void {
    const collection = document.querySelectorAll(
      'marketplace-dynamic-form button'
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].remove();
    }
  }

  /**
   * To Close the MKP Popup for Mandatory fields
   */
  cancelPopUp(): void {
    this.inActivationPopup = false;
    this.activationPopup = false;
  }
}
