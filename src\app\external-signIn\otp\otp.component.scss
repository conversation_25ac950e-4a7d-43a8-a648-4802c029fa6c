app-otp {
      .row {
        display: flex;
        margin: 20px 0px;
        justify-content: center;
        align-self: center;
        width: 100%;
  
        a {
          font-size: 12px;
          text-align: center;
          color: #007BFF !important; 
          cursor: pointer;
  
          &:hover {
              text-decoration: none;
              color: #0056b3 !important; 
          }
      }
      }
  
      .elevated-card {
        box-shadow: 0 5px 5px -3px #0003, 0 8px 10px 1px #00000024, 0 3px 14px 2px #0000001f;
        height: fit-content;
        width: 440px;
        display: flex;
      flex-direction: column;
        align-items: center;
        padding: 50px 25px 60px 25px;
      margin: 10px 0;
  
        h3 {
          font-weight: bold;
        }
  
        marketplace-dynamic-form {
          margin: 10px 0;
  
          .form_button_holder {
            text-align: center;
          }
  
        }
  
        .red-font {
          color: #FF0000;
          padding: 0 0 14px 14px;
          font-size: 12px;
        }
      }
    
  }