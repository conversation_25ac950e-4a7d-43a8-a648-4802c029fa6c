import { Component, OnInit, Output, EventEmitter, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-type-details',
  templateUrl: './type-details.component.html',
  styleUrls: ['./type-details.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class TypeDetailsComponent implements OnInit {
  public headerText = 'Add New Rule Sub Type';
  public isPriviousRedirectPage = true;
  breadcrumbDataset:any = [{label: 'Home', url:'/dashboard'},
  { label: 'Rules Engine', url:'/rules'},
  { label: 'Setup Rule Sub Type', url:'rules/rule-type'},{ label: 'Add New Rule Sub Type', url:'/rules/rule-type/details'}];
  
  enableSumbitButton: boolean = false;

  viewType = "details";
  ruleTypes: any = [ {name: "Exclusion", value: "Exclusion"} , {name: "Expiration/Lookback Rule", value: "Expiration/Lookback Rule"} , {name: "On Hold", value: "On Hold"} , {name: "No Recovery", value: "No Recovery"} , {name: "Lag", value: "Lag"} ];
  RequiredFieldsText: string = "Required Fields";
  RequiredFieldsData = [{
    label: 'Rule Subtype',
    value: 'item_1'
  }, {
    label: 'Calculation Fields',
    value: 'item_2'
  }, {
    label: 'Lookback period',
    value: 'item_3'
  }, {
    label: 'Field name 4',
    value: 'item_4'
  }, {
    label: 'Field name 5',
    value: 'item_5'
  }, {
    label: 'Field name 6',
    value: 'item_6'
  }];

  label: string = "Required Fields";
  name: string = "required_fields_dropdown";
  type: string = "multiselect";

  ruleSubTypeFormJSON: any = [
    {
      options: this.ruleTypes,
      type: 'select',
      name: 'rule_type',
      label: 'Rule Type',
      required: true,
      optionName: 'name',
      optionValue: 'value',
      column: 3,
      closeOnSelect: true,
      id: 'rule_type',
      placeholder:'Choose Rule Type'
    },
    {
      type: 'text',
      name: 'ruleSubtype',
      label: 'Rule Sub Type Name',
      required: true,
      column: 3,
      id: 'ruleSubtype',
      placeholder:'Enter Rule Sub Type Name'
    }
  ];
  
  constructor(private router: Router) { }

  ngOnInit(): void {
  }
  /**
   * checks for null value
  */
  isNull(fieldValue) {
    if (fieldValue == null || fieldValue == "") return true;
    else return false;
  }

  /**
   * on dynamic form value changes
   */
  onValueChanges(event: any) {
    if (!(this.isNull(event.current.ruleSubtype) || this.isNull(event.current.rule_type))) {
      this.enableSumbitButton = true;
    } else {
      this.enableSumbitButton = false
    }
  }

  /**
   * method fires on click of submit buttton
   */
  onSubmitRuleTypeDetails(event: any) {

  }

  /**
   * method fires on click of Cancel buttton
   */
  onCancelDetails(event: any) {
    this.router.navigate([`product-catalog/rules/rule-type`]);
  }

  /**
    * breadcrumSelection Funtion
    */
   breadscrumSelection(event){ 
    this.router.navigate([`${event.selected.url}`]);
  }

}
