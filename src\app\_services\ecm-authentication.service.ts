import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { ToastService } from './toast.service';

@Injectable({
  providedIn: 'root'
})
export class EcmAuthenticationService {
  sessionToken: string;
  private refreshTokenTimeout;

  constructor(private http: HttpClient, private alertService: ToastService) { }

  /**
   * @function authenticateECMUser: Used to authenticate ECM users 
   * @returns User JWT token which expires frequently
   * @params username, password and productId where the productId is used to help access to DBG(10)
   * @description Once login is implemented, we will be utilizing this code at the bootstrap of application.
   * @usedby Scheduler Team, Rules team..
   */
  authenticateECMUser() {
    return this.http.get(`${environment.authorizationUrl}/api/dbg-authorization/ecm/accessToken`)
      .pipe(map(response => {
        if (response['success']) {
          this.sessionToken = response['token'];
          localStorage.setItem('token', 'Bearer ' + this.sessionToken);
          this.startRefreshTokenTimer();
        } else {
          this.alertService.setErrorNotification({
            notificationHeader: "Fail",
            notificationBody: `You don\'t have permission to access Scheduler module! `,
          });
        }
      })).subscribe();
  }
  /**
  * @function checkSessionToken: This is used whenever the session token is about to expire. We will be getting new token when we call this method 
  * @returns User JWT token which expires frequently
  * @description token and productId has to be passed in request header to get updated token.
  * Once login is implemented, we will be utilizing this code at the bootstrap of application.
  * @usedby Scheduler Team, Rules team..
  */
  checkSessionToken() {
    return this.http.post(`${environment.schedulerECMLgnUrl}/token`, {}, this.setDefaultHeaders())
      .pipe(
        map(response => {
          if (response['success']) {
            this.sessionToken = response['token'];
            localStorage.setItem('token', 'Bearer ' + this.sessionToken);
            this.startRefreshTokenTimer();
          }
        })).subscribe();
  }
  /**
   * @function setDefaultHeaders Used when token is available
   * @returns headers
   */
  setDefaultHeaders() {
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer ' + this.sessionToken,
      'Product-Id': '10'
    });
    return { headers: headers };

  }
  /**
   * @function setInitialHeaders Used for API calls
   * @returns initial headers
   */
  setInitialHeaders() {
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });
    return { headers: headers };
  }
  /**
   * @function startRefreshTokenTimer Calls refresh token every 5 minutes
   */
  private startRefreshTokenTimer() {
    this.refreshTokenTimeout = setTimeout(() => this.checkSessionToken(), 5 * 60 * 1000);
  }
  /**
   * @function stopRefreshTokenTimer Stops timer for refresh token call after application log out
   */
  public stopRefreshTokenTimer() {
    clearTimeout(this.refreshTokenTimeout);
  }
}
