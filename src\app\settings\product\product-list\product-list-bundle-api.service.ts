import { Injectable } from '@angular/core';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { map } from 'rxjs/operators'

@Injectable({
  providedIn: 'root'
})
export class GetProductBundleApiService {

  constructor(private http : HttpClient) { }

  getProductListBundles(){
      return this.http.get<any>("https://internal-a6b4f0517cf7b455796533935937cf0e-1891451937.us-east-1.elb.amazonaws.com/api/dbg-productdomain/getAllProductBundles") //https://internal-a6b4f0517cf7b455796533935937cf0e-1891451937.us-east-1.elb.amazonaws.com/api/dbg-productdomain/getAllProductBundles
      .pipe(map((res: any) => {
        return res;
      }
    ));
  }

}