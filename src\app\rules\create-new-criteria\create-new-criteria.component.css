app-create-new-criteria .btn-custom{
    float: right;
    margin-right: '15px'; 
    width: '100px'; 
    color:#42518c !important; 
    background-color:#ffffff;
    outline-color:#000;
}

.btn-span {
    float: right;
  }

  .btn-criteria {
    background: #5009B5;
    
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    color: #ffffff;
  }
  .pd-bottom-15 {
    padding-bottom: 15px;
  }
  .pd-25 {
    padding: 25px 25px 25px 25px;
  }
  .custom-btn {
    padding: 0.375rem 3rem !important;
    margin-right: 20px;
  }
  .pd-5 {
    padding: 5px;
  }
  .level-indicator {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 10px 50px;
    left: 1228px;
    top: 130px;
    border-radius: 4px;
    padding: 10px, 50px, 10px, 50px;
    background: #fde2b9;
  }
  
  .card-title {
    
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    color: #5009B5;
    margin-left: 10px;
  }
  .tabs-padding {
    padding: 0px 25px 25px 0px !important;
  }
  .query-builder-title {
    color: #5009B5 !important;
    font-size: 24px !important;
    padding-bottom: 10px;
  }
  .notification-title {
    
    font-style: normal;
    font-weight: bold;
    font-size: 18px;
    line-height: 22px;
    color: #161616;
    padding: 13px 0px 0px 15px;
  }
  .notification-font-wt {
    font-weight: 600 !important;
    padding: 13px 0px 0px 0px !important;
  }
  .custom-control-input:checked ~ .custom-control-label::before {
    color: #fff;
    background: #7fbf89;
    border-radius: 20px;
  }
  .custom-control-label {
    margin-left: 5%;
  }
  .custom-switch {
    padding-left: 1em;
  }

.pd-bottom-15 {
    padding-bottom: 15px;
  }
.pd-25 {
    padding: 25px 25px 25px 25px;
  }
.custom-btn {
    padding: 0.375rem 3rem !important;
    margin-right: 20px;
  }

app-create-new-criteria .btn-custom2{
    float: right;
    margin: '15px';  
    outline-color:#000; 
}

app-create-new-criteria .btn-custom3{
    float: right; 
    margin-left: 15px!important;
    width: '100px'; 
    background-color:#42518c;
}

app-create-new-criteria .btn-info {
    background-color: #8064A2 !important;
    border-color: black;
    float: right;
    margin: '15px';
}

app-create-new-criteria .fa-lg{
    float: right; color: #42518c; margin-right: 15px; padding-right: 1%;
}

app-create-new-criteria .h2{
    color: #42518c;
}

app-create-new-criteria .breadcrumb{
    background:#fff;
}


app-create-new-criteria .float-right{
    float: right;
}

app-create-new-criteria .clearfix{
    clear: both;
}

app-create-new-criteria .card {
  border: 3px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
}

app-create-new-criteria .card .card-no-border {
  border: none !important;
  padding: 0px 25px 0px 25px;
}

app-create-new-criteria .query-builder-title {
  float: left;
  width: 200px;
  height: 34px;
  left: 195px;
  top: 384px;
  
  font-style: normal;
  font-weight: 500;
  font-size: 28px;
  line-height: 34px;
  color: #000000;
}

app-create-new-criteria .mar-10 {
  margin-top: 10px;
}

app-create-new-criteria .pd-15 {
  padding: 15px;
}

app-create-new-criteria hr.qb {
  margin-top: 3rem;
}

app-create-new-criteria .mt-10 {
  margin-top: 10px;
  margin-bottom: 20px;
}
