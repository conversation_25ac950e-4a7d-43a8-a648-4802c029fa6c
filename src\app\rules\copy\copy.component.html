<div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>
<div class="fixed-nav bg-gray" *ngIf="showForms">
  <div class="content-wrapper">
    <div class="container-fluid">
      <div class="breadcrumb-container">
        <app-breadcrumbs-nav [headerText]="headerText" [isPriviousRedirectPage]="isPriviousRedirectPage"
          [breadcrumbDataset]="breadcrumbDataset">
        </app-breadcrumbs-nav>
        <div *ngIf="isRuleLevelPresent" class="pd-5">
          <span class="level-indicator float-right">{{levelIndicator}}</span>
        </div>
      </div>
      <hr />
      <div>
        <div class="row">
          <div class="col-md-9">
            <span class="card-title">Enter Below Details</span>
            <marketplace-dynamic-form [formJSON]="relationSHJSON" [isSubmitNeeded]="false"
              (onValueChange)="mapValuesFromMainToJson($event)" (onValueChanges)="validateValueChanges($event)">
            </marketplace-dynamic-form>
          </div>

          <div class="col-md-3 wrapper mar-lt-neg" *ngIf="enableInventoryStatus">
            <div class="search-input">
              <marketplace-input [label]="labelName" [name]="inputname" [groupText]="groupIcon"
                [(ngModel)]="selectedValue" (input)="giveDescriptionForStatus($event)"
                (onFocusOut)="inventoryInputfocusOut($event)" ngDefaultControl>
              </marketplace-input>
              <div class="searchResults" *ngIf="searchResultsWindow">

                <li *ngFor="let item of filteredResults" (click)="onSelect(item)">{{item.cdValName}}</li>

              </div>
              <div class="noResFound" *ngIf="noResultsFound">
                <li>No Results Found</li>
              </div>
              <div class="selectedItemsInformation" *ngIf="suggestionWindow">
                <div class="row">
                  <i class="fa fa-info-circle" aria-hidden="true"></i>
                  <h6> Status Description </h6>
                </div>
                <p> {{ statusDescription }} </p>
                <hr>
                <div class="similarSuggestion">
                  <h6>Suggestion Similar code</h6>
                  <p> {{ statusSuggestion }}</p>
                </div>
              </div>
              <div class="DescriptionProvider" *ngIf="openAccordion">
                <marketplace-accordion [openPanel]="openPanelIndex">
                  <marketplace-panel [header]="'Status Description'">
                    <div class="label info">
                      <span class="btn-span statusHeader"><i class="fa-thin fa-circle-info close-icon-color"></i></span>
                      <br>
                      {{statusDescription}}
                    </div>
                  </marketplace-panel>
                </marketplace-accordion>
              </div>
            </div>
          </div>
        </div>


        <hr />
        <div class="mb-3 tabs-padding">
          <marketplace-tabs class="container col-12" [selectedTabIndex]="0" (onTabSelection)="onTabSelection($event)">
            <marketplace-tab [header]="'General Details'">
              <div class="mb-3 tabs-padding">
                <marketplace-dynamic-form [isSubmitted]="isFormSubmitted" [formJSON]="generalDetailsJson"
                  [isSubmitNeeded]="false" (onValueChange)="mapValuesFromGeneralToJson($event)"
                  (onValueChanges)="onBussinessOwnerChange($event)">
                </marketplace-dynamic-form>
              </div>
              <div class="mb-3 tabs-padding">
                <span class="notification-title">Attention: <span class="attention-note">The following change will
                    effect the entire inventory. Please be mindful ! </span></span>
                <hr />
                <div class="row">
                  <span class="notification-title notification-font-wt">
                    Retro apply the rules to inventory
                  </span>
                  <div class="custom-switch">
                    <marketplace-switch class="switch floating" [enabled]="true" [value]="retroApply"
                      (onChange)="setRetro($event)">
                    </marketplace-switch>
                  </div>
                </div>
                <div class="row">
                  <span class="notification-title notification-font-wt">
                    Bypass Enhancement
                  </span>
                  <div class="custom-control custom-switch">
                    <marketplace-switch class="switch floating" [enabled]="true" [value]="bypassApply"
                      (onChange)="setBypass($event)">
                    </marketplace-switch>
                  </div>
                </div>
                <div class="row">
                  <span class="notification-title notification-font-wt">
                    Apply the rules to :&nbsp;&nbsp; Line level
                  </span>
                  <div class="custom-switch ">
                    <marketplace-switch class="switch floating" [enabled]="true" [value]="headerLevel"
                      (onChange)="setLevel($event)">
                    </marketplace-switch>
                  </div>
                  <span class="notification-title notification-font-wt">
                    Header level
                  </span>
                </div>
              </div>
            </marketplace-tab>
            <marketplace-tab [header]="'Additional Details'">
              <div class="mb-3 pd-25">
                <marketplace-dynamic-form [formJSON]="additionalDetailsJson" [isSubmitNeeded]="false"
                  (onValueChange)="mapValuesFromAdditionalToJson($event)">
                </marketplace-dynamic-form>
              </div>
            </marketplace-tab>
          </marketplace-tabs>
        </div>
      </div>

    </div>
  </div>
</div>
<div class="fixed-nav bg-gray mar-10" *ngIf="showForms">
  <div class="content-wrapper">
    <div class="container-fluid">
      <div class="row">
        <div class="ruleDefinition">
          <span class="card-title">Rule Definition</span>
          <div class="chip-Container">
            <ng-container *ngIf="conceptIdSelectedForChips.length > 0">
              <label *ngFor="let chip of conceptIdSelectedForChips" class="chips">
                <span class="chips-text" title="{{ chip }}">Concept: {{ chip }}</span>
                <span class="close-button" (click)="closeStateChip(chip)">&times;</span>
              </label>
            </ng-container>
          </div>
        </div>
        <div class="col-md-12 mar-20">
          <marketplace-dynamic-form *ngIf="showQuerySpec" [formJSON]="querySpecificationJson" [isSubmitNeeded]="false"
            (onValueChanges)="mapValuesFromQuerySpecToJson($event)" (onValueChange)="ruleLevelChange($event)">
          </marketplace-dynamic-form>
        </div>
      </div>
      <ng-container *ngIf="!showQueryBuilderComponents">
        <div class="mar-30">
          <marketplace-dynamic-form [formJSON]="customSqlJson" [isSubmitNeeded]="false"
            (onValueChanges)="_onSqlChange($event)">
          </marketplace-dynamic-form>
        </div>
      </ng-container>
      <ng-container *ngIf="showQueryBuilderComponents">
        <div class="mar-10">
          <marketplace-segmented-control *ngIf="showSegmentedControl" [name]="'queryBuilderToggle'"
            [dataset]="sgDashboardDataset" (onSelection)="_onDashboardSGSelection($event)">
          </marketplace-segmented-control>
        </div>
        <div class="pad-1rem">
          <ng-container *ngIf="isStandardQBSelected">
            <marketplace-query-builder *ngIf="showQBuilder" [switchToggleNames]="switchToggleNames"
              [isReadableQueryRequired]=true [bulkUploadParams]="uploadParams" [query]="qbQuery" [qbConfig]="qbConfig"
              [operators]="operators" [importTableProperties]="tableProperties" [dragDropList]="recentQueryList"
              (onDropquery)="dropRecentList($event)" (onFieldChange)="qbFieldChange($event)"
              (fieldValueChange)="getClientConceptValue($event)" (onqbChange)="qbChange($event)">
            </marketplace-query-builder>
          </ng-container>
          <ng-container *ngIf="!isStandardQBSelected">
            <marketplace-file-parser [(parserModel)]="parserDataset" (onParseComplete)="onParseComplete($event)"
              (onChange)="uploadMultiCriteriaFile($event)" [fileUploadType]="'single'"
              [isMultipleSelectionAllowed]=false [buttonLabel]="'Select Document to Upload'"
              [tableProperties]="fileParserTableProperties" [fileAccept]="fileParserFileAccept">
            </marketplace-file-parser>
            <div class="enabledQb">
              <marketplace-query-builder *ngIf="showQBuilder" [switchToggleNames]="switchToggleNames"
                [isReadableQueryRequired]=true [bulkUploadParams]="uploadParams" [query]="qbQuery" [qbConfig]="qbConfig"
                [operators]="operators" [importTableProperties]="tableProperties" [dragDropList]="recentQueryList"
                (onDropquery)="dropRecentList($event)" (onFieldChange)="qbFieldChange($event)"
                (fieldValueChange)="getClientConceptValue($event)" (onqbChange)="qbChange($event)">
              </marketplace-query-builder>
            </div>
          </ng-container>
        </div>
      </ng-container>
      <span class="btn-span pd-25 align-right">
        <marketplace-button [label]="'Cancel'" [type]="'tertiary'" [name]="'tertiary'" (onclick)="cancelCreate()">
        </marketplace-button>

        <marketplace-button *ngIf="showSubmit" [label]="'Save'" [type]="'secondary'" [name]="'secondary'" [enabled]="ruleSubmitButton"
          (onclick)="validateCreateDynamicForms('save')">
        </marketplace-button>

        <marketplace-button *ngIf="showSubmit" [label]="'Submit'" [type]="'primary'" [name]="'primary'"
          [enabled]="ruleSubmitButton" (onclick)="validateCreateDynamicForms('submit')">
        </marketplace-button>

        <marketplace-button [label]="'Upload File'" [enabled]="!disableUploadBtn" *ngIf="!showSubmit" [type]="'primary'"
          [name]="'primary'" (onclick)="multipleCriteriaFileUpload()">
        </marketplace-button>


      </span>

    </div>
  </div>
</div>

<div [ngClass]="{'createPopup':!showMessage }">
  <marketplace-popup id="createRulePopup" [open]="createOpenPopup" [size]="'large'" (onClose)="createClosePopup()">
    <div mpui-modal-header>
      <div class="modal-header-custom">
        <h4 *ngIf="showMessage" class="modal-title custom-title">Attention !</h4>
        <h4 *ngIf="!showMessage" class="modal-title custom-title">Duplicate Rule(s) !</h4>
      </div>

    </div>
    <div mpui-modal-body>
      <ng-container *ngIf="showMessage">
        <p class="pad-20">You are about to create a Global Rule that will affect all clients, concepts and insights.</p>
        <p class="pad-30">Please click continue if you wish to proceed</p>
      </ng-container>
      <div *ngIf="!showMessage">
        <p class="p-align">A rule(s) already exists for the given criteria. Please click Cancel to quit create screen or
          click Edit to review rule and make changes.</p>
        <marketplace-table [id]="'example-static-table'" [redraw]="tableRedraw" [dataset]="duplicateRuleTableJson"
          [rowHeight]="ruleDashbordTableRowhg" [headerRowHeight]="ruleDashbordTableHeaderhg" [isRowSelectable]="false"
          [columnDefinitions]="columnConfigDuplicatePopup" (onTableReady)="tableReady($event)">
        </marketplace-table>
      </div>
    </div>
    <div mpui-modal-footer class="rolefooterPopup">
      <marketplace-button [label]="'EDIT'" [type]="'secondary'" [name]="'secondary'" (onclick)="closePopup()">
      </marketplace-button>
      <marketplace-button [label]="'Cancel'" [type]="'cyan-secondary'" [name]="'cyan-secondary'" *ngIf="!showMessage"
        (onclick)="cancelCreate()">
      </marketplace-button>
      <marketplace-button [label]="'Continue'" [type]="'primary'" [name]="'primary'" (onclick)="createRule()"
        *ngIf="showMessage">
      </marketplace-button>
    </div>
  </marketplace-popup>
</div>


<marketplace-popup [open]="createErrorOpenPopup" [size]="'small'" (onClose)="createClosePopup()">
  <div mpui-modal-header>
    <div class="modal-header-custom">
      <h4 class="modal-title custom-title">Attention !</h4>
    </div>
  </div>
  <div mpui-modal-body>
    <p class="pad-35">Please fill all the mandatory fields</p>
  </div>
  <div mpui-modal-footer>
    <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closePopup()">
    </marketplace-button>
  </div>
</marketplace-popup>

<marketplace-popup [open]="openbypassConfirm" [size]="'large'">
  <div mpui-modal-header>
    Are you certain you want to override the enhancement of Concepts in ECM?
  </div>
  <div mpui-modal-body class="bypassmessgae">This action could potentially impact the concepts in production.
  </div>
  <div mpui-modal-footer>
    <marketplace-button [label]="'Cancel'" [type]="'tertiary'" [name]="'primary'" (onclick)="closebypassConfirm()">
    </marketplace-button>
    <marketplace-button [label]="'Submit'" [type]="'primary'" [name]="'primary'"
      (onclick)="validateCreateDynamicForms('submitbypass')">
    </marketplace-button>
  </div>
</marketplace-popup>




<div class="createUploadPopup" *ngIf="createUploadOpenPopup">
  <marketplace-popup [open]="createUploadOpenPopup" [size]="'small'" (onClose)="createUploadClosePopup()">
    <div mpui-modal-header>
      <h4 class="modal-title custom-title">Do you want to upload or skip and go to dashboard?</h4>
    </div>
    <div mpui-modal-body class="custom-message">
      <marketplace-file-upload [label]="fileUploadLabelText" [type]="fileUploadType" [accept]="fileAccept"
        (onSelection)="upload($event)" *ngIf="isFileReady">
      </marketplace-file-upload>
      <marketplace-dynamic-form [formJSON]="fileDetailsSectionJson" (onValueChange)="mapValuesToUploadJson($event)"
        *ngIf="fileDetailsSectionJson && isTextReady" [isSubmitNeeded]="false">
      </marketplace-dynamic-form>
      <div class="red-font" *ngIf="showMaxLimitMsg">File size maxed out (Max limit is 25mb)</div>
    </div>
    <div mpui-modal-footer>
      <marketplace-button [label]="'Skip'" [type]="'secondary'" [name]="'secondary'" (onclick)="onSubmitSkipClicked()">
      </marketplace-button>

      <marketplace-button [label]="'Upload'" [type]="'primary'" [name]="'primary'" (onclick)="onSubmitUploadClicked()"
        [enabled]="!isDisabled || isLoading">
      </marketplace-button>
    </div>
  </marketplace-popup>
</div>

<marketplace-popup [open]="openConfirmationModal" [size]="'small'">
  <div mpui-modal-header>
    <h3>Attention</h3>
  </div>
  <div mpui-modal-body><span style="text-align: center;">All the changes done on Query builder will be lost</span></div>
  <div mpui-modal-footer>
    <marketplace-button [label]="'Cancel'" [type]="'primary'" [name]="'primary'" (onclick)="closeConfirmationModal()">
    </marketplace-button>
    <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="clearQB()">
    </marketplace-button>
  </div>
</marketplace-popup>

<marketplace-popup [open]="openFileUploadConfirmModal" [size]="'small'">
  <div mpui-modal-header>
    <h3>{{ uploadFileStatus }}</h3>
  </div>
  <div mpui-modal-body><span style="text-align: center;">{{ uploadFileStatusMsg }}</span></div>
  <div mpui-modal-footer>
    <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closeFileUploadModal()">
    </marketplace-button>
  </div>
</marketplace-popup>

<!-- 25.1 - <marketplace-popup id="impactReportPopup" [open]="openImpactReportPopup" [size]="'small'"
  (onClose)="savedConfirmPopupClose()">
  <div mpui-modal-header>
    <h6 class="modal-title">Rule {{updatedRuleId}} Successfully Saved </h6>
  </div>
  <div mpui-modal-body class="custom-message save-sucess-popup">
    <p>You can now generate an impact report to see this rule's effect on past executions</p>
  </div>
  <div mpui-modal-footer>
    <marketplace-button [label]="'Back to Rules Management'" [type]="'tertiary'" [name]="'tertiary'"
      (onclick)="cancelGenerateView()">
    </marketplace-button>

    <marketplace-button [label]="'Generate Impact Report'" [type]="'primary'" [name]="'primary'"
      (onclick)="generatePreview()">">
    </marketplace-button>

  </div>
</marketplace-popup> -->