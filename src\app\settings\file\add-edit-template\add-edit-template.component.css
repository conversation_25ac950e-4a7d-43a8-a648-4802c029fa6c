app-add-edit-template .tab-style {
    background: #e0e3e7;
    border-radius: 4px;
    margin-right: 5px;
    padding: 6px;
    text-align: center;
}

app-add-edit-template .tab-style .h5 {
    background-color: #e0e3e7;
    font-family : sans-serif;
}
app-add-edit-template .tab-style-active {
    font-size: 16.0437px;
    font-family: sans-serif;
    background-color: #FFF;
    border-bottom: 2px solid #335eac;
    border-radius: 0px;
    border-radius: none;
}
app-add-edit-template .tabmenu{
    margin: 0px 20px;
}

app-add-edit-template .tab-style-active .h5{
    font-size: 1rem;
    font-family: sans-serif;
    background-color: #FFF;
    border-bottom: 2px solid #335eac;
    border-radius: 0px;
    border-radius: none;
}

app-add-edit-template .form-control {
    margin: 0;
}
app-add-edit-template .elevated-card {
    box-shadow: 0 5px 5px -3px #0003, 0 8px 10px 1px #00000024, 0 3px 14px 2px #0000001f;
}
app-add-edit-template .delimiterInput{
    margin-left: 17px;
}
app-add-edit-template .form-check-label {
    margin-left: 19px;
    margin-bottom: 5px;
    font-size: 14px;
    width: max-content;
}

app-add-edit-template .checkbox-group {
    margin-top: -35px;
}
app-add-edit-template .fixed-value {
    margin-top: -15px;
}
app-add-edit-template .pad-25 {
    padding: 25px;
}
app-add-edit-template .delimiter-box {
    border: 1px solid #c4ceff;
    border-radius: 0.25rem;
    padding: 7px;
    margin-left: 10px;
}

app-add-edit-template .rule-dashboard .three-dots:after {
    cursor: pointer;
    color: #444;
    content: "\2807";
    font-size: 20px;
    padding: 0 5px;
    z-index: 999;
}
app-add-edit-template .rule-dashboard input:checked ~ .dropdown {
    opacity: 1;
    z-index: 100;
    transition: opacity 0.2s;
    z-index: 1;
}
app-add-edit-template .rule-dashboard .three-dots:after {
    cursor: pointer;
    color: #444;
    content: "\2807";
    font-size: 20px;
    padding: 0 5px;
    z-index: 999;
}
app-add-edit-template .rule-dashboard .table-action-menu .fa-eye,
app-add-edit-template .rule-dashboard .table-action-menu .fa-edit,
app-add-edit-template .rule-dashboard .table-action-menu .fa-trash,
app-add-edit-template .rule-dashboard .table-action-menu .fa-plus,
app-add-edit-template .rule-dashboard .table-action-menu .fa-check {
    font-size: 20px;
    color: #5009B5;
    padding-right: 15px;
}
app-add-edit-template .rule-dashboard .table-action-menu {
    border: 8.5px solid #ffffff;
}
app-add-edit-template .rule-dashboard .dropdown a {
    color: black;
    padding: 5px 10px;
    text-decoration: none;
    display: block;
}
app-add-edit-template .rule-dashboard .dropdown {
    position: absolute;
    background-color: gray;
    padding: 5px;
    outline: none;
    opacity: 0;
    min-width: 160px;
    overflow: auto;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    background: #ffffff;
    box-shadow: 0px 0px 20px rgb(0 0 0 / 40%);
    border-radius: 4px;
}

.right {
    display: flex;
}
app-add-edit-template marketplace-dynamic-form .form-row.form-element-group{
border: none !important;
display: inline-grid;
}
app-add-edit-template .buttontoleft {
    margin: 9px;
    margin-right: 6%;
}
app-add-edit-template .italic-blue{
    color: #5009b5;
    
}
app-add-edit-template .form-control:disabled, .form-control[readonly]{
    pointer-events: none;
}
app-add-edit-template marketplace-dynamic-form .grp-header-blue{
    color: #5009b5;
}
app-add-edit-template .nc {
    padding: 1.65rem;
}

app-add-edit-template marketplace-dynamic-form .form-group .form-label{
    font-size: 0.9rem;
    font-family: sans-serif;
    font-weight: 400;
}
app-add-edit-template marketplace-dynamic-form i.italic-blue{
    font-size: 18.875px;
    font-family: sans-serif;
    font-style: normal;
}
app-add-edit-template marketplace-dynamic-form form-group form-row form-col label input[type=checkbox] {
    font-size: 13.2125px;
    font-family: sans-serif;
}
app-add-edit-template marketplace-dynamic-form .form-row.form-element-group label {
    display: inline-flex;
    font-size: 13.3812px;
    font-family: sans-serif;
    font-weight: 500;
}
app-add-edit-template marketplace-dynamic-form .form-row.form-element-group label input[type=radio]{
    margin-right: 3px;
}
app-add-edit-template marketplace-checkbox .checkbox-holder.vertical .form-check .align-left{
    font-size: 13.3812px;
    font-family: sans-serif;
    font-weight: 500;
}
app-add-edit-template marketplace-breadcrumb span.breadcrumb__holder a {
    font-size: 14px !important;
} 
app-add-edit-template .headerfile{
    font-family: sans-serif;
    color: #335eac;
}
app-add-edit-template .count{
    font-size: 14px;
    font-family: sans-serif;
    font-weight: 400;
    color: #666666;
}
app-add-edit-template .countright{
    float:right;
    margin: -12px -15px;
}
app-add-edit-template .secondPage{
    margin-top: 45px;
    display: flex;
    justify-content: end;
}
app-add-edit-template marketplace-dynamic-form .form-col-2 {
    max-width: 100% !important;
    flex: 1 1 50%;
}
app-add-edit-template marketplace-form-switch .switch-holder input:checked+.slider {
    background-color: #23ec38;
}
app-add-edit-template .fixedColumn {
    top: 6px;
    position: absolute;
    display: flex;
}
app-add-edit-template .delimiter {
    display: flex;
    margin-bottom: 1rem;
}
app-add-edit-template .npdelimiter {
    pointer-events: none;
}
#example-static-table_gridNm .slick-cell-checkboxsel ~ .cell-reorder {
    display: none !important;
}

#example-static-table_gridNm .slick-cell-checkboxsel.selected ~ .cell-reorder {
    display: block !important;
}

app-add-edit-template .submitBtnHide {
    display: none;
}

.text-area-component:disabled, .text-area-component[readonly] {
    background-color: #e9ecef;
    opacity: 1;
}

app-add-edit-template .submitButton{
    width: 170px;
    margin-left: 20px;
}

app-add-edit-template .nextBtnWidth{
    width: 70px;
    margin-left: 20px;
}

app-add-edit-template .dashbord-title {
    height: 51px;
    font-style: normal;
    font-weight: bold;
    font-size: 25px;
    padding: 17px 0px 0px 0px;
}

app-add-edit-template .dashbord-title i {
    font-size: 30px;
    color: #5009B5;
    margin-left: 18px;
}

app-file-home marketplace-breadcrumb span.breadcrumb__holder .current-page {
    font-size: 14px;
}

app-add-edit-template .publishbBtnGroup {
    display: flex;
    justify-content: flex-end;
}

app-add-edit-template .autoSaveInCorner{
    position: absolute;
    right: 0;
    top: 0rem;
    color: #5009B5;
    text-align: right;
}

app-add-edit-template marketplace-switch .switch-holder label.switch {
    margin-right: 65% !important; 
}

app-add-edit-template .viewSmbtBtnHide{
    display: none !important;
}

app-add-edit-template h5.sub-header {
    margin-top: 1rem;
    color: #5009b5;
}

app-add-edit-template h5.sub-header.first-elem {
    margin-top: 0rem;
}

app-add-edit-template .specificationBroder{
    border:1px solid lightgrey;
    border-radius: 5px;
}

app-add-edit-template .cffLinkPopupTxtColor{
    color: red;
}

app-add-edit-template marketplace-popup .modal .modal-dialog.modal-sm .modal-content{
    height: 40vh !important;
}

app-add-edit-template marketplace-dynamic-form .form-row label.turn-label {
    color: black !important;
}

app-add-edit-template marketplace-select .select-holder .ng-select#sufix ng-dropdown-panel {
    top: auto;
    bottom: 100%;
}
app-add-edit-template input[type=checkbox], input[type=radio] {
    accent-color: #794cff !important;
}

app-add-edit-template input[type=checkbox], input[type=radio]:focus {
    accent-color: #794cff !important;
}


app-file-home .filesBreadcrumb .breadcrumb__holder{
    margin-left: 0;
}