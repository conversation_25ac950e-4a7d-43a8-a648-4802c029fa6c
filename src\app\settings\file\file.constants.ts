export const MAPPING_COLUMNS = {
    "switches": {
        "enableSorting": true,
        "enablePagination": false,
        "editable": true,
        "enableFiltering": true
    },
    "colDefs": [
        {
            "name": "Field#",
            "field": "fieldSeqNo",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Field Name",
            "field": "fieldName",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Data Type",
            "field": "fieldDataType",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Size",
            "field": "fieldSize",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Record Type",
            "field": "recordType",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Field Option",
            "field": "fieldOptions",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "CFF Field To Link",
            "field": "cffFldLink",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "CFF Length",
            "field": "cffLength",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Actions",
            "field": "",
            "filterType": "",
            "visible": "True",
            "editorType": "",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": "",
            "customFormatter": customRenderFn
        },
    ]
};

export const MAPPING_COLUMNS_MAPPING_TBL = {
    "switches": {
        "enableSorting": true,
        "enablePagination": false,
        "editable": true,
        "enableFiltering": true
    },
    "colDefs": [
        {
            "name": "Field#",
            "field": "fieldSeqNo",
            "filterType": null,
            "visible": "True",
            "editorType": "",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": "",
            "id": "dup-name1",
            "sortable": false,
        },
        {
            "name": "Field Name *",
            "field": "fieldName",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": "",
            "id": "dup-name2",
            "sortable": false
        },
        {
            "name": "Data Type *",
            "field": "fieldDataType",
            "filterType": null,
            "visible": "True",
            "editorType": "Single Select",
            "editorTypeRoot": "fieldDataType",
            "editorTypeLabel": "label",
            "editorTypeValue": "label",
            "id": "dup-name3",
            "sortable": false
        },
        {
            "name": "Size *",
            "field": "fieldSize",
            "filterType": null,
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": "",
            "id": "dup-name4",
            "sortable": false,
            "customValidator": fieldSizeMappingValidator
        },
        {
            "name": "Record Type",
            "field": "recordType",
            "filterType": null,
            "visible": "True",
            "editorType": "Single Select",
            "editorTypeRoot": "RecordType",
            "editorTypeLabel": "label",
            "editorTypeValue": "label",
            "id": "dup-name5",
            "sortable": false
        },
        {
            "name": "Field Option",
            "field": "fieldOption",
            "filterType": null,
            "visible": "True",
            "editorType": "Single Select",
            "editorTypeRoot": "Req",
            "editorTypeLabel": "label",
            "editorTypeValue": "label",
            "id": "dup-name6",
            "sortable": false
        },
        {
            "name": "Target or Xref",
            "field": "invClmTypInd",
            "filterType": null,
            "visible": "True",
            "editorType": "Single Select",
            "editorTypeRoot": "invClmTypInd",
            "editorTypeLabel": "label",
            "editorTypeValue": "label",
            "id": "dup-name3",
            "sortable": false
        },
        {
            "name": "CFF Field To Link *",
            "field": "cffFldLink",
            "filterType": null,
            "visible": "True",
            "editorType": "Single Select",
            "editorTypeRoot": "CFF",
            "editorTypeLabel": "label",
            "editorTypeValue": "id",
            "id": "dup-name7",
            "sortable": false
        },
        {
            "name": "Constant Value (*Field Option Custom)",
            "field": "cffConstValue",
            "filterType": null,
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": "",
            "id": "dup-name9",
            "sortable": false
        },
        {
            "name": "CFF Length",
            "field": "cffLength",
            "filterType": null,
            "visible": "True",
            "editorType": null,
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": "",
            "id": "dup-name8",
            "sortable": false
        },
    ]
};

/**
 * Method to Inline validations for field #
 */
function fieldSizeMappingValidator(event: any) {
    let validate = Number.isFinite(Number(event.value));
    if (!validate) {
        return { valid: false, msg: 'Please enter only number' };
    }
    else {
        return { valid: true, msg: '' }
    }
};

function customRenderFn(event) {
    return `<div class="rule-dashboard dropdown-container" tabindex=“-1”>
    <input id=“selector-${event.dataContext.id}” type="checkbox" name="menu" style="display:none;" />
    <label  for=“selector-${event.dataContext.id}” class="three-dots"></label>
    <div class="dropdown">
    <div class="table-action-menu"><i class="fa fa-eye" title="View" dataaction="view" datevalue=${event.dataContext.id}></i>View</div>
    <div class="table-action-menu"><i class="fa fa-edit" title="Edit" dataaction="edit" datevalue=${event.dataContext.id}></i>Edit</div>
    <div class="table-action-menu"><i class="fa fa-trash" title="Delete"  dataaction="delete" datevalue=${event.dataContext.id}></i>Delete</div>
    </div>
    </div>`
}
function customtableRenderFn(event) {
    return `<div class="rule-dashboard dropdown-container" tabindex=“-1”>
    <input id=“selector-${event.dataContext.id}” type="checkbox" name="menu" style="display:none;" />
    <label  for=“selector-${event.dataContext.id}” class="three-dots"></label>
    <div class="dropdown">
    <div class="table-action-menu"><i class="fa fa-plus" title="Add" dataaction="Add" datevalue=${event.dataContext.id}></i>Add</div>
    <div class="table-action-menu"><i class="fa fa-edit" title="Edit" dataaction="edit" datevalue=${event.dataContext.id}></i>Edit</div>
    <div class="table-action-menu"><i class="fa fa-trash" title="Delete"  dataaction="delete" datevalue=${event.dataContext.id}></i>Delete</div>
    </div>
    </div>`
}

export const TEMPLATE_COLUMNS = {

    "switches": {
        "enableSorting": true,
        "enableFiltering": true
    },
    "colDefs": [
        {
            "name": "Template Name",
            "field": "name",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Long Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Created By",
            "field": "createdBy",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Long Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Date",
            "field": "date",
            "filterType": "Long Text",
            "visible": "True",
            "editorType": "",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": "",
        },
        {
            "name": "Status",
            "field": "status",
            "filterType": "Multi Select",
            "visible": "True",
            "editorType": "",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
        }
    ]
};


export const File_COLUMNS = {

    "switches": {
        "enableSorting": false,
        "enablePagination": false,
        "editable": false,
        "enableFiltering": false
    },
    "colDefs": []
};
export const FILE_STATUS_COLUMNS = {

    "switches": {
        "enableSorting": false,
        "enablePagination": false,
        "editable": false,
        "enableFiltering": false
    },
    "colDefs": [
        {
            "name": "Data Loaded",
            "field": "dataLoaded",
            "filterType": "Multi Select",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Count Success",
            "field": "countSuccess",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Count Failure",
            "field": "countFailure",
            "filterType": "Text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Loaded By",
            "field": "loadedBy",
            "filterType": "Multi Select",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Status",
            "field": "status",
            "filterType": "Multi Select",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        }
    ]
};

export const FILE_TEMPLATE_CONSTANTS = {
    DELIMETER: "delimiter",
    VIEW: "View",
    EDIT: "Edit",
    FILE_TEMPLATE_NAME: "fileTmplName",
    DESCRIPTION_TEXT: "descText",
    ACTIVE_IND: "actvInd",
    FILE_FORMAT_TXT: "fileFrmtTxt",
    SYS_ID: "sysId",
    TEMPLATE_TYPE: "tmpltType",
    PROD_ID: "prodId",
    HEADER_FLAG: "headerFlag",
    FIXED_WIDTH_FLAG: "fixedWidthFlag",
    IMPORT_FLAG: "importFlag",
    EXPORT_FLAG: "exportFlag",
    PREFIX: "prefix",
    SUFIX: "sufix",
    CREATED_USER_ID: "createdUserId",
    CREATED_DATE: "createdDate",
    LAST_MODIFIED_DATE: "lastModifiedDate",
    LAST_MODIFIED_USER_ID: "lastModifiedUserId",
    CREATE_NEW: "Create new",
    VIEW_TEMPLATE: "View template",
    EDIT_TEMPLATE: "Edit template",
    CREATE_NEW_TEMPLATE: "Create new template",
    CREATE_TEMPLATE: "Create Template",
    UPDATE_TEMPLATE: "Update Template",
    NONE: "none",
    CONSTANT_VALUE: "constant value",
    UPDATE: "update",
    READ: "read",
    VALID: "VALID",
    DETAILS: "details",
    TEMPLATE_CREATED_SUCCESSFULLY: "Template Created Successfully",
    TEMPLATE_UPDATED_SUCCESSFULLY: "Template Updated Successfully",
    NOTIFICATION_BODY_AUTO_SAVE: "Information entered so far has been auto saved successfully",
    NOTIFICATION_BODY_SAVE: "Information entered so far has been saved successfully",
    TEMPLATE_NAME_EXIST: "Template Name already exists",
    FILE_TEMPLATE_ALREADY_EXIST: "File Template Name already exists or has been previously used. Please choose a different name",
    FIELD_SEQ_NO: "fieldSeqNo",
    FIELD_NAME: "fieldName",
    FIELD_DATA_TYPE: "fieldDataType",
    FIELD_TARGET_XREF: "invClmTypInd",
    XREF: "Xref",
    TARGET: "Target",
    LABEL: "label",
    FIELD_SIZE: "fieldSize",
    CONST_VALUE: "Constant Value",
    TEMPLATE_NAME: "templateName",
    SPEC_DESC_TXT_AREA: "specDescTxtArea",
    FILE_TYPE: "fileType",
    SYSTEM_INVENTORY: "systemInventory",
    INVENTORY_TYPE: "inventoryType",
    PRODUCT_NAME: "productName",
    ACTIVE_STATUS: "activeStatus",
    HEADER_CHECKBOX: "headerCheckBox",
    DATE_TIME_FORMAT: "MM/DD/YYYY hh:mm:ss"
};