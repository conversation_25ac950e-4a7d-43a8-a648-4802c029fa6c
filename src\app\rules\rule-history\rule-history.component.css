.rule__history-container {
display: flex;
justify-content: space-between;
width: 90%;
}

::ng-deep .timepart {
    min-width: 28%;
}
.header {
font-weight: bold;
margin-bottom: 5px;
}

.backdrop {
    position: fixed;
    top: 11%;
    left: 20%;
    width: 100vw;
    height: 100vh;
    z-index: 999;
    background-color: rgb(0, 0, 0, 0.2);
  }

.rule__history-reinitiate-btn marketplace-button {
    opacity: 0.5;
}
  

.spinner-border {
    display: block;
    position: fixed;
    top: calc(50% - (58px / 2));
    right: calc(40% - (58px / 2));
    width: 5rem;
    height: 5rem;
  }

.item {
margin-bottom: 5px;
}

::ng-deep .headerbadge {
    font-size: 14px;
    font-weight: 500;
    height: 24px;
}
    
::ng-deep .badge {
    padding: 5px 12px 2px 12px !important;
}

.headerValue {
    font-weight: 700;
}

::ng-deep .badge-light {
    background: #BAB9B9 !important;
    margin-top: 5px;
}

::ng-deep .badge-active {
    background: #E1EDFF !important;
    color: #1C4DA0 !important;
}

.mar-20 {
    margin-top: 24px;
}

.rule-history__search {
    margin: 24px 0;
}

.advSearch-overlay-search-icon {
    border: none;
    background-color: #E4E4E4;
}
.rule__history-reinstate-btn {
    padding: 0 1rem;
}

::ng-deep marketplace-panel span.header-content {
    display: flex;
}

::ng-deep .sub-text-content {
    width: 100% !important;
}

::ng-deep .rule-history__query-builder div {
    display: none !important;
}

::ng-deep .rule-history__query-builder .output-result {
    display: block !important;
    border-radius: 0 !important;
    background: none !important;
    padding: 0 !important;
}

.btn-link {
    cursor: pointer;
    color: #286CE2;
    font-family: 'elevance-medium';
}
marketplace-popup .modal .modal-dialog .modal-header h3 {
    color: #000;
    font-size: 18px;
    font-weight: 600;
    line-height: 26px;
    font-style: normal;
    letter-spacing: -.1px;
}
 marketplace-button marketplace-button .btn .btn-text .confirm-btn span{
color:#fff !important;
}   

marketplace-popup .modal .modal-dialog {
    height: 70vh !important;
    width: 130% ;
  }
