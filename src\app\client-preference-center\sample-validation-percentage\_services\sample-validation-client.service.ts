import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SampleValidationClientService {

  constructor(private http: HttpClient) { }

  /**
   * Method to call save api to update client percentage
   */
  updateClientPercentageByClientId(payload: any) {
    return this.http.post<any>(`${environment.validatorSvc}/invProxy/clnt-smpl-valdtn-prcntg/save`, payload).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method to call get api to fetch the client percentage for a client
   */
  getClientPercentageByClientId(clientId) {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/clnt-smpl-valdtn-prcntg/getClntprcntgDetails/${clientId}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method to call get api to fetch past updates for a client
   */
  getPastUpdatesPercentagesByClientId(clientId) {
    return this.http.get<any>(`${environment.validatorSvc}/invProxy/clnt-smpl-valdtn-prcntg/past-updates?clientId=${clientId}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method to call get api to get userlist based on clientId
   */
  getUserNameForClient(): Observable<any> {
    let currentClientId = sessionStorage.getItem('clientId');
    return this.http.get<any>(environment.authService + '/api/v1/roles/getallusersbyclientid').pipe(
      catchError(err => {
        return throwError(err);
      }));
  }
}