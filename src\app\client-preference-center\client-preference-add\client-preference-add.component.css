app-client-preference-add .container,
app-client-preference-add .container-fluid,
app-client-preference-add .container-lg,
app-client-preference-add .container-md,
app-client-preference-add .container-sm,
app-client-preference-add .container-xl {
  width: 100%;
  padding-right: 5px;
  padding-left: 5px;
  margin-right: auto;
  margin-left: auto;
}
app-client-preference-add .pd-left-30 {
  padding-left: 30px;
}
app-client-preference-add .pd-righ-10 {
  padding-right: 10px;
}
app-client-preference-add .pd-30 {
  padding-bottom: 30px;
}

app-client-preference-add .form-repeater{
  width: 30%
}

app-client-preference-add .pd-25 {
  padding: 11px 25px 25px 25px;
}
app-client-preference-add .custom-btn {
  padding: 0.375rem 3rem !important;
  margin-right: 20px;
}

app-client-preference-add .btn-span {
  float: right;
  padding: 0px 30px 25px 30px;
}
app-client-preference-add .footer-btns {
  display: flex;
  justify-content: right;
}
app-client-preference-add .dashbord-title {
  
  font-style: normal;
  font-weight: 900;
  font-size: 24px;
  line-height: 34px;
  color: #000000;
  padding: 0px;
}

app-client-preference-add  .fa-chevron-circle-left {
  font-size: 25px;
  color: #5009B5;
  margin-right: 5px;
}

app-client-preference-add .rounded {
  border-radius: 1.25rem !important;
}

app-client-preference-add .btn-span {
  float: right;
  padding: 25px 0px 0px 30px;
}

app-client-preference-add .cardqb {
  border: 3px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
}
app-client-preference-add .card {
  border: 0px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
}

app-client-preference-add .dashbord-card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 2rem 1rem 2rem 1rem;
}

app-client-preference-add.card-title {
  
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 17px;
  color: #161616;
}

app-client-preference-add .pad-10 {
  padding-left: 10px;
}

app-client-preference-add .timebtn {
  width: 193%;
  border: 1px solid #c4ceff;
  outline: none;
  border-radius: 5px;
  color: #818080;
  width: 173%;
  height: 36px;
  padding: 0.5rem;
}

app-client-preference-add .col-6 {
  padding-left: 102px;
  width: 53%;
}
app-client-preference-add .pd-13 {
  padding-top: 8px;
}

app-client-preference-add .pdg-13 {
  padding-top: 13px;
}

app-client-preference-add .freq {
  width: 466px;
  margin: -15px;
  padding-left: 36px;
  height: 180px;
  position: absolute;
  box-shadow: rgb(0 0 0 / 35%) 0px 5px 15px;
}

app-client-preference-add .padd-top {
  width: 36%;
  padding-top: 9px;
}

app-client-preference-add .pad-topPx {
  padding-top: 11px;
}

app-client-preference-add select {
  height: 35px;
  border: 1px solid #c4ceff;
  border-radius: 5px;
  width: 211px;
}

app-client-preference-add .form-pad {
  padding: 5px 0px 15px 15px;
}

app-client-preference-add .qb-mar-2 {
  margin-left: -2px;
}

app-client-preference-add .card.card-no-border {
  padding-bottom: 0px !important;
}

app-client-preference-add .custom-title {
  
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 29px;
  color: #5009B5;
}

app-client-preference-add .modal-header-custom {
  width: 100%;
  display: flex;
  justify-content: center;
}

app-client-preference-add .custom-message {
  
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 19px;
  color: #4e4e4e;
}

app-client-preference-add .pad-30 {
  margin-left: 35%;
}

app-client-preference-add .close-icon-color {
  color: #5009B5;
}
app-client-preference-add .spinner-border {
  display: block;
  position: fixed;
  top: calc(50% - (58px / 2));
  right: calc(40% - (58px / 2));
  width: 5rem;
  height: 5rem;
}

app-client-preference-add .backdrop {
  position: fixed;
  top: 11%;
  left: 20%;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  background-color: rgb(0, 0, 0, 0.2);
}
