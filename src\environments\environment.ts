
export const environment = {
  production: false,
  name: 'dev',

  // Auth Endpoints
  iamManagerAuth: 'https://api.portal.pi.dev.gcpdns.internal.das/pf-pi-iam-manager/api/v1/auth',
  iamManagerUser: 'https://api.portal.pi.dev.gcpdns.internal.das/pf-pi-iam-manager/api/v1/user',
  authService: 'https://api.portal.pi.dev.gcpdns.internal.das/pf-pi-auth-svc',
  //callBackUrl: 'http://localhost:4200/login/callback',
  callBackUrl: 'https://ui.portal.pi.dev.gcpdns.internal.das/login/callback',
  // oktaCallBackUrl:'http://localhost:4200/login/auth/callback',
  oktaCallBackUrl: 'https://ui.portal.pi.dev.gcpdns.internal.das/login/auth/callback',
  enableOkta: true,
  commonServicesUrl: "https://ui.common-services.portal.pi.dev.gcpdns.internal.das/callback?navigateTo=",
  clientPreferenceDomainUrl: 'https://api.portal.pi.dev.gcpdns.internal.das/pf-pi-clientpreferencedomain',
  productDomainUrl: 'https://api.portal.pi.dev.gcpdns.internal.das/pf-pi-productdomain',
  rulesDomainUrl: 'https://enso-api-dbg.ecp-uat.gcpdns.internal.das',
  inventoryInsightUrl: "https://invinsight.pi.dev.gcpdns.internal.das/",
  inventoryDomainUrl: 'https://invdom.pi.uat.gcpdns.internal.das/',
  workflowDomianUrl: "https://workflow.pi.dev.gcpdns.internal.das/",
  refreshInterval: 60000, // 1 minute
  idleSeconds: 1800, // 30 minutes
  schedulerECMLibraryUrl: 'https://ecm.lib.uat.gcpdns.internal.das',
  // MFA
  mfaTokenUrl: "https://sit.api.digitalproducts.ps.awsdns.internal.das/v1/oauth/accesstoken",
  mfaUrl: "https://sit.api.digitalproducts.ps.awsdns.internal.das/v1/cpicad/multifactorauthentication",
  publicKey: `{
    "kty": "RSA",
    "e": "AQAB",
    "use": "sig",
    "kid": "OlJ_exxuKgPQwzkTTrT4giTn6kZs7DFlSKYHMs997Sk",
    "alg": "RS256",
    "n": "pavUG6nawph2ouzGoJFpZ3Cdl61rjtasUi3VUBtKyXCAC-k3rcWtCoikHOinS3hBs1xqfT3YQ0S_7xJFcq3ma6ZMg7npM8OFryOWWX126FQBf5mu41g-YXHa3R-oJXVvZRYcDLH4HCngPcFd-Ab3YDFv_7aaWWh1IZBGBXY1clRNawMgTXggz32PJUkesp6ZWRXFvfwzikBTxjdITWXK1JDo5d9w27Ncrrk25pRq4_WY889SUBT4DBkjy13Hl7bTo9ywbquNiB4DF1SByRZ-LMHApuA6OMnvAUqCP-wl13mxadOkL_jIrwgbVHWSVTmAEMFwIr2xAg0ypZOE91XKuw"
}`,
  schedulerECMLgnUrl: 'https://ecm.login.uat.gcpdns.internal.das',
  //C2P
  loginType: "INTERNAL",
  authorizationUrl: 'https://authorization.pi.uat.gcpdns.internal.das',
  identityServiceSoa: 'https://api.portal.pi.dev.gcpdns.internal.das/pf-pi-identity-svc/api/v1/webusers',
  identityServiceMfa: 'https://api.portal.pi.dev.gcpdns.internal.das/pf-pi-identity-svc/api/v1',
  validatorSvc: 'https://api.portal.pi.dev.gcpdns.internal.das/pf-pi-validator-svc',

  // SOA Links
  soaTokenUrl: "https://sit.api.anthem.com/v1/oauth/accesstoken",
  soaWebUsersUrl: "https://api.portal.pi.dev.gcpdns.internal.das/pf-pi-identity-svc/api/v1/webusers",
  piPortal: 'https://ui.portal.pi.dev.gcpdns.internal.das',
  //Identity Service
  identityUrl: 'https://api.portal.pi.dev.gcpdns.internal.das/pf-pi-identity-svc'

}