import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { ROUTING_LABELS } from 'src/app/_constants/menu.constant';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { externalAuthenticationConstants } from 'src/app/_helpers/helpers.constants';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { MPUIButtonModule } from 'marketplace-button';

@Component({
  selector: 'app-reset-password',
  standalone: true,
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule, 
    FormsModule, 
    RouterModule, 
    HttpClientModule,
    MPUIDynamicFormModule,
    MPUIButtonModule
  ]
})
export class ResetPasswordComponent implements OnInit {
  isChangePassword: boolean = true;
  NewPasswordFormJSON: any;
  NewPasswordFormJSONPath: any = "./assets/json/external-signIn/new-password-form.json";
  password: any;
  confirmPassword: any;
  isPasswordNotMatched: boolean = false;
  isPasswordMatch: boolean = false;
  contactUsUrl: string = externalAuthenticationConstants.CONTACT_US_URL;

  constructor(private userManagementSvc: UserManagementApiService, private router: Router) { }

  ngOnInit(): void {
    let _fetchPage = this.userManagementSvc.getAssetsJson(this.NewPasswordFormJSONPath);
    _fetchPage.subscribe(data => {
      this.NewPasswordFormJSON = data[ROUTING_LABELS.FORM_DETAILS];
    });
  }

  /**
  * This function is triggered when any change happens in field values
  * @param event 
  */
  onPasswordValueChange(event) {
    this.password = event.current.password;
    this.confirmPassword = event.current.confirmPassword;
  }

  /**
  * This function is triggered when we click on Update password button
  * @param event 
  */
  onUpdatePasswordButtonClicked(event) {
    if (this.password == this.confirmPassword) {
      this.isChangePassword = false;
      this.isPasswordMatch = true;
      this.isPasswordNotMatched = false;
    }
    else {
      this.isPasswordNotMatched = true;
    }
  }

  /**
  * this function will navigate to signin screen when we click on signin button
  * @param event 
  */
  onSigninButtonClicked() {
    this.router.navigate([ROUTING_LABELS.SIGN_IN]);
  }
}
