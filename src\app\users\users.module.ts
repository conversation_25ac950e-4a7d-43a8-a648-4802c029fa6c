import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MPUIBreadcrumbModule } from 'marketplace-breadcrumb';
import { MPUIButtonModule } from 'marketplace-button';
import { MPUICheckboxModule } from 'marketplace-checkbox';
import { MPUIDatePickerModule } from 'marketplace-date-picker';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { MPUIFormRepeaterModule } from 'marketplace-form-repeater';
import { MPUIInputModule } from 'marketplace-input';
import { MPUIModalDialogModule } from 'marketplace-popup';
import { MPUINotificationModule } from 'marketplace-notification';
import { MPUISelectModule } from 'marketplace-select';
import { MPUIStepperModule } from 'marketplace-stepper';
import { MPUISwitchModule } from 'marketplace-switch';
import { MPUITableModule } from 'marketplace-table';
import { MPUITabsModule } from 'marketplace-tabs';
import { MPUITextareaModule } from 'marketplace-textarea';
import { EditUserComponent } from '../users/edit-user/edit-user.component';
import { UsersComponent } from '../users/users.component';
import { ViewUserComponent } from '../users/view-user/view-user.component';
import { UsersRoutingModule } from './users-routing.module';
import { PermissionGuard } from '../_helpers/permission.guard';


@NgModule({
  declarations: [
    UsersComponent,
    ViewUserComponent,
    EditUserComponent
  ],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    MPUIInputModule,
    MPUINotificationModule,
    MPUIModalDialogModule,
    MPUIButtonModule,
    MPUITabsModule,
    MPUITableModule,
    MPUIBreadcrumbModule,
    MPUITextareaModule,
    MPUISwitchModule,
    MPUIDatePickerModule,
    MPUIDynamicFormModule,
    MPUICheckboxModule,
    MPUIStepperModule,
    UsersRoutingModule,
    MPUIFormRepeaterModule,
    MPUISelectModule
  ],
  providers: [PermissionGuard],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class UsersModule { }