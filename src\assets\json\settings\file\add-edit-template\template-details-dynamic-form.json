[{"type": "group", "name": "memberDetails", "label": "Enter Template Details", "column": "3", "groupControls": [{"label": "Template Name", "id": "templateName", "type": "text", "name": "templatename", "placeholder": "Name", "column": "2", "required": true}, {"label": "Description", "type": "textarea", "name": "description", "id": "specDescTxtArea", "column": "1", "required": true, "placeholder": "Write some description about the template"}]}, {"type": "group", "name": "details", "label": "Enter Details Below", "column": "2", "groupControls": [{"optionName": "name", "optionValue": "id", "id": "systemInventory", "label": "Choose The System", "type": "select", "multiple": false, "closeOnSelect": true, "name": "system", "column": "3", "required": true, "placeholder": "Origin from system", "relationship": [{"updateDataset": [{"id": "inventoryType", "dataset": []}], "when": null}, {"updateDataset": [{"id": "productName", "dataset": []}], "when": null}, {"updateSelectedValue": [{"id": "inventoryType", "value": null}, {"id": "productName", "value": null}], "when": null}]}, {"options": [], "optionName": "name", "optionValue": "id", "label": "Choose Inventory Type", "type": "select", "multiple": false, "closeOnSelect": true, "name": "inventoryType", "id": "inventoryType", "column": "3", "required": true, "placeholder": "Inventory Type", "relationship": [{"updateDataset": [{"id": "productName", "dataset": []}], "when": null}, {"updateSelectedValue": [{"id": "productName", "value": null}], "when": null}]}, {"options": [], "optionName": "name", "optionValue": "id", "label": "Choose Product Name", "type": "select", "multiple": false, "closeOnSelect": true, "name": "productName", "id": "productName", "column": "3", "required": true, "placeholder": "Product Name"}, {"options": [{"name": "PROD", "code": "PROD"}, {"name": "UAT", "code": "UAT"}, {"name": "PROD AND UAT", "code": "PROD AND UAT"}], "optionName": "name", "optionValue": "code", "label": "Choose Template Type", "type": "select", "multiple": false, "closeOnSelect": true, "name": "templateType", "id": "tmpltType", "column": "3", "required": true}, {"options": [{"name": "CSV", "code": "CSV"}, {"name": "Txt", "code": "Txt"}, {"name": "Excel", "code": "Excel"}], "optionName": "name", "optionValue": "code", "label": "Choose a file type to upload", "type": "select", "multiple": false, "closeOnSelect": true, "name": "fileType", "column": "3", "required": true, "id": "fileType", "placeholder": "File type"}, {"label": "Active Status", "id": "activeStatus", "type": "switch", "name": "status", "column": "4", "alignment": "horizontal"}, {"type": "checkboxgroup", "name": "header", "id": "headerCheckBox", "label": "", "column": "3", "checkboxgroupcolumn": 1, "customClass": "headersCheckbox", "options": [{"name": "headers", "label": "My Data has headers"}]}]}]