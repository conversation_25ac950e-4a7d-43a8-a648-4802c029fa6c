import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { MPUINotificationModule } from 'marketplace-notification';
import { CommonModule } from '@angular/common';
import { ProductRoutingModule } from './product-routing.module';
import { ProductListComponent } from './product-list/product-list.component';
import { BundleListComponent } from './bundle-list/bundle-list.component';
import { AddBundleComponent } from './add-bundle/add-bundle.component';
import { EditBundleComponent } from './edit-bundle/edit-bundle.component';
import { ViewBundleComponent } from './view-bundle/view-bundle.component';
import { MPUITableModule } from 'marketplace-table';
import { MPUIBreadcrumbModule } from 'marketplace-breadcrumb';
import { MPUIButtonModule } from 'marketplace-button';
import { MPUIDatePickerModule } from 'marketplace-date-picker';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { MPUITextareaModule } from 'marketplace-textarea';
import { MPUISwitchModule } from 'marketplace-switch';
import { MPUIModalDialogModule } from 'marketplace-popup';

@NgModule({
  declarations: [
    ProductListComponent,
    BundleListComponent,
    AddBundleComponent,
    EditBundleComponent,
    ViewBundleComponent
  ],
  imports: [
    CommonModule,
    ProductRoutingModule,
    MPUITableModule,
    MPUITextareaModule,
    MPUISwitchModule,
    MPUIDatePickerModule,
    MPUIButtonModule,
    MPUIModalDialogModule,
    MPUINotificationModule,
    MPUIBreadcrumbModule,
    MPUIDynamicFormModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ProductModule { }
