import { Component, ElementRef, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import systemjson from '../../../../assets/json/settings/system/system.json';
import systemFileType from '../../../../assets/json/settings/system/systemFileType.json';

import { HttpClient } from '@angular/common/http';
import { SystemService } from 'src/app/_services/system.service';
import { data } from 'jquery';
import { get } from 'lodash';

import clientjson from '../../../../assets/json/settings/system/clientjson.json';
import moment from 'moment';
import { AuthService } from 'src/app/_services/authentication.services';
@Component({
  selector: 'app-system',
  templateUrl: './system.component.html',
  styleUrls: ['./system.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SystemComponent implements OnInit {

  inventoryTypeObj: any = [];
  editInventoryTypeObj: any = [];
  isdefaultchnk: boolean = false;
  inventoryList: any[];
  editInventoryList: any[];
  checkboxneeded: any = true;
  headerText: any = "System List";
  notificationReady: boolean;
  isEdit: boolean = false;
  isCreate: boolean = true;
  isView: boolean = true;
  isVisible: boolean = true;
  editSystemName: string;
  public inputname = 'Used By';
  public headerTextNew = 'Add New System';
  public headerTextEdit = 'Edit System';
  public isPriviousRedirectPage = true;
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  dataURL: any[];
  dataURLfileType: any[];
  public dataDate: any = new Date();
  public lastRegfreshDate: any;
  public totalEntries: number = 0;
  totalClients: number;
  clients: any[];
  inputtextbox: any;
  openPanelIndex = 1;
  clientData: any;
  invList: any;
  systemDataJSON: any = [];
  notificationOpen: any = false;
  notificationHeader: string;
  notificationBody: string;
  notificationPosition: any;
  notificationDuration: any;
  notificationType: any;
  enableFormButtton: boolean = false;
  showData: boolean = false;
  selectedCurrRow: any
  changeInputText: string = ""
  editTblRedraw: any;
  selectedRow: any = "";
  showTickText: any = "";
  editSysTblChanges: any = [];
  usedBy: string = "";
  currentRowDetails: any
  breadcrumbDataset: any = [{ label: 'Home', url: '/' }, { label: 'System list' }];
  kebabOptions: any = [{ label: '<i  class="fa fa-edit"></i> Edit', id: 'Edit' }]
  kebabOptions_Readonly: any = []
  isReadOnly: boolean = false;
  // Pagination Search
  stepsConfig = [
    { first: "A", last: "H" },
    { first: "I", last: "P" },
    { first: "Q", last: "Z" }

  ];
  public enablePopup: boolean = false;
  columnDefinitions: any = {
    "switches": {
      "enableSorting": true,
      "enablePagination": true,
      "enableFiltering": true
    },

    "colDefs": [
      {
        "name": "System Code",
        "field": "systemCode",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Long Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": "",
        "width": 60
      },

      {
        "name": "System Name",
        "field": "systemName",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Long Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": "",
        "width": 180
      },

      {
        "name": "Inventory Type",
        "field": "inventoryType",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Long Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": "",
        "width": 160
      },
      {
        "name": "Created Date",
        "field": "createdDate",
        "filterType": "Calendar",
        "visible": "True",
        "editorType": "Long Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": "",
        "width": 110,
        "dateFormat": "MM/DD/YYYY"
      },

      {
        "name": "Created By",
        "field": "createdBy",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Long Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": "",
        "width": 55
      },

      {
        "name": "Last Modified Date",
        "field": "lastModifiedDate",
        "filterType": "Calendar",
        "visible": "True",
        "editorType": "Long Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": "",
        "width": 110,
        "dateFormat": "MM/DD/YYYY"
      },

      {
        "name": "Last Modified By",
        "field": "lastModifiedBy",
        "filterType": "Text",
        "visible": "True",
        "editorType": "Long Text",
        "editorTypeRoot": "",
        "editorTypeLabel": "",
        "editorTypeValue": ""
      }
    ]
  }

  columnConfig: any = this.columnDefinitions;
  columnConfigSystemFiletype: any = systemFileType.columnConfigSystemFiletype
  loggedInUser: any;

  constructor(private router: Router, private http: HttpClient, private el: ElementRef, private systmService: SystemService, private authService: AuthService) { }

  ngOnInit(): void {
    this.isReadOnly = !this.authService.isWriteOnly;
    this.isReadOnly ? this.kebabOptions = this.kebabOptions_Readonly : this.kebabOptions;
    let fetchCookie: any = document?.cookie?.split(";");
    fetchCookie?.forEach(e => {
      if (e?.includes("userId")) {
        this.loggedInUser = e?.trim()?.slice(7) ? e?.trim()?.slice(7) : "System";
      }
    })
    this.loggedInUser = this.loggedInUser ?? sessionStorage.getItem("userId")?.toUpperCase();
    this.getAllSystems();
    this.getAllInventoryType();
    this.createSysConfig()
    this.dataDate = ((this.dataDate.getMonth() > 8) ? (this.dataDate.getMonth() + 1) : ('0' + (this.dataDate.getMonth() + 1))) + '/' + ((this.dataDate.getDate() > 9) ? this.dataDate.getDate() : ('0' + this.dataDate.getDate())) + '/' + this.dataDate.getFullYear()
    this.isVisible = false;
  }

  /*-- Adding Custom formatter to table Column Config --*/
  createSysConfig() {
    systemFileType.columnConfigSystemFiletype.colDefs.forEach((e) => {
      e.name == "Enable Upload" ? e['customFormatter'] = this.customDefaultCheckBox : ''
      e.name == "File Settings" ? e['customFormatter'] = this.customFormatterFileType : ''
    })
  }

  /*--Get All The Inventory Type List --*/
  getAllInventoryType() {
    this.systmService.getAllInventoryTypes().subscribe(
      (invdata) => {
        this.invList = invdata;
      }
    )
  }


  /*--Get All The System List--*/
  getAllSystems() {
    this.systmService.getAllSystem().subscribe((sysdata: any) => {
      if (sysdata.length) {
        this.totalEntries = sysdata.length;
        this.lastRegfreshDate = new Date(sysdata[0].lastModDate.slice(0, 10));
        this.lastRegfreshDate = ((this.lastRegfreshDate.getMonth() > 8) ? (this.lastRegfreshDate.getMonth() + 1) : ('0' + (this.lastRegfreshDate.getMonth() + 1))) + '/' + ((this.lastRegfreshDate.getDate() > 9) ? this.lastRegfreshDate.getDate() : ('0' + this.lastRegfreshDate.getDate())) + '/' + this.lastRegfreshDate.getFullYear()
        sysdata.forEach(element => {
          const syselem = {
            "id": get(element, 'sysId'),
            "systemCode": get(element, 'sysCode'),
            "systemName": get(element, 'sysName'),
            "inventoryType": get(element, 'invTypeNames'),
            "createdDate": get(element, 'createdDate'),
            "createdBy": get(element, 'createdBy'),
            "lastModifiedDate": get(element, 'lastModDate'),
            "lastModifiedBy": get(element, 'lastModBy'),
            "status": get(element, 'status'),
          };
          this.systemDataJSON.push(syselem);

        });
        setTimeout(() => this.showData = true, 10);
      }
    }, (err) => {

      this.showData = true;
    })
  }

  /*--Get All The Client List By System ID--*/
  getAllClients(systemId: any) {
    this.systmService.getAllClients(systemId).subscribe(
      (clntdata) => {
        this.clients = <any[]>clntdata;
        if (this.clients.length > 0) {
          this.clients.forEach(client => {
            client.createdDate = new Date(client?.creatDtm)
            client.createdDate = (moment(client?.creatDtm).format('MM-DD-YYYY')).toString();
          })
        }
        this.totalClients = this.clients.length;
        this.clientData = this.clients;
      }
    )

  }

  /*--customFormatterStatus funtion for Status Coulumn in  System table--*/
  customFormatStatus(event) {
    let btn;
    switch (event.value) {
      case 'Active':
        btn = "<button type='button' class='btn btn rule-dashboard btn-active pointer'>Active</button>";
        break;
      case 'Inactive':
        btn = "<button type='button' class='btn btn rule-dashboard btn-inactive pointer'>Inactive</button>";
        break;
      case 'Draft':
        btn = "<button type='button' class='btn btn rule-dashboard btn-draft pointer'>Draft</button>";
        break;
      case 'On Hold':
        btn = "<button type='button' class='btn btn rule-dashboard btn-onhold pointer'>On Hold</button>";
        break;
    }
    return btn;
  }


  /*--customDefaultCheckBox funtion for Enabled/Disabled functionality for to know  File Type is Acive or Invactive. --*/
  customDefaultCheckBox(event) {
    return `<div class="custom-control custom-switch">
    <input type="checkbox" class="custom-control-input" id=“selector-${event.dataContext.id}” ${event.dataContext.actvInd && "checked"}>
    <label class="custom-control-label" for=“selector-${event.dataContext.id}”></label>
  </div>`;
  }



  /**
   * customFormatterStatus funtion for button in  System table
   * @param event 
   */
  customFormatterAction(event) {
    return `<div class="rule-dashboard dropdown-container" tabindex=“-1”>
    <input id=“selector-${event.dataContext.id}” type="checkbox" name="menu" style="display:none;" />
    <label  for=“selector-${event.dataContext.id}” class="three-dots"></label>
    <div class="dropdown">
    <div class="table-action-menu" style="cursor:pointer;" ><i  class="fa fa-edit" title="Edit" dataaction="edit"  datevalue=${event.dataContext.id}></i>Edit System</div>
    </div>
    </div>`
  }

  /**
    * customFormatterStatus funtion for File Details while Editing System
    * @param event 
    */
  customFormatterFileType(event) {
    let btnInsideText = event.dataContext.importFlag & event.dataContext.exportFlag ? "Import and Export" : event.dataContext.importFlag ? "Import" : event.dataContext.exportFlag ? "Export" : "Import"
    return `
    <button type="button" id="sel-${event.dataContext.id}" class="rule-dashboard dropdown-container">
    <label for=sel-${event.dataContext.id}” class="fa fa-lock btn  dropdown-toggle drpdwnColor">
    <b class="font" id="dropdown_Input-${event.dataContext.id}">${btnInsideText}</b>
    <i class="bi bi-arrow-down"></i>
    </label>
    </button>
`
  }

  /*-- To get the co-ordinates where mouse is clicked for edit screen's table Dropdown --*/
  getOffset(el) {
    const rect = el.getBoundingClientRect();
    return {
      left: rect.left + window.scrollX,
      top: rect.top + window.scrollY
    };
  }

  /**
   * Change Inner text in Edit system screen's Table's Dropdown's initial input Box & 
   * Close the Dropdown popup when one option is selected.
   */
  changeText() {
    if (this.changeInputText != "") {
      this.selectedRow.innerText = this.changeInputText;
      this.changeInputText = ""
      setTimeout(() => {
        this.editTblRedraw = Date.now()
      }, 100);
      let target = document.getElementById('dropdownBtn');
      target.style.display = "none";
    }
  }

  changeUsedByColor = () => {
    let usedByText = document.getElementById('usedBy');
    usedByText.style.color = "#794cff";
  }
  removeUsedByColor() {
    let usedByText = document.getElementById('usedBy');
    usedByText.style.color = ""
  }

  /**
   * To Get which option is selected from dropDown in Edit System's Table and make Changes in initial input box by calling changeText()
   * @param inputInnerText 
   */
  clickedInDropdown(inputInnerText) {
    this.changeInputText = inputInnerText
    let dropdownStatusImport = inputInnerText == "Import" ? true : inputInnerText == "Import and Export" ? true : false;
    let dropdownStatusExport = inputInnerText == "Export" ? true : inputInnerText == "Import and Export" ? true : false;
    let chngdFileTemplts

    if (this.editSysTblChanges.length > 0 && this.editSysTblChanges.some(e => e.fileTmplId == this.currentRowDetails.fileTmplId)) {
      this.editSysTblChanges.map((e, i) => {
        if (e.fileTmplId == this.currentRowDetails.fileTmplId) {
          if (dropdownStatusImport) { this.editSysTblChanges[i].importFlag = dropdownStatusImport; this.editSysTblChanges[i].exportFlag = dropdownStatusExport; }
          if (dropdownStatusExport) { this.editSysTblChanges[i].exportFlag = dropdownStatusExport; this.editSysTblChanges[i].importFlag = dropdownStatusImport; }
          if (dropdownStatusImport && dropdownStatusExport) { this.editSysTblChanges[i].exportFlag = dropdownStatusExport; this.editSysTblChanges[i].importFlag = dropdownStatusImport }
        }
      })
    }
    else {
      chngdFileTemplts = {
        "fileTmplId": this.currentRowDetails.fileTmplId,
        "importFlag": dropdownStatusImport,
        "exportFlag": dropdownStatusExport,
        "fileTmplName": this.currentRowDetails.fileTmplName,
        "actvInd": this.currentRowDetails.actvInd,
        "invTypeName": this.currentRowDetails.invTypeName,
        "fileFrmtTxt": this.currentRowDetails.fileFrmtTxt
      }
      this.editSysTblChanges.push(chngdFileTemplts)
    }
    this.changeText()
    this.enableFormButtton = false;
  }



  /*-- OnClick of row in Edit Screen's Table --*/
  rendererTableClicked(event) {
    let _clickedElement: any = event.eventData.target;
    let _elemId: any = event.currentRow.id;
    let Id = "sel-" + _elemId
    let currRowId = "dropdown_Input-" + _elemId
    // On add click
    let array = this.releationSHJSON;
    if (_clickedElement.innerText == "Edit") {
      this.editSystem(event.currentRow);
    }
    let target = document.getElementById('dropdownBtn');

    if (_clickedElement.innerText.trim() == "Import" || _clickedElement.innerText.trim() == "Export" || _clickedElement.innerText.trim() == "Import and Export") {
      this.enablePopup = true;
      let _element = document.getElementById(Id);
      this.selectedRow = document.getElementById(currRowId)
      this.currentRowDetails = event.currentRow

      target.style.left = (this.getOffset(_element).left - 230) + 'px';
      target.style.top = this.getOffset(_element).top + 'px';
      target.style.display = "block";
      this.showTickText = _clickedElement.innerText.trim()
    } else {
      target.style.display = "none";
    }

    if (_clickedElement.classList.contains("custom-control-input")) {
      let chngdFileTemplts
      if (this.editSysTblChanges.length > 0 && this.editSysTblChanges.some(e => e.fileTmplId == event.currentRow.fileTmplId)) {
        this.editSysTblChanges.map((e, i) => {
          if (e.fileTmplId == event.currentRow.fileTmplId) {
            this.editSysTblChanges[i].actvInd = !e.actvInd
          }
        })
      } else {
        chngdFileTemplts = {
          "fileTmplId": event.currentRow.fileTmplId,
          "fileTmplName": event.currentRow.fileTmplName,
          "actvInd": !event.currentRow.actvInd,
          "importFlag": event.currentRow.importFlag,
          "exportFlag": event.currentRow.exportFlag,
          "invTypeName": event.currentRow.invTypeName,
          "fileFrmtTxt": event.currentRow.fileFrmtTxt
        }
        this.editSysTblChanges.push(chngdFileTemplts)
      }

      this.enableFormButtton = false;
    }

  }

  /*-- remove dropdown when mouse leaves the table in Edit Screen --*/
  displayNone(event: any) {
    if (!this.enablePopup) {
      let target = document.getElementById('dropdownBtn');
      target.style.display = "none";
    } else {
      this.enablePopup = false;
    }
  }

  /*--Showinng the Popup for Creating New System. --*/
  createSystem() {
    if (!this.isReadOnly) {
      this.releationSHJSON[0].groupControls.filter(x => x.name == "syscode")[0].disabled = false;
      this.isView = false;
      this.invList.invTypeList.map((e) => { e.description = "" })
      this.inventoryList = this.invList.invTypeList;
      this.enableFormButtton = false;
      this.isVisible = true;
    }
  }

  /*--Update System Details--*/
  editSystem(sysData: any) {
    this.getAllClients(sysData.sysId);
    this.invList.invTypeList.map((e) => { e.description = "" })
    this.editInventoryList = this.invList.invTypeList;
    this.editSystemName = '' + sysData.sysName + '';
    this.isView = false;
    this.isEdit = true;
    this.isCreate = false;
    this.enableFormButtton = true;
    this.showData = false
    this.releationSHJSONEdit[0].groupControls.filter(x => x.name == "sysName")[0].value = sysData.sysName;
    this.releationSHJSONEdit[0].groupControls.filter(x => x.name == "sysDesc")[0].value = sysData.sysDesc;
    let onBoardDateStr: any = new Date(sysData.onBoardDate);
    onBoardDateStr = ((onBoardDateStr.getMonth() > 8) ? (onBoardDateStr.getMonth() + 1) : ('0' + (onBoardDateStr.getMonth() + 1))) + '-' + ((onBoardDateStr.getDate() > 9) ? onBoardDateStr.getDate() : ('0' + onBoardDateStr.getDate())) + '-' + onBoardDateStr.getFullYear();
    this.releationSHJSONEdit[0].groupControls.filter(x => x.name == "onBoardDate")[0].value = onBoardDateStr;
    this.isVisible = true;
  }


  /**
   * marketplace-pagination onSelection in New System Screen
   * @param event 
   */
  selectedLinkCreate(event) {
    if (event.target.checked) {
      this.inventoryTypeObj.push(event.selected);
    }
    else {
      this.inventoryTypeObj.forEach((elem, i) => {
        if (elem.name == event["selected"].name) {
          this.inventoryTypeObj.splice(i, 1)
        }
      })
    }
  }

  /**
   * marketplace-pagination onSelection in Edit System Screen
   * @param event 
   */
  selectedLinkEdit(event) {
    this.enableFormButtton = false;
    if (event.target.checked) {
      this.editInventoryTypeObj.push(event.selected);
    }
    else {
      this.editInventoryTypeObj.forEach((elem, i) => {
        if (elem.name == event["selected"].name) {
          this.editInventoryTypeObj.splice(i, 1)
        }
      })
    }
  }

  clientss:
    [
      {

        "clientName": "Client Pareo",
        "clientDesc": "Fusce vehicula dolor arcu, sit amet blandit dolor mollis nec. Donec viverra eleifend lacus, vitae ullamcorper metus."

      },
      {
        "clientName": "DBG Pareo",
        "clientDesc": "Fusce vehicula dolor arcu, sit amet blandit dolor mollis nec. Donec viverra eleifend lacus, vitae ullamcorper metus."
      }
    ]

  isOpenableActive: boolean = false;
  openPanelActiveIndex: string = "0";
  httpRequestActive: any = {
    "url": "./assets/json/settings/clientjson.json",
    "dataRoot": "src",
    "headerLabel": "clientName",
    "body": "clientDesc",

  }

  noRecordsEditSys: boolean = false

  /** Get The Client Details List */
  getClients(val: any) {
    let inputValue = document.getElementById('usedById')['value'];
    if (inputValue) {
      this.clients = this.clientData.filter(x => x.clientName.toLowerCase().includes(inputValue.toLowerCase()));
      this.noRecordsEditSys = this.clients.length == 0 ? true : false
    }
    else {
      this.noRecordsEditSys = false
      this.clients = this.clientData;
    }
  }

  /**
   * Refresh Button , to get latest last modified Data.
   */
  refreshLstUpdted() {
    this.lastRegfreshDate = new Date(this.systemDataJSON[0].lastModifiedDate.slice(0, 10));
    this.lastRegfreshDate = ((this.lastRegfreshDate.getMonth() > 8) ? (this.lastRegfreshDate.getMonth() + 1) : ('0' + (this.lastRegfreshDate.getMonth() + 1))) + '/' + ((this.lastRegfreshDate.getDate() > 9) ? this.lastRegfreshDate.getDate() : ('0' + this.lastRegfreshDate.getDate())) + '/' + this.lastRegfreshDate.getFullYear()
  }

  /*--Save System to DB--*/
  saveSystem() {
    if (this.isReadOnly) return false;
    this.notificationReady = false;
    if (this.isEdit) {
      let systemName = (<HTMLInputElement>document.getElementById("sysName")).value;
      let sysDesc = document.getElementsByTagName("textarea")[0].value;

      if (systemName && sysDesc) {

        let editSysPayload = {
          "sysId": this.selectedCurrRow.sysId,
          "sysCode": this.selectedCurrRow.sysCode,
          "sysName": systemName,
          "sysDesc": sysDesc,
          "status": this.selectedCurrRow.status,
          "invTypeIds": this.editInventoryTypeObj.map(e => e.id),
          "fileTemplates": this.editSysTblChanges,
          "createModBy": this.loggedInUser,
        }

        this.systmService.editExistingSystem(editSysPayload).subscribe((apiResponse) => {

          if (apiResponse?.message?.includes("Successfully")) {
            let notificationHeader = "Success";
            let notificationBody = "System Updated Successfully.";
            let notificationType = "success";
            this.showNotification(notificationHeader, notificationBody, notificationType)
            setTimeout(() => {
              location.reload()
            }, 3000);
          } else {
            if (apiResponse?.includes("Name already exists")) {
              let notificationHeader = "Warning";
              let notificationBody = "System Code Or Name already exists or has been previously used. Please choose a different code and name.";
              let notificationType = "warning";
              this.showNotification(notificationHeader, notificationBody, notificationType);
            }
          }
        })


      }
      else {
        if (systemName == "") {
          let notificationHeader = "Error";
          let notificationBody = "Please Enter System Name.";
          let notificationType = "error";
          this.showNotification(notificationHeader, notificationBody, notificationType)
          return false;
        }

        else if (sysDesc == "") {
          let notificationHeader = "Error";
          let notificationBody = "Please Enter System Desciption.";
          let notificationType = "error";
          this.showNotification(notificationHeader, notificationBody, notificationType);
          return false;
        }

      }
    }

    else {
      let systemNameCr = (<HTMLInputElement>document.getElementById("sysNameCr")).value;
      let sysDescCr = document.getElementsByTagName("textarea")[0].value;
      let syscode = (<HTMLInputElement>document.getElementById("syscode")).value;

      if (systemNameCr && sysDescCr && syscode) {

        let newSysPayload = {
          "sysId": null,
          "sysCode": syscode,
          "sysName": systemNameCr,
          "sysDesc": sysDescCr,
          "status": "Active",
          "invTypeIds": this.inventoryTypeObj.map(e => e.id),
          "createModBy": this.loggedInUser
        }

        this.systmService.createNewSystem(newSysPayload).subscribe((apiResponse) => {


          if (apiResponse?.message?.includes("Successfully")) {
            let notificationHeader = "Success";
            let notificationBody = "System added Successfully.";
            let notificationType = "success";
            this.showNotification(notificationHeader, notificationBody, notificationType);
            this.inventoryTypeObj = []
            setTimeout(() => {
              location.reload()
            }, 3000);
          } else {
            if (apiResponse?.includes("Name already exists")) {
              let notificationHeader = "Warning";
              let notificationBody = "System Code Or Name already exists or has been previously used. Please choose a different code and name.";
              let notificationType = "error";
              this.showNotification(notificationHeader, notificationBody, notificationType);
            }
          }
        })


      }
      else {
        if (syscode == "") {
          let notificationHeader = "Error";
          let notificationBody = "Please Enter System Code.";
          let notificationType = "error";
          this.showNotification(notificationHeader, notificationBody, notificationType);
          return false;
        }
        if (systemNameCr == "") {
          let notificationHeader = "Error";
          let notificationBody = "Please Enter System Name.";
          let notificationType = "error";
          this.showNotification(notificationHeader, notificationBody, notificationType);
          return false;
        }

        if (sysDescCr == "") {
          let notificationHeader = "Error";
          let notificationBody = "Please Enter System Desciption.";
          let notificationType = "error";
          this.showNotification(notificationHeader, notificationBody, notificationType);
          return false;
        }
      }
    }

    return true;
  }

  /*--Show The Notification Toaster--*/
  showNotification(notificationHeader: any, notificationBody: any, notificationType: any) {
    this.notificationOpen = true;
    this.notificationHeader = notificationHeader;
    this.notificationBody = notificationBody;
    this.notificationPosition = "top-right";
    this.notificationDuration = 5000;
    this.notificationType = notificationType;

    setTimeout(() => {
      this.notificationReady = true;
    }, 100);

  }

  /*--Go Previous Screen--*/
  goToPrevious() {
    this.inventoryTypeObj = []
    location.reload()
  }
  releationSHJSON: any[] = [{
    type: "group",
    name: "system",
    label: "",
    column: 1,
    groupControls: [
      {
        "label": "System Code",
        "type": "text",
        "name": "syscode",
        "id": "syscode",
        "column": "1",
        "visible": true,
      },
      {
        "label": "System Name",
        "type": "text",
        "name": "sysNameCr",
        "id": "sysNameCr",
        "column": "1",
        "disabled": false
      },
      {
        "label": "System Description",
        "type": "textarea",
        "name": "sysDescCr",
        "id": "sysDescCr",
        "column": "1",
        "disabled": false,
        "maxLimit": 255
      }
    ]
  }];

  releationSHJSONEdit: any[] = [{
    type: "group",
    name: "systemEdit",
    label: "",
    column: 1,
    groupControls: [
      {
        "label": "System Name",
        "type": "text",
        "name": "sysName",
        "id": "sysName",
        "column": "1",
        "disabled": false

      },
      {
        "label": "System Description",
        "type": "textarea",
        "name": "sysDesc",
        "id": "sysDesc",
        "column": "1",
        "disabled": false,
        "maxLimit": 255,
      },
      {
        "label": "On boarded date",
        "type": "date",
        "name": "onBoardDate",
        "dateFormat": "MM-DD-YYYY",
        "column": "1",
        "disabled": true,
        "visible": true,
        "pickerType": "single",
      },
    ]
  }];


  /**
   * Going Back previous page from Add new system Screen OR From Edit screen
   */
  backToPreviousPage() {
    this.inventoryTypeObj = []
    location.reload()
  }

  /**
   * On Value Change Function for Edit Screen - marketplace-dynamic-form
   * @param event 
   * @param item 
   */
  formValue(event: any, item: string) {
    if (item === 'details') {
      this.enableFormButtton = !event.valid;
    }
  }

  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }

  /*-- System Screen's Table Kebab options Navigate --*/
  onDropdownOptionsClick(event) {
    let rulesAction = event.text;
    switch (rulesAction) {
      case 'Edit':
        let currSysId = event.currentRow.id
        this.systmService.fetchSingleSystem(currSysId).subscribe((apiResponse) => {
          if (apiResponse.length == 0) {
            let notificationHeader = "Warning";
            let notificationBody = "Unable to fetch System Data .";
            let notificationType = "warning";
            this.showNotification(notificationHeader, notificationBody, notificationType);
          } else {
            this.selectedCurrRow = apiResponse;
            this.editSystem(apiResponse);
            this.invList.invTypeList.map((e) => {
              apiResponse.invTypeIds.map((elem) => {
                if (e.id == elem) {
                  e['isChecked'] = true
                  this.editInventoryTypeObj.push(e)
                }
              })
            })
            this.dataURLfileType = apiResponse.fileTemplates;
          }
        })
        break;

    }
  }
}
