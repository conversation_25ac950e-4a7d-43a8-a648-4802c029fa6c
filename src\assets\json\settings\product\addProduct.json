{"product": [{"type": "group", "name": "General_1", "label": "", "column": "2", "groupControls": [{"label": "Membership Count", "type": "number", "name": "membership_count", "column": "1", "disabled": false, "required": true, "value": "", "placeholder": "Please Enter Membership Count"}, {"label": "Revenue Amount ($)", "type": "number", "name": "revenue_amount", "column": "1", "disabled": false, "required": true, "value": "", "placeholder": "Please Enter Revenue Count"}, {"options": [], "optionName": "name", "optionValue": "id", "label": "Product", "type": "select", "multiple": false, "closeOnSelect": true, "name": "product", "id": "product", "column": "2", "disabled": false, "required": true, "placeholder": "Please Select a Product", "relatedDateControls": [{"target": "invType"}]}]}, {"type": "group", "name": "General_2", "label": "", "column": "2", "groupControls": [{"label": "Created By", "type": "text", "name": "create_by", "column": "2", "disabled": true, "value": "DBG USER"}, {"label": "Updated By", "type": "text", "name": "update_by", "column": "2", "disabled": true, "value": "DBG USER"}, {"label": "Client Product Effective Date", "type": "date", "name": "effective_date", "column": "2", "disabled": false, "pickerType": "single", "value": null, "required": true, "placeholder": "Select Effective Start Date", "relatedDateControls": [{"target": "effend_date"}]}, {"id": "effend_date", "label": "Client Product Termination Date", "type": "date", "name": "termination_date", "column": "2", "disabled": false, "pickerType": "single", "value": null, "required": true, "placeholder": "Select Effective Termination Date"}, {"options": [{"name": "Active", "value": "true"}, {"name": "InActive", "value": "false"}], "optionName": "name", "optionValue": "value", "label": "Status", "type": "radio", "name": "Status", "id": "Status", "column": "4", "required": true, "disabled": false, "customClass": "form-radio-button"}]}], "inventoryType": [{"type": "group", "name": "General_3", "label": "", "column": "2", "groupControls": [{"options": [], "optionName": "name", "optionValue": "id", "label": "Inventory Type", "type": "select", "multiple": true, "closeOnSelect": true, "name": "invType", "id": "invType", "column": "2", "disabled": false, "required": true, "placeholder": "Please Select an Inventory Type"}]}]}