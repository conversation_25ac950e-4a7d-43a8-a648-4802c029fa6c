<!-- Template list -->
<div class="page-wrapper">
    <div *ngIf="showLoader">

        <div class="loaderPosition loader"></div>
    </div>
    <div class="row ml-0 filesBreadcrumb">
        <app-filebreadcum [schedulerBreadCrumbData]='schedulerBreadCrumbData'></app-filebreadcum>
    </div>
    <h3>Files</h3>
    <h5>Manage and create templates of table with all the columns predefined for frequent use</h5>
    <div class="card elevated-card">
        <div class="card-header">
            <h5>Template list &#124; <span class="template-count">{{templateDataJSON.length}} Template(s)</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="templateList">
                <div class="templateListTbl">
                    <marketplace-table [id]="'templates-table'"
                                       [dataset]="templateDataJSON"
                                       *ngIf="showData"
                                       [pagination]="10"
                                       [isRowSelectable]='true'
                                       [isSingleSelectionRequired]="true"
                                       [columnDefinitions]="templateColumnConfigFilters"
                                       (onCellClick)="templateTableClicked($event)"
                                       [dropdownOptions]="kebabOptions"
                                       (onDropdownOptionsClick)="onDropdownOptionsClick($event)"
                                       [recreate]="tableRecreate"
                                       [enableAutoHeight]="false">
                    </marketplace-table>
                </div>
                <div class="templateListCreateBtn">
                    <marketplace-button class="justify-content-center"
                                        [label]="'Create Template'"
                                        [type]="'primary'"
                                        [enabled]="!isReadOnly"
                                        [name]="'Create Template'"
                                        (onclick)="createBtnClicked()">
                    </marketplace-button>
                </div>
            </div>
        </div>
    </div>
    <br />
    <!-- File List -->
    <div class="card elevated-card">
        <div class="card-header">
            <h5>File list &#124; <span class="template-count">{{2}} Files(s)</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-9">
                    <div class="card-headerfile">
                        <span style="float: right;">
                            <svg width="16"
                                 height="17"
                                 viewBox="0 0 16 17"
                                 fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path d="M8 4.78223C8.55228 4.78223 9 4.33451 9 3.78223C9 3.22994 8.55228 2.78223 8 2.78223C7.44772 2.78223 7 3.22994 7 3.78223C7 4.33451 7.44772 4.78223 8 4.78223Z"
                                      fill="#231E33" />
                                <path d="M9 8.78223C9 9.33451 8.55228 9.78223 8 9.78223C7.44772 9.78223 7 9.33451 7 8.78223C7 8.22994 7.44772 7.78223 8 7.78223C8.55228 7.78223 9 8.22994 9 8.78223Z"
                                      fill="#231E33" />
                                <path d="M9 13.7822C9 14.3345 8.55228 14.7822 8 14.7822C7.44772 14.7822 7 14.3345 7 13.7822C7 13.2299 7.44772 12.7822 8 12.7822C8.55228 12.7822 9 13.2299 9 13.7822Z"
                                      fill="#231E33" />
                            </svg>
                        </span>

                    </div>
                    <div class="form-control col-12"
                         rows="10"
                         style="height: 200px;"
                         placeholder="Select template and import file into">
                        <div class="files">
                            <div [ngClass]="[i == 0 ? 'alert alert-success' : 'alert alert-danger']"
                                 *ngFor="let file of filesData; let i = index">
                                <input name="file"
                                       type="radio"
                                       (change)="onFileClick($event)"
                                       [value]="file.id">
                                <span class="pad2">{{file.name}}</span>
                                <span *ngIf="i == 0"
                                      class="float">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                         viewBox="0 0 512 512"
                                         aria-hidden="true"
                                         focusable="false"
                                         style="width: 1em; height: 1em; vertical-align: middle; fill: currentColor;">
                                        <path d="M173.898 439.404l-166.4-166.4c-12.496-12.496-12.496-32.758 0-45.255l45.255-45.255c12.497-12.497 32.758-12.497 45.255 0L192 312.69l261.493-261.494c12.496-12.496 32.757-12.497 45.255 0l45.255 45.255c12.496 12.496 12.497 32.757 0 45.255l-312 312.003c-12.497 12.496-32.758 12.496-45.255-.001z" />
                                    </svg>
                                </span>
                                <span *ngIf="i == 1"
                                      class="float">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                         viewBox="0 0 576 512"
                                         aria-hidden="true"
                                         focusable="false"
                                         style="width: 1em; height: 1em; vertical-align: middle; fill: currentColor;">
                                        <path d="M569.52 440.13L327.8 54.1c-28.84-48-98.76-47.92-127.52 0L6.48 440.13c-29.78 49.47 4.6 111.87 63.76 111.87h496.34c59.15 0 93.54-62.39 63.76-111.87zM287.97 160c8.35 0 15.31 6.71 15.31 15.31v130.69c0 8.6-6.96 15.31-15.31 15.31s-15.31-6.71-15.31-15.31v-130.69c0-8.6 6.96-15.31 15.31-15.31zm0 240c-13.25 0-24-10.75-24-24s10.75-24 24-24 24 10.75 24 24-10.75 24-24 24z" />
                                    </svg>
                                </span>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="col-3 elemToCenter">
                    <input type="file"
                           id="imgupload"
                           (change)="onFileSelected($event)"
                           style="display:none"
                           #fileUpload />

                    <marketplace-button [label]="'Import Data'"
                                        [type]="'primary'"
                                        [enabled]="enableImportButton && !isReadOnly"
                                        [name]="'Import Data'"
                                        (onclick)="fileUpload.click()">
                    </marketplace-button>

                </div>
            </div>
        </div>
    </div>

    <br />
    <div class="card elevated-card">
        <div class="card-header">
            <span class="spanrem">Selected File Status - {{selectedFile}} </span>
            <span style="float: right;">
                <svg width="16"
                     height="17"
                     viewBox="0 0 16 17"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 4.78223C8.55228 4.78223 9 4.33451 9 3.78223C9 3.22994 8.55228 2.78223 8 2.78223C7.44772 2.78223 7 3.22994 7 3.78223C7 4.33451 7.44772 4.78223 8 4.78223Z"
                          fill="#231E33" />
                    <path d="M9 8.78223C9 9.33451 8.55228 9.78223 8 9.78223C7.44772 9.78223 7 9.33451 7 8.78223C7 8.22994 7.44772 7.78223 8 7.78223C8.55228 7.78223 9 8.22994 9 8.78223Z"
                          fill="#231E33" />
                    <path d="M9 13.7822C9 14.3345 8.55228 14.7822 8 14.7822C7.44772 14.7822 7 14.3345 7 13.7822C7 13.2299 7.44772 12.7822 8 12.7822C8.55228 12.7822 9 13.2299 9 13.7822Z"
                          fill="#231E33" />
                </svg>
            </span>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-12">
                    <marketplace-table *ngIf="isFileSelect"
                                       [id]="'file-status-table'"
                                       [dataset]="fileStatusDataJSON"
                                       [columnDefinitions]="fileStatusColumnConfigFilters"
                                       (onCellClick)="templateTableClicked($event)"
                                       (onTableReady)="tableReady($event)">
                    </marketplace-table>
                    <div *ngIf="!isFileSelect"
                         style="padding-left: 38%;"><b>Please Select The File to View.</b></div>
                </div>
            </div>
        </div>
    </div>

    <marketplace-popup [open]="confirmPopup"
                       [size]="'small'"
                       (onClose)="closePopup($event)">
        <div mpui-modal-header>Confirm</div>
        <div mpui-modal-body>
            <h4>Do you want to Delete the Template</h4>
        </div>
        <div mpui-modal-footer>
            <marketplace-button [label]="'No'"
                                [type]="'secondary'"
                                (onclick)="closePopup($event)">
            </marketplace-button>
            <marketplace-button [label]="'Yes'"
                                [type]="'primary'"
                                (onclick)="confimDelete($event)">
            </marketplace-button>
        </div>
    </marketplace-popup>

</div>

<br />
<marketplace-notification *ngIf="notificationOpen=='true'"
                          [open]="notificationOpen"
                          [header]="notificationHeader"
                          [bodyText]="notificationBody"
                          [type]="notificationType"
                          [duration]="notificationDuration"
                          [position]="notificationPosition">
</marketplace-notification>