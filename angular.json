{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"pi-pf-portal-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "sass"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/pi-pf-portal-ui", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "sass", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.sass", "./node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/@slickgrid-universal/common/dist/styles/css/slickgrid-theme-bootstrap.css", "./node_modules/flatpickr/dist/flatpickr.min.css"], "scripts": ["./node_modules/jquery/dist/jquery.js", "./node_modules/bootstrap/dist/js/bootstrap.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "200kb"}], "outputHashing": "all"}, "develop": {"budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "200kb"}], "optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "sit": {"budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "200kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.sit.ts"}], "outputHashing": "all", "optimization": false, "sourceMap": true}, "uat": {"budgets": [{"type": "initial", "maximumWarning": "12mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "200kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}], "outputHashing": "all", "optimization": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "pi-pf-portal-ui:build:production"}, "develop": {"buildTarget": "pi-pf-portal-ui:build:develop"}, "sit": {"buildTarget": "pi-pf-portal-ui:build:sit"}}, "defaultConfiguration": "develop"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "pi-pf-portal-ui:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "sass", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.sass"], "scripts": [], "codeCoverage": true, "codeCoverageExclude": ["src/**/*.spec.ts", "src/**/*.mock.ts", "src/test.ts", "src/environments/**", "src/app/users/**", "src/app/users/_services/**", "src/app/_services/**", "src/app/_constants/**"]}}}}}}