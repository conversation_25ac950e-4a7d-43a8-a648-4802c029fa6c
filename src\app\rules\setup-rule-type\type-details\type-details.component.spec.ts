import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { NO_ERRORS_SCHEMA } from '@angular/core';

import { TypeDetailsComponent } from './type-details.component';

describe('TypeDetailsComponent', () => {
  let component: TypeDetailsComponent;
  let fixture: ComponentFixture<TypeDetailsComponent>;
  let mockRouter: any;

  beforeEach(async () => {
    mockRouter = { navigate: jasmine.createSpy('navigate') };
    await TestBed.configureTestingModule({
      declarations: [ TypeDetailsComponent ],
      providers: [ { provide: Router, useValue: mockRouter } ],
      schemas: [NO_ERRORS_SCHEMA]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TypeDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should have default header text', () => {
      expect(component.headerText).toBe('Add New Rule Sub Type');
    });

    it('should initialize with correct default properties', () => {
      expect(component.isPriviousRedirectPage).toBeTrue();
      expect(component.enableSumbitButton).toBeFalse();
      expect(component.viewType).toBe('details');
      expect(component.RequiredFieldsText).toBe('Required Fields');
      expect(component.label).toBe('Required Fields');
      expect(component.name).toBe('required_fields_dropdown');
      expect(component.type).toBe('multiselect');
    });
  });

  describe('Data Structure Validation', () => {
    it('should have valid breadcrumb dataset structure', () => {
      expect(Array.isArray(component.breadcrumbDataset)).toBeTrue();
      expect(component.breadcrumbDataset.length).toBe(4);

      // Validate each breadcrumb item has required properties
      component.breadcrumbDataset.forEach(item => {
        expect(item.label).toBeDefined();
        expect(item.url).toBeDefined();
        expect(typeof item.label).toBe('string');
        expect(typeof item.url).toBe('string');
      });

      // Validate specific breadcrumb items
      expect(component.breadcrumbDataset[0]).toEqual({label: 'Home', url:'/dashboard'});
      expect(component.breadcrumbDataset[1]).toEqual({label: 'Rules Engine', url:'/rules'});
      expect(component.breadcrumbDataset[2]).toEqual({label: 'Setup Rule Sub Type', url:'rules/rule-type'});
      expect(component.breadcrumbDataset[3]).toEqual({label: 'Add New Rule Sub Type', url:'/rules/rule-type/details'});
    });

    it('should have valid rule types structure', () => {
      expect(Array.isArray(component.ruleTypes)).toBeTrue();
      expect(component.ruleTypes.length).toBe(5);

      // Validate each rule type has required properties
      component.ruleTypes.forEach(ruleType => {
        expect(ruleType.name).toBeDefined();
        expect(ruleType.value).toBeDefined();
        expect(typeof ruleType.name).toBe('string');
        expect(typeof ruleType.value).toBe('string');
      });

      // Validate specific rule types
      const expectedRuleTypes = [
        {name: "Exclusion", value: "Exclusion"},
        {name: "Expiration/Lookback Rule", value: "Expiration/Lookback Rule"},
        {name: "On Hold", value: "On Hold"},
        {name: "No Recovery", value: "No Recovery"},
        {name: "Lag", value: "Lag"}
      ];
      expect(component.ruleTypes).toEqual(expectedRuleTypes);
    });

    it('should have valid RequiredFieldsData structure', () => {
      expect(Array.isArray(component.RequiredFieldsData)).toBeTrue();
      expect(component.RequiredFieldsData.length).toBe(6);

      // Validate each required field has required properties
      component.RequiredFieldsData.forEach(field => {
        expect(field.label).toBeDefined();
        expect(field.value).toBeDefined();
        expect(typeof field.label).toBe('string');
        expect(typeof field.value).toBe('string');
      });

      // Validate specific required fields
      expect(component.RequiredFieldsData[0]).toEqual({label: 'Rule Subtype', value: 'item_1'});
      expect(component.RequiredFieldsData[1]).toEqual({label: 'Calculation Fields', value: 'item_2'});
      expect(component.RequiredFieldsData[2]).toEqual({label: 'Lookback period', value: 'item_3'});
    });

    it('should have valid ruleSubTypeFormJSON structure', () => {
      expect(Array.isArray(component.ruleSubTypeFormJSON)).toBeTrue();
      expect(component.ruleSubTypeFormJSON.length).toBe(2);

      // Validate rule type select field
      const ruleTypeField = component.ruleSubTypeFormJSON[0];
      expect(ruleTypeField.type).toBe('select');
      expect(ruleTypeField.name).toBe('rule_type');
      expect(ruleTypeField.label).toBe('Rule Type');
      expect(ruleTypeField.required).toBeTrue();
      expect(ruleTypeField.options).toBe(component.ruleTypes);
      expect(ruleTypeField.optionName).toBe('name');
      expect(ruleTypeField.optionValue).toBe('value');
      expect(ruleTypeField.column).toBe(3);
      expect(ruleTypeField.closeOnSelect).toBeTrue();
      expect(ruleTypeField.id).toBe('rule_type');
      expect(ruleTypeField.placeholder).toBe('Choose Rule Type');

      // Validate rule subtype text field
      const ruleSubtypeField = component.ruleSubTypeFormJSON[1];
      expect(ruleSubtypeField.type).toBe('text');
      expect(ruleSubtypeField.name).toBe('ruleSubtype');
      expect(ruleSubtypeField.label).toBe('Rule Sub Type Name');
      expect(ruleSubtypeField.required).toBeTrue();
      expect(ruleSubtypeField.column).toBe(3);
      expect(ruleSubtypeField.id).toBe('ruleSubtype');
      expect(ruleSubtypeField.placeholder).toBe('Enter Rule Sub Type Name');
    });
  });

  describe('isNull Method', () => {
    it('should return true for null values', () => {
      expect(component.isNull(null)).toBeTrue();
    });

    it('should return true for empty string', () => {
      expect(component.isNull('')).toBeTrue();
    });

    it('should return false for non-empty string values', () => {
      expect(component.isNull('test')).toBeFalse();
      expect(component.isNull('0')).toBeFalse();
      expect(component.isNull(' ')).toBeFalse(); // space is not empty
    });

    it('should return false for numeric values', () => {
      expect(component.isNull(1)).toBeFalse();
      expect(component.isNull(-1)).toBeFalse();
      expect(component.isNull(0.5)).toBeFalse();
    });

    it('should return true for boolean false (due to == comparison)', () => {
      expect(component.isNull(false)).toBeTrue(); // false == "" is true in JavaScript
    });

    it('should return false for boolean true', () => {
      expect(component.isNull(true)).toBeFalse();
    });

    it('should return true for arrays and objects that are falsy in == comparison', () => {
      expect(component.isNull([])).toBeTrue(); // [] == "" is true in JavaScript
      expect(component.isNull({})).toBeFalse(); // {} == "" is false
      expect(component.isNull([1, 2, 3])).toBeFalse();
      expect(component.isNull({key: 'value'})).toBeFalse();
    });

    it('should handle edge cases based on == comparison', () => {
      expect(component.isNull(undefined)).toBeTrue(); // undefined == null is true
      expect(component.isNull(0)).toBeTrue(); // 0 == "" is true in JavaScript
    });
  });

  describe('onValueChanges Method', () => {
    beforeEach(() => {
      component.enableSumbitButton = false;
    });

    it('should enable submit button when both fields are filled with valid values', () => {
      component.onValueChanges({ current: { ruleSubtype: 'Test Subtype', rule_type: 'Exclusion' } });
      expect(component.enableSumbitButton).toBeTrue();
    });

    it('should enable submit button when both fields have non-empty values', () => {
      component.onValueChanges({ current: { ruleSubtype: 'A', rule_type: 'B' } });
      expect(component.enableSumbitButton).toBeTrue();
    });

    it('should disable submit button when ruleSubtype is empty', () => {
      component.enableSumbitButton = true;
      component.onValueChanges({ current: { ruleSubtype: '', rule_type: 'Exclusion' } });
      expect(component.enableSumbitButton).toBeFalse();
    });

    it('should disable submit button when ruleSubtype is null', () => {
      component.enableSumbitButton = true;
      component.onValueChanges({ current: { ruleSubtype: null, rule_type: 'Exclusion' } });
      expect(component.enableSumbitButton).toBeFalse();
    });

    it('should disable submit button when rule_type is empty', () => {
      component.enableSumbitButton = true;
      component.onValueChanges({ current: { ruleSubtype: 'Test Subtype', rule_type: '' } });
      expect(component.enableSumbitButton).toBeFalse();
    });

    it('should disable submit button when rule_type is null', () => {
      component.enableSumbitButton = true;
      component.onValueChanges({ current: { ruleSubtype: 'Test Subtype', rule_type: null } });
      expect(component.enableSumbitButton).toBeFalse();
    });

    it('should disable submit button when both fields are empty', () => {
      component.enableSumbitButton = true;
      component.onValueChanges({ current: { ruleSubtype: '', rule_type: '' } });
      expect(component.enableSumbitButton).toBeFalse();
    });

    it('should disable submit button when both fields are null', () => {
      component.enableSumbitButton = true;
      component.onValueChanges({ current: { ruleSubtype: null, rule_type: null } });
      expect(component.enableSumbitButton).toBeFalse();
    });

    it('should throw error when current object is missing', () => {
      expect(() => component.onValueChanges({})).toThrow();
    });

    it('should throw error when event object is null', () => {
      expect(() => component.onValueChanges(null)).toThrow();
    });

    it('should toggle submit button state correctly', () => {
      // Start disabled
      component.enableSumbitButton = false;

      // Enable with valid data
      component.onValueChanges({ current: { ruleSubtype: 'Test', rule_type: 'Exclusion' } });
      expect(component.enableSumbitButton).toBeTrue();

      // Disable with invalid data
      component.onValueChanges({ current: { ruleSubtype: '', rule_type: 'Exclusion' } });
      expect(component.enableSumbitButton).toBeFalse();

      // Enable again with valid data
      component.onValueChanges({ current: { ruleSubtype: 'Test2', rule_type: 'Lag' } });
      expect(component.enableSumbitButton).toBeTrue();
    });
  });

  describe('onSubmitRuleTypeDetails Method', () => {
    it('should not throw when called with valid event', () => {
      const mockEvent = { formData: { ruleSubtype: 'Test', rule_type: 'Exclusion' } };
      expect(() => component.onSubmitRuleTypeDetails(mockEvent)).not.toThrow();
    });

    it('should not throw when called with empty event', () => {
      expect(() => component.onSubmitRuleTypeDetails({})).not.toThrow();
    });

    it('should not throw when called with null event', () => {
      expect(() => component.onSubmitRuleTypeDetails(null)).not.toThrow();
    });

    it('should handle undefined event gracefully', () => {
      expect(() => component.onSubmitRuleTypeDetails(undefined)).not.toThrow();
    });
  });

  describe('onCancelDetails Method', () => {
    beforeEach(() => {
      mockRouter.navigate.calls.reset();
    });

    it('should navigate to rule-type page with correct path', () => {
      component.onCancelDetails({});
      expect(mockRouter.navigate).toHaveBeenCalledWith(['product-catalog/rules/rule-type']);
      expect(mockRouter.navigate).toHaveBeenCalledTimes(1);
    });

    it('should navigate regardless of event content', () => {
      component.onCancelDetails({ someData: 'test' });
      expect(mockRouter.navigate).toHaveBeenCalledWith(['product-catalog/rules/rule-type']);
    });

    it('should navigate when called with null event', () => {
      component.onCancelDetails(null);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['product-catalog/rules/rule-type']);
    });

    it('should navigate when called with undefined event', () => {
      component.onCancelDetails(undefined);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['product-catalog/rules/rule-type']);
    });
  });

  describe('breadscrumSelection Method', () => {
    beforeEach(() => {
      mockRouter.navigate.calls.reset();
    });

    it('should navigate to selected url from breadcrumb', () => {
      const event = { selected: { url: '/test-url' } };
      component.breadscrumSelection(event);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/test-url']);
      expect(mockRouter.navigate).toHaveBeenCalledTimes(1);
    });

    it('should navigate to dashboard when home breadcrumb is selected', () => {
      const event = { selected: { url: '/dashboard' } };
      component.breadscrumSelection(event);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should navigate to rules when rules breadcrumb is selected', () => {
      const event = { selected: { url: '/rules' } };
      component.breadscrumSelection(event);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules']);
    });

    it('should navigate to rule-type when setup breadcrumb is selected', () => {
      const event = { selected: { url: 'rules/rule-type' } };
      component.breadscrumSelection(event);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['rules/rule-type']);
    });

    it('should handle complex urls correctly', () => {
      const event = { selected: { url: '/complex/path/with/multiple/segments' } };
      component.breadscrumSelection(event);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/complex/path/with/multiple/segments']);
    });

    it('should handle empty url gracefully', () => {
      const event = { selected: { url: '' } };
      expect(() => component.breadscrumSelection(event)).not.toThrow();
      expect(mockRouter.navigate).toHaveBeenCalledWith(['']);
    });
  });

  describe('Component Properties Validation', () => {
    it('should have correct multiselect dropdown properties', () => {
      expect(component.label).toBe('Required Fields');
      expect(component.name).toBe('required_fields_dropdown');
      expect(component.type).toBe('multiselect');
    });

    it('should have correct view type', () => {
      expect(component.viewType).toBe('details');
    });

    it('should have correct required fields text', () => {
      expect(component.RequiredFieldsText).toBe('Required Fields');
    });
  });

  describe('Integration Tests', () => {
    it('should maintain consistent state during form interactions', () => {
      // Initial state
      expect(component.enableSumbitButton).toBeFalse();

      // Fill form partially
      component.onValueChanges({ current: { ruleSubtype: 'Test', rule_type: '' } });
      expect(component.enableSumbitButton).toBeFalse();

      // Complete form
      component.onValueChanges({ current: { ruleSubtype: 'Test', rule_type: 'Exclusion' } });
      expect(component.enableSumbitButton).toBeTrue();

      // Submit form (should not throw)
      expect(() => component.onSubmitRuleTypeDetails({ formData: { ruleSubtype: 'Test', rule_type: 'Exclusion' } })).not.toThrow();
    });

    it('should handle navigation scenarios correctly', () => {
      mockRouter.navigate.calls.reset();

      // Test breadcrumb navigation
      component.breadscrumSelection({ selected: { url: '/dashboard' } });
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);

      // Test cancel navigation
      component.onCancelDetails({});
      expect(mockRouter.navigate).toHaveBeenCalledWith(['product-catalog/rules/rule-type']);

      expect(mockRouter.navigate).toHaveBeenCalledTimes(2);
    });
  });
});
