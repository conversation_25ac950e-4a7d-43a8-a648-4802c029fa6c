
app-system .container, app-system .container-fluid, app-system .container-lg, app-system .container-md, app-system .container-sm, app-system .container-xl{
    width: 100%;
    padding-right: 5px; 
    padding-left: 5px;
    margin-right: auto;
    margin-left: auto;
}
app-system .pd-left-30{
    padding-left: 30px;
    padding-top: 57px;

}


app-system .btn {
    display: inline-block;

    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    border-color: #794cff;;
}



app-system .pd-righ-20{
    padding-right: 20px;
    padding-left: 5px;
}

app-system .fa-list, app-system .fa-chevron-circle-left{
    font-size: 30px;
    color: #5009B5;
}

/*system dashboard Table action menu dropdown*/
app-system .dropdown {
    background-color: #FFFFFF !important;
    padding: 2px;
    outline: none;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    background: #ffffff;
    box-shadow: 0px 0px 20px rgb(0 0 0 / 40%);
    border-radius: 10px;
}



app-system .container, app-system .container-fluid, app-system .container-lg, app-system .container-md, app-system .container-sm, app-system .container-xl{
    width: 100%;
    padding-right: 5px; 
    padding-left: 5px;
    margin-right: auto;
    margin-left: auto;
}
app-system .table-title {
    float: left;
    
    font-size: 24px;
    padding: 15px 0px 0px 30px;
}
app-system .pad-left-80 {
    margin-top: -3%;
}

app-system .form-row>.col, .form-row>[class*=col-] {
    padding-right: 5px;
    padding-left: 14px;
    padding-top:9px;
}



app-system .btn.rule-dashboard {
    color: white;
    font-weight:350;
}
app-system .btn.focus.rule-dashboard, app-system .btn:focus.rule-dashboard {
    outline: 0;
    box-shadow: 0 0 0 0rem;
}
app-system .btn.rule-dashboard-big {
    padding: 0.075rem 0.1rem !important;
    color: white;
    font-weight:200;
}
app-system .btn.focus.rule-dashboard-big, app-system .btn:focus.rule-dashboard-big {
    outline: 0;
    box-shadow: 0 0 0 0rem;
}
app-system .dropdown-container{
    border: none;
    background: transparent;
    margin-top: -9px;
}



  app-system  .btn-span {
    float: right;
    padding: 0px 15px 2px 30px;
  }


  app-system .fa-lock:before {
    content: "\f023";
    color: red;
}
/* MarketPlace Pagination */


app-system marketplace-pagination .contactWrapper {
    height: 350px;
    overflow-y: scroll;

}




app-system .pd-30{
    padding-bottom: 30px;
}
app-system .dashbord-title {
    float: left;
    height: 41px;
    font-weight: bold;
    font-size: 28px;
    line-height: 41px;
    color: #000000;
    padding: 25px 0px 0px 30px;
}


.clientText{
    margin-top: 20px;
}



app-system body {
    background: #d1d5db
}


app-system .form {
    position: relative
}


app-system p {
    margin-top: 11px;
    margin-bottom: 0rem;
}

app-system .csearch{
    
        padding-right: 20px;
        float: right;
        padding-top: 12px;
    
}

app-system .hidden {display:none;}




app-system .pointer
{
    pointer-events: none;
}
app-system .font{
    font: icon;
    font-size: 16px;
    color: #4f00bf;
}

app-system .drpDwnFont{
    font: icon;
    font-size: 16px;
    color: #000000;
    padding-top:5px;
}

app-system .pad-left-29 {
    position: absolute;
    top: 8px;
    left: 10px;
}


app-system .font-size-13 {
    font-size: 13px;
}
app-system .pad-top-22 {
    padding-top: 22px;
}

app-system marketplace-pagination .h3, app-system marketplace-pagination h3{
    font-size: 1.15rem !important;
}


app-system #dropdownBtn {
    width: 250px;
    height: 130px;
    background-color: #ccc;
    position: absolute;
    z-index: 100;
    padding: 10px 10px 10px 5px;
}
app-system .container-fluid .row{
    padding-bottom: 20px;
}

app-system .bgcolor{
    color: #794cff;
    font-size: 16px;
    font-weight: 600;
}


app-system .dottedLine {
    border: none;
    border-top: 1px dotted #794cff;
    color: #794cff;
    background-color: #794cff;
    margin-top: 5px;
    margin-bottom: 5px;
  }

  app-system .readOnly{
    font-size: 12px;
    color: rgb(99, 99, 99);
    
    position: absolute;
    top: 208px;
    left: 59px;
  }

  app-system .addReadOnly{
    font-size: 12px;
    color: rgb(99, 99, 99);
    
    position: absolute;
    top: 367px;
    left: 40px;
  }

  app-system .editIcon{
    position: absolute;
    top: 9.3%;
    left: 88%;
    color: #794cff;
}
  app-system .editIconDesc{
    position: absolute;
    top: 23%;
    left: 88%;
    color: #794cff;
}

app-system .addEditIcon{
    position: absolute;
    top: 48.5%;
    left: 38%;
    color: #794cff;
}
  app-system .addEditIconDesc{
    position: absolute;
    top: 72.2%;
    left: 38%;
    color: #794cff;
}

app-system .addSys{
    display: flex;
}


app-system .custom-control-input:checked~.custom-control-label::before {
    border-color: #794cff !important;
    background-color: #794cff !important;
}

app-system .custom-control-input:focus:not(:checked)~.custom-control-label::before {
    border-color: #794cff !important;
}

app-system .custom-control-input:focus~.custom-control-label::before {
    box-shadow: 0 0 0 0.1rem #cbb9ff !important;
}

app-system .custom-control-label::before {
    border: #794cff solid 1px !important;
}

app-system .custom-switch .custom-control-label::after {
    background-color: #794cff;
}

app-system .fa-check:before {
    color: #794cff;
}

app-system .drpdwnColor{
    background:#ffc0cb; 
    color:#ffc0cb;
}

app-system input[type=checkbox]{
    accent-color: #5009b5;
}

app-system .check-svg path{
    fill: #794cff;
}
app-system .table-controls_features {
    padding-top: 5px;
}
