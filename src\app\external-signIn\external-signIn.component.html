<div class="d-flex justify-content-center backdrop" *ngIf="isLoading">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
    </div>
</div>
<div class="body-content">
    <div class="page-header-container">
        <img src="./assets/images/logo.png">
        <span class="seperator"></span>
        <div class="page-header-text">Payment Integrity</div>
    </div>
    <div *ngIf="!isOtpScreen" class="card elevated-card">
        <div>
            <h3>Sign in to your account</h3>
        </div>
        <marketplace-dynamic-form [formJSON]="signInFormJSON" [isSubmitNeeded]="true" [buttonLabel]="btnLabel"
        [submitTrailSVG]="loginSVG"
            [isSubmitted]="isFormSubmitted" (onSubmitForm)="onSignIn($event)">
        </marketplace-dynamic-form>
        <div class="red-font">{{errorMsg}}</div>
        <div class="row">
            <span class="col-4"><a routerLink="forgotuserid">Forgot User Id?</a></span>
            <span class="col-4"><a routerLink="forgotpassword">Forgot Password?</a></span>
            <!-- <span class="col-4"><a routerLink="/changePassword" [queryParams]="{FromSignin: 'yes'}">Change
                    Password?</a></span> -->
        </div>
        <div class="register">
        	Don't have an Account? <a routerLink="register">&nbsp; Create One</a>
        </div>
    </div>
    <div *ngIf="isOtpScreen" class="card elevated-card">
        <div>
            <h3>Verify OTP</h3>
        </div>

        <marketplace-dynamic-form [formJSON]="otpFormJSON" [isSubmitNeeded]="false"
            (onValueChanges)="otpValueChanges($event)">
        </marketplace-dynamic-form>
        <span class="red-font">{{errorMsg}}</span>
        <div class="btn-holder">
            <marketplace-button [label]="'Submit'" [enabled]="isOtpEntered" name="primary" (onclick)="validateOTP()">
            </marketplace-button>
        </div>
        <div class="row">
            <span><a [routerLink]="" (click)="onResendOtp()">Resend OTP</a></span>
        </div>
    </div>
    <div class="page-footer">
        Need Help? 
        <a href="{{contactUsUrl}}" target="_blank">&nbsp; Email Support</a>
    </div>
</div>