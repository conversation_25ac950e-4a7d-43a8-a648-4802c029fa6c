<div class="fixed-nav bg-gray">
    <div class="content-wrapper card">
        <div class="row">
            <div class="col-md-3">
                <div class="client-name">Client Name : <b>{{clientName}}</b></div>
            </div>
            <div class="col-md-8">
                <marketplace-dynamic-form #fileExchange [isSubmitNeeded]="false"
                    [formJSON]="conceptAndExecutionFromJsonConfig" (onValueChanges)="formValue($event)">
                </marketplace-dynamic-form>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8">
                <div class="pd-left-5 mg-top-10">
                    <span class="pd-righ-25 title-table-span">Recent File List </span>
                    <span class="pd-righ-25 span-lable-title">{{recentFileListBool ? "No Data Available" : "Total Files
                        Sent :"}} <b>{{recentFileListBool ? "" :totalFilesSent}}</b></span>
                    <span class="pd-righ-25 span-lable-title">{{recentFileListBool ? "" : "Total Insights Sent :"}}
                        <b>{{recentFileListBool ? "" :totalInsightsSent}}</b></span>
                </div>
                <marketplace-table [id]="'clntTable'" [dataset]="filehistoryData" [rowHeight]="ruleDashbordTableRowhg"
                    [headerRowHeight]="ruleDashbordTableHeaderhg" [columnDefinitions]="filehistoryColConfig"
                    [isSingleSelectionRequired]="true" (onRowSelection)="recentFileListTblsel($event)" [pagination]="10"
                    (onCellClick)="rendererTableClicked($event)" *ngIf="filehistoryColConfig">
                </marketplace-table>
            </div>
            <div class="col-md-4 timeline-title">
                <span>{{this.filename ? this.filename : "File Name"}}</span>
                <div>
                    <marketplace-timeline [dataset]="timeLineDataset" [type]="'vertical'"></marketplace-timeline>
                </div>
            </div>
        </div>
    </div>

    <div class="content-wrapper mg-top-5 card">
        <marketplace-accordion [openAll]="openAll" [openPanel]="panelIndex">
            <marketplace-panel [header]="'File Report Details'">
                <span *ngIf="!showFileRptDetails">No Data Available</span>

                <div *ngIf="showFileRptDetails" class="row tabaccordion">
                    <div class="col-md-4">
                        <div class="panel panel-default">
                            <div class="panel-heading" role="tab" id="headingOne">
                                <h4 class="panel-title">
                                    <a aria-expanded="true" data-toggle="collapse" data-parent="#accordion"
                                        href="#collapseOne" aria-controls="collapseOne">
                                        <div class="file-name">{{filename}}</div>
                                        <div class="file-name">{{filedate}}</div>
                                    </a>
                                </h4>
                            </div>
                            <div id="collapseOne" class="panel-collapse collapse show" role="tabpanel"
                                aria-labelledby="headingOne">
                                <div class="circle">{{selectedFileReportDetails?.errorCount == null ? "" :
                                    selectedFileReportDetails?.errorCount }}/{{selectedFileReportDetails?.sentCount ==
                                    null
                                    ?
                                    "" : selectedFileReportDetails?.sentCount }} <p class="circle-text">Total Insight
                                        Missing
                                    </p>
                                </div>
                            </div>
                        </div>
                            <div class="alert-card-box">Missing</div>
                            <div class="alert-card-box-subtitle">These claims are missing from the client
                                acknowledgement
                                file</div>
                            <div class="mt-4 mb-2 widthForMissingBtns">
                                <marketplace-button class="resendBtn" [label]="'Resend'" [type]="'secondary'" [size]="'small'"
                                    [name]="'secondary'" (onclick)="insightViewDetailsPopUp('Missing')">
                                </marketplace-button>

                                <marketplace-button [label]="'View Insight'" [type]="'primary'" [size]="'small'"
                                    [name]="'primary'" (onclick)="insightViewDetailsPopUp('Missing')">
                                </marketplace-button>


                            </div>
                    </div>
                    <div class="col-md-4">
                        <div class="panel panel-default">
                            <div class="panel-heading" role="tab" id="headingTwo">
                                <h4 class="panel-title">
                                    <a aria-expanded="true" data-toggle="collapse" data-parent="#accordion"
                                        href="#collapseTwo" aria-controls="collapseTwo">
                                        <div class="file-name">{{this.filename}}</div>
                                        <div class="file-name">{{this.filedate}}</div>
                                    </a>
                                </h4>
                            </div>
                            <div id="collapseTwo" class="panel-collapse collapse show" role="tabpanel"
                                aria-labelledby="headingTwo">
                                <div class="circle">{{selectedFileReportDetails?.scssCount == null ? "" :
                                    selectedFileReportDetails?.scssCount }}/{{selectedFileReportDetails?.sentCount ==
                                    null ?
                                    "" : selectedFileReportDetails?.sentCount }} <p class="circle-text">Total Insight
                                        Accepted
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="container-alert">
                            <div class="alert-card-box">Accepted</div>
                            <div class="alert-card-box-subtitle">These claims were accepted by client</div>
                            <div class="notification-btn">
                                <marketplace-button [label]="'View Insight'" [type]="'primary'" [size]="'small'"
                                    [name]="'primary'" (onclick)="insightViewDetailsPopUp('Accepted')">
                                </marketplace-button>

                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="panel panel-default">
                            <div class="panel-heading" role="tab" id="headingThree">
                                <h4 class="panel-title">
                                    <a aria-expanded="true" data-toggle="collapse" data-parent="#accordion"
                                        href="#collapseThree" aria-controls="collapseThree">
                                        <div class="file-name">{{this.filename}}</div>
                                        <div class="file-name">{{this.filedate }}</div>
                                    </a>
                                </h4>
                            </div>
                            <div id="collapseThree" class="panel-collapse collapse show" role="tabpanel"
                                aria-labelledby="headingThree">
                                <div class="circle">{{selectedFileReportDetails?.rejectedCount == null ?
                                    "" : selectedFileReportDetails?.rejectedCount
                                    }}/{{selectedFileReportDetails?.sentCount == null ? "" :
                                    selectedFileReportDetails?.sentCount }} <p class="circle-text">Total Insight
                                        Rejected</p>
                                </div>
                            </div>
                        </div>
                        <div class="container-alert">
                            <div class="alert-card-box">Rejected</div>
                            <div class="alert-card-box-subtitle">These claims were rejected by client</div>
                            <div class="notification-btn">
                                <marketplace-button [label]="'View Insight'" [type]="'primary'" [size]="'small'"
                                    [name]="'primary'" (onclick)="insightViewDetailsPopUp('Rejected')">
                                </marketplace-button>

                            </div>
                        </div>
                    </div>
                    <div id="accordion" class="fouthCard">
                    </div>
                </div>
            </marketplace-panel>
        </marketplace-accordion>
    </div>


    <marketplace-popup [open]="openHTMLModal">
        <div mpui-modal-header>
            <h4>{{fileStatus}} Claims</h4>
        </div>
        <div mpui-modal-body><span style="text-align: center;"></span>
            <marketplace-table [id]="'bulk-validation-id'" *ngIf="tblFlag"
                [columnDefinitions]="rejectedFileReportsTblColumnConfig" [dataset]="fileReportTblDataset"
                [redraw]="fileDetailsTableRedraw" [isXScrollNeeded]="true">
            </marketplace-table>
        </div>
        <div mpui-modal-footer>
            <button class="btn btn-secondary btnfooter" (click)="closeModelPopup()">Close</button>
        </div>
    </marketplace-popup>

    <div class="content-wrapper mg-top-5 card">
        <div class="row">
            <div>
                <div class="section-heading viewInDtls">View Insight Details</div>
                <span id="vwInDtlsNoData" [ngClass]="{'errMsgBold': errMsgBold}"
                    *ngIf="insightErrMsgBool">{{insightErrorMsg}}</span>
            </div>
            <div *ngIf="showChart" class="col-md-12">
                <div class="row">
                    <div class="col-md-8 pl-5 pt-3">

                        <marketplace-button *ngIf="isYearWise" [label]="'Back To Year'" [type]="'primary'"
                                    [name]="'Back To Year'" (onclick)="backToYear()">
                                </marketplace-button>
                    </div>
                    <h3 class="monthInBarChart" *ngIf="isYearWise">Month - {{selectedMonth}}</h3>
                    <div class="col-md-2 yearDrpDwn">
                        <marketplace-select [label]="insightYearDropdownLabel" [name]="drpdwnName"
                            [dataset]="insightYearDataset" [(ngModel)]="dropdownSelectedValue" [type]="drptype"
                            (onSelection)="insightYearChange($event)">
                        </marketplace-select>
                    </div>
                </div>
                <marketplace-charts [chartType]="chartType" [chartProperties]="properties" *ngIf="showOnlyChart"
                    [dataset]="barChartDataset" [redraw]="redrawInsightChart" (onPointClick)="onBarClick($event)">
                </marketplace-charts>
            </div>
        </div>
    </div>
</div>