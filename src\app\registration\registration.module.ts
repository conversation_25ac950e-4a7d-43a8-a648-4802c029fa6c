import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { RegistrationComponent } from './registration.component';
import { RegistraionRoutingModule } from './registration-routing.module';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { MPUIInputModule } from 'marketplace-input';
import { MPUISelectModule } from 'marketplace-select';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { MPUIButtonModule } from 'marketplace-button';
import { CommonHeader } from 'src/app/_helpers/commonheader.interceptor';
import { ToastService } from 'src/app/_services/toast.service';
import { MPUINotificationModule } from 'marketplace-notification';
import { AuthService } from 'src/app/_services/authentication.services';
//import { RegistrationSuccessComponent } from './registration-success/registration-success.component';



@NgModule({
  
  imports: [
    CommonModule,
    RegistrationComponent,
    FormsModule,
    MPUIDynamicFormModule,
    MPUIInputModule,
    MPUISelectModule,
    MPUIButtonModule,
    MPUINotificationModule,
    RegistraionRoutingModule
  ],
  providers:[ ToastService,
    {provide: HTTP_INTERCEPTORS, useClass: CommonHeader, multi: true}
  ],
  
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class RegistrationModule { }