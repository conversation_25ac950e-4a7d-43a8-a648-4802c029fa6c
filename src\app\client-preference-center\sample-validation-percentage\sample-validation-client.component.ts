import { Component, OnInit, ViewEncapsulation, Input, Output, EventEmitter } from '@angular/core';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ActivatedRoute } from '@angular/router';
import { constants, sampleValidationGBDAnthemJSON, sampleValidationCSBDAnthemJSON, sampleValidationJSON, columnConfigs } from './utils/client-preference-sample-validation-util';
import { SampleValidationClientService } from './_services/sample-validation-client.service';
import { ToastService } from 'src/app/_services/toast.service';
import moment from 'moment';
import { AuthService } from 'src/app/_services/authentication.services';

@Component({
  selector: 'app-sample-validation-client',
  templateUrl: './sample-validation-client.component.html',
  styleUrls: ['./sample-validation-client.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SampleValidationClientComponent implements OnInit {

  @Input() isReadOnly: boolean = false;
  clientId: any;
  updateButtonReady: boolean = false;
  lastUpdatedBy: any = "N/A";
  showPastUpdatesPopUp: boolean = false;
  isClientAnthem: boolean = false;
  sampleValJSON: any = sampleValidationJSON;
  sampleValGBDAnthemJSON: any = sampleValidationGBDAnthemJSON;
  sampleValCSBDAnthemJSON: any = sampleValidationCSBDAnthemJSON;
  currentGBDPercentage: any = "";
  currentCSBDPercentage: any = "";
  clntSmplValdtnPrcntgRecordList: any = [];
  isPastUpdateTblReady: boolean = false;
  pastUpdatesDataSet: any;
  columnConfigs: any = columnConfigs;
  userNames: any[] = [];
  isScreenReady: boolean = false;

  constructor(
    private clientApiService: ClientApiService,
    private route: ActivatedRoute,
    private authService: AuthService,
    private clientSampleValidationService: SampleValidationClientService,
    public alertService: ToastService) { }

  ngOnInit(): void {
    this.clientId = this.route.snapshot.paramMap.get('clientId');
    this.isClientAnthem = this.clientId == constants.ANTHEM_CLIENT_ID;
    this.clientSampleValidationService.getUserNameForClient().subscribe(response => {
      this.userNames = response;
      if (this.isClientAnthem) {
        this.columnConfigs.colDefs.find(c => c.field == "businessDiv").visible = true;
        this.loadAnthemInputData();
      }
      else {
        this.columnConfigs.colDefs.find(c => c.field == "businessDiv").visible = false;
        this.getClientPercentage();
      }
    });
  }

  /**
  * Inputs form data if client is Anthem
  */
  loadAnthemInputData() {
    const updateBusinessDivision = (jsonArray: any[], division: string) => {
      const item = jsonArray.find(x => x.name == constants.BUSINESS_DIVISION);
      if (item) {
        item.value = division;
        item.disabled = true;
      }
    };
    updateBusinessDivision(this.sampleValGBDAnthemJSON, constants.GBD);
    updateBusinessDivision(this.sampleValCSBDAnthemJSON, constants.CSBD);
    this.getClientPercentage();
  }

  /**
   * Method to get client percentage from api.
   */
  getClientPercentage() {
    this.clientSampleValidationService.getClientPercentageByClientId(this.clientId).subscribe(response => {
      const defaultPercentage = 100;

      // Helper function to set percentage values 
      const setPercentageValues = (gbdPercentage: number, csbdPercentage: number) => {
        this.currentGBDPercentage = gbdPercentage;
        this.currentCSBDPercentage = csbdPercentage;
        this.sampleValGBDAnthemJSON.find(x => x.name === constants.PERCENTAGE).value = gbdPercentage;
        this.sampleValCSBDAnthemJSON.find(x => x.name === constants.PERCENTAGE).value = csbdPercentage;
      };
      if (response.length === 0) {
        // No records found 
        setPercentageValues(defaultPercentage, defaultPercentage);
        this.sampleValJSON.find(x => x.name === constants.PERCENTAGE).value = defaultPercentage;
        this.lastUpdatedBy = 'N/A';
      }
      else {
        if (this.isClientAnthem) {
          const gbdRecord = response.find(obj => obj.businessDiv === constants.GBD);
          const csbdRecord = response.find(obj => obj.businessDiv === constants.CSBD);
          setPercentageValues(gbdRecord?.percentage ?? defaultPercentage, csbdRecord?.percentage ?? defaultPercentage);
        } else {
          const nonAnthemRecord = response.find(obj => obj.businessDiv == null);
          const nonAnthemPercentage = nonAnthemRecord?.percentage ?? defaultPercentage;
          this.sampleValJSON.find(x => x.name === constants.PERCENTAGE).value = nonAnthemPercentage;
        }
        // Set last updated by based on the most recent record
        const mostRecentUpdate = response.reduce((prev, current) =>
          new Date(prev.lastUpdatedDate) > new Date(current.lastUpdatedDate) ? prev : current);
        this.lastUpdatedBy = mostRecentUpdate?.lastUpdatedByUserId ?
          this.userNames.find(c => c.userId === mostRecentUpdate.lastUpdatedByUserId)?.userName : 'N/A';
      }
      this.isScreenReady = true;
    },
      error => {
        this.alertService.setErrorNotification(
          { notificationBody: constants.GET_CLIENT_PERCENTAGE_ERROR });
      });
  }

  /**
  * Triggered on GBD form change
  */
  onGBDPercentageChange(event) {
    this.currentGBDPercentage = event.current.percentage;
    if (event.current.percentage != event.previous.percentage)
      this._validateAnthemInputs();
    this.onPercentageChange(event,constants.GBD);
  }

  /**
  * Triggered on CSBD form change
  */
  onCSBDPercentageChange(event) {
    this.currentCSBDPercentage = event.current.percentage;
    if (event.current.percentage != event.previous.percentage)
      this._validateAnthemInputs();
    this.onPercentageChange(event,constants.CSBD);
  }

  /**
 * Method to create payload on form changes
 */
  onPercentageChange(event, businessDiv?: string) {
    if (event.current.businessDiv == "") {
      event.current.businessDiv = businessDiv      
      this.clntSmplValdtnPrcntgRecordList.push(event.current);
    }
    else {
      const existingItem = this.clntSmplValdtnPrcntgRecordList.find((item) =>
        item.businessDiv === event.current.businessDiv
      );
      if (existingItem)
        existingItem.percentage = event.current.percentage;
      else
        this.clntSmplValdtnPrcntgRecordList.push(event.current);
    }
  }

  /**
  * Triggered on non-Anthem form change
  */
  onNonAnthemPercentageChange(event) {
    let currPercentage = Number(event.current.percentage);
    this.clntSmplValdtnPrcntgRecordList = [];
    this.clntSmplValdtnPrcntgRecordList.push(event.current);
    if (event.current.percentage != "" && currPercentage != null && (currPercentage != event.previous.percentage)) {
      this.updateButtonReady = this._validatePercentage(currPercentage);
    }
  }

  /**
  * Method to validate the Anthem inputs
  */
  _validateAnthemInputs() {
    if ((this.currentCSBDPercentage != "" && this.currentCSBDPercentage != null) && (this.currentGBDPercentage != "" && this.currentGBDPercentage != null)) {
      this.updateButtonReady = this._validatePercentage(this.currentCSBDPercentage) && this._validatePercentage(this.currentGBDPercentage);
    }
  }

  /**
  * Method to validate percentage constraints
  */
  _validatePercentage(percentage: any) {
    const num = Number(percentage);
    return num >= 0 && num <= 100 && Number.isInteger(num);
  }

  /**
  * Triggers on click of update button
  */
  clickUpdateButton(event) {
    let payload =
    {
      "clientId": this.clientId,
      "clntSmplValdtnPrcntgRecordListDTO": this.clntSmplValdtnPrcntgRecordList
    }
    this.clientSampleValidationService.updateClientPercentageByClientId(payload).subscribe(response => {
      if (response.responseCode == 200) {
        this.alertService.setSuccessNotification({
          notificationBody: constants.VALIDATION_UPDATE_MSG
        })
        this.updateButtonReady = false;
        this.isScreenReady = false;
        this.getClientPercentage();
      }
    }, (error) => {
      this.alertService.setErrorNotification({
        notificationBody: constants.ERROR
      })
    });
  }


  /**
  * Triggers on click of Past Updates link which opens the pop up
  */
  openPastUpdates() {
    this.clientSampleValidationService.getPastUpdatesPercentagesByClientId(this.clientId).subscribe(pastUpdate => {
      this.isPastUpdateTblReady = false;
      this.pastUpdatesDataSet = pastUpdate.map(item => {
        return {
          ...item,
          pctag: `${item.pctag}%`,
          lastUpdatedDateTime: moment(item.lastUpdatedDateTime).format('YYYY-MM-DD'),
          lastUpdatedUserId: this.userNames.find(c => c.userId.toUpperCase() == item.lastUpdatedUserId.toUpperCase())?.userName ?? item.lastUpdatedUserId
        };
      });
      this.isPastUpdateTblReady = true;
    });
    this.showPastUpdatesPopUp = true;
  }

  /**
  * Triggers on click out of Past Updates
  */
  closeViewUpdatesPopup() {
    this.showPastUpdatesPopUp = false;
  }
}
