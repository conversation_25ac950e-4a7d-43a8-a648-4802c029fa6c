import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, ActivatedRoute } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CookieService } from 'ngx-cookie-service';
import { of, throwError } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

import { CopyComponent } from './copy.component';
import { RulesApiService } from '../_services/rules-api.service';
import { UtilitiesService } from '../../_services/utilities.service';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ProductApiService } from 'src/app/_services/product-api.service';
import { ToastService } from 'src/app/_services/toast.service';
import { AuthService } from 'src/app/_services/authentication.services';
import { BusinessDivisionService } from 'src/app/_services/business-division.service';


describe('CopyComponent', () => {
  let component: CopyComponent;
  let fixture: ComponentFixture<CopyComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: jasmine.SpyObj<ActivatedRoute>;
  let mockRulesApiService: jasmine.SpyObj<RulesApiService>;
  let mockUtilitiesService: jasmine.SpyObj<UtilitiesService>;
  let mockCookieService: jasmine.SpyObj<CookieService>;
  let mockUserManagementApiService: jasmine.SpyObj<UserManagementApiService>;
  let mockClientApiService: jasmine.SpyObj<ClientApiService>;
  let mockProductApiService: jasmine.SpyObj<ProductApiService>;
  let mockToastService: jasmine.SpyObj<ToastService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockBusinessDivisionService: jasmine.SpyObj<BusinessDivisionService>;


  const mockRuleData = {
    status: { code: 200 },
    result: {
      metadata: {
        rules: [{
          id: 123,
          rule_name: 'Test Rule',
          rule_type: 'Exclusion',
          status: 'Active',
          created_by: 'Test User',
          created_ts: '2023-01-01'
        }]
      }
    }
  };

  // Standardized master data response for all tests
  const mockMasterDataResponse = {
    status: { code: 200 },
    result: {
      fields: {
        rule_type: [
          { 'Exclusion': { rule_sub_type: ['Test Sub Type'] } },
          { 'Inclusion': { rule_sub_type: ['Test Sub Type 2'] } }
        ],
        letter_type: ['Test Letter Type'],
        calculation_fields: ['Test Field'],
        lookup_dates: ['30 days'],
        lagging_period: ['7 days'],
        query_fields: [
          {
            field_type: 'dropdown',
            value: 'test_field',
            name: 'Test Field',
            type: 'string',
            options: [{ id: 1, name: 'Option 1' }]
          }
        ]
      },
      clients: [{ clientId: 1, clientName: 'Test Client 1' }],
      concepts: [{ conceptId: 1, conceptName: 'Test Concept 1' }],
      products: []
    }
  };

  const mockJsonFileResponse = {
    sqlStructure: [
      { name: 'group1', groupControls: [] },
      {
        name: 'group2',
        groupControls: [
          { name: 'PRODUCT', visible: true },
          { name: 'CONCEPT_ID', visible: true, options: [] }
        ]
      }
    ],
    customSQL: {}
  };

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    routerSpy.url = '/rules/copy/123'; // Mock the URL property
    const activatedRouteSpy = jasmine.createSpyObj('ActivatedRoute', [], {
      params: of({ id: '123' }),
      queryParams: of({ level: 'Global' })
    });
    const rulesApiServiceSpy = jasmine.createSpyObj('RulesApiService', [
      'getListOfRules', 'saveRule', 'updateRule', 'getFieldsForRuleType', 'getInventoryStatusData',
      'getAllViewEditRuleAPIs', 'addFilesToRules', 'getColumnConfigJsonDuplicate'
    ]);

    // Setup default mock responses before component creation
    rulesApiServiceSpy.getListOfRules.and.returnValue(of({ status: { code: 200 }, result: { metadata: { rules: [] } } }));
    rulesApiServiceSpy.getInventoryStatusData.and.returnValue(of({ status: { code: 200 }, result: [] }));
    rulesApiServiceSpy.getAllViewEditRuleAPIs.and.returnValue(of([
      { status: { code: 200 }, result: { fields: {
        rule_type: [
          { 'Exclusion': { value: 'Exclusion', rule_sub_type: ['Test Sub Type'] } },
          { 'Inclusion': { value: 'Inclusion', rule_sub_type: ['Test Sub Type 2'] } }
        ],
        query_fields: [
          { field_type: 'dropdown', value: 'test_field', name: 'Test Field', options: [] }
        ]
      } } },
      { status: { code: 200 }, result: { metadata: { rules: [{}] } } }
    ]));
    const utilitiesServiceSpy = jasmine.createSpyObj('UtilitiesService', ['formatDate', 'getDbgDateFormat']);
    const cookieServiceSpy = jasmine.createSpyObj('CookieService', ['get', 'set']);
    cookieServiceSpy.get.and.returnValue('TEST_USER');
    const userManagementApiServiceSpy = jasmine.createSpyObj('UserManagementApiService', ['getUsers']);
    const clientApiServiceSpy = jasmine.createSpyObj('ClientApiService', ['getClients', 'getAllClientsInPreferenceCenter']);
    const productApiServiceSpy = jasmine.createSpyObj('ProductApiService', ['getProducts', 'getProductConceptsId']);
    const toastServiceSpy = jasmine.createSpyObj('ToastService', ['setSuccessNotification', 'setErrorNotification']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['isWriteOnly']);
    const businessDivisionServiceSpy = jasmine.createSpyObj('BusinessDivisionService', ['getBusinessDivision']);

    // Add missing getFutureDate mock for dateService
    utilitiesServiceSpy.getFutureDate = jasmine.createSpy('getFutureDate').and.returnValue('2023-01-02');
    // Add missing getAssetsJson mock for RulesApiService
    rulesApiServiceSpy.getAssetsJson = jasmine.createSpy('getAssetsJson').and.returnValue(of({ sqlStructure: [], customSQL: [] }));
    // Add missing getFileDetailsOfRules mock for RulesApiService
    rulesApiServiceSpy.getFileDetailsOfRules = jasmine.createSpy('getFileDetailsOfRules').and.returnValue(of({ status: { code: 200 }, result: { files: [] } }));
    // Add missing getColumnConfigJsonDuplicate mock for RulesApiService
    rulesApiServiceSpy.getColumnConfigJsonDuplicate = jasmine.createSpy('getColumnConfigJsonDuplicate').and.returnValue(of({ switches: { enableSorting: true }, colDefs: [] }));

    await TestBed.configureTestingModule({
      declarations: [CopyComponent],
      imports: [HttpClientTestingModule],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: activatedRouteSpy },
        { provide: RulesApiService, useValue: rulesApiServiceSpy },
        { provide: UtilitiesService, useValue: utilitiesServiceSpy },
        { provide: CookieService, useValue: cookieServiceSpy },
        { provide: UserManagementApiService, useValue: userManagementApiServiceSpy },
        { provide: ClientApiService, useValue: clientApiServiceSpy },
        { provide: ProductApiService, useValue: productApiServiceSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: BusinessDivisionService, useValue: businessDivisionServiceSpy },

      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(CopyComponent);
    component = fixture.componentInstance;

    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockActivatedRoute = TestBed.inject(ActivatedRoute) as jasmine.SpyObj<ActivatedRoute>;
    mockRulesApiService = TestBed.inject(RulesApiService) as jasmine.SpyObj<RulesApiService>;
    mockUtilitiesService = TestBed.inject(UtilitiesService) as jasmine.SpyObj<UtilitiesService>;
    mockCookieService = TestBed.inject(CookieService) as jasmine.SpyObj<CookieService>;
    mockUserManagementApiService = TestBed.inject(UserManagementApiService) as jasmine.SpyObj<UserManagementApiService>;
    mockClientApiService = TestBed.inject(ClientApiService) as jasmine.SpyObj<ClientApiService>;
    mockProductApiService = TestBed.inject(ProductApiService) as jasmine.SpyObj<ProductApiService>;
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockBusinessDivisionService = TestBed.inject(BusinessDivisionService) as jasmine.SpyObj<BusinessDivisionService>;

  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CopyComponent);
    component = fixture.componentInstance;

    // Initialize component properties to prevent undefined errors
    component.postUploadDataJson = { commentsInUpload: 'test comment' };
    component.fileUploadEditJSON = {};
    component.updatedRuleId = '';
    component.userId = 'test_user';
    component.levelIndicator = 'Global';
    component.isLoading = false;
    component.openImpactReportPopup = false;

    // Setup default mock responses
    mockRulesApiService.getListOfRules.and.returnValue(of({ status: { code: 200 }, result: { metadata: { rules: [] } } }));
    mockRulesApiService.getInventoryStatusData.and.returnValue(of({ status: { code: 200 }, result: [] }));
    mockRulesApiService.getAssetsJson.and.returnValue(of({
      status: { code: 200 },
      sqlStructure: [
        { id: 'sqlType', value: 'qb', groupControls: [{ name: 'test', visible: true }] },
        {
          id: 'queryBuilder',
          groupControls: [
            { name: 'PRODUCT', visible: true, options: [] },
            { name: 'CONCEPT_ID', visible: true, options: [] },
            { name: 'rulesLevel', visible: true, selectedVal: '', options: [] }
          ]
        }
      ],
      customSQL: []
    }));
    mockRulesApiService.getFileDetailsOfRules.and.returnValue(of({ status: { code: 200 }, result: { files: [] } }));
    mockRulesApiService.getColumnConfigJsonDuplicate.and.returnValue(of({ status: { code: 200 }, switches: { enableSorting: true }, colDefs: [] }));
    mockRulesApiService.getAllViewEditRuleAPIs.and.returnValue(of([
      { status: { code: 200 }, result: { fields: {
        rule_type: [
          { 'Exclusion': { value: 'Exclusion', rule_sub_type: ['Test Sub Type'] } },
          { 'Inclusion': { value: 'Inclusion', rule_sub_type: ['Test Sub Type 2'] } }
        ],
        query_fields: [
          { field_type: 'dropdown', value: 'test_field', name: 'Test Field', options: [] }
        ]
      } } },
      { status: { code: 200 }, result: { metadata: { rules: [{}] } } }
    ]));
    mockBusinessDivisionService.getBusinessDivision.and.returnValue('test-division');
    mockAuthService.isWriteOnly = true;
    mockCookieService.get.and.returnValue('TEST_USER');
    mockUtilitiesService.getDbgDateFormat.and.returnValue('2023-01-01');
    mockUtilitiesService.getFutureDate.and.returnValue('2023-01-02');
    mockClientApiService.getAllClientsInPreferenceCenter.and.returnValue(of([
      { clientId: 1, clientName: 'Test Client 1' },
      { clientId: 2, clientName: 'Test Client 2' }
    ]));
    mockProductApiService.getProductConceptsId.and.returnValue(of({
      executionConceptAnalyticResponse: [
        { clientId: 1, exConceptReferenceNumber: 'CONCEPT-001', conceptName: 'Test Concept 1' }
      ]
    }));
    // Patch: Always return an array for clientData
    component.clientData = [
      { clientId: 1, clientName: 'Test Client 1' },
      { clientId: 2, clientName: 'Test Client 2' }
    ];
    // Patch: Always return groupControls as array for all expected usages
    component.querySpecificationJson = [
      { id: 'sqlType', value: 'qb', groupControls: [{ name: 'test', visible: true, options: [] }] },
      {
        id: 'queryBuilder',
        groupControls: [
          { name: 'PRODUCT', visible: true, options: [] },
          { name: 'CONCEPT_ID', visible: true, options: [] },
          { name: 'rulesLevel', visible: true, selectedVal: '', options: [] }
        ]
      }
    ];
    // Patch: Always return an array for customSqlJson
    component.customSqlJson = [{ id: 'customSql', value: '', groupControls: [], options: [] }];
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with correct default values', () => {
      expect(component.headerText).toBe('Create New Rule');
      expect(component.inventoryStatusDataset).toBeDefined();
      expect(component.ruleId).toBe(123); // From the mocked URL
    });

    it('should handle route parameters', () => {
      expect(component).toBeTruthy();
      // The component should handle route params in ngOnInit
    });

    it('should call all required methods on init', () => {
      spyOn(component, 'callGetRuleApis');
      spyOn(component, 'getAllJsonFilesData');
      spyOn(component, 'callGetFileDetailsRules');
      spyOn(component, 'getConfigForDuplicateRules');
      component.ngOnInit();
      expect(component.callGetRuleApis).toHaveBeenCalled();
      expect(component.getAllJsonFilesData).toHaveBeenCalled();
      expect(component.callGetFileDetailsRules).toHaveBeenCalled();
      expect(component.getConfigForDuplicateRules).toHaveBeenCalled();
    });
  });

  describe('Service Dependencies', () => {
    it('should have all required services injected', () => {
      expect(mockRouter).toBeDefined();
      expect(mockActivatedRoute).toBeDefined();
      expect(mockRulesApiService).toBeDefined();
      expect(mockUtilitiesService).toBeDefined();
      expect(mockCookieService).toBeDefined();
      expect(mockUserManagementApiService).toBeDefined();
      expect(mockClientApiService).toBeDefined();
      expect(mockProductApiService).toBeDefined();
      expect(mockToastService).toBeDefined();
      expect(mockAuthService).toBeDefined();
      expect(mockBusinessDivisionService).toBeDefined();

    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', () => {
      mockRulesApiService.getListOfRules.and.returnValue(throwError(() => new Error('API Error')));
      expect(() => component.ngOnInit()).not.toThrow();
    });
  });

  describe('Component Methods', () => {
    it('should update ruleEditUploadRedraw', (done) => {
      component.ruleEditUploadRedraw = 0;
      component.onTabSelection({});
      setTimeout(() => {
        expect(component.ruleEditUploadRedraw).not.toBe(0);
        done();
      }, 150);
    });

    it('should navigate to selected url', () => {
      const event = { selected: { url: '/test' } };
      component.breadcrumSelection(event);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/test']);
    });

    it('should update status fields and open accordion', () => {
      const item = { cdValLongDesc: 'desc', cdValShrtDesc: 'short', cdValName: 'name' };
      component.onSelect(item);
      expect(component.statusDescription).toBe('desc');
      expect(component.statusSuggestion).toBe('short');
      expect(component.selectedValue).toBe('name');
      expect(component.openAccordion).toBeTrue();
    });

    it('should use fallback description', () => {
      const item = { cdValName: 'name' };
      component.onSelect(item);
      expect(component.statusDescription).toContain('No Description');
    });

    it('should set inventoryStatusDataset and call showDescriptionandInventoryStatus', (done) => {
      spyOn(component, 'showDescriptionandInventoryStatus');
      const data = [{ cdValName: 'A' }];
      mockRulesApiService.getInventoryStatusData.and.returnValue(of(data));
      component.getInventoryStatusData();
      expect(component.inventoryStatusDataset).toEqual(data);
      setTimeout(() => {
        expect(component.showDescriptionandInventoryStatus).toHaveBeenCalled();
        done();
      }, 150);
    });

    it('should reset selectedValue if filteredResults is empty', (done) => {
      component.filteredResults = [];
      component.selectedValue = 'test';
      component.inventoryInputfocusOut({});
      setTimeout(() => {
        expect(component.selectedValue).toBe('');
        done();
      }, 150);
    });

    it('should set noResultsFound to false', () => {
      component.noResultsFound = true;
      component.filteredResults = [1];
      component.inventoryInputfocusOut({});
      expect(component.noResultsFound).toBeFalse();
    });

    it('should update fields for single match', () => {
      component.inventoryStatusDataset = [{ cdValName: 'A', cdValLongDesc: 'desc', cdValShrtDesc: 'short' }];
      const event = { target: { value: 'A' } };
      component.giveDescriptionForStatus(event);
      expect(component.suggestionWindow).toBeTrue();
      expect(component.statusDescription).toBe('desc');
      expect(component.statusSuggestion).toBe('short');
      expect(component.selectedValue).toBe('A');
    });

    it('should handle no matches', () => {
      component.inventoryStatusDataset = [{ cdValName: 'A' }];
      const event = { target: { value: 'Z' } };
      component.giveDescriptionForStatus(event);
      expect(component.noResultsFound).toBeTrue();
      expect(component.selectedValue).toBe('');
    });

    it('should handle multiple matches', () => {
      component.inventoryStatusDataset = [{ cdValName: 'A' }, { cdValName: 'AB' }];
      const event = { target: { value: 'A' } };
      component.giveDescriptionForStatus(event);
      expect(component.noResultsFound).toBeFalse();
      expect(component.suggestionWindow).toBeFalse();
    });

    it('should handle empty input', () => {
      component.inventoryStatusDataset = [{ cdValName: 'A' }];
      const event = { target: { value: '' } };
      component.giveDescriptionForStatus(event);
      expect(component.noResultsFound).toBeFalse();
      expect(component.filteredResults).toEqual([]);
    });

    it('should set statusDescription and openAccordion if selectedValue exists', () => {
      component.rule = { inventory_status: 'A' };
      component.inventoryStatusDataset = [{ cdValName: 'A', cdValLongDesc: 'desc' }];
      component.showDescriptionandInventoryStatus();
      expect(component.statusDescription).toBe('desc');
      expect(component.openAccordion).toBeTrue();
    });

    it('should not throw if no selectedValue', () => {
      component.rule = {};
      component.inventoryStatusDataset = [];
      expect(() => component.showDescriptionandInventoryStatus()).not.toThrow();
    });

    it('should set showForms true and assign rule', () => {
      const rule = { rule_type: 'A', letter_type: 'B', ltr_rule_sub_type: 'C', number_of_reminder_letter: 1, retro_apply: true, bypass_apply: false, header_level: true, inventory_status: 'A', conditions: [{}], execution_type: 'sql_query', client: 1, clientId: 2 };
      component.qbConfig = { fields: { a: 1 } };
      component.querySpecificationJson = [{ id: 'sqlType', value: '' }, { groupControls: [{ id: 'rulesLevel', selectedVal: '' }] }];
      component.customSqlJson = [{ id: 'customSql', value: '' }];
      spyOn(component, 'getConceptsClientsData');
      spyOn(component, 'getDependentDropdownsValues');
      spyOn(component, 'getDependentDropdownsLtrType');
      spyOn(component, 'getDependentDropdownsLtrSubType');
      spyOn(component, 'getDependentDropdownLtrOVPDuration');
      spyOn(component, 'showDescriptionandInventoryStatus');
      component.populateRuleDataOnForm(rule);
      expect(component.showForms).toBeTrue();
      expect(component.rule).toBe(rule);
      expect(component.getConceptsClientsData).toHaveBeenCalled();
      expect(component.getDependentDropdownsValues).toHaveBeenCalled();
      expect(component.getDependentDropdownsLtrType).toHaveBeenCalled();
      expect(component.getDependentDropdownsLtrSubType).toHaveBeenCalled();
      expect(component.getDependentDropdownLtrOVPDuration).toHaveBeenCalled();
      expect(component.showDescriptionandInventoryStatus).toHaveBeenCalled();
    });
  });

  afterAll(() => {
    // Patch: Defensive cleanup for test artifacts
    if (component && component.querySpecificationJson && Array.isArray(component.querySpecificationJson)) {
      component.querySpecificationJson.forEach(q => {
        if (q && q.groupControls && !Array.isArray(q.groupControls)) {
          q.groupControls = [];
        }
      });
    }
    if (component && component.customSqlJson && !Array.isArray(component.customSqlJson)) {
      component.customSqlJson = [];
    }
    if (component && component.clientData && !Array.isArray(component.clientData)) {
      component.clientData = [];
    }
  });

  // --- Additional Coverage Tests ---
  describe('Additional Coverage', () => {
    it('should show upload file modal', () => {
      component.fileDetailsExcelOpenModel = false;
      component.isFileReady = false;
      component.isTextReady = false;
      component.fileUploadPopup = 'none';
      component.showMessage = false;
      component.displayDuplicateMessage = false;
      component.showMaxLimitMsg = true;
      component.uploadFileInEditRule();
      expect(component.fileDetailsExcelOpenModel).toBeTrue();
      expect(component.isFileReady).toBeTrue();
      expect(component.isTextReady).toBeTrue();
      expect(component.fileUploadPopup).toBe('block');
      expect(component.displayDuplicateMessage).toBeTrue();
      expect(component.showMaxLimitMsg).toBeFalse();
    });

    it('should close file details excel popup', () => {
      component.fileDetailsExcelOpenModel = true;
      component.fileDetailsExcelClosePopup();
      expect(component.fileDetailsExcelOpenModel).toBeFalse();
    });

    it('should reset upload file popup', () => {
      component.isFileReady = true;
      component.isTextReady = true;
      component.fileUploadPopup = 'block';
      spyOn(component, 'cancelEdit');
      component.fileUploadpopUpReset();
      expect(component.isFileReady).toBeFalse();
      expect(component.isTextReady).toBeFalse();
      expect(component.fileUploadPopup).toBe('none');
      expect(component.cancelEdit).toHaveBeenCalled();
    });

    it('should close upload file modal for edit rule', () => {
      spyOn(component, 'fileUploadpopUpReset');
      component.closePopupUploadForEditRule();
      expect(component.fileUploadpopUpReset).toHaveBeenCalled();
    });

    it('should handle submit skip clicked', () => {
      component.createUploadOpenPopup = true;
      component.openImpactReportPopup = false;
      component.onSubmitSkipClicked();
      expect(component.createUploadOpenPopup).toBeFalse();
      expect(component.openImpactReportPopup).toBeTrue();
    });

    it('should map values to upload json and validate', () => {
      spyOn(component, 'checkValidationForUploadFile');
      const event = { value: { comments: 'Test comment' } };
      component.mapValuesToUploadJson(event);
      expect(component.postUploadDataJson.commentsInUpload).toBe('Test comment');
      expect(component.checkValidationForUploadFile).toHaveBeenCalled();
    });

    it('should validate upload file form (all branches)', () => {
      // All valid
      component.fileUploadEditJSON = 'file';
      component.postUploadDataJson = { commentsInUpload: 'abc' };
      component.showMaxLimitMsg = false;
      component.isDisabled = true;
      component.checkValidationForUploadFile();
      expect(component.isDisabled).toBeFalse();
      // Invalid: missing file
      component.fileUploadEditJSON = '';
      component.checkValidationForUploadFile();
      expect(component.isDisabled).toBeTrue();
      // Invalid: missing comments
      component.fileUploadEditJSON = 'file';
      component.postUploadDataJson.commentsInUpload = '';
      component.checkValidationForUploadFile();
      expect(component.isDisabled).toBeTrue();
      // Invalid: showMaxLimitMsg true
      component.postUploadDataJson.commentsInUpload = 'abc';
      component.showMaxLimitMsg = true;
      component.checkValidationForUploadFile();
      expect(component.isDisabled).toBeTrue();
    });

    it('should get config for duplicate rules', () => {
      const spy = spyOn(mockRulesApiService, 'getColumnConfigJsonDuplicate').and.returnValue(of({ switches: {}, colDefs: [] }));
      component.modalColumnConfigDuplicate = 'test.json';
      component.getConfigForDuplicateRules();
      expect(spy).toHaveBeenCalledWith('test.json');
    });

    it('should validate max file size (not exceeded)', () => {
      component.fileUploadEditJSON = { a: { size: 1000 }, b: { size: 2000 } };
      expect(component.validateMaxFileSize()).toBeFalse();
    });
    it('should validate max file size (exceeded)', () => {
      component.fileUploadEditJSON = { a: { size: 26214401 } };
      expect(component.validateMaxFileSize()).toBeTrue();
    });

    it('should handle submit upload clicked (success)', () => {
      component.isLoading = false;
      component.fileUploadEditJSON = { a: new Blob(['test'], { type: 'text/plain' }) };
      component.updatedRuleId = '123';
      component.postUploadDataJson = { commentsInUpload: 'Test comment' };
      component.userId = 'user1';
      // Use existing spy instead of creating new one
      mockRulesApiService.addFilesToRules.and.returnValue(of({}));
      spyOn(mockRulesApiService, 'addFilesToRules').and.returnValue(of({}));
      spyOn(component['alertService'], 'setSuccessNotification');
      component.levelIndicator = 'Global';
      component.onSubmitUploadClicked();
      expect(component.isLoading).toBeFalse();
      expect(component['alertService'].setSuccessNotification).toHaveBeenCalled();
      expect(component.openImpactReportPopup).toBeTrue();
    });

    it('should handle submit upload clicked (error)', () => {
      component.isLoading = false;
      component.fileUploadEditJSON = { a: new Blob(['test'], { type: 'text/plain' }) };
      component.updatedRuleId = '123';
      component.postUploadDataJson = { commentsInUpload: 'Test comment' };
      component.userId = 'user1';
      // Use existing spy instead of creating new one
      mockRulesApiService.addFilesToRules.and.returnValue(throwError(() => ({ statusText: 'Fail' })));
      spyOn(mockRulesApiService, 'addFilesToRules').and.returnValue(throwError(() => ({ statusText: 'Fail' })));
      spyOn(component['alertService'], 'setErrorNotification');
      component.levelIndicator = 'Global';
      component.onSubmitUploadClicked();
      expect(component.isLoading).toBeFalse();
      expect(component['alertService'].setErrorNotification).toHaveBeenCalled();
    });

    it('should handle upload (file size not exceeded)', () => {
      spyOn(component, 'validateMaxFileSize').and.returnValue(false);
      component.postUploadDataJson = { commentsInUpload: '' };
      const mockFile = new Blob(['test'], { type: 'text/plain' });
      Object.defineProperty(mockFile, 'size', { value: 1000 });
      const mockEvent = { target: { files: [mockFile] } } as any;
      component.upload(mockEvent);
      expect(component.fileUploadEditJSON).toEqual(mockEvent);
    });
    it('should handle upload (file size exceeded)', () => {
      spyOn(component, 'validateMaxFileSize').and.returnValue(true);
      component.postUploadDataJson = { commentsInUpload: '' };
      const largeMockFile = new Blob(['test'], { type: 'text/plain' });
      Object.defineProperty(largeMockFile, 'size', { value: 26214401 });
      const largeMockEvent = { target: { files: [largeMockFile] } } as any;
      component.upload(largeMockEvent);
      expect(component.fileUploadEditJSON).toEqual(largeMockEvent);
    });
  });

  describe('CopyComponent Edge Cases', () => {
    afterEach(() => {
      // Remove spies to avoid conflicts
      jasmine.getEnv().allowRespy(true);
    });
    it('should handle file upload with empty file', () => {
      // Simulate empty file upload
      const event = { target: { files: [new Blob([''], { type: 'text/plain' })] } } as any;
      component.upload(event);
      expect(component.fileUploadEditJSON).toEqual(event);
    });
    it('should handle file upload with large file', () => {
      // Simulate large file upload
      const largeFile = new Blob(['a'.repeat(26214401)], { type: 'text/plain' });
      const event = { target: { files: [largeFile] } } as any;
      component.upload(event);
      expect(component.fileUploadEditJSON).toEqual(event);
    });
    it('should handle duplicate rule creation', () => {
      // Test the actual duplicate rules functionality
      component.getConfigForDuplicateRules();
      expect(mockRulesApiService.getColumnConfigJsonDuplicate).toHaveBeenCalled();
    });
    it('should handle duplicate rules check', () => {
      // Test the checkForDuplicateRules method
      component.checkForDuplicateRules();
      expect(component.createOpenPopup).toBe(true);
      expect(component.showMessage).toBe(false);
      expect(component.displayDuplicateMessage).toBe(true);
      expect(component.displayStyle).toBe('block');
    });
  });
});


