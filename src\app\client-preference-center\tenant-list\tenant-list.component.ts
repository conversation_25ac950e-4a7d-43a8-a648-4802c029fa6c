import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { list } from '../constant';

@Component({
  selector: 'app-tenant-list',
  templateUrl: './tenant-list.component.html',
  styleUrls: ['./tenant-list.component.css']
})
export class TenantListComponent implements OnInit {
  clientId: Number;
  breadcrumbDataset = [{ label: 'Home', url: '/' }, { label: 'Client Setup' }];
  columnConfigForViewTenantTableUrl: string = "./assets/json/client-preference/tenant-list.json";
  public kebabOptions: any = [{ label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.5H2.75C2.61193 2.5 2.5 2.61193 2.5 2.75V13.25C2.5 13.3881 2.61193 13.5 2.75 13.5H7.25C7.66421 13.5 8 13.8358 8 14.25C8 14.6642 7.66421 15 7.25 15H2.75C1.7835 15 1 14.2165 1 13.25V2.75C1 1.7835 1.7835 1 2.75 1H8.25C10.8734 1 13 3.12665 13 5.75V7H8.75C7.7835 7 7 6.2165 7 5.25V2.5ZM8.5 2.50947V5.25C8.5 5.38807 8.6119 5.5 8.75 5.5H11.4905C11.3691 3.9044 10.0956 2.63085 8.5 2.50947Z" fill="black"/>
<path d="M12.7432 9.64823C12.6935 9.28215 12.3797 9 12 9C11.5858 9 11.25 9.33579 11.25 9.75V11.25H9.75L9.64823 11.2568C9.28215 11.3065 9 11.6203 9 12C9 12.4142 9.33579 12.75 9.75 12.75H11.25V14.25L11.2568 14.3518C11.3065 14.7178 11.6203 15 12 15C12.4142 15 12.75 14.6642 12.75 14.25V12.75H14.25L14.3518 12.7432C14.7178 12.6935 15 12.3797 15 12C15 11.5858 14.6642 11.25 14.25 11.25H12.75V9.75L12.7432 9.64823Z" fill="black"/>
</svg>  Edit Tenant`, id: 'edit' }, 
    { label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg>  View Tenant`, id: 'view' }];
  public dataset: any;
  tenantListColumnConfig: any;
  public selectedRowData: any;
  enablePage: any;
  viewTenantPageName: any;
  isListing: boolean = true;
  @Output() activePageInfoEvent = new EventEmitter();
  constructor(private router: Router, private clientApiService: ClientApiService, private route: ActivatedRoute) { }

  ngOnInit(): void {
    this.clientId = Number(this.route.snapshot.paramMap.get('clientId'));
    this.clientApiService.getTableColumnforClientsTable(this.columnConfigForViewTenantTableUrl).subscribe((columndata) => {
      columndata.colDefs.forEach(e => {
        e.field == "active" ? e.customFormatter = this.customFormatterStatus : "";
        // e.field == "products" ? e.customFormatter = this.customFormatterProducts : "";
        // e.field == "bundles" ? e.customFormatter = this.customFormatterBundles : "";
        // e.field == "feeSchedule" ? e.customFormatter = this.customFormatterFeeSchedules : "";
      });
      this.tenantListColumnConfig = columndata;
      this.getAllTenantsByCliendId();

    });

  }

  /**
   * @function getAllTenantsByCliendId - To get tenant Details
   */
  getAllTenantsByCliendId() {
    this.clientApiService.getAllTenantsByClientId(this.clientId).subscribe((data) => {
      this.dataset = data;
      this.dataset.forEach(record => {
        record.lastUpdatedDate = record.lastUpdatedDate?.split('.')[0].replace('T', ' ');
        record.createdDate = record.createdDate?.split('.')[0].replace('T', ' ');
      })
    })
  }

  /**
    * @function breadcrumbSelection - routes to respective url
    * @param event default event
    */
  breadcrumSelection(event) {
    this.router.navigate([`${event.selected.url}`]);
  }

  moveToClientTenant(event: any) {
    let currentRow = event['currentRow'];
    let selectedOption = event['text'];
    this.isListing = false;
    switch (selectedOption) {
      case list.TEEDIT:
        this.selectedRowData = event['currentRow'];
        this.selectedRowData.isView = false;
        this.enablePage = list.EDIT;
        this.viewTenantPageName = list.TEEDIT;
        this.clientApiService.selectedTenantId = this.selectedRowData.tenantId;
        this.activePageInfoFunction();
        break;
      case list.TEVIEW:
        this.selectedRowData = event['currentRow'];
        this.selectedRowData.isView = true;
        this.enablePage = list.VIEW;
        this.viewTenantPageName = list.TEVIEW;
        this.clientApiService.selectedTenantId = this.selectedRowData.tenantId;
        this.activePageInfoFunction();
        break;
    }
  }

  createTenant() {
    this.isListing = false;
    this.enablePage = list.ADD;
    this.viewTenantPageName = list.TEADD;
    this.activePageInfoFunction();
    this.getAllTenantsByCliendId();

  }

  /**
   *  receiveFromAddEdit event data from child component
   * @param event 
   */
  receiveFromAddEdit() {
    this.enablePage = "";
    this.isListing = true;
    this.viewTenantPageName = list.TENANT;
    this.activePageInfoFunction();
    this.getAllTenantsByCliendId();
    // this.resPageDetails();
  }

  /**
  * customFormatterStatus funtion for button in Rule table
  * @param event 
  */
  customFormatterStatus(event) {
    let btn;
    switch (event.value) {
      case true:
        btn = "<button type='button' title='Active' class='mt-n2 btn btn rule-dashboard btn-active btn-wrap-text'>Active</button>";
        break;
      case false:
        btn = "<button type='button' title='Inactive' class='mt-n2 btn btn rule-dashboard btn-inactive btn-wrap-text'>Inactive</button>";
        break;
    }
    return btn;
  }

  /**
   * Method is used get data based on table screen on view,edit,add
   * 'info' carries data of page,row 
   */
  activePageInfoFunction() {
    let info = {
      viewTenantPageName: this.viewTenantPageName,
      selectedRowData: this.selectedRowData,
      name: list.TENANT,
    }
    this.activePageInfoEvent.emit(info);
  }

}
