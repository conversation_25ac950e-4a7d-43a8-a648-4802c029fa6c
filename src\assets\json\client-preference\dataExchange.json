[{"type": "group", "name": "exchange", "label": "", "column": "1", "groupControls": [{"options": [], "optionName": "productName", "optionValue": "productName", "label": "Product Name", "type": "select", "multiple": false, "placeholder": "Choose Product Name", "closeOnSelect": true, "id": "productName", "name": "productName", "column": "3", "required": true, "disabled": false}, {"options": [{"name": "GBD", "id": 1}, {"name": "CSBD", "id": 2}], "optionName": "name", "optionValue": "name", "label": "Business Division", "type": "select", "multiple": false, "placeholder": "Choose Business Division", "closeOnSelect": true, "name": "businessDivision", "id": "businessDivision", "column": "3", "required": true, "disabled": false}, {"label": "Preference Name", "placeholder": "Enter Preference Name", "type": "text", "name": "preferenceName", "id": "preferenceName", "column": "3", "required": true, "disabled": false}, {"options": [], "optionName": "fileTmplName", "optionValue": "fileTmplName", "label": "Template Name", "type": "select", "multiple": false, "placeholder": "<PERSON><PERSON> Template Name", "closeOnSelect": true, "name": "templateName", "id": "templateName", "column": "3", "required": true, "disabled": false}, {"options": [], "optionName": "name", "optionValue": "name", "label": "Concept State Name", "type": "select", "multiple": false, "placeholder": "Choose Concept State Name", "closeOnSelect": true, "name": "conceptState", "column": "3", "required": true, "disabled": false}, {"label": "System", "type": "text", "id": "system", "placeholder": "Read System", "name": "system", "required": true, "column": "3", "disabled": true}, {"label": "Inventory Type", "type": "text", "id": "inventoryType", "placeholder": "Read Inventory Type", "name": "inventoryType", "column": "3", "disabled": true, "required": true}, {"options": [], "optionName": "dbgUnitName", "optionValue": "dbgUnitName", "label": "DBG Unit", "type": "select", "multiple": false, "placeholder": "Choose DBG Unit", "closeOnSelect": true, "name": "dbgUnit", "required": true, "column": "3", "disabled": false}, {"label": "File Destination", "type": "text", "multiple": false, "closeOnSelect": true, "placeholder": "Enter File Destination", "name": "fileDestination", "required": true, "column": "3", "disabled": false}, {"options": [{"name": "Daily", "id": 1}, {"name": "Weekly", "id": 2}, {"name": "Monthly", "id": 3}], "optionName": "name", "optionValue": "name", "label": "Frequency", "required": true, "type": "select", "placeholder": "<PERSON><PERSON>ncy", "multiple": false, "closeOnSelect": true, "name": "frequency", "column": "3", "disabled": false}, {"label": "Start Date", "type": "date", "name": "startDate", "id": "startDate", "column": "3", "placeholder": "<PERSON>ose <PERSON>", "disabled": false, "value": "", "pickerType": "single", "required": true, "enableTime": true, "noCalendar": true, "dateFormat": "H:i", "time_24hr": true, "relatedDateControls": [{"target": "endDate"}]}, {"label": "End Date", "type": "date", "name": "endDate", "id": "endDate", "column": "3", "placeholder": "<PERSON>ose <PERSON>", "disabled": false, "pickerType": "single", "value": "", "required": true, "dateFormat": "MM-DD-YYYY"}, {"label": "File Name", "placeholder": "Enter File Name", "type": "text", "name": "fileName", "id": "fileName", "column": "3", "required": true, "disabled": false}]}]