<div class="body-content">
    <div class="page-header-container">
        <img src="./assets/images/logo.png">
        <span class="seperator"></span>
        <div class="page-header-text">Payment Integrity</div>
    </div>

    <div class="card elevated-card" *ngIf="!isOtpScreen && !isFoundUserId">
        <div class="page-header">
            <h3>Forgot your UserId?</h3>
            <span class="text-content">Enter your email address below and we'll send you an OTP to your email</span>
        </div>
        <marketplace-dynamic-form [formJSON]="emailIdFormJSON" [isSubmitNeeded]="true" [buttonLabel]="'Send OTP >'"
            (onSubmitForm)="onSendOtpButtonClicked()" (onValueChanges)="onForgotUserIdFormValueChange($event)">
        </marketplace-dynamic-form>
        <div class="red-font" *ngIf="isEmailNotFound">Email not found in our system</div>
        <a routerLink="../">Return to log in</a>
    </div>


    <div *ngIf="isOtpScreen">
        <app-otp [email]="emailAddress" (isValidationSuccessful)="actionAfterOTPValidation($event)"></app-otp>
    </div>

    <div class="card elevated-card-userIdscreen" *ngIf="isFoundUserId">
        <div class="page-header">
            <h3>Please find your userId here</h3>
            <h3>{{ fetchedUserId }}</h3>
        </div>
        <a routerLink="../">Return to log in</a>
    </div>
    <div class="page-footer">
        Need Help ?
        <a href="{{contactUsUrl}}" target="_blank">Email Support</a>
    </div>
</div>