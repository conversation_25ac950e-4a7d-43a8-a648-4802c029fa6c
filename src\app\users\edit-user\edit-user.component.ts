import { Compo<PERSON>, OnInit, ElementRef, ViewEncapsulation, ViewChild, OnDestroy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router, ActivatedRoute } from '@angular/router';
import { forkJoin, Subscription } from 'rxjs';
import { constants, emptyProduct, nullValueProduct, tableColumnConfig } from '../users-constants';
import { formatDate } from '@angular/common';
import { CookieService } from 'ngx-cookie-service';
import { cloneDeep } from 'lodash';
import { UserManagementApiService } from '../_services/user-management-api.service';
import { ToastService } from '../../_services/toast.service';
import { UtilitiesService } from '../../_services/utilities.service';
import { ExternalSOAService } from '../../_services/external-soa.service';
import { AuthService } from '../../_services/authentication.services';
import { IExternalUserStatus } from '../../_models/external/external-user-status';
import { RegistrationConstants } from '../../_constants/app.constants';
import { EXTERNALUSER } from '../../_models/external/external-user-constants';
import { IExternalUserSearch } from '../../_models/external/external-user-search';
import { externalAuthenticationConstants } from '../../_helpers/helpers.constants';
import { environment } from '../../../environments/environment';
import { AUTH_CONFIG } from '../../_constants/menu.constant';
import { LoaderService } from '../../_services/loader.service';

const VERIFIER = 'Carelon Verifier';
const AUTHENTICATOR = 'Carelon Authenticator';

@Component({
  selector: 'app-edit-user',
  templateUrl: './edit-user.component.html',
  styleUrls: ['./edit-user.component.sass'],
  encapsulation: ViewEncapsulation.None
})
export class EditUserComponent implements OnInit, OnDestroy {

  internalFlagFromEvent: any;
  userFormJSON: any;
  newSkillJSON: any;
  roleFromDefJSON: any;
  previewSkillsJSON: any;
  isUserJSONReady: boolean = false;
  isNewSkillsReady: boolean;
  masterData: any;
  stateValues: any = [];
  @ViewChild('formRef') formRef: any;
  @ViewChild('userFormRef') userFormRef: any;

  breadcrumbDataset: any = [{
    label: 'Users Management',
    //C2P url: '/product-catalog/security/users'
    url: 'users'
  }, {
    label: 'Edit User'
  }]

  viewUsersDataset: any = [];
  viewUsersConfig: any = {};
  isTableGroupReady: boolean = false;
  groupDataset: any = [];
  groupConfig: any = {};
  _formData: any;
  preSelectedRows: any;
  currentUserFormData: any;
  currentUserSkillsFormData = {};
  isShowMore = false;
  userStepperConfig: any = [];
  maxTargetSelection: number = 20;
  redrawForms: any;
  userConfigFormValues: any = [];
  userConfigDataset: any = [];
  userConfigPreviewDataset: any = [];
  showPreviewRoleForm: boolean = false;
  showClientAndProdForPreviewSkill: boolean = false;
  isFormSubmitted: boolean = false;
  errorMsg: string;
  isSegmentStepperReady: boolean = false;
  categoryData: any = [];
  userTypeOptions = { false: "External", true: "Internal" };
  invalidUserRole: boolean = false;
  clntRoleRelationList: any = [];
  prodRoleRelationList: any = [];
  clientRoleMasterData: any[] = [];
  rolesBasedOnUserSite: any[] = [];  // this is based on client site onshore or offshore
  isCarelonAdmin: boolean = false;
  userInfoPreviewDtls: any;
  showFooterButtons: boolean = true;
  userSearchPayload: IExternalUserSearch;
  userStatusPayload: IExternalUserStatus;
  isFormRepeaterReady: boolean = false;
  selectedClientSite: string = "";
  invalidCharPresent: boolean;
  tableDataJSON: any;
  mode: string = "";
  pageHeadingTitle: string = constants.EDIT_USER_HEADER;
  submitBtnLabel: string = constants.SUBMIT;
  isRejectBtnVisible: boolean = false;
  tableColumnConfig: any = tableColumnConfig;
  isRejectUserPopupReady: boolean = false;
  isDeactivateUserPopupReady: boolean = false;

  distinctClients: object[] = [];
  distinctProducts: object[] = [];
  selectedClientForSkills: {
    name: string,
    id: number
  };
  selectedPIProductForSkills: {
    name: string,
    id: number
  };
  selectedClientForSkillsId: number = null;
  selectedPIProductForSkillsId: number = null;
  openPIProductWarning: any;
  openExistingRolePopup: any;
  showCobSubskill: boolean = false;
  initialSkillJSON: any;
  skillProductForChanged: boolean = false;
  savedSkillSet: number[] = [];
  savedSkillSetForclient: number[] = [];
  private roleDefSubscription: Subscription | undefined;
  clientAndProductWarningMessage: any = { product: false, client: false }
  currentMaxClaimAssignment: number = 250;
  userDetailsClaimCount: number;

  constructor(private http: HttpClient, private router: Router, private route: ActivatedRoute, private userManagementSvc: UserManagementApiService,
    private notificationService: ToastService, private dateService: UtilitiesService,
    private cookieService: CookieService,
    private soaService: ExternalSOAService, private authService: AuthService, private loaderService: LoaderService) { }

  ngOnInit() {
    this.route.queryParams.subscribe((data: any) => {
      this.mode = data?.mode;
    });
    if (this.mode === constants.PREVIEW) {
      this.pageHeadingTitle = constants.APPROVE_USER;
      this.submitBtnLabel = constants.APPROVE_USER;
      this.isRejectBtnVisible = true;
      this.breadcrumbDataset[1].label = constants.APPROVE_USER;
      this.maxTargetSelection = 1;
    }
    this.userManagementSvc.getAssetsJson(constants.USER_FORM_CONFIGURATION_JSON).subscribe((data) => {
      this.userStepperConfig = data["userStepperConfig"];
      for (var i = 1; i < this.userStepperConfig.length; i++) {
        this.userStepperConfig[i].valid = true;
        this.userStepperConfig[i].enabled = true;
      }
      this.isSegmentStepperReady = true;
    });
    this.errorMsg = constants.ERROR_MSG;
    let userId = JSON.parse(localStorage.getItem('user-row-selected'))[constants.USER_ID];
    let _fetchUserDetails = this.userManagementSvc.getUserDetails(userId);
    let _fetchUser = this.userManagementSvc.getAssetsJson(constants.USER_FORM_JSON);
    let _fetchSkills = this.userManagementSvc.getAssetsJson(constants.SKILL_HISTORY_JSON);
 //   let _fetchMasterData = this.userManagementSvc.getUserMasterData();
    let _fetchClientRoleList = this.userManagementSvc.getRolesList();
    this.isCarelonAdmin = this.authService.isWriteOnly;
    this.loaderService.show();
    forkJoin([_fetchUser, _fetchSkills, _fetchUserDetails, _fetchClientRoleList]).subscribe(
      ([userForm, skill, userDetails, clientRoleMasterList]) => {
        if (this.mode === constants.PREVIEW) {//default setting for preview flow
          userForm.find(x => x.label == 'User Type').visible = false;
          userForm.find(x => x.label == "Client Site").disabled = true;
          userForm.find(x => x.label == "User Status").options[0].enabled = false;
          userForm.find(x => x.label == "User Status").column = 2;
          let userTypeIndex = userForm.findIndex(x => x.label == 'User Type'),
            userStatusIndex = userForm.findIndex(x => x.label == 'User Status');
          [userForm[userTypeIndex], userForm[userStatusIndex]] = [userForm[userStatusIndex], userForm[userTypeIndex]];//To prevent empty space, move the hidden field to the end of the form.
          userDetails.status = false;
          userDetails.clientSite = "Onshore";
          userDetails.internalFlag = false;
        }

        // userDetails.skills.forEach(element => {
        //   this.savedSkillSet.push(element.prodId);
        //   this.savedSkillSetForclient.push(element.clientId);

        // });
        this.savedSkillSet = [...new Set(this.savedSkillSet)];
        this.savedSkillSetForclient = [...new Set(this.savedSkillSetForclient)];
        this.userFormJSON = userForm;
        this._formData = userDetails;
        this.initialSkillJSON = skill;
        //this.masterData = masterData;
        this.isUserJSONReady = true;
        this.newSkillJSON = cloneDeep(this.initialSkillJSON);

        let internalFlag = sessionStorage.getItem(constants.INTERNAL_FLAG)
        this.internalFlagFromEvent = internalFlag == "true" ? "internal" : "external";

        if (environment.loginType != AUTH_CONFIG.INTERNAL) {
          this.userFormJSON.find((x) => x.name == constants.INTERNAL_FLAG)[constants.DISABLED] = true;
          this.userFormJSON.find((x) => x.name == constants.INTERNAL_FLAG)["value"] = false;

          this.userFormJSON.find((x) => x.name == constants.CLIENT_SITE)[constants.DISABLED] = true;
          this.userFormJSON.find((x) => x.name == constants.CLIENT_SITE)["value"] = false;
        }
        else if (this.isCarelonAdmin) {
          this.userFormJSON.find((x) => x.name == constants.INTERNAL_FLAG)[constants.DISABLED] = false;
        } else {
          this.userFormJSON = this.userFormJSON.filter(x => x.name !== constants.INTERNAL_FLAG);
        }
        this.userFormJSON.find((x) => x.name == constants.USER_ID)[constants.DISABLED] = true;
        this.isUserJSONReady = true;
        if (this.isCarelonAdmin) {
          this.clientRoleMasterData = clientRoleMasterList[constants.RESPONSE_DATA]; //  BELOW COMMENTED LINES ARE NEEDED IN FUTURE
          // this.rolesBasedOnUserSite = clientRoleMasterList[constants.RESPONSE_DATA]?.filter(c => c.clientSite == userDetails.clientSite && c.teamType == this.internalFlagFromEvent)
        }
        else {
          this.clientRoleMasterData = clientRoleMasterList[constants.RESPONSE_DATA].filter(c => c.clientId == sessionStorage.getItem(constants.CLIENT_ID));
          // this.rolesBasedOnUserSite = clientRoleMasterList[constants.RESPONSE_DATA]?.filter(c => c.clientSite == userDetails.clientSite  && c.teamType == this.internalFlagFromEvent)
        }
        this.userFormJSON.find(x => x.id == 'comments').required = true;//mandatory in edit screen
        if (!this._formData.status && !this.mode) {
          this.userFormJSON.find(x => x.id == 'deactivationStatus').visible = true;
          this.userFormJSON.find(x => x.id == 'deactivationStatus').required = true;
          this.userFormJSON.find(x => x.id == 'deactivationReason').visible = true;
          this.userFormJSON.find(x => x.id == 'deactivationReason').required = true;
        }
        // userDetails.skills.forEach(x => {
        //   if (x.skillType == constants.MAX_CLAIM_COUNT) {
        //     this.userDetailsClaimCount = x.skillValue ? x.skillValue : null;
        //   }
        // })
        this.loadClientAndProductForSkills(); //Load clients and products to prepopulate the skills client and product selections as well as roles screen selections
        let clientId = sessionStorage.getItem("clientId");
        this.populateMasterDataOnForm(this.masterData); //By default loads Anthem client and CAD product.
        this.constructRelationForClientRole(this._formData);
        setTimeout(() => {
          this.checkUserRoleFormVaid();
        }, 200);
        this.loaderService.hide();
      }
    )
  }

  /** //  BELOW COMMENTED LINES ARE NEEDED IN FUTURE
   * Method to show only roles based on client site
   */
  onClientSiteChange(event: any) {
    if ((event.current.clientSite != event.previous.clientSite) || event.current.internalFlag != event.previous.internalFlag) {
      this.userConfigFormValues = [];
      this.userConfigDataset = {};
      this.constructRelationForClientRole();
      this.populateNewUserRoleForm();
    }
    if ((event.current.status != event.previous.status)) {
      this.userManagementSvc.handleUserDependFieldsVisibility(this, event.current.status);
      this._checkFormisValid(this.userFormRef.form, 1);


    }
  }
  /**
   * Method to refresh and get Group list
   */

  refreshData(): void {
    this.isTableGroupReady = false
    this.userManagementSvc.getAssetsJson(constants.GROUP_LIST_JSON)
      .subscribe(
        (data: any) => {
          this.groupConfig = data['columnConfig'];
          this.groupConfig['colDefs'] = data['columnConfig']['colDefs'].filter(x => x.field == constants.TEAM_NAME || x.field == constants.DESCRIPTION || x.field == constants.ROLE);
          this.groupDataset = data['dataset']
          setTimeout(() => this.isTableGroupReady = true, 50);
        }
      )
  }

  onRejectClose() {
    this.isRejectUserPopupReady = false;
  }
  onRejectBtnclk(event) {
    this.isRejectUserPopupReady = true;
  }
  onRejectUser(event) {
    this.isRejectUserPopupReady = false;
    let userDetailsObj = {
      userId: this.currentUserFormData.userId,
      registrationStatus: constants.REQUEST_NOT_APPROVED,
      requestType: "reject",
      updatedBy: this._getLoggedInUserId()
    }
    this.userManagementSvc.rejectUserApproval(userDetailsObj)
      .subscribe((data) => {
        if (data.responseCode == 200) {
          this._showErrorNotification(constants.USER_REJECTED, data.responseData.userName + constants.USER_REJECTION_MSG);
          this.rejectExternalUser(data.responseData ? data.responseData.userId : this.currentUserFormData.userId);
        }
        else {
          this._showErrorNotification(constants.ERROR, data.responseData);
        }
      }, err => {
        this._showErrorNotification(constants.ERROR, "");
      });
  }
  /**
   * Method to call Soa to update user when rejected
   */
  rejectExternalUser(userId) {

    this.soaService.searchExtUser(userId).subscribe((data: any) => {
      this.userStatusPayload = {
        comments: constants.DISABLE_COMMENTS,
        enableUser: false
      }
      if (data == externalAuthenticationConstants.NOT_FOUND || (data?.exceptions && data.exceptions[0].code == externalAuthenticationConstants.USER_IS_NOT_FOUND)) {
        this.backToPreviousPage();
      } else if (data?.exceptions) {
        this.notificationService.setErrorNotification({
          notificationHeader: externalAuthenticationConstants.ERROR,
          notificationBody: externalAuthenticationConstants.SEARCH_USER_ERRMSG
        });
      }
      if (data.user && data.user[0].userRoleEnum == EXTERNALUSER.APPLICATION) {
        this.updateUserStatusForReject(userId, this.userStatusPayload);
      } else {
        this.backToPreviousPage();
      }
    }, (error: any) => {
      if (error == externalAuthenticationConstants.NOT_FOUND) {
        this.backToPreviousPage();
      } else {
        this.notificationService.setErrorNotification({
          notificationHeader: externalAuthenticationConstants.ERROR,
          notificationBody: externalAuthenticationConstants.SEARCH_USER_ERRMSG
        });
      }
    })
  }
  /**
   * Update User Status
   * @param token 
   * @param status 
   * @param userStatusPayload 
   */
  updateUserStatusForReject(user: string, userStatusPayload: IExternalUserStatus) {
    this.soaService.updateUserStatus(user, userStatusPayload).subscribe((data: any) => {
      this.backToPreviousPage();
    }, (error: any) => {
      this.notificationService.setErrorNotification({
        notificationHeader: RegistrationConstants.ERROR,
        notificationBody: constants.ERR_MSG_SOA
      });
    });
  }
  onDeactivateUser(event) {
    this._validateUserForms(event);
  }
  onDeactivateClose() {
    this.isDeactivateUserPopupReady = false;
  }


  /**
   * Method Fires on user form change
   * @param event 
   */
  _onUserSelection(event: any, step: number): void {
    let internalFlag = this.currentUserFormData?.[constants.INTERNAL_FLAG];

    this.currentUserFormData = event.value;
    if (event.value[constants.INTERNAL_FLAG] === undefined) {
      this.currentUserFormData[constants.INTERNAL_FLAG] = internalFlag;
    }

    this.isFormSubmitted = false
    this.invalidCharPresent = false;
    this.userFormJSON[3].customErrMsg = '';
    let managerName = event.value.managerName;
    managerName ? this.userManagementSvc.checkManagerNameValidation(managerName) : '';
    if (this.userManagementSvc.validationOfName) {
      this.isFormSubmitted = true
      this.userFormJSON[3].customErrMsg = RegistrationConstants.INVALID_CHAR;
      this.invalidCharPresent = true;
    }
    this.populatePreviewScreenData();
    this._checkFormisValid(event, step);
  }

  /**
   * Method Fires on user form Skills change
   * @param event 
   */
  _onUserSkillSelection(event) {
    this.isNewSkillsReady = false;
    if ((event.current.groupSkillJson["Market Skill"]?.length > 0 || event.previous.groupSkillJson["Market Skill"]?.length > 0) && event.current.groupSkillJson["Market Skill"] !== event.previous.groupSkillJson["Market Skill"]) {
      let isWestSelected = event.current.groupSkillJson["Market Skill"].includes(1001);
      let isNortheastSelected = event.current.groupSkillJson["Market Skill"].includes(1002);
      let isCentralSelected = event.current.groupSkillJson["Market Skill"].includes(1003);

      let california = this.stateValues.find(e => e.value === "California").id;
      let nevada = this.stateValues.find(e => e.value === "Nevada").id;
      let colorado = this.stateValues.find(e => e.value === "Colorado").id;
      let connecticut = this.stateValues.find(e => e.value === "Connecticut").id;
      let maine = this.stateValues.find(e => e.value === "Maine").id;
      let newHamsphire = this.stateValues.find(e => e.value === "New Hampshire").id;
      let kentucky = this.stateValues.find(e => e.value === "Kentucky").id;
      let ohio = this.stateValues.find(e => e.value === "Ohio").id;
      let wisconsin = this.stateValues.find(e => e.value === "Wisconsin").id;
      let missouri = this.stateValues.find(e => e.value === "Missouri").id;
      let indiana = this.stateValues.find(e => e.value === "Indiana").id;
      let all = this.stateValues.find(e => e.value === "ALL")?.id;
      if (isWestSelected || isNortheastSelected || isCentralSelected) {
        const indexOfAll = event.current.groupSkillJson["Market Skill"].indexOf(all);
        if (indexOfAll > -1) {
          event.current.groupSkillJson["Market Skill"].splice(indexOfAll, 1);
        }
        switch (isWestSelected || isNortheastSelected || isCentralSelected) {
          case isWestSelected:
            event.current.groupSkillJson["Market Skill"].push(california); // CA california
            event.current.groupSkillJson["Market Skill"].push(nevada); // CO NEVADA
            event.current.groupSkillJson["Market Skill"].push(colorado); // NV Colorado
            const index = event.current.groupSkillJson["Market Skill"].indexOf(1001);
            if (index > -1) {
              event.current.groupSkillJson["Market Skill"].splice(index, 1);
            }
            break;
          case isNortheastSelected:
            event.current.groupSkillJson["Market Skill"].push(connecticut); // CT Connecticut
            event.current.groupSkillJson["Market Skill"].push(maine); // ME Maine
            event.current.groupSkillJson["Market Skill"].push(newHamsphire); // NH New Hamsphire 
            const indexNorth = event.current.groupSkillJson["Market Skill"].indexOf(1002);
            if (indexNorth > -1) {
              event.current.groupSkillJson["Market Skill"].splice(indexNorth, 1);
            }
            break;
          case isCentralSelected:
            event.current.groupSkillJson["Market Skill"].push(kentucky); //KY Kentucky
            event.current.groupSkillJson["Market Skill"].push(ohio); // OH Ohio
            event.current.groupSkillJson["Market Skill"].push(wisconsin); // WI Wisconsin 
            event.current.groupSkillJson["Market Skill"].push(missouri); // MO Missouri
            event.current.groupSkillJson["Market Skill"].push(indiana); // IN INDIANA
            const indexCentral = event.current.groupSkillJson["Market Skill"].indexOf(1003);
            if (indexCentral > -1) {
              event.current.groupSkillJson["Market Skill"].splice(indexCentral, 1);
            }
            break;

          default:
            break;
        }
      }
      setTimeout(() => {
        this.isNewSkillsReady = true;
      }, 10);
    }
    this.currentUserSkillsFormData = event.current.groupSkillJson;
    let grpNumberSkills = event.current.groupSkillJson[constants.GROUP_NUMBER];
    let allId = this.masterData[constants.SKILLS].find(c => c.skillName == constants.GROUP_NUMBER)?.values?.find(c => c.value == constants.ALL)?.id;
    let nonKCId = this.masterData[constants.SKILLS].find(c => c.skillName == constants.GROUP_NUMBER)?.values?.find(c => c.value == constants.EXCLUDE_25)?.id;
    let groupIdMasterData = this.masterData[constants.SKILLS].find(c => c.skillName == constants.GROUP_NUMBER)?.values;

    if (grpNumberSkills?.length > 1) {
      if (this.masterData[constants.SKILLS].find(c => c.skillName == constants.GROUP_NUMBER)?.values?.find(c => c.id == grpNumberSkills[0])?.value == constants.ALL) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = this.currentUserSkillsFormData[constants.GROUP_NUMBER]?.filter(c => c != grpNumberSkills[0]);
      }
      else if (grpNumberSkills[grpNumberSkills.length - 1] == groupIdMasterData.find(c => c.value == constants.GROUP_ID_65).id
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(groupIdMasterData.find(c => c.value == constants.EXCLUDE_65).id)) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != groupIdMasterData.find(c => c.value == constants.EXCLUDE_65).id);
      }
      else if (grpNumberSkills[grpNumberSkills.length - 1] == groupIdMasterData.find(c => c.value == constants.EXCLUDE_65).id
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(groupIdMasterData.find(c => c.value == constants.GROUP_ID_65).id)) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != groupIdMasterData.find(c => c.value == constants.GROUP_ID_65).id);
      }
      else if (grpNumberSkills[grpNumberSkills.length - 1] == groupIdMasterData.find(c => c.value == constants.GROUP_ID_85).id
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(groupIdMasterData.find(c => c.value == constants.EXCLUDE_85).id)) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != groupIdMasterData.find(c => c.value == constants.EXCLUDE_85).id);
      }
      else if (grpNumberSkills[grpNumberSkills.length - 1] == groupIdMasterData.find(c => c.value == constants.EXCLUDE_85).id
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(groupIdMasterData.find(c => c.value == constants.GROUP_ID_85).id)) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != groupIdMasterData.find(c => c.value == constants.GROUP_ID_85).id);
      }
      else if (groupIdMasterData.filter(c => c.value.includes(constants.GROUP_ID_25)).map(c => c.id).includes(grpNumberSkills[grpNumberSkills.length - 1])
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(nonKCId)
        && event.previous.groupSkillJson[constants.GROUP_NUMBER].toString() !== event.current.groupSkillJson[constants.GROUP_NUMBER].toString()) {
        this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != nonKCId);
      }
      else if (grpNumberSkills[grpNumberSkills.length - 1] == nonKCId &&
        groupIdMasterData.filter(value => event.previous.groupSkillJson[constants.GROUP_NUMBER].includes(value.id)).map(c => c.value).find(c => c.includes(constants.GROUP_ID_25))) {
        event.current.groupSkillJson[constants.GROUP_NUMBER].forEach((element, index) => {
          if (groupIdMasterData.find(c => c.id == element).id != nonKCId && groupIdMasterData.find(c => c.id == element).value.includes(constants.GROUP_ID_25))
            this.currentUserSkillsFormData[constants.GROUP_NUMBER] = event.current.groupSkillJson[constants.GROUP_NUMBER].filter(c => c != element)
        });
      }
      else {
        if (grpNumberSkills?.find(c => c == allId)) {
          this.currentUserSkillsFormData[constants.GROUP_NUMBER] = [allId];
        }
      }
    }

    if (this.currentUserSkillsFormData[constants.COB_SKILL].length > 0 && this.selectedPIProductForSkills.id === constants.COORDINATION_OF_BENEFITS_ID) {
      this.newSkillJSON[0][constants.GROUP_CONTROLS].map((skillItem) => (skillItem.name.toLowerCase() === constants.COB_SUB_SKILL.toLowerCase()) ? skillItem.visible = true : null);
    } else {
      this.newSkillJSON[0][constants.GROUP_CONTROLS].map((skillItem) => (skillItem.name.toLowerCase() === constants.COB_SUB_SKILL.toLowerCase()) ? (skillItem.visible = false, skillItem.selectedVal = []) : null);
    }

    if (event.current.groupSkillJson[constants.MAX_CLAIM_COUNT] != event.previous.groupSkillJson[constants.MAX_CLAIM_COUNT]) {
      this.currentMaxClaimAssignment = Number(event.current.groupSkillJson[constants.MAX_CLAIM_COUNT]);
    }

    this.isNewSkillsReady = false;
    this.newSkillJSON[0][constants.GROUP_CONTROLS].forEach(skill => {
      skill.name == constants.MARKET_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.MARKET_SKILL] : '';
      skill.name == constants.CLAIM_SYSTEM ? skill.selectedVal = this.currentUserSkillsFormData[constants.CLAIM_SYSTEM] : '';
      skill.name == constants.FUNDING_TYPE ? skill.selectedVal = this.currentUserSkillsFormData[constants.FUNDING_TYPE] : '';
      skill.name == constants.LOB_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.LOB_SKILL] : '';
      skill.name == constants.SERVICE_TYPE_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.SERVICE_TYPE_SKILL] : '';
      skill.name == constants.CONCEPT_CATEGORY_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.CONCEPT_CATEGORY_SKILL] : '';
      skill.name == constants.GROUP_NUMBER ? skill.selectedVal = this.currentUserSkillsFormData[constants.GROUP_NUMBER] : '';
      skill.name == constants.BLUE_CARD_ACCESS ? skill.selectedVal = this.currentUserSkillsFormData[constants.BLUE_CARD_ACCESS] : '';
      skill.name == constants.COB_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.COB_SKILL] : '';
      skill.name == constants.COB_SUB_SKILL ? skill.selectedVal = this.currentUserSkillsFormData[constants.COB_SUB_SKILL] : '';
      skill.name == constants.MAX_CLAIM_COUNT ? skill.value = this.currentMaxClaimAssignment : '';
      skill.name == constants.MAX_CLAIM_COUNT ? this.currentUserSkillsFormData[constants.MAX_CLAIM_COUNT] = skill.selectedVal : [1003];
      skill.name == constants.CONCEPT_STATE ? skill.selectedVal = this.currentUserSkillsFormData[constants.CONCEPT_STATE] : '';
      skill.name == constants.MEMBER_BRAND_Label ? skill.selectedVal = this.currentUserSkillsFormData[constants.MEMBER_BRAND_Label] : '';
      skill.name == constants.SERVICE_PROVIDER_REGION_Label ? skill.selectedVal = this.currentUserSkillsFormData[constants.SERVICE_PROVIDER_REGION_Label] : '';
    });
    setTimeout(() => {
      this.isNewSkillsReady = true;
    }, 0);
  }


  /**
* Close the edit form
*/
  onCancelEditUser(): void {
    this.router.navigate([`${this.breadcrumbDataset[0].url}`]);
  }

  /**
  * Method fires once stepper options are selected
  * @param event 
  */
  stepperNext(step) {
    if (step === 1) {
      //Everytime the stepper goes back to the roles stepper, we need to enable back the roles so that user can reedit before moving to preview. Hence mode is removed here
      this.userConfigDataset.map(data => {
        return data.formJSON.map(item => {
          if ((data.formJSON[0].selectedVal != constants.ANTHEM_CLIENT_ID || data.formJSON[1].selectedVal != constants.CAD_PROD_ID) && item.label == constants.BUSINESS_DIVISION) {
            return item.disabled = true;
          }
          return item.disabled = false;
        });
      });
    }
    if (step === 2) {
      //only proceed with data which has selections
      this.userConfigDataset = this.userConfigDataset.filter(c => { return c.formJSON.find(d => { return d.id === constants.ROLE_ID_CAMEL_CASE && d.selectedVal.length > 0 }) })

      if (Array.isArray(this.userConfigDataset) && !this.userConfigDataset.length) {
        this.populateNewUserRoleForm();
        this.showPreviewRoleForm = false;
      }
    }
    if (step === 3) {
      this.userInfoPreviewDtls.internalFlag = this.userInfoPreviewDtls.internalFlag === true || this.userInfoPreviewDtls.internalFlag === false ? this.userInfoPreviewDtls.internalFlag : this.currentUserFormData.internalFlag ? true : false;

      if (this.selectedClientForSkills && this.selectedPIProductForSkills) {
        this.previewSkillsJSON = cloneDeep(this.newSkillJSON);
        this.previewSkillsJSON[0]['groupControls']?.forEach(element => element.disabled = true);
        this.showClientAndProdForPreviewSkill = true;
      } else {
        this.previewSkillsJSON = [];
        this.showClientAndProdForPreviewSkill = false;
      }

      this.populatepreviewUserRoleForm();

      let clientRoleObject = this.createRolesPayload();
      let clientData: Array<{ clientName: string, roleName: string }> = [];

      const uniqueClient = [...new Set(clientRoleObject.map(obj => obj.clientName))];
      uniqueClient.forEach(data => {
        let roleName = [];
        let role = clientRoleObject.filter(c => c.clientName != null && c.clientName == data);
        role.forEach(row => {
          roleName.push(row.roleName);
        })
        let name = roleName.join(', ');
        let client: string = data;
        clientData.push({ clientName: client, roleName: name });

      })

      this.tableDataJSON = clientData;
    }

  }

  /**
  * Method fires on add or delete a row in user role tab
  * @param event 
  */
  onAddOrDeleteRow(event: any) {
    if (event.action == 'delete') {
      this.checkUserRoleFormVaid();
    }
  }

  /**
 * Method fires on form Value Change
 * @param event 
 */
  onUsersChange(event: Event): void {
    //"emits on form value change"
  }

  /**
   * Navigate to the home page
   * @param event 
   */
  selectedLink(event: any): void {
    this.router.navigate([event.selected.url]);
  }

  /**
   * Method to set the selected Form Values
   */
  setFormData(isProductChange): void {
    if (isProductChange) {
      this.currentUserFormData = (this.currentUserFormData !== undefined) ? this.updateExistingFormData(this.currentUserFormData) : this._formData;
    } else {
      this.currentUserFormData = this._formData;
    }
    this.currentUserFormData[constants.REMINDER_DATE] = this.dateService.getDbgDateFormat(this.currentUserFormData[constants.REMINDER_DATE]);
    this.userFormJSON.forEach(e => {
      e[constants.OPTION_VALUE] = this._formData[e.name];
      e[constants.SELECTED_VALUE] = this._formData[e.name];

      if (e.name == constants.CLIENT_SITE) {
        e[constants.OPTION_VALUE] = this._formData[constants.CLIENT_SITE] == constants.CLIENT_OFFSHORE ? true : false;
      }

      (e.name === constants.STATUS) ? ((this._formData[e.name] === true || this._formData[e.name] === constants.ACTIVE) ? e[constants.OPTION_VALUE] = constants.ACTIVE : e[constants.OPTION_VALUE] = constants.INACTIVE) : '';
    });

    this.currentUserFormData[constants.STATUS] = (this.currentUserFormData[constants.STATUS] === true || this.currentUserFormData[constants.STATUS] === constants.ACTIVE) ? constants.ACTIVE : constants.INACTIVE;

    this.prePopulateSkillsBasedOnProductAndClient();
    //even if product changes don't update the basic details again
    !isProductChange ? this.populatePreviewScreenData() : null;

  }
  onUserSubmit(event: any): void {
    if (this.mode && this.mode === constants.PREVIEW) {
      this._validateUserForms(event);
    } else {
      if (this.currentUserFormData[constants.STATUS] === constants.ACTIVE) {
        this._validateUserForms(event);
      } else {
        this.isDeactivateUserPopupReady = true;
      }

    }

  }

  updateExistingFormData(formData) {
    return {
      ...this._formData,
      userId: formData?.userId,
      userName: formData?.userName,
      experienceLevelId: formData?.experienceLevelId,
      managerName: formData?.managerName,
      reminderDate: formData?.reminderDate,
      comments: formData?.comments,
      status: formData?.status,
      clientSite: formData?.clientSite,
      internalFlag: formData?.internalFlag,
    };
  }

  /**
   * Validate forms and consolidate before save API call
   */
  _validateUserForms(event: any): void {
    this.showFooterButtons = false;
    let userInfo = this.currentUserFormData;
    userInfo[constants.CLIENT_SITE] = this.currentUserFormData.clientSite ? constants.CLIENT_OFFSHORE : constants.CLIENT_ONSHORE;
    userInfo[constants.STATUS] === constants.ACTIVE ? userInfo[constants.STATUS] = true : userInfo[constants.STATUS] = false;
    if (userInfo[constants.STATUS] || (this.mode && this.mode === constants.PREVIEW)) {//send only when status is Inactive
      delete userInfo['deactivationStatus'];
      delete userInfo['deactivationReason'];
    }
    if (userInfo[constants.INTERNAL_FLAG] == '') {
      userInfo[constants.INTERNAL_FLAG] = false;
    }
    userInfo[constants.CLIENT_ROLE] = this.createRolesPayload();
    userInfo[constants.REMINDER_DATE] = this.dateService.getECPDateFormat(userInfo[constants.REMINDER_DATE]);
    // userInfo[constants.SKILLS] = (this.selectedClientForSkills && this.selectedPIProductForSkills) ? this.createSkillsPayload() : [];
    // userInfo[constants.SKILLS] = this.createSkillsPayload();
    // let newMarketSkillsValueInuserInfo = userInfo["skills"].find((e) => e.skillType == "Market Skill")?.values
    // if (newMarketSkillsValueInuserInfo) {
    //   let uniqueMarketSkillsValueInUserInfo = [...new Set(newMarketSkillsValueInuserInfo)];
    //   userInfo["skills"].find((e) => e.skillType == "Market Skill").values = uniqueMarketSkillsValueInUserInfo;

    // }
    userInfo[constants.MANAGER_NAME] = userInfo[constants.MANAGER_NAME] ? userInfo[constants.MANAGER_NAME] : null;
    delete userInfo[constants.CLIENT_RODUCT_ROLE];
    userInfo[constants.CREATED_BY] = this._getLoggedInUserId();
    userInfo[constants.CREATED_DATE] = this._getFormatDate();
    userInfo[constants.UPDATED_BY] = this._getLoggedInUserId();
    userInfo[constants.UPDATED_DATE] = this._getFormatDate();
    userInfo[constants.REQUEST_TYPE] = constants.EDIT_API;
    userInfo[constants.USER_NAME] = userInfo[constants.USER_NAME];
    if (this.mode === constants.PREVIEW) {
      userInfo[constants.REGISTRATION_STATUS] = constants.APPROVED;
    }
    this._updateUser(userInfo);
  }

  /**
     * Method to get logged in userId from cookie
     * Returns string
     */
  _getLoggedInUserId(): string {
    return this.cookieService.get(constants.USER_ID).toUpperCase();
  }

  /**
  * Method to get formatted date
  * Returns string
  */
  _getFormatDate(): string {
    return formatDate(Date.now(), "yyyy-MM-dd", 'en-US');
  }

  /**
   * Add new user to list
   */
  _updateUser(userInfo): void {
    //API method to post data
    this.userManagementSvc.createUpdateUser(userInfo)
      .subscribe((data) => {
        if (data.responseCode == 200) {
          if (!this.userInfoPreviewDtls[constants.INTERNAL_FLAG]) {
            this.externalUserUpdate(userInfo, data.responseData ? data.responseData : {});
          } else {
            this.userUpdateSuccess();
          }
        }
      }, err => {
        this._showErrorNotification(constants.ERROR, "");
      })
  }

  /**
   * Common method called on showing success notofication on the top
   * @param header and @param message
   */
  _showSuccessNotification(header: string, message: string): void {
    this.notificationService.setSuccessNotification({
      notificationHeader: header,
      notificationBody: message,
    });
  }

  /**
   * Common method called on showing error notification on the top
   * @param header and @param message
   */
  _showErrorNotification(header: string, message: string): void {
    this.notificationService.setErrorNotification({
      notificationHeader: header,
      notificationBody: message,
    });
  }

  /**
   * Common method to load Add skills drop downs
   * @param masterData
   */
  loadSkillsDD(isProductChange) {
    // this.stateValues = [];
    // this.masterData.skills.forEach(e => {
    //   if (e.skillName == "Market Skill") {
    //     if (e.prodId === this.selectedPIProductForSkillsId && e.clientId === this.selectedClientForSkillsId) {
    //       this.stateValues = e.values;
    //     }
    //   }
    // })
    // if (this.selectedClientForSkillsId && this.selectedPIProductForSkillsId) {
    //   const skillJSON = this.newSkillJSON.length > 0 ? this.newSkillJSON[0]['groupControls'] : this.newSkillJSON['groupControls'];
    //   skillJSON.forEach(skill => {
    //     if (skill.name == constants.MAX_CLAIM_COUNT) {
    //       let skillDetail = this.masterData.skills?.find(x => x.skillName.toLowerCase() === constants.MAX_CLAIM_COUNT.toLowerCase()
    //         && x.clientId === this.selectedClientForSkillsId && x.prodId === this.selectedPIProductForSkillsId);
    //       if (skillDetail && skillDetail?.values[0]?.id) {
    //         skill.selectedVal = [skillDetail?.values[0].id]
    //       }
    //       const maxClaimCountValue = this.filterSkillsBasedOnClientAndProduct(this.selectedClientForSkillsId, this.selectedPIProductForSkillsId)
    //         ?.find(c => c.skillType == constants.MAX_CLAIM_COUNT)?.skillValue;
    //       if (!maxClaimCountValue) {
    //         skill.value = 250;
    //       } else {
    //         skill.value = maxClaimCountValue;
    //         this.currentMaxClaimAssignment = maxClaimCountValue;
    //       }
    //     }
    //     skill.options = [];
    //     skill.options = masterData.skills.find(element => skill.name?.toLowerCase() === element.skillName?.toLowerCase() && element.prodId === this.selectedPIProductForSkillsId && element.clientId === this.selectedClientForSkillsId)?.values?.map(skillOptions => {
    //       return {
    //         label: skillOptions.value,
    //         value: skillOptions.id
    //       }
    //     }) ?? [];
    //   });
    //   this.newSkillJSON = this.newSkillJSON.length > 0 ? this.newSkillJSON : [this.newSkillJSON];
    //   this.userManagementSvc.addingAllOption(this.newSkillJSON, this.currentUserSkillsFormData);
    // }
    this.setFormData(isProductChange);

  }

  /**
  * method to populate master data on all dropdowns
  * @param masterData 
  */
  populateMasterDataOnForm(masterData) {
    let _skillexperience = this.userManagementSvc.getAssetsJson(constants.USER_FORM_EXPERIENCE_JSON);
    _skillexperience.subscribe(data => {
      this.userFormJSON.find((x) => x.id == constants.EXPERIENCE_LVL_ID).options = data["skillexperience"];
    });
    this.loadSkillsDD(false);
  }

  /**
  * method to populate user details on preview page   
  */
  populatePreviewScreenData() {

    this.userInfoPreviewDtls = {
      clientSite: (this.currentUserFormData.clientSite === true || this.currentUserFormData.clientSite === constants.CLIENT_OFFSHORE) ? constants.CLIENT_OFFSHORE : constants.CLIENT_ONSHORE,
      comments: this.currentUserFormData.comments,
      experienceLevelId: this.currentUserFormData.experienceLevelId ? this.masterData.experience.
        filter(c => c.id == this.currentUserFormData.experienceLevelId)[0]?.value : '',
      internalFlag: this.currentUserFormData.internalFlag ?? false,
      managerName: this.currentUserFormData.managerName?.length == 0 ? '' : this.currentUserFormData.managerName,
      reminderDate: this.currentUserFormData.reminderDate,
      status: this.currentUserFormData.status,
      userId: this.currentUserFormData.userId,
      deactivationStatus: this.currentUserFormData.status === constants.INACTIVE ? this.currentUserFormData.deactivationStatus : '',
      deactivationReason: this.currentUserFormData.status === constants.INACTIVE ? this.currentUserFormData.deactivationReason : '',
      userNm: this.currentUserFormData.userName,
    };
  }


  /**
  * Going Back to previous page
  */
  backToPreviousPage() {
    this.router.navigate(['/product-catalog/security/users']);
  }

  /**
   * Method will be called on the user form change to allow for the next step
   * @param event and @param step
   */
  _checkFormisValid(event: any, step: any): void {
    let valueToBeUpdated: boolean = false;
    if (event.status == constants.VALID) {
      valueToBeUpdated = true;
    }
    if (this.invalidCharPresent == true) {
      valueToBeUpdated = false;
    }
    if (this.invalidUserRole) {
      this.userStepperConfig[1].valid = valueToBeUpdated;
      this.userStepperConfig[1].enabled = valueToBeUpdated;
    }
    else {
      for (var i = step; i < this.userStepperConfig.length; i++) {
        this.userStepperConfig[i].valid = valueToBeUpdated;
        this.userStepperConfig[i].enabled = valueToBeUpdated;
      }
    }
  }

  /**
   * Method invoked when form values are changed
   * @param event 
   */
  _onRolesFormValueChange(event: any) {
    //[0] since at any point of time only 1 stepper will be changed
    let selectedCl = (event.dataJSON.formJSON.filter((item) => item.id === constants.CLIENT_ID)[0].selectedVal);
    let selectedPr = (event.dataJSON.formJSON.filter((item) => item.id === constants.PROD_ID)[0].selectedVal);
    let selectedBD = (event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].selectedVal);

    if (selectedCl === undefined || selectedPr === undefined) {
      event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].disabled = true;
      event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].required = true;
      event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].selectedVal = null;
      event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].disabled = true;
      event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = null;
    }
    if (selectedCl && selectedCl != undefined && selectedPr && selectedPr != undefined) {
      if (selectedCl == constants.ANTHEM_CLIENT_ID && selectedPr == constants.CAD_PROD_ID) {
        event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].disabled = false;
        event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].required = true;
        event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].disabled = false;
        if (this.isClProdBDRoleAlreadyPresent(event, selectedCl, selectedPr, selectedBD)) {
          this.openExistingRolePopup = Date.now();
          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.CLIENT_ID)[0].selectedVal = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.PROD_ID)[0].selectedVal = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].selectedVal = [];
        }
      } else {
        event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].disabled = true;
        event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].required = false;
        event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].disabled = false;
        if (this.isClProdRoleAlreadyPresent(event, selectedCl, selectedPr)) {
          this.openExistingRolePopup = Date.now();
          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.CLIENT_ID)[0].selectedVal = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.PROD_ID)[0].selectedVal = [];
        }
      }
    }

    if (selectedCl && selectedCl !== '' && selectedPr && selectedPr !== '') {
      if (selectedCl == constants.ANTHEM_CLIENT_ID && selectedPr == constants.CAD_PROD_ID) {
        if (!selectedBD) {
          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].options = [];
          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];
        }
        if (selectedBD && selectedBD != '') {
          if (event.form.filter(item => item.event.current.clientName !== item.event.previous.clientName || item.event.current.prodName !== item.event.previous.prodName || item.event.current.busDivision !== item.event.previous.busDivision).length > 0) {

            event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];
          }
          selectedCl = parseInt(selectedCl);
          selectedPr = parseInt(selectedPr);

          let roleOptions = [].concat(this.clientRoleMasterData.filter(c => c.clientId === selectedCl && c.prodId === selectedPr && c.teamType === this.internalFlagFromEvent && c.clientSite === this.selectedClientSite && (c.businessDivision === selectedBD || !c.businessDivision)).map(c => {
            return {
              label: c.roleName,
              value: "" + c.roleId
            }
          }));
          roleOptions = this.filterRoles(event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal, roleOptions);
          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].options = roleOptions;
        }
      }
      else {
        if (event.form.filter(item => item.event.current.clientName !== item.event.previous.clientName || item.event.current.prodName !== item.event.previous.prodName).length > 0) {

          event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];

        }
        selectedCl = parseInt(selectedCl);
        selectedPr = parseInt(selectedPr);
        let roleOptions = [].concat(this.clientRoleMasterData.filter(c => c.clientId === selectedCl && c.prodId === selectedPr && c.teamType === this.internalFlagFromEvent && c.clientSite === this.selectedClientSite).map(c => {
          return {
            label: c.roleName,
            value: "" + c.roleId
          }
        }));
        roleOptions = this.filterRoles(event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal, roleOptions);
        event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].options = roleOptions;
      }
    } else {
      event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].options = [];
      event.dataJSON.formJSON.filter((item) => item.id === constants.ROLE_ID_CAMEL_CASE)[0].selectedVal = [];
      event.dataJSON.formJSON.filter((item) => item.id === constants.BUS_DIV_ID)[0].selectedVal = null;
    }
    this.checkUserRoleFormVaid();
  }

  /* Restricting user to proceed further if user roles are not provided correctly */
  checkUserRoleFormVaid() {
    this.invalidUserRole = false;
    this.userConfigDataset?.some((dataSet, index) => {
      let eachRow = dataSet.ngModel.value;
      let isRoleInValid: boolean = false;
      isRoleInValid = this.formRef.dataset[index]["formJSON"].find(c => c.label == "Role").selectedVal.length == 0;
      if (this.formRef.dataset[index]["formJSON"].find(c => c.label == constants.BUSINESS_DIVISION).selectedVal == null && this.formRef.dataset[index]["formJSON"].find(c => c.label == constants.BUSINESS_DIVISION).disabled == false) {
        isRoleInValid = true;
      }
      if (!eachRow['clientName'] || !eachRow['prodName'] || isRoleInValid) {
        this.userStepperConfig[2].valid = false;
        this.userStepperConfig[2].enabled = false;
        //this.userStepperConfig[3].valid = false;
        //this.userStepperConfig[3].enabled = false;
        this.invalidUserRole = true;
        return true;
      }
      else if (!this.invalidUserRole) {
        this.userStepperConfig[2].valid = true;
        this.userStepperConfig[2].enabled = true;
        //this.userStepperConfig[3].valid = true;
        //this.userStepperConfig[3].enabled = true;
      }
      return false;
    })
  }


  /**
* Check if selected client and product is already present in the previous selections
*/
  isClProdRoleAlreadyPresent(event, selectedCl, selectedPr) {
    return this.userConfigDataset.filter(item => item.name !== event.dataJSON.name).map(item => item.formJSON).filter(item => item.find(f => f.id === constants.PROD_ID && f.selectedVal === selectedPr)).filter(item => item.find(f => f.id === constants.CLIENT_ID && f.selectedVal === selectedCl)).length > 0
  }

  /**
* Check if selected client and product and business division is already present in the previous selections
*/
  isClProdBDRoleAlreadyPresent(event, selectedCl, selectedPr, selectedBd) {
    return this.userConfigDataset.filter(item => item.name !== event.dataJSON.name).map(item => item.formJSON).filter(item => item.find(f => f.id === constants.PROD_ID && f.selectedVal === selectedPr)).filter(item => item.find(f => f.id === constants.CLIENT_ID && f.selectedVal === selectedCl)).filter(item => item.find(f => f.id === constants.BUS_DIV_ID && f.selectedVal === selectedBd)).length > 0
  }

  /**
 * Update External User Account
 */
  externalUserUpdate(userInfo: any, userResData: any) {

    let userStatus = userResData && userResData.status ? userResData.status : userInfo.status;

    this.soaService.searchExtUser(this.userInfoPreviewDtls.userId).subscribe((data: any) => {
      this.userStatusPayload = {
        comments: "",
        enableUser: true
      }

      if (data == externalAuthenticationConstants.NOT_FOUND || (data?.exceptions && data.exceptions[0].code == externalAuthenticationConstants.USER_IS_NOT_FOUND)) {
        this.userUpdateSuccess();
      } else if (data?.exceptions) {
        this.notificationService.setErrorNotification({
          notificationHeader: externalAuthenticationConstants.ERROR,
          notificationBody: externalAuthenticationConstants.SEARCH_USER_ERRMSG
        });
      }

      if (data.user[0].userRoleEnum == EXTERNALUSER.APPLICATION) {
        if (data.user[0].userAccountStatus.disabled && userStatus) {
          this.userStatusPayload.comments = constants.ENABLE_COMMENTS;
          this.updateUserStatus(this.userInfoPreviewDtls.userId, this.userStatusPayload);
        } else if (!data.user[0].userAccountStatus.disabled && !userStatus) {
          this.userStatusPayload.comments = constants.DISABLE_COMMENTS;
          this.userStatusPayload.enableUser = false;
          this.updateUserStatus(this.userInfoPreviewDtls.userId, this.userStatusPayload);
        } else {
          this.userUpdateSuccess();
        }
      } else {
        this.userUpdateSuccess();
      }

    }, (error: any) => {
      if (error == externalAuthenticationConstants.NOT_FOUND) {
        this.userUpdateSuccess();
      } else {
        this.notificationService.setErrorNotification({
          notificationHeader: externalAuthenticationConstants.ERROR,
          notificationBody: externalAuthenticationConstants.SEARCH_USER_ERRMSG
        });
      }
    })
  }

  /**----------------------------------------------------------------NOTIFICATIONS----------------------------------------------------------------- */

  /**
   * Update User Status
   * @param token 
   * @param status 
   * @param userStatusPayload 
   */
  updateUserStatus(user: string, userStatusPayload: IExternalUserStatus) {
    this.soaService.updateUserStatus(user, userStatusPayload).subscribe((data: any) => {
      this.userUpdateSuccess();
    }, (error: any) => {
      this.notificationService.setErrorNotification({
        notificationHeader: RegistrationConstants.ERROR,
        notificationBody: constants.ERR_MSG_SOA
      });
      this.showFooterButtons = true;
    });
  }

  /**
   * User Update Success
   * @param responseMessage 
   */
  userUpdateSuccess() {
    if (this.mode === constants.PREVIEW) {
      this.currentUserFormData && this.currentUserFormData.userName ? this._showSuccessNotification(constants.APPROVE_USER, this.currentUserFormData.userName + constants.USER_APPROVED_MSG) : this._showSuccessNotification(constants.APPROVE_USER, constants.USER_APPROVED_MSG_SUCCESS);
    } else {
      this._showSuccessNotification(constants.SUCCESS, constants.SUCCESS_USER_MSG);
    }
    //C2P this.router.navigate([`/product-catalog/security/users`]);
    this.router.navigate(['users']);
  }

  /*-------------------------------------------------------------------ROLES STEPPER------------------------------------------------------------- */

  /**
   * To construct relationship among client and respective roles   
   */
  constructRelationForClientRole(formData?) {

    this.clntRoleRelationList = [];
    if (formData) {
      this.internalFlagFromEvent = formData.internalFlag ? "internal" : "external";
      this.selectedClientSite = formData.clientSite ? formData.clientSite : constants.CLIENT_ONSHORE;
    }
    else {
      this.internalFlagFromEvent = this.currentUserFormData.internalFlag ? "internal" : "external";
      this.selectedClientSite = this.currentUserFormData.clientSite ? constants.CLIENT_OFFSHORE : constants.CLIENT_ONSHORE;
    }
    let relationshipNullObj = nullValueProduct;
    let relationshipSelectedValObj = emptyProduct;
    this.clntRoleRelationList.push(relationshipNullObj);
    this.clntRoleRelationList.push(relationshipSelectedValObj);

    let distinctClnts = this.clientRoleMasterData.filter(c => c.clientName != null).filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    let distinctProducts = this.clientRoleMasterData.filter(c => c.prodName != null).filter(function (a) {
      var key = a.prodId + '|' + a.prodName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));


    distinctClnts.forEach(client => {
      let relationshipProdObj = {
        updateDataset: [
          {
            id: "prodName", dataset: distinctProducts.filter(c => c.clientId === client.clientId).map(({ prodName, prodId }) => ({ label: prodName, value: prodId }))
          }],
        when: client.clientId && client.prodId
      }
      this.prodRoleRelationList.push(relationshipProdObj);

    });

    distinctProducts.forEach(client => {
      let relationshipObj = {
        updateDataset: [
          {
            id: "roleName", dataset: this.clientRoleMasterData.filter(c => c.clientId === client.clientId && c.prodId === client.prodId && c.teamType === this.internalFlagFromEvent && c.clientSite === this.selectedClientSite).map(({ roleName, roleId }) => ({ label: roleName, value: roleId }))
          }],
        when: client.clientId && client.prodId
      }
      this.clntRoleRelationList.push(relationshipObj);

    });
    if (formData) {
      formData.clientRole.length > 0 ? this.populateUserRoleForm(formData.clientRole) : this.populateNewUserRoleForm();
    }
  }

  createFinalRolePrepopulationData(userClientRoleList: any[]): any[] {
    return Object.values(userClientRoleList.reduce((acc, { clientId, prodId, clientName, prodName, roleId, roleName, businessDivision }) => {
      const key = `${clientId}: ${prodId}: ${businessDivision}`;
      if (!acc[key]) acc[key] = { clientId, prodId, clientName, prodName, role: [], businessDivision };
      acc[key].role.push({ roleId, roleName });
      return acc;
    }, {}));
  }

  /**
   * method to populate clients and roles form details   
   * @param userClientRoleList    
   */
  populateUserRoleForm(userClientRoleList: any[]) {
    this.isFormRepeaterReady = false;
    this.userConfigDataset = [];
    let userConfigDatasetJson: UsersClientRoleForm[] = [];

    let distinctUserClnt = userClientRoleList.filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    let distinctMasterClnt = this.clientRoleMasterData.filter(c => c.clientName != null).filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    let distinctProducts = this.clientRoleMasterData.filter(c => c.prodName != null).filter(function (a) {
      var key = a.prodId + '|' + a.prodName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    const rolePrepopulationData = this.createFinalRolePrepopulationData(userClientRoleList);

    rolePrepopulationData?.forEach(clnt => {
      let roleProdConfig = new UsersClientRoleForm();
      roleProdConfig.formJSON.push({
        optionName: constants.OPTION_NAME,
        optionValue: constants.OPTION_VALUE,
        label: "Client",
        type: constants.OPTION_TYPE_SELECT,
        multiple: false,
        closeOnSelect: true,
        name: "clientName",
        id: "clientId",
        column: "4",
        disabled: this.mode === constants.PREVIEW ? true : false,
        hidden: false,
        required: true,
        allowUnique: false,
        options: [].concat(distinctMasterClnt.map(mstClnt => {
          return {
            label: mstClnt.clientName,
            value: "" + mstClnt.clientId
          }
        })),
        selectedVal: "" + clnt.clientId,
        relationship: this.clntRoleRelationList
      });

      roleProdConfig.formJSON.push({
        optionName: constants.OPTION_NAME,
        optionValue: constants.OPTION_VALUE,
        label: "Product",
        type: constants.OPTION_TYPE_SELECT,
        multiple: false,
        closeOnSelect: true,
        name: "prodName",
        id: "prodId",
        column: "4",
        disabled: false,
        hidden: false,
        required: true,
        allowUnique: false,
        options: [].concat(distinctProducts.map(mstClnt => {
          return {
            label: mstClnt.prodName,
            value: "" + mstClnt.prodId
          }
        })),
        selectedVal: "" + clnt.prodId,
        relationship: this.prodRoleRelationList
      });

      roleProdConfig.formJSON.push({
        optionName: constants.OPTION_NAME,
        optionValue: constants.OPTION_VALUE,
        label: constants.BUSINESS_DIVISION,
        type: constants.OPTION_TYPE_SELECT,
        multiple: false,
        closeOnSelect: true,
        name: constants.BUS_DIV_NAME,
        id: constants.BUS_DIV_ID,
        column: "4",
        disabled: false,
        hidden: false,
        required: true,
        allowUnique: false,
        options: [
          {
            "label": constants.CSBD,
            "value": constants.CSBD
          },
          {
            "label": constants.GBD,
            "value": constants.GBD
          }
        ],
        selectedVal: clnt.businessDivision == null ? null : clnt.businessDivision,
      });

      if (clnt.clientId != constants.ANTHEM_CLIENT_ID || clnt.prodId != constants.CAD_PROD_ID) {
        roleProdConfig.formJSON[2].disabled = true;
        roleProdConfig.formJSON[2].required = false;
        roleProdConfig.formJSON[2].selectedVal = null;
      }

      roleProdConfig.formJSON.push({
        optionName: 'label',
        optionValue: 'value',
        label: "Role",
        type: constants.OPTION_TYPE_SELECT,
        multiple: true,
        closeOnSelect: false,
        name: "roleName",
        id: "roleId",
        column: "4",
        disabled: false,
        hidden: false,
        required: true,
        allowUnique: false,
        options: this.filterRoles([].concat(clnt.role.map(c => "" + c.roleId)), [].concat(this.clientRoleMasterData.filter(c => c.clientId == clnt.clientId && c.prodId === clnt.prodId && c.teamType === this.internalFlagFromEvent && c.clientSite === this.selectedClientSite
          && ((clnt.businessDivision == c.businessDivision) || !c.businessDivision)).map(c => {
            return {
              label: c.roleName,
              value: "" + c.roleId
            }
          }))),
        selectedVal: [].concat(clnt.role.map(c => "" + c.roleId))
      });
      userConfigDatasetJson.push(roleProdConfig);
    });
    this.userConfigDataset = userConfigDatasetJson;
    this.userConfigDataset.filter(c => c.formJSON[0].selectedVal != constants.ANTHEM_CLIENT_ID || c.formJSON[1].selectedVal != constants.CAD_PROD_ID).map(c => {
      c.formJSON[2].disabled = true;
      c.formJSON[2].required = false;
      c.formJSON[2].selectedVal = null;
    })
    setTimeout(() => {
      this.isFormRepeaterReady = true;
    }, 50);
  }

  /**
   * method to populate clients and roles form details when no role is assigned to user     
   */
  populateNewUserRoleForm() {
    this.isFormRepeaterReady = false;
    this.userConfigDataset = [];
    let userConfigDatasetJson: UsersClientRoleForm[] = [];
    let roleProdConfig = new UsersClientRoleForm();
    let distinctMasterClnt = this.clientRoleMasterData.filter(c => c.clientName != null).filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    let distinctProducts = this.clientRoleMasterData.filter(c => c.prodName != null).filter(function (a) {
      var key = a.prodId + '|' + a.prodName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null));

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: "Client",
      type: constants.OPTION_TYPE_SELECT,
      multiple: false,
      closeOnSelect: true,
      name: "clientName",
      column: "4",
      disabled: false,
      hidden: false,
      allowUnique: false,
      id: "clientId",
      options: [].concat(distinctMasterClnt.map(mstClnt => {
        return {
          label: mstClnt.clientName,
          value: "" + mstClnt.clientId
        }
      })),
      relationship: this.clntRoleRelationList
    });

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: "Product",
      type: constants.OPTION_TYPE_SELECT,
      multiple: false,
      closeOnSelect: true,
      name: "prodName",
      column: "4",
      disabled: false,
      hidden: false,
      allowUnique: false,
      id: "prodId",
      options: [].concat(distinctProducts.map(mstClnt => {
        return {
          label: mstClnt.prodName,
          value: "" + mstClnt.prodId
        }
      })),
      relationship: this.prodRoleRelationList
    });

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: constants.BUSINESS_DIVISION,
      type: constants.OPTION_TYPE_SELECT,
      multiple: false,
      closeOnSelect: true,
      name: constants.BUS_DIV_NAME,
      id: constants.BUS_DIV_ID,
      column: "4",
      disabled: false,
      hidden: false,
      required: true,
      allowUnique: false,
      options: [
        {
          "label": constants.CSBD,
          "value": constants.CSBD
        },
        {
          "label": constants.GBD,
          "value": constants.GBD
        }
      ],
    });

    roleProdConfig.formJSON.push({
      optionName: constants.OPTION_NAME,
      optionValue: constants.OPTION_VALUE,
      label: "Role",
      type: constants.OPTION_TYPE_SELECT,
      multiple: true,
      closeOnSelect: false,
      allowUnique: false,
      name: "roleName",
      id: "roleId",
      column: "4",
      disabled: false,
      hidden: false,
      options: [],
      selectedVal: []
    });
    userConfigDatasetJson.push(roleProdConfig);
    this.userConfigDataset = userConfigDatasetJson;
    setTimeout(() => {
      this.isFormRepeaterReady = true;
      this.checkUserRoleFormVaid();
    }, 50);
  }

  /**
   * @function createRolesPayload Creates request payload for roles stepper
   */
  createRolesPayload() {
    let clientObj = [];
    this.userConfigDataset?.forEach(data => {
      let rowValue = data.ngModel.value;
      rowValue.roleName?.forEach(role => {
        let clientData = this.clientRoleMasterData.filter(c => c.clientName !== null && c.clientId === parseInt(rowValue.clientName));
        clientObj.push({
          clientId: parseInt(rowValue.clientName),
          clientName: this.distinctClients.find(c => c[constants.ID] === parseInt(rowValue.clientName))[constants.NAME] ?? '',
          roleId: +role,
          roleName: this.clientRoleMasterData.find(c => c.roleId === parseInt(role))?.roleName ?? '',
          prodId: parseInt(rowValue.prodName),
          prodName: this.clientRoleMasterData.find(p => p[constants.PROD_ID] === parseInt(rowValue.prodName))[constants.PROD_NAME] ?? '',
          businessDivision: rowValue.busDivision
        })
      });
    });
    return clientObj;
  }

  populatepreviewUserRoleForm() {
    this.userConfigPreviewDataset = [];

    this.userConfigPreviewDataset = this.userConfigDataset;

    if (this.userConfigDataset.filter(c => { return c.formJSON.find(d => { return d.id === constants.ROLE_ID_CAMEL_CASE && d.selectedVal.length > 0 }) })) {
      //All changes for this will be same as current edit form. Only that it will be disabled in Preview stepper.
      this.userConfigPreviewDataset.map(data => {
        return data.formJSON.map(item => {
          return item.disabled = true;
        });
      });
      this.showPreviewRoleForm = true;

    } else {
      //All changes for this will be same as current edit form. Only that it will be disabled in Preview stepper.
      this.userConfigPreviewDataset = [];
      this.showPreviewRoleForm = false;
    }
  }


  /*-------------------------------------------------------------------SKILLS STEPPER------------------------------------------------------------- */

  /**
   * @function createSkillsPayload Creates request payload for skills stepper
   */
  createSkillsPayload() {
     let resultSkills = [];
    return resultSkills;
    // let skillObj = [],
    //   originalSkills = this._formData[constants.SKILLS],
    //   resultSkills = [];
    // originalSkills = originalSkills.map((skill) => {
    //   skill.skillType === constants.CONCEPT_CATEGORY_SKILL_1 ? skill.skillType = constants.CONCEPT_CATEGORY_SKILL : null;
    //   if (this.selectedClientForSkillsId == skill.clientId && this.selectedPIProductForSkillsId == skill.prodId && skill.skillType === constants.MAX_CLAIM_COUNT) {
    //     skill.skillValue = this.currentMaxClaimAssignment;
    //   }
    //   if (skill.prodId === this.selectedPIProductForSkillsId && skill.clientId === this.selectedClientForSkillsId && skill.skillType in this.currentUserSkillsFormData) {
    //     skill.values = this.currentUserSkillsFormData[skill.skillType];
    //     delete this.currentUserSkillsFormData[skill.skillType];
    //   }
    //   return skill;
    // });
    // if (this.selectedClientForSkillsId && this.selectedPIProductForSkillsId) {
    //   Object.keys(this.currentUserSkillsFormData).forEach((key, i) => {
    //     let skillValue = key == constants.MAX_CLAIM_COUNT ? this.currentMaxClaimAssignment : null;
    //     if (this.selectedPIProductForSkillsId !== constants.COORDINATION_OF_BENEFITS_ID) {
    //       if (this.currentUserSkillsFormData[key] != null && key != constants.CONCEPT_CATEGORY_SKILL_1 && key != constants.COB_SKILL && key != constants.COB_SUB_SKILL) {
    //         (key === constants.COB_SUB_SKILL && this.selectedPIProductForSkills.id !== constants.COORDINATION_OF_BENEFITS_ID) ? this.currentUserSkillsFormData[key].values = [] : null;
    //         skillObj.push(this.userManagementSvc.loadingSkillValuesForProduct(key, this.currentUserSkillsFormData, this.masterData, this.selectedClientForSkills, this.selectedPIProductForSkills, skillValue));
    //       }
    //     } else {
    //       if (this.currentUserSkillsFormData[key] != null && key != constants.CONCEPT_CATEGORY_SKILL_1) {
    //         (key === constants.COB_SUB_SKILL && this.selectedPIProductForSkills.id !== constants.COORDINATION_OF_BENEFITS_ID) ? this.currentUserSkillsFormData[key].values = [] : null;
    //         skillObj.push(this.userManagementSvc.loadingSkillValuesForProduct(key, this.currentUserSkillsFormData, this.masterData, this.selectedClientForSkills, this.selectedPIProductForSkills, skillValue));
    //       }
    //     }
    //   });
    // }
    // //API is expecting payload with new and existing skills to update in db. Any common field changes will intersect in array before sending payload to api.
    // resultSkills = [...originalSkills, ...skillObj];

    // //// Updating the max claim count id/values as per master for existing user's
    // resultSkills?.filter(skill => skill.skillType == constants.MAX_CLAIM_COUNT)?.forEach(clmCountSkill => {
    //   let skillDetail = this.masterData.skills?.find(x => x.skillName.toLowerCase() === clmCountSkill.skillType.toLowerCase() && x.clientId === clmCountSkill.clientId && x.prodId === clmCountSkill.prodId);

    //   if (skillDetail && skillDetail?.values[0]?.id) {
    //     clmCountSkill.values = [skillDetail?.values[0].id]
    //   }
    // })


    // return resultSkills.filter(item => {
    //   return (item.prodId === 31 && (this.savedSkillSetForclient.includes(item.clientId) || item.clientId === this.selectedClientForSkillsId)) ? // if product is MAD then we will remove hidden fields from payload
    //     (item.skillType !== constants.BLUE_CARD_ACCESS && item.skillType !== constants.CONCEPT_CATEGORY_SKILL_1 && item.skillType !== constants.CONCEPT_CATEGORY_SKILL && item.skillType !== constants.CLAIM_SYSTEM) :
    //     ((this.savedSkillSet.includes(item.prodId) || item.prodId === this.selectedPIProductForSkillsId) && (this.savedSkillSetForclient.includes(item.clientId) || item.clientId === this.selectedClientForSkillsId)) ? item : "";
    // });
  }

  /**
  * @function loadClientAndProductForSkills Get unique clients and products to repopulate skills based on client and product selections
  */
  loadClientAndProductForSkills() {
    this.distinctClients = this.getDistinctClientsData(this.clientRoleMasterData);
    this.distinctProducts = this.getDistinctProductsData(this.clientRoleMasterData);
  }

  /**
   * @function getDistinctClientsData Gets distinct client data based on master data
   * @param clientRoleMasterData
   */
  getDistinctClientsData(clientRoleMasterData) {
    return clientRoleMasterData.filter(c => c.clientName != null).filter(function (a) {
      var key = a.clientId + '|' + a.clientName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null)).map(({ clientName, clientId }) => ({ name: clientName, id: clientId }));
  }

  /**
   * @function getDistinctProductsData Gets distinct product data based on master data
   * @param clientRoleMasterData
   */
  getDistinctProductsData(clientRoleMasterData) {
    return clientRoleMasterData.filter(c => c.prodName != null).filter(function (a) {
      var key = a.prodId + '|' + a.prodName;
      if (!this[key]) {
        this[key] = true;
        return true;
      }
      return false
    }, Object.create(null)).map(({ prodName, prodId }) => ({ name: prodName, id: prodId }));
  }

  /**
  * @function prePopulateSkillsBasedOnProductAndClient Default or parameterized prepopulation of skills based on client and product selection in skills stepper
  * @param selectedClientName
  * @param selectedProductName
  */
  prePopulateSkillsBasedOnProductAndClient(): void {
    /**
     * Mentioning all use case conditions here for any future reference.
     * 1. By default pre-populate client as Anthem and product as CAD
     * 2. if there is data for multiple clients and products, by default it will always pre-populate point1
     * 3. If user updates the client or product, the respective pre-population logic will happen in respective change events of client and product
     * 4. Hiding and showing the skill fields will also be updated on change event and by default it will be configurations for Anthem client and CAD product
     */
    this.isNewSkillsReady = (this.selectedClientForSkillsId && this.selectedPIProductForSkillsId) ? true : false;
    if (this.isNewSkillsReady) {
      const selectedClientInSkillStep = this.distinctClients.filter(c => c[constants.ID] === this.selectedClientForSkillsId);
      const selectedProductInSkillStep = this.distinctProducts.filter(c => c[constants.ID] === this.selectedPIProductForSkillsId);
      if (this.selectedClientForSkillsId && selectedClientInSkillStep?.length > 0) {
        let selecetdClient = selectedClientInSkillStep.filter(x => x[constants.ID] = this.selectedClientForSkillsId)[0]
        this.selectedClientForSkills = {
          name: selecetdClient[constants.NAME],
          id: selecetdClient[constants.ID],
        }
        this._formData[constants.CLIENT_ID] = this.selectedClientForSkillsId;
        this._formData[constants.CLIENT_NAME] = this.selectedClientForSkills.name;
      }
      if (this.selectedPIProductForSkillsId && selectedProductInSkillStep?.length > 0) {
        let selecetdClient = selectedProductInSkillStep.filter(x => x[constants.ID] = this.selectedPIProductForSkillsId)[0];
        this.selectedPIProductForSkills = {
          name: selecetdClient[constants.NAME],
          id: selecetdClient[constants.ID],
        }
        this._formData[constants.PROD_ID] = this.selectedPIProductForSkillsId;
        this._formData[constants.PROD_NAME] = this.selectedPIProductForSkills.name;
      }

      this.showAndHideSkillsBasedOnProductSelection();
      if (this.showCobSubskill) {
        this.newSkillJSON[0].groupControls.forEach(e => { e.id === constants.COB_SUB_SKILL_ID ? e.visible = true : null; });
      }
      const filteredSkills = this.filterSkillsBasedOnClientAndProduct(this.selectedClientForSkills.id, this.selectedPIProductForSkills.id);
      filteredSkills.forEach(skill => {
        this.newSkillJSON[0].groupControls.forEach(e => {
          (e.name?.toLowerCase() === skill.skillType?.toLowerCase()) ? e[constants.SELECTED_VALUE] = skill.values : null;
        });
        this.currentUserSkillsFormData[skill.skillType] = skill.values;
      });
    }
  }

  /**
   * @function showAndHideSkillsBasedOnProductSelection Shows and hides skill level fields based on product and client selections
   */
  showAndHideSkillsBasedOnProductSelection() {
    this.showCobSubskill = false;
    this.newSkillJSON[0].groupControls.forEach(e => {
      if (this.selectedPIProductForSkills.id === constants.COORDINATION_OF_BENEFITS_ID) {
        e.id === constants.COB_SKILL_ID ? e.visible = true : null;
        e.id === constants.CLAIM_SYSTEM_1 || e.id === constants.CONCEPT_CATEGORY_SKILL_2 || e.id === constants.BLUE_CARD_ID ? e.visible = false : null;
      } else {
        e.id === constants.COB_SKILL_ID || e.id === constants.COB_SUB_SKILL_ID ? e.visible = false : null;
        e.id === constants.CLAIM_SYSTEM_1 || e.id === constants.CONCEPT_CATEGORY_SKILL_2 || e.id === constants.BLUE_CARD_ID ? e.visible = true : null;
      }
      if (this.selectedClientForSkills.id == constants.ANTHEM_CLIENT_ID) {
        e.id === constants.MEMBER_BRAND ? e.visible = true : null;
        e.id === constants.SERVICE_PROVIDER_REGION ? e.visible = true : null;
      }
      (e.name?.toLowerCase() === constants.COB_SKILL.toLowerCase()
        && this.currentUserFormData.skills.find(skill => skill.name = e.name)?.values?.length > 0
        && this.selectedPIProductForSkills.id === constants.COORDINATION_OF_BENEFITS_ID)
        ? this.showCobSubskill = true
        : null;
    });
  }

  /**
   * @function filterSkillsBasedOnClientAndProduct Filters skills based on client and product selection
   */
  filterSkillsBasedOnClientAndProduct(client, product) {
    return this._formData.skills.filter(c => c.clientId === client && c.prodId === product);
  }

  /**
   * @function _onClientSelectionForSkills Triggers on client selection on skills stepper
   */
  _onClientSelectionForSkills(event) {
    this.selectedClientForSkills = event;
    if (this.clientAndProductWarningMessage.client) {
      this.openProductWarning();
    }
    else {
      this.clientAndProductWarningMessage.client = true;
      this.updatePIProductSelection()
    }
  }

  /**
   * @function _onPIProductSelectionForSkills Triggers on product selection on skills stepper
   */
  _onPIProductSelectionForSkills(event) {
    this.selectedPIProductForSkills = event;
    if (this.clientAndProductWarningMessage.product) {
      this.openProductWarning();
    } else {
      this.clientAndProductWarningMessage.product = true;
      this.updatePIProductSelection()
    }

  }

  /**
  * Method Fires warning popup on selection of a different product/client in skills stepper
  * @param event 
  */
  openProductWarning(): void {
    this.openPIProductWarning = Date.now();
  }

  /**
  * Closes warning poup making no changes to the selected value (ie; persisting previous selection)
  */
  closeProductWarning(): void {
    this.openPIProductWarning = false;
    this.selectedPIProductForSkills = {
      name: this._formData[constants.PROD_NAME],
      id: this._formData[constants.PROD_ID]
    }
    this.selectedClientForSkills = {
      name: this._formData[constants.CLIENT_NAME],
      id: this._formData[constants.CLIENT_ID]
    }
    this.selectedPIProductForSkillsId = this._formData[constants.PROD_ID];
    this.selectedClientForSkillsId = this._formData[constants.CLIENT_ID];

  }

  /**
 * @function updatePIProductSelection on click of yes in warning popup
 * Updates the product and client selection to the selected value (ie; persisting latest selection)
 */
 updatePIProductSelection(): void {
    this.openPIProductWarning = false;
    this.newSkillJSON = cloneDeep(this.initialSkillJSON);
    this.skillProductForChanged = true;
    let businessDivision = this.userConfigDataset[0].formJSON.find(x => x.label == constants.BUSINESS_DIVISION).selectedVal;
    this.userManagementSvc.getClientMasterData(this.selectedClientForSkills.id).subscribe((data) => {
      data.skills.forEach((e) => {
        if (e.skillName == constants.MARKET_SKILL && businessDivision == constants.CSBD) {
          e.values.push({
            id: 1001,
            value: "West"
          }, {
            id: 1002,
            value: "Northeast"
          }, {
            id: 1003,
            value: "Central"
          });
        }
      })
      this.masterData = data;
      this.loadSkillsDD(true);
    });
  }

  /** Cleaning memory leaks */
  ngOnDestroy() {
    (this.roleDefSubscription) ? this.roleDefSubscription.unsubscribe() : null;
  }

  /**
   * filter role options based on the selection
   * Remove "authenticator" if "verifier" is selected and vice versa
   * @param selectedRoles 
   * @param roleOptions 
   */
  filterRoles(selectedRoles, roleOptions) {
    let verifierIdRole = roleOptions.find(x => x.label == VERIFIER);
    let authenticatorIdRole = roleOptions.find(x => x.label == AUTHENTICATOR);
    if (verifierIdRole && selectedRoles.find(x => x == verifierIdRole.value)) {
      let index = roleOptions.indexOf(authenticatorIdRole);
      if (index !== -1) {
        roleOptions.splice(index, 1);
      }
    }
    else if (authenticatorIdRole && selectedRoles.find(x => x == authenticatorIdRole.value)) {
      let index = roleOptions.indexOf(verifierIdRole);
      if (index !== -1) {
        roleOptions.splice(index, 1);
      }
    }
    return roleOptions;
  }

  /**
 * @function clearSelectionDropdown method to unselect the client and product on stepper 3
 */
  clearSelectionDropdown(whatToClear) {
    if (whatToClear === "all") {
      this.selectedPIProductForSkillsId = null;
      this.selectedClientForSkillsId = null;
      this.clientAndProductWarningMessage = { product: false, client: false }
    } else {
      this.selectedPIProductForSkillsId = null;
      this.clientAndProductWarningMessage.product = false;
    }
  }

}

class UsersClientRoleForm {
  name: string;
  formJSON: FormJsonClass[] = [];
}
class FormJsonClass {
  options?: any[] = [];
  optionName?: string;
  optionValue?: string;
  label?: string;
  type?: string;
  multiple?: boolean;
  closeOnSelect?: boolean;
  name?: string;
  column?: string;
  disabled?: boolean;
  hidden?: boolean;
  value?: string;
  key?: string;
  id?: string;
  allowUnique: boolean;
  selectedVal?: any;
  relationship?: any;
  required?: boolean;
  visible?: boolean;
}
class UsersClientPreviewRoleForm {
  name: string;
  formJSON: PreviewFormJsonClass[] = [];
}

class PreviewFormJsonClass {
  options?: any[] = [];
  optionName?: string;
  optionValue?: string;
  label?: string;
  type?: string;
  multiple?: boolean;
  closeOnSelect?: boolean;
  name?: string;
  column?: string;
  disabled?: boolean;
  hidden?: boolean;
  value?: string;
  key?: string;
  id?: string;
  selectedVal?: any;
  relationship?: any;
  visible?: boolean;
}