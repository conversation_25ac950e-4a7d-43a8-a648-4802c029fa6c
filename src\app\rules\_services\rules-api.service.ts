import { Injectable } from '@angular/core';
import { of, Observable, throwError, forkJoin } from 'rxjs';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { constants } from '../rules-constants'

const solutionId = "claimsol1";

@Injectable({
  providedIn: 'root'
})

export class RulesApiService {



  ruleLevelToBeOpened: string = "Global";

  constructor(private http: HttpClient) { }
  /**
   * calls ECP save rule enso API 
   */
  createEditRule(createEditData) {
    let headers = {
      source: "DBG"
    }
    return this.http.post<any>(`${environment.validatorSvc}/proxy/ecp/rule/save`, createEditData, { 'headers': headers });
  }

  /**
   * Function Gets config for columns in table
   */
  getColumnConfigJsonDuplicate(url): Observable<any> {
    return this.http.get(url);
  }

  /**
   * calls ECP list rule enso API 
   */
  getListOfRules(constraints: { clientId?: string, ruleId?: any }): Observable<any> {
    let requestPayload = {};
    let payload = {};
    if (constraints?.ruleId) {
      payload[constants.RULE_ID] = constraints.ruleId;
      if (this.ruleLevelToBeOpened == constants.GLOBAL) {
        payload[constants.RULE_LEVEL] = constants.GLOBAL;
      }
    }
    requestPayload = {
      "data": payload
    };
    return this.http.post<any>(`${environment.validatorSvc}/proxy/ecp/api/rule/list`, requestPayload).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * calls ECP get master data enso API 
   */
  getMasterData(): Observable<any> {
    let requestPayload = {};
    return this.http.post<any>(`${environment.validatorSvc}/proxy/ecp/rules/GetFields`, requestPayload).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Forks enso master data & get rule API 
   */
  getAllViewEditRuleAPIs(ruleId): Observable<any> {
    let masterData = this.getMasterData();
    let ruleInfo = this.getListOfRules({ "ruleId": ruleId });
    //uncomment below line once E2E is done
    //let inventoryStatusData = this.getInventoryStatusData();
    return forkJoin([masterData, ruleInfo]);
  }

  /**
   * calls ECP delete rule enso API 
   */
  deleteRule(deleteRequest, headers) {
    return this.http.post<any>(`${environment.validatorSvc}/proxy/ecp/api/rule/delete`, deleteRequest, { headers }).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * calls ECP file upload enso API 
   */
  addFilesToRules(formData, ruleLevel) {
    let headers = {
      'rule_level': ruleLevel
    };
    return this.http.post<any>(`${environment.validatorSvc}/proxy/ecp/file/upload`, formData, { headers }).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * calls ECP get files for rules enso API 
   */
  getFileDetailsOfRules(ruleId, ruleLevel, versionSeq?: any) {
    //Merge Update payload 
    let requestPayloadData = {
      "rule_id": ruleId,
      "rule_level": ruleLevel,
      "version_seq": 2,
      "reinstate": false
    };
    let headers = {
      'x-api-key': "c385f1ef-fa35-4eb0-a2b4-bd0f53b571fd"
    };
    return this.http.post<any>(`${environment.rulesDomainUrl}/file/get`, requestPayloadData, { headers });
  }

  /**
   * Method To get static data set for setting inventory status
   */
  getInventoryStatusData() {
    return this.http.get(environment.inventoryDomainUrl + 'api/dbg-inventorydomain/crosswalk/getsysstatusval/PI-Inventory/Inventory Status');
  }

  /**
   * Method To save multiple criteria file and Query builder mappings by calling enso API
   * changed to authorization domain
   */
  uploadFileAndQBCriteria(formData: any, ruleLevelIndicator?: any) {
    let headers;
    if (ruleLevelIndicator != constants.GLOBAL_LEVEL) {
      headers = { 'x-api-key': "750d99b6-fb7f-4a55-a09b-1df2b6052a30" }
    } else {
      headers = { 'x-api-key': "750d99b6-fb7f-4a55-a09b-1df2b6052a30", 'rule_level': constants.GLOBAL }
    }
    return this.http.post<any>(`${environment.validatorSvc}/proxy/ecp/multicriteria/file/upload`, formData, { headers }).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * calls ECP get files for rules enso API 
   */
  getMultipleCriteriaFile(ruleId, corpusId, levelIndicator?: any) {
    let requestPayloadData;
    if (levelIndicator != constants.GLOBAL_LEVEL) {
      requestPayloadData = { "rule_id": ruleId, "corpus_id": corpusId }
    } else {
      requestPayloadData = { "rule_id": ruleId, "corpus_id": corpusId, "rule_level": constants.GLOBAL }
    }
    let options: any = { observe: 'response', responseType: 'text' as 'json' }
    return this.http.post<any>(`${environment.validatorSvc}/proxy/ecp/file/download`, requestPayloadData, options);
  }

  /**
   * Method to get any json's from the assets
   * @returns 
   */
  getAssetsJson(url): Observable<any> {
    return this.http.get<any>(url).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

   /**
   * function to get rule history data list from api using rule id
   * @returns 
   */
  getRuleHistoryData(ruleId: number, ruleLevel: string) {
    const payload = {
      data: {
        rule_id: ruleId,
        rule_level: ruleLevel
      }
    };
    return this.http.post<any>(`${environment.validatorSvc}/proxy/api/dbg-authorization/ecp/api/rule-history`, payload).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * function to get username list from api using clientId
   * @returns 
   */
  getUserNameForClient() {
    let currentClientId = sessionStorage.getItem('clientId');
    return this.http.get<any>(environment.validatorSvc + '/proxy/api/dbg-authorization/user/getAllUsersByClientId/' + currentClientId).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * function to get concept ids and execution ids based on concept state
   * @returns 
   */
  getConceptExecutionByConceptState(conceptState: string, businessDvn: string) {
    let currentClientId = sessionStorage.getItem('clientId');
    return this.http.get<any>(environment.inventoryInsightUrl + '/api/dbg-inventoryinsight/invinsight/getLatestExtIdByCncptSt?cncptST=' + conceptState + '&clientId=' + currentClientId + '&businessDvn=' + businessDvn).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Trigger Perform Analysis
   * @returns 
   */
  triggerPerformAnalysis(payload: any) {
    return this.http.post<any>(environment.validatorSvc + '/proxy/api/dbg-authorization/ecp/api/rule/generate-preview', payload)
      .pipe(
        catchError(err => {
          return throwError(err);
        }));
  }


    /**
  * Method To call api for table data when impact report is ready and user navigates from notification   
  */
  getImpactReport(requestPayload) {
    return this.http.post<any>(`${environment.validatorSvc}/proxy/api/dbg-authorization/ecp/api/rule/get-preview`, requestPayload).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

}
