import { HttpErrorResponse, HttpEvent, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { catchError, Observable, throwError } from "rxjs";
import { AUTH_CONFIG } from "../_constants/menu.constant";
import { externalAuthenticationConstants } from "./helpers.constants";
import { AuthService } from "../_services/authentication.services";
import { ToastService } from "../_services/toast.service";
import { environment } from "../../environments/environment";
import { CookieService } from "ngx-cookie-service";

@Injectable()
export class CommonHeader implements HttpInterceptor {
    constructor(private authService: AuthService,
        private toastService: ToastService, private cookieService: CookieService) { }
    intercept(
        request: HttpRequest<any>,
        next: HttpHandler
    ): Observable<HttpEvent<any>> {
        let appToken = sessionStorage.getItem('appToken');
        let cadDbgToken = sessionStorage.getItem('cadDbgToken');
        let userToken = this.cookieService.get('userToken');

        //Review Required
        let cliendDetails = JSON.parse(sessionStorage.getItem('clientDetails'));
        let cliendId = '59';

        if (cliendDetails) {
            cliendId = cliendDetails['clientId'] ?? '59';
        }

        if (request.url.includes(environment.productDomainUrl) ||
            request.url.includes(environment.clientPreferenceDomainUrl) ||
            request.url.includes(environment.validatorSvc) || 
            request.url.includes(environment.authService)) {
            request = request.clone({
                setHeaders: {
                    'x-appid': 'PORTAL',
                    'userToken': `${userToken}`,
                    'appToken': `${appToken}`,
                    'cad-dbg': cadDbgToken,
                    'clientId': '59',
                    'loginType': environment.loginType,
                    ...(environment.enableOkta && { source: 'internal' })
                }
            });
        }

        return next.handle(request).pipe(
            catchError((error) => {
                if (error instanceof HttpErrorResponse) {
                    const httpErrorCode: number = error[AUTH_CONFIG.STATUS];
                    switch (httpErrorCode) {
                        case 400:
                            return throwError(error);
                        case 401:
                            if (error.error.message == externalAuthenticationConstants.NO_ROLES) {
                                this.authService.isUnAuthorized = true;
                                this.logoutUser();
                            }
                            else if (error.error.message == externalAuthenticationConstants.TOKEN_EXPIRED) {
                                //C2P return this.handleUnauthorizedRequest(request, next);
                            }
                            else if (error.error.message == externalAuthenticationConstants.ACCOUN_DEACTIVATED) {
                                this.authService.isDeactivated = true;
                                this.logoutUser();
                            }
                            else if (error.error.message == externalAuthenticationConstants.USER_NOT_FOUND_IN_DB) {
                                this.authService.isUnAuthorized = true;
                                this.logoutUser();
                            }

                            return throwError(error);

                        case 403:
                            this.toastService.setErrorNotification({
                                notificationHeader: externalAuthenticationConstants.WARNING,
                                notificationBody: externalAuthenticationConstants.INSUFFICIENT_PRIVILEGE_MESSAGE,
                            });
                            return throwError(externalAuthenticationConstants.INSUFFICIENT_PRIVILEGE_MESSAGE);

                        default:
                            return throwError(error);
                    }
                } else {
                    return throwError(error);
                }
            })
        );
    }

    /**
    * Logout the user
    */
    logoutUser(): void {
        this.authService.logoutPortal();
    }
}