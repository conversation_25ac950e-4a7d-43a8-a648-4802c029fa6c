import { Component, OnInit, ViewEncapsulation, AfterViewInit, Input, EventEmitter, Output, OnDestroy, ElementRef, AfterContentInit } from '@angular/core';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ActivatedRoute } from '@angular/router';
import { list } from './../constant';
import { AuthService } from 'src/app/_services/authentication.services';
@Component({
  selector: 'app-client-preference-list',
  templateUrl: './client-preference-list.component.html',
  styleUrls: ['./client-preference-list.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class ClientPreferenceListComponent implements OnInit, AfterViewInit, OnDestroy {

  public clientPreferenceData = [];
  public totalEntries: number;
  public clientPreferenceColConfig: any;
  public ruleDashbordTableRowhg: number = 45;
  public tableRedraw: any;
  isListing: boolean = true;
  enablePage: any;
  public selectedRowData: any;
  @Input() isReadOnly: boolean = false;
  @Input() clientSelected: string;
  @Output() activePageInfoEvent = new EventEmitter();
  dataExchangePageName: any;
  url: any;
  showTable = false;
  clientId: any;
  customExport: any = {
    enabled: true,
    fileName: 'dataExchange.xls'
  };
  @Input()
  set navigate(val) {
    if (val) {
      this.receiveFromAddEdit()
    }
  }
 public kebabOptions: any = [
    { label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.5H2.75C2.61193 2.5 2.5 2.61193 2.5 2.75V13.25C2.5 13.3881 2.61193 13.5 2.75 13.5H7.25C7.66421 13.5 8 13.8358 8 14.25C8 14.6642 7.66421 15 7.25 15H2.75C1.7835 15 1 14.2165 1 13.25V2.75C1 1.7835 1.7835 1 2.75 1H8.25C10.8734 1 13 3.12665 13 5.75V7H8.75C7.7835 7 7 6.2165 7 5.25V2.5ZM8.5 2.50947V5.25C8.5 5.38807 8.6119 5.5 8.75 5.5H11.4905C11.3691 3.9044 10.0956 2.63085 8.5 2.50947Z" fill="black"/>
<path d="M12.7432 9.64823C12.6935 9.28215 12.3797 9 12 9C11.5858 9 11.25 9.33579 11.25 9.75V11.25H9.75L9.64823 11.2568C9.28215 11.3065 9 11.6203 9 12C9 12.4142 9.33579 12.75 9.75 12.75H11.25V14.25L11.2568 14.3518C11.3065 14.7178 11.6203 15 12 15C12.4142 15 12.75 14.6642 12.75 14.25V12.75H14.25L14.3518 12.7432C14.7178 12.6935 15 12.3797 15 12C15 11.5858 14.6642 11.25 14.25 11.25H12.75V9.75L12.7432 9.64823Z" fill="black"/>
</svg>  Edit Data Exchange`, id: 'edit' },
    { label: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg>  View Data Exchange', id: 'view' }
  ];
  public kebabOptions_Readonly: any = [
    { label: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg>  View Data Exchange', id: 'view' }
  ];
  constructor(private clientApiService: ClientApiService, private route: ActivatedRoute, private elementRef: ElementRef, private authService: AuthService) { }

  ngOnInit(): void {
    this.isReadOnly = !this.authService.isWriteOnly;
    this.isReadOnly ? this.kebabOptions = this.kebabOptions_Readonly : this.kebabOptions
    let client = this.clientSelected;
    setTimeout(() => this.tableRedraw = Date.now(), 2000);
    this.clientId = this.route.snapshot.paramMap.get('clientId');
    this.resPageDetails();
  }
  /**
   * clientPreferenceGridCustomFormatter function
   * @param event 
   * @returns 
   */
  clientPreferenceGridCustomFormatter(event) {
    return `<div class="product-dashboard dropdown-container" tabindex=“-1”>
     <span class="bundles-text-field"> ${event.value} </span>
     <input id=“selector-${event.dataContext.id}” type="checkbox" name="menu" style="display:none;" />
     <label  for=“selector-${event.dataContext.id}” class="three-dots"></label>
     <div class="dropdown">
     <div class="table-action-menu"><i class="fa fa-edit" title="Edit Preference" dataaction="edit" datevalue=${event.dataContext.id}></i>Edit</div>
     <div class="table-action-menu"><i class="fa fa-eye" title="View Criteria"  dataaction="view" datevalue=${event.dataContext.id}></i>View</div>
     </div>
     </div>`
  }



  ngAfterViewInit(): void {

    setTimeout(() => this.tableRedraw = Date.now(), 2000);
  }

  /**
   * redirect to add data exchange screen
   */
  createPrference() {
    if (this.isReadOnly) return
    this.isListing = false;
    this.enablePage = list.ADD;
    this.dataExchangePageName = list.DEADD;
    this.activePageInfoFunction();
  }
  /**
   * editViewPreference function
   * @param event 
   */
  editViewPreference(event: Event): void {
    let currentRow = event['currentRow'];
    let selectedOption = event['text'];
    this.isListing = false;
    this.redrawTable();
    switch (selectedOption) {
      case list.DEEDIT:
        this.selectedRowData = event['currentRow'];
        this.selectedRowData.isView = false;
        this.enablePage = list.EDIT;
        this.dataExchangePageName = list.DEEDIT;
        this.activePageInfoFunction();
        break;
      case list.DEVIEW:
        this.selectedRowData = event['currentRow'];
        this.selectedRowData.isView = true;
        this.enablePage = list.VIEW;
        this.dataExchangePageName = list.DEVIEW;
        this.activePageInfoFunction();
        break;
    }
  }
  /*
   *Method is used to load table based on screen change
   */
  redrawTable() {
    setTimeout(() => this.tableRedraw = Date.now(), 100);
  }
  /**
   *  receiveFromAddEdit event data from child component
   * @param event 
   */
  receiveFromAddEdit() {
    this.showTable = false;
    this.isListing = true;
    this.enablePage = "";
    this.dataExchangePageName = list.DE;
    this.activePageInfoFunction();
    this.resPageDetails();
  }

  /**
   * Method is used get data based on table screen on view,edit,add
   * 'info' carries data of page,row 
   */
  activePageInfoFunction() {
    let info = {
      dataExchangePageName: this.dataExchangePageName,
      selectedRowData: this.selectedRowData,
      name: list.DE,
    }
    this.activePageInfoEvent.emit(info);
  }
  /*
  Fetch all details of data exchange based on CLient 
  */

  resPageDetails() {
    this.clientPreferenceColConfig = {
      "switches": {
        "enableSorting": true,
        "enablePagination": true,
        "enableFiltering": true
      },
      "colDefs": [
        {
          "name": "PRODUCT NAME",
          "field": "productName",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "BUSINESS DIVISION",
          "field": "businessDivision",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "PREFERENCE NAME",
          "field": "preferenceName",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": ""
        },
        {
          "name": "DBG UNIT",
          "field": "dbgUnit",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
          "sortable": false,
        },

        {
          "name": "SYSTEM",
          "field": "system",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
          "sortable": false,
        },
        {
          "name": "INVENTORY TYPE",
          "field": "inventoryType",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
          "sortable": false,
        },
        {
          "name": "TEMPLATE NAME",
          "field": "templateName",
          "filterType": "Text",
          "visible": "True",
          "editorType": "",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "sortable": false,
        },
        {
          "name": "FILE DESTINATION",
          "field": "fileDestination",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
          "sortable": false,
        },
        {
          "name": "FREQUENCY",
          "field": "frequency",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
          "sortable": false,
        },
        {
          "name": "START DATE",
          "filterType": "Calendar",
          "field": "startDate",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
          "sortable": false,
          "dateFormat": 'MM/DD/YYYY'
        },
        {
          "name": "END DATE",
          "filterType": "Calendar",
          "field": "endDate",
          "visible": "True",
          "editorType": "",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "sortable": false,
          "dateFormat": 'MM/DD/YYYY'
        },
        {
          "name": "FILE NAME",
          "field": "fileName",
          "filterType": "Text",
          "visible": "True",
          "editorType": "Text",
          "editorTypeRoot": "",
          "editorTypeLabel": "",
          "editorTypeValue": "",
          "sortable": false,
        },

      ]
    };
    this.clientPreferenceData = [];
    let url = this.clientId;
    if (this.clientApiService.selectedProductName) {
      url = url + '/' + this.clientApiService.selectedProductName;
    }
    this.clientApiService.getClientPreferences(url)
      .subscribe(data => {
        if (data) {
          this.clientPreferenceData = data;
          this.totalEntries = data.length
        }
        this.showTable = true;
        this.redrawTable();
      }, err => {
        this.clientPreferenceData = [];
        this.totalEntries = 0;
        this.showTable = true;
        this.redrawTable();
      });
  }



  ngOnDestroy() {
    this.elementRef.nativeElement.remove();
  }
}
