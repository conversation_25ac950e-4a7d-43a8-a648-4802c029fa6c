app-user {
    
    .header-blue {
        color: #390681
    }
    .header-blue-client {
        margin-top: 10px;
        margin-bottom: -20px;
        color: #390681;
        font-size: 18px;
        font-family: 'elevance-semi-bold';
    }
    marketplace-table .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: -7px;
        font-size: 14px;
    }

    .btn-active {
        background: #D9F5F5;
        width: 100%;
        border: 1px solid #00BBBA;
    }

    .btn-inactive {
        background: #F5F5F5;
        width: 100%;
        border: 1px solid #231E33;
    }

    .btn-wrap-text {
        overflow: hidden;
        white-space: nowrap;
        display: inline-block;
        text-overflow: ellipsis;
    }

    .button-row{
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }
    .right-button{
        display:flex;
        gap:10px
    }
   /*  .left-button{
        margin-right: auto;
    } */
    .left-button-container{
        display: flex;
        width: 100px;
        justify-content: flex-start;
    }
    .right-button-container{
        display: flex;
        justify-content: flex-end;
    }

    .page-wrapper {
        padding: 1rem 1rem;

        .page-header {
            h3 {
                color: #5009B5;
                margin-top: 8px;
                margin-bottom: 1rem;
                font-family: 'elevance-medium';
            }

        }

        .segment-container {
            margin-top: 20px;
        }

        .tile-container {
            .div-border-right {
                border-right: solid blue;
            }
        }

        .btn-holder {
            display: flex;
            justify-content: flex-end;
        }

        .users-audit-table_container {
            marketplace-table .permission-denied {
                color: #da1e28;
                font-size: 1rem;
                display: flex;
                align-items: center;
            }

            .permission-granted {
                color: #00a551;
                font-size: 1rem;
                display: flex;

                .changed {
                    font-size: 8px;
                    margin-top: -8px;
                    color: #da1e28;
                }
            }

            .slickgrid-container .grid-canvas .slick-row {
                .slick-cell {
                    display: flex;
                    justify-content: center;
                }
            }
        }
    }

    marketplace-popup {
              
        
        .status-success {
            background-color: #00a551;
            border: 1px solid #ffffff;
            color: #fff;
            padding: 0 8px;
            position: absolute;
            right: 3rem;
            top: 10px;
        }

        .note-color {
            color: #da1e28;
            font-size: 1rem;
            margin-top: 0px;
            margin-left: 5px;
            align-items: center;
        }

        marketplace-dynamic-form {
           
            .form-group {
                marketplace-switch .switch-holder {
                    flex-direction: row;
                    float: left;
                    align-items: inherit;

                    .switch {
                        margin-right: 1rem;
                        margin-left: 1rem;
                    }
                }
            }

            .form-row.form-element-group {
                border-radius: 8px;
                margin: 0 0.4rem;
                padding: 4px 8px;
                border-style: dashed;
            }

            .grp-header-blue {
                margin-top: 8px;
                color: #390681;
                font-size: 18px;
                font-family: 'elevance-semi-bold';
            }

        }


        .skill-container {
            height: 510px;
            position: relative;
            marketplace-select .select-holder .ng-select#memberBrand ng-dropdown-panel,
            marketplace-select .select-holder .ng-select#serviceProviderRegion ng-dropdown-panel,
            marketplace-select .select-holder .ng-select#conceptStateSelect ng-dropdown-panel{
                top: auto;
                bottom: 100%;
            }

            .show-more-container {
                position: absolute;
                z-index: 2;
                font-size: 16px;
                right: 1.8rem;
                top: 6px;
                cursor: pointer;

                .angle-icon {
                    color: #794cff;
                }
            }

        }

        .show {
            overflow: visible;
            height: auto;
        }

        .action-container {
            position: relative;

            .flex-note {
                display: flex;
                justify-content: center;
            }
        }

        .refresh-btn {
            right: 0;
        }

        .action-btns {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 20px;
            position: relative;
            z-index: 2;
        }

        .action-container marketplace-table {
            margin-top: -2rem;
        }

        .label-title {
            color: #231e33 !important;
            font-family: 'elevance-semi-bold';
        }

        .label-value {
            font-weight: 700;
            font-size: 16px;
            color: #666;
            word-break: break-word;
            font-family: 'elevance';

        }

        .uimp-key-values {
            margin: 0.8rem 0;
        }

        .uimp-audit-key-values {
            margin: 1.5rem 0;
        }

        .border-style {
            border-right: 1px solid #ccc;
        }

        .card .card-no-border {
            border: none !important;
            padding: 0px 25px 0px 25px;
        }

    }

    .red-font {
        color: #FF0000;
        text-align: center;
    }
    
    .btn-approve,
    .btn-approve:hover {
        background: #5009b5;
        font-family: 'elevance';
        font-weight: 500;
        font-size: 15px;
        line-height: 17px;
        color: #fff;
        width: 100px;
        outline: 0;
        // box-shadow: 0 0 0 0rem;
    }

    .headerbadge {
        font-weight: 500;
        height: 24px
    }

    .badge {
        padding: 4px 16px 4px 14px;
        display: inline
    }
       
    .badge-active {
        background-color: #00A551 !important;
        color: #fff;
    }

    .badge-inactive {
        background-color: #DA1E28 !important;
        color: #fff;
    }

    .badge-userStatus {
        background-color: #FFCD54 !important;
        // color: #fff;
    }



    .btn-reject,
    .btn-reject:hover {
        background: #C5C4C4;
        font-family: 'elevance';
        font-weight: 500;
        font-size: 15px;
        line-height: 17px;
        color: #000;
        width: 100px;
        outline: 0;
        // box-shadow: 0 0 0 0rem;
    }

    .asterisk {
        color: red
    }


    .plus-hide {



        marketplace-form-repeater .form-repeater--holder .form-repeater__container {
            border: 1px solid #E4E4E4;
            width: 101%;
            margin:-7px;
            border-style: dashed;
            padding: 1px;
            border-radius:6px;
            padding-top: 10px;
        } 
        .form-repeater__icon-holder{
            display: none !important;
        }
        marketplace-dynamic-form
        .form-row 
        {
            width: 109%;
        }
            
    }
    .userId-as-hyperlink {
        cursor: pointer;
        color: #08c !important;
    }
    .userId-as-hyperlink:hover {
        text-decoration: underline;
    }
}