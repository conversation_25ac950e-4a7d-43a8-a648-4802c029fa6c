<div class="logout-container">
  <div class="left-container"></div>
  <div class="logo-holder"></div>
  <div class="logout-info-container">
    <div class="logout-heading">
      @if(isUnAuthorized){
      <div class="un-authorize-error">
        You have insufficient privileges to access the application. Please reach out to the administrator to get access.
      </div>
      }
      @if(isDeactivated){
      <div class="un-authorize-error">
        You don't have access to the application as your account is inactivated, please contact your administrator.
      </div>
      }
      @if(isTimedout){
      <div class="un-authorize-error">
        Your session has been logged out due to inactivity. Please sign in again.
      </div>
      }
      <div class="logout-icon"><img src="/assets/icons/Logout.svg" alt="logout" /></div>
      <h3 [innerHTML]="headerText"></h3>
    </div>
    <div class="info-logout">

    </div>
    @if(!isTimedout){
    <div class="info-logout">
      <span [innerHTML]="logOutMessage"></span>
    </div>
    }
    <div class="login-button">
      <marketplace-button [type]="'primary'" [label]="'Login'" (onclick)="loginClick($event)">
      </marketplace-button>
    </div>
  </div>
</div>