app-change-password {
    .spinner-border {
      display: block;
      position: fixed;
      top: calc(50% - (58px / 2));
      right: calc(50% - (58px / 2));
      width: 4rem;
      height: 4rem;
    }
    .body-content {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      height: 100vh;
  
      .page-header-container {
        display: flex;
        width: 400px;
        justify-content: center;
        align-items: center;

        .page-header-text{
          font-family: var(--font)!important;
          font-size: 20px;
          font-style: normal;
        }
      

        img {
          width: 100px;
          display: flex;
          align-items: center;
        }

        .seperator{
          width: 1px;
          background: black;
          display: flex;
          align-items: center;
          margin: 6px;
          border-radius: 8px;
          height: 1rem;
        }
  
      }
  
      .elevated-card {
        margin-top: 1em;
        margin-bottom: 5em;
        box-shadow: 0 5px 5px -3px #0003, 0 8px 10px 1px #00000024, 0 3px 14px 2px #0000001f;
        height: fit-content;
        width: 440px;
        display: flex;
        align-items: center;
        padding: 20px 25px 60px 25px;
        margin: 10px 0px;

        .signin {
          margin-top: 1em;
        }

        .page-header {
          h3 {
            font-weight: bold;
          }
        }

        a {
          margin: 20px 0px;
          font-size: 12px;
          text-align: center;
        }
  
        marketplace-dynamic-form {
          margin: 10px 0px;

          input.form-control#answer {
            border-width: 0 0 1px 0;
            margin-top: 10px
          }

          sub, sup {
            color: red;
          }
          #parentanswer.form-group .form-label{
            display: none;
          }
        }
  
        .red-font {
          color: #FF0000;
          padding: 0px 0px 14px 14px;
          font-size: 12px;
        }

        .redBorder {
          border-color: red !important;
        }
      }

      .elevated-card-securityquestion {
        box-shadow: 0 5px 5px -3px #0003, 0 8px 10px 1px #00000024, 0 3px 14px 2px #0000001f;
        height: fit-content;
        width: 640px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 50px 30px 60px 30px;
        margin: 10px 0px;
  
        .page-header {
          h3 {
            font-weight: bold;
            display: flex;
            justify-content: center;
            font-size: 1.5rem;
          }
  
          .text-content {
            display: flex;
            justify-content: center;
            text-align-last: center;
            font-size: 14px;
          }
        }
  
        a {
          margin: 20px 0px;
          font-size: 12px;
          text-align: center;
        }
  
        .btn-holder {
          margin-top: 25px;
        }
      }
    }
  
  }