import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MPUIInputModule } from 'marketplace-input';
import { MPUINotificationModule } from 'marketplace-notification';
import { MPUIModalDialogModule } from 'marketplace-popup';
import { MPUIButtonModule } from 'marketplace-button';
import { MPUITabsModule } from 'marketplace-tabs';
import { MPUITableModule } from 'marketplace-table';
import { MPUIFileUploadModule } from 'marketplace-file-upload';
import { MPUIFileParserModule } from 'marketplace-file-parser';
import { MPUIQueryBuilderModule } from 'marketplace-query-builder';
import { MPUITextareaModule } from 'marketplace-textarea';
import { MPUISwitchModule } from 'marketplace-switch';
import { MPUIDatePickerModule } from 'marketplace-date-picker';
import { MPUIDynamicFormModule } from 'marketplace-form';
//import { MPUISimpleTableModule } from 'marketplace-simple-table';
//import { MPUICardsModule } from 'marketplace-cards';
//import { MPUISearchListModule } from 'marketplace-search-list';
import { MPUICheckboxModule } from 'marketplace-checkbox';
import { MPUIBreadcrumbModule } from 'marketplace-breadcrumb';
//import { MPUIPickListModule } from 'marketplace-picklist';
import { ViewRoleComponent } from './view-role/view-role.component';
import { EditRoleComponent } from './edit-role/edit-role.component';
import { RolesComponent } from './roles.component';
import { RolesRoutingModule } from './roles-routing.module';
import { MPUIProgressBarModule } from 'marketplace-progressbar';
import { MPUIStepperModule } from 'marketplace-stepper';
import { MPUISegmentedControlModule } from 'marketplace-segmented-control';
import { MPUIViewListModule } from 'marketplace-view-list';


@NgModule({
  declarations: [
    ViewRoleComponent,
    EditRoleComponent,
    RolesComponent
  ],
  imports: [
    CommonModule,    
    MPUIInputModule,
    MPUINotificationModule,
    MPUIModalDialogModule,
    MPUIButtonModule,
    MPUITabsModule,
    MPUIFileUploadModule,
    MPUIProgressBarModule,
    MPUIFileParserModule,
    MPUITableModule,
    MPUIBreadcrumbModule,
    MPUIQueryBuilderModule,
    MPUITextareaModule,
    MPUISwitchModule,
    MPUIDatePickerModule,
    // MPUISimpleTableModule,
    // MPUIPickListModule,
    MPUIDynamicFormModule,
    // MPUICardsModule,
    // MPUISearchListModule,
    MPUICheckboxModule,
    RolesRoutingModule,
    MPUIStepperModule,
    MPUIViewListModule,
    MPUISegmentedControlModule,   
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class RolesModule { }
