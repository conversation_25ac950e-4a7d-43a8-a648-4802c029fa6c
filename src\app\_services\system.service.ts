import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { catchError } from 'rxjs/operators';


@Injectable({
  providedIn: 'root'
})
export class SystemService {

  constructor(private http: HttpClient) { }

  getAllSystem() {
    return this.http.get(environment.validatorSvc + '/invProxy/system/list');
  }

  getSystemFileType() {
    return this.http.get("./assets/json/settings/systemFileType.json");

    /**Merge */
    //return this.http.get("../../../../../assets/json/systemFileType.json");
  }

  /**
   * To get all the Client Details with System ID
   * @param systemId
   * @returns 
   */
  getAllClients(systemId: any): Observable<any[]> {

    let url = environment.clientPreferenceDomainUrl + '/clientPreference/getMappedClient/' + systemId
    return this.http.get<any[]>(url).pipe(
      catchError(err => of([]))
    );

  }

  /**
   * 
   * @returns All Inventory Types
   */
  getAllInventoryTypes() {
    return this.http.get(environment.validatorSvc + '/invProxy/system/formdata');
  }

  /**
   * To Create New System
   * @param newSysPayload 
   * @returns 
   */
  createNewSystem(newSysPayload: any): Observable<any> {
    let url = environment.validatorSvc + "/invProxy/system/create"
    return this.http.post(url, newSysPayload).pipe(
      catchError(err => of(err))
    );
  }

  /**
 * To Edit Existing System
 * @param editSysPayload 
 * @returns 
 */
  editExistingSystem(editSysPayload: any): Observable<any> {
    let url = environment.validatorSvc + "/invProxy/system/update"
    return this.http.put(url, editSysPayload).pipe(
      catchError(err => of(err))
    );
  }

  /**
* To Fetch Single System
* @param systemId
* @returns 
*/
  fetchSingleSystem(systemId: any): Observable<any> {
    let url = environment.validatorSvc + "/invProxy/system/detail/" + systemId
    return this.http.get(url).pipe(
      catchError(err => of([]))
    );
  }

}
