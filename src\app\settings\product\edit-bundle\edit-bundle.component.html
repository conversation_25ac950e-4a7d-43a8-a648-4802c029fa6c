<div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>

<div class="fixed-nav bg-gray">
  <marketplace-breadcrumb
    [dataset]="breadcrumbDataset"
    (onSelection)="breadcrumSelection($event)"
  >
  </marketplace-breadcrumb>
  <div class="dynamic-form-container">
    <span class="title-topheading">
      <a (click)="backToPreviousPage()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="icon icon-tabler icons-tabler-filled icon-tabler-circle-chevron-left" aria-hidden="true" focusable="false" style="vertical-align: middle;">
              <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
              <path d="M17 3.34a10 10 0 0 1 5 8.66c0 5.523 -4.477 10 -10 10s-10 -4.477 -10 -10a10 10 0 0 1 15 -8.66m-3.293 4.953a1 1 0 0 0 -1.414 0l-3 3a1 1 0 0 0 0 1.414l3 3a1 1 0 0 0 1.414 0l.083 -.094a1 1 0 0 0 -.083 -1.32l-2.292 -2.293l2.292 -2.293a1 1 0 0 0 0 -1.414"/>
          </svg>
      </a>
      Edit Bundle
  </span>
  
    <marketplace-dynamic-form
      [formJSON]="generalDetailsJson"
      [isSubmitNeeded]="false"
      (onValueChange)="mapValuesToJson($event)"
      *ngIf="isFormready || showLoader"
    >
    </marketplace-dynamic-form>
  </div>

  <span class="btn-span">
    <marketplace-button
      [label]="'Cancel'"
      [type]="'secondary'"
      [name]="'secondary'"
      (onclick)="backToPreviousPage()"
    >
    </marketplace-button>

    <marketplace-button
  [label]="'Update bundle'"
  [type]="'primary'"
  [name]="'primary'"
  [enabled]="isEnabled || isLoading"
  (onclick)="validateEditDynamicForms()"
>
  <svg *ngIf="isLoading" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 512 512" fill="currentColor" aria-hidden="true" focusable="false" class="spinner">
    <path d="M304 48h-96c-8.8 0-16 7.2-16 16v32c0 8.8 7.2 16 16 16h96c8.8 0 16-7.2 16-16V64c0-8.8-7.2-16-16-16zm-96 432h96c8.8 0 16-7.2 16-16v-32c0-8.8-7.2-16-16-16h-96c-8.8 0-16 7.2-16 16v32c0 8.8 7.2 16 16 16zm158.6-266.3l22.7-22.6c6.2-6.2 6.2-16.4 0-22.6l-22.7-22.6c-6.2-6.2-16.4-6.2-22.6 0l-22.6 22.7c-6.2 6.2-6.2 16.4 0 22.6l22.6 22.6c6.3 6.2 16.4 6.2 22.6-.1zm22.6 138.6l-22.7-22.6c-6.2-6.2-16.4-6.2-22.6 0l-22.6 22.7c-6.2 6.2-6.2 16.4 0 22.6l22.7 22.6c6.2 6.2 16.4 6.2 22.6 0l22.6-22.7c6.3-6.2 6.3-16.4.1-22.6zM48 256c0 8.8 7.2 16 16 16H96c8.8 0 16-7.2 16-16s-7.2-16-16-16H64c-8.8 0-16 7.2-16 16zm384 0c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16s7.2 16 16 16h32c8.8 0 16-7.2 16-16zm-118.6-154.6l22.6 22.7c6.2 6.2 16.4 6.2 22.6 0l22.6-22.7c6.2-6.2 6.2-16.4 0-22.6l-22.7-22.6c-6.2-6.2-16.4-6.2-22.6 0l-22.6 22.7c-6.3 6.2-6.3 16.4-.1 22.6zM416 256c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16s7.2 16 16 16h32c8.8 0 16-7.2 16-16zM159.4 137.4l-22.6 22.6c-6.2 6.2-6.2 16.4 0 22.6l22.7 22.6c6.2 6.2 16.4 6.2 22.6 0l22.6-22.7c6.2-6.2 6.2-16.4 0-22.6l-22.6-22.6c-6.3-6.2-16.4-6.2-22.6 0zM48 256c0-8.8-7.2-16-16-16H16c-8.8 0-16 7.2-16 16s7.2 16 16 16h16c8.8 0 16-7.2 16-16zm118.4 118.4l22.6-22.7c6.2-6.2 6.2-16.4 0-22.6l-22.7-22.6c-6.2-6.2-16.4-6.2-22.6 0l-22.6 22.7c-6.2 6.2-6.2 16.4 0 22.6l22.6 22.6c6.3 6.3 16.4 6.2 22.7.1zm82.6 32c-8.8 0-16-7.2-16-16v-16c0-8.8 7.2-16 16-16s16 7.2 16 16v16c0 8.8-7.2 16-16 16z"/>
  </svg>
</marketplace-button>

  </span>
</div>
<div>
  <marketplace-popup
    [open]="inActivationPopup"
    [size]="'small'"
    (onClose)="cancelPopUp()"
  >
    <div mpui-modal-header>
      <div class="modal-header-custom">
        <h4 class="modal-title custom-title">Attention !</h4>
      </div>
    </div>
    <div mpui-modal-body>
      <ng-container>
        <p class="pad-20">
          You are inactivating the bundle so that respective fee schedules will
          become inactive.
        </p>
      </ng-container>
    </div>
    <div mpui-modal-footer>
      <marketplace-button
        [label]="'Cancel'"
        [type]="'cyan-secondary'"
        [name]="'cyan-secondary'"
        (onclick)="cancelPopUp()"
      >
      </marketplace-button>
      <marketplace-button
        [label]="'Continue'"
        [type]="'primary'"
        [name]="'primary'"
        (onclick)="updateBundleData(true)"
      >
      </marketplace-button>
    </div>
  </marketplace-popup>
</div>

<div>
  <marketplace-popup
    [open]="activationPopup"
    [size]="'small'"
    (onClose)="cancelPopUp()"
  >
    <div mpui-modal-header>
      <div class="modal-header-custom">
        <h4 class="modal-title custom-title">Attention !</h4>
      </div>
    </div>
    <div mpui-modal-body>
      <ng-container>
        <p class="pad-20">
          This will activate the Product bundle. Ensure to activate the fee
          schedules associated with the bundle if there exists one.
        </p>
      </ng-container>
    </div>
    <div mpui-modal-footer>
      <marketplace-button
        [label]="'Cancel'"
        [type]="'cyan-secondary'"
        [name]="'cyan-secondary'"
        (onclick)="cancelPopUp()"
      >
      </marketplace-button>
      <marketplace-button
        [label]="'Ok'"
        [type]="'primary'"
        [name]="'primary'"
        (onclick)="updateBundleData(false)"
      >
      </marketplace-button>
    </div>
  </marketplace-popup>
</div>

<marketplace-popup
  [open]="editErrOpenModel"
  [size]="'small'"
  (onClose)="editErrClosePopup()"
  [ngStyle]="{ display: popupDisplayStyle }"
>
  <div mpui-modal-header>
    <h5 class="modal-title custom-title">Attention !</h5>
  </div>
  <div mpui-modal-body class="custom-message">
    <p class="pad-35">Please fill all the mandatory fields</p>
  </div>
  <div mpui-modal-footer>
    <marketplace-button
      [label]="'OK'"
      [type]="'primary'"
      [name]="'primary'"
      (onclick)="closePopup()"
      >">
    </marketplace-button>
  </div>
</marketplace-popup>
