<div *ngIf="showAccordian" class="rule-history__search d-flex justify-content-end">
    <!-- <button class="advSearch-overlay-search-icon">
        <i class="fa fa-solid fa-search"></i>
    </button> -->
</div>
<div class="d-flex justify-content-center backdrop" *ngIf="showLoader">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
    </div>
</div>
<div *ngIf="showAccordian">
    <marketplace-accordion [openPanel]="'false'" id="searchAccordian" [mutuallyExclusive]="false"
        [openPanel]="openPanelActiveIndex">
        <marketplace-panel [header]="item.headerString" [subText]="item.subText"
            *ngFor="let item of displayRuleHIstoryRecords ; let i=index">
            <div [ngClass]="screenName != 'view' ? 'd-flex justify-content-end' : 'rule__history-reinitiate-btn  d-flex justify-content-end'">
                <marketplace-button [enabled]="screenName != 'view'" *ngIf="i!=0 && i < 4" (onclick)="showReinstatePopup($event, item)"
                    [id]="'secondary-lg-btn-id'" [size]="'medium'" [label]="'Reinstate as Current'" [type]="'tertiary'"
                    [leadIconSVG]="editundoSVG" [trailIconSVG]="svgIcon">
                </marketplace-button>
            </div>

            <div class="rule__history-container">
                <div class="rule__history-section">
                    <div class="rule__history-block">
                        <div class="header">Rule Type</div>
                        <div class="item">{{item?.rule_type ? item.rule_type : '-'}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Rule Subtype</div>
                        <div class="item">{{item?.rule_subtype ? item.rule_subtype : '-'}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Inventory Status</div>
                        <div class="item">{{item?.inventory_status ? item.inventory_status : '-'}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Reason for Edit</div>
                        <div class="item">{{item?.edit_reason ? item.edit_reason : '-'}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Rule Name</div>
                        <div class="item">{{item.rule_name}}</div>
                    </div>
                </div>
                <div class="rule__history-section">
                    <div class="rule__history-block">
                        <div class="header">Start Date</div>
                        <div class="item">{{item.start_date}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">End Date</div>
                        <div class="item">{{item.end_date}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Status</div>
                        <div class="item">{{item?.status == 'true' ? "Active" : "Inactive" }}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Review Date</div>
                        <div class="item">{{item.review_remainder_date}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Term Reason</div>
                        <div class="item">{{item?.term_reason ? item.term_reason : '-'}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Business Owner</div>
                        <div class="item">{{item.business_owner}}</div>
                    </div>
                </div>
                <div class="rule__history-section">
                    <div class="rule__history-block">
                        <div class="header">Calculation Field</div>
                        <div class="item">{{item.calculation_fields ? item.calculation_fields : '-'}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Lookback Period/Log Period</div>
                        <div class="item">{{item.lagging_period ? item.lagging_period : '-'}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Rules Grid</div>
                        <div class="item">-</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Bypass Enhancement</div>
                        <div class="item">{{item.bypass_apply}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Apply Rules To</div>
                        <div class="item">{{item.header_level ? 'Header Level' : 'Line Level'}}</div>
                    </div>
                    <div class="rule__history-block">
                        <div class="header">Rule Level</div>
                        <div class="item">{{item.rule_level}}</div>
                    </div>
                </div>
                <div class="rule__history-section">
                    <div class="rule__history-block">
                        <div class="header">Rule Description</div>
                        <div class="item">{{item.description}}</div>
                    </div>
                </div>
            </div>
            <div class="rule__history-block">
                <div class="header">Concept(s)</div>
                <div class="item">{{item.concept}}</div>
            </div>
            <div class="rule__history-block">
                <div class="header">SQL Query</div>
                <div class="item">
                    <!-- [(CLM_Sor_CD Equal 1000) or (CLMADJ_KEY Contains 0291) or [(CNCPT_NM contains Test) and (PROJ_CD
                    Equals
                    1234)] or [(CNCPT_NM contains Test) and (PROJ_CD Equals 1234)]]] -->
                    <div *ngIf="item.execution_type != 'sql_query'">
                        <marketplace-query-builder class="rule-history__query-builder" [switchToggleNames]="switchToggleNames"
                            [isReadableQueryRequired]=true [bulkUploadParams]="uploadParams" [query]="item.qbQuery" [qbConfig]="qbConfig"
                            [operators]="operators">
                        </marketplace-query-builder>
                    </div>
                    <div *ngIf="item.execution_type == 'sql_query'">
                        {{item.qbQuery}}
                    </div>
                </div>
            </div>
        </marketplace-panel>
        <span *ngIf="hasMoreRecords()"><a class="btn-link" [routerLink]="" (click)="displayNextRecords()"> Show More
            </a></span>
        <span *ngIf="displayShowLessBtn()"><a class="btn-link" [routerLink]="" (click)="resetToInitialRecords()"> Show
                Less
            </a></span>
    </marketplace-accordion>
</div>

<marketplace-popup class="p-1" [open]="showReinstate" [size]="'small'" (onClose)="closePopup($event)">
    <div mpui-modal-header>
        <h3 class="modal-title">Reinstate Version</h3>
    </div>
    <div mpui-modal-body>
        Reinstate rule {{ruleUpdatedDateTime}} as the current version? Once reinstated,
        the rule must be submitted or saved to complete the process.</div>
    <div mpui-modal-footer>
        <marketplace-button [label]="'Cancel'" [type]="'secondary'" (onclick)="cancelReinstate($event)">
        </marketplace-button>
        <marketplace-button class="confirm-btn" [label]="'Reinstate'" [type]="'primary'"
            (onclick)="onReinstateAsCurrent($event)">
        </marketplace-button>
    </div>
</marketplace-popup>