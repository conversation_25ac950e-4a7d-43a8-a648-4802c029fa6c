<div class="fixed-nav bg-gray">
  <marketplace-breadcrumb [dataset]="breadcrumbDataset" (onSelection)="breadcrumSelection($event)">
  </marketplace-breadcrumb>
  <div class="card-body">
    <div class="clientsHeader">
      <span>Clients List</span>
    </div>
    <marketplace-table [id]="'view-client-table'" [dataset]="dataURL" *ngIf="dataURL"
      [customExportConfig]="customExport" [rowHeight]="ruleDashbordTableRowhg"
      [headerRowHeight]="ruleDashbordTableHeaderhg" [columnDefinitions]="clientListColumnConfig"
      [isRowSelectable]="false" [redraw]="tableRedraw" [dropdownOptions]="kebabOptions"
      (onDropdownOptionsClick)="moveToClientFeeSetup($event)">
    </marketplace-table>
  </div>
</div>

<div class="client-site-overlay-wrapper" *ngIf="showClientEditOverlay">
  <div class="client-site-overlay">
    <div class="client-site-overlay-header">
      <marketplace-button class="client-site-overlay-close-icon" [label]="backButton" [type]="'secondary'"
        [name]="'primary'" (onclick)="onCloseOverlay()">
      </marketplace-button>

    </div>
    <div>
      <p class="overlay-header-title overlayData">Client Site Edit</p>
      <p class="overlayHeadings"> Client Name :</p>
      <b class="overlayData"> {{ clientOverlayData[0].currentRow.clientName }}</b>

      <p class="overlayHeadings"> Client Type :</p>
      <b class="overlayData"> {{ clientOverlayData[0].currentRow.clientType }}</b>

      <p class="overlayHeadings"> Client ID :</p>
      <b class="overlayData"> {{ clientOverlayData[0].currentRow.clientId }}</b>

      <p class="overlayHeadings"> No. of Products :</p>
      <b class="overlayData"> {{ clientOverlayData[0].currentRow.products }}</b>

      <p class="overlayHeadings"> DBG Unit :</p>
      <b class="overlayData"> {{ clientOverlayData[0].currentRow.dbgUnit }}</b>

      <p class="overlayHeadings"> Status :</p>
      <b class="overlayData"> {{ clientOverlayData[0].currentRow.status }}</b>
    </div>

    <marketplace-select [label]="dropdownLabel" [(ngModel)]="defaultSelectedDbSchema"
      (onSelection)="valueSelectionForClientSchema($event)" [dataset]="clientDbSchema">
    </marketplace-select>

    <p class="overlayHeadings"> Client Site :</p>
    <div class="switch">
      <marketplace-dynamic-form [formJSON]="clientSiteFormJSON" [isSubmitNeeded]="false"
        (onValueChanges)="editClientSiteInOverlay($event)">
      </marketplace-dynamic-form>
    </div>
    <p class="hit-rate-heading"> Target Hit Rate :</p>
    <div class="hitRateForm">
      <marketplace-dynamic-form [isSubmitNeeded]="false" [formJSON]="hitRateFormJSON" [isSubmitNeeded]="false"
        (onValueChanges)="onHitRateValChange($event)" [(ngModel)]="hitRateError">
      </marketplace-dynamic-form>&nbsp;%
    </div>
    <p class="hit-rate-error" *ngIf="!correctHitRate">{{ hitRateError }}</p>
    <div class="modal-footer wrapper">
      <marketplace-button [label]="'Cancel'" [type]="'secondary'" [name]="'secondary'" (onclick)="onCloseOverlay()">
      </marketplace-button>

      <marketplace-button [label]="'Submit'" [type]="'primary'" [name]="'primary'" (onclick)="changeClientSite()"
        [enabled]="isEnabled">
        <i class="fa fa-spinner fa-spin" *ngIf="isLoading"> </i>
      </marketplace-button>
    </div>
  </div>
</div>