<!-- Test -->
@if(authService.isLogin){
  <div class="header-section">
    <marketplace-header 
      [logoSource]="appLogo" 
      [name]="appName" 
      [userName]="userName"
      [dataset]="dataset"
      (onMenuSelection)="onMenuSelection($event)"
      [isOverlayIconNeeded]="true"
      (onLOGOClick)="_onHomeClick()"
      (onLogoutClick)="_onLogoutClick()"
      (onOverlayClick)="_onOverlayClick()">
    </marketplace-header>
  </div>
  <div class="side-panel-section">
    <marketplace-slide-panel 
        [id]="'header-slide-out-panel'" 
        [orientation]="'left'" 
        [width]="'20%'"
        [header]="'Payment Integrity'" 
        [open]="isOverlayVisible" 
        [logoSource]="appLogo"
  
        (onClose)="isOverlayVisible = false">
        <div mpui-modal-body class="side-navigation_container">
          <div class="side-navigation">
  
            <div class="subscribed-apps">
              <marketplace-target-cards 
                [dataset]="listOfAccessibleCards" 
                [isSelectable]="true"
                
                (onSelection)="onScreenSelection($event)">
              </marketplace-target-cards>
            </div>
  
            <div class="quick-navgations">
              <marketplace-quick-navs 
                [dataset]="listOfAccessibleQuickLinks" 
                
                (onSelection)="quickLinkClick($event)">
              </marketplace-quick-navs>
            </div>
          </div>
        </div>
      </marketplace-slide-panel>
  </div>
  
  <div class="container-wrapper">
    <div class="app-container">
      @if(alertService.showAlert){
        <marketplace-notification [open]="alertService.notification.notificationOpen"
          [header]="alertService.notification.notificationHeader" [bodyText]="alertService.notification.notificationBody"
          [type]="alertService.notification.notificationType" [duration]="alertService.notification.notificationDuration"
          [position]="alertService.notification.notificationPosition" (onClose)="clearNotification()">
        </marketplace-notification>
      }
      <router-outlet />
    </div>
    <div class="app-footer">
      <marketplace-footer 
        [dataset]="footerDataset" 
        [justification]="'center'">
      </marketplace-footer>
    </div>
  </div>
  <app-loader *ngIf="(loaderService.isLoading$ | async)"></app-loader>
}



@if(!authService.isLogin){
  <app-loader *ngIf="(loaderService.isLoading$ | async)"></app-loader>
  <router-outlet></router-outlet>
}
