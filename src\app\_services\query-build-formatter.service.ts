import { Injectable } from '@angular/core';
import { formatDate } from '@angular/common';

import moment from 'moment'

@Injectable({
  providedIn: 'root'
})
export class QueryBuildFormatterService {


  constructor() { }


  /**
 * modifies selected query builder criterias to the format query builder understands
*/
  modifyStructureToShowQB(qbQuery) {
    const operatorMap = {
      '=': 'Equal',
      '!=': 'Not Equal',
      '>': 'Greater Than',
      '>=': 'Greater Than Or Equal',
      '<': 'Less Than',
      '<=': 'Less Than Or Equal',
      'contains': 'contains',
      'like': 'like',
      'not contains': 'Does Not Contain(s)',
      'startswith': 'Begins With',
      'endswith': 'Ends With',
      'not startswith': 'Does Not Begins With',
      'not endswith': 'Does Not End With',
      'isnull': 'Is Null',
      'isnotnull': 'Is Not Null',
      "between": "Between",
      "not between": "Not Between",
      "in": "in"
    };
    var parsed = JSON.parse(JSON.stringify(qbQuery), function (k, v) {
      switch (k) {
        case 'stat':
          this.static = v;
          delete qbQuery[k];
          break;

        case 'operator':
          return operatorMap[v] ? operatorMap[v] : v;

        default:
          return v;
      }
    });
    return parsed;
  }


  /**
   * Method to include tableName in the query rules
   * @param query 
   * @param config 
   */
  getQueryOutput(query: any, config: any): any {
    if (!query) {
      return {};
    }
    const conditions = { "and": "and", "or": "or" };
    const mapRule = (rule) => {
      rule.tblNm = config[rule.field].table;
      if (rule.operator == "in") {
        let value = rule.value.replace(/\"/g, '');
        rule.value = value.split(",");
      }
      return rule;
    }

    const mapRuleSet = (ruleSet) => {
      if (ruleSet.rules.length < 1) return false;
      return {
        "condition": ruleSet.condition,
        "rules":
          ruleSet.rules.map(
            rule => rule.operator ? mapRule(rule) : mapRuleSet(rule)
          )
      }
    };
    return mapRuleSet(query);
  }




}
