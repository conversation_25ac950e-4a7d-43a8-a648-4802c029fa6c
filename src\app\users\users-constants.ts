export const constants = {
    NAME: 'name',
    ID: 'id',
    INVENTORY_TYPE: "inventoryType",
    CLIENT_ROLE: "clientRole",
    SKILL_TYPE: "skillType",
    DISABLED: 'disabled',
    VALUES: "values",
    INTERNAL_FLAG: 'internalFlag',
    SKILLS: "skills",
    MANAGER_NAME: "managerName",
    BLUE_CARD_ID: "blueCardId",
    EXPERIENCE_LVL_ID: "experienceLevelId",
    CLIENT_SITE: "clientSite",
    CREATED_BY: "createdBy",
    CREATED_DATE: "createdDate",
    UPDATED_BY: "updatedBy",
    UPDATED_DATE: "updateDate",
    MODIFIED_BY: "modified_by",
    MODIFIED_DATE: "modified_Date",
    REQUEST_TYPE: "requestType",
    EDIT_API: "edit",
    CREATE_API: "create",
    STATUS: "status",
    REGISTRATION_STATUS: "registrationStatus",
    SUCCESS: "Success",
    ERROR: "Some Error Occurred",
    CONCEPT_CATEGORY_SKILL: "Concept Category",
    CONCEPT_CATEGORY_SKILL_1: "Concept category",
    CONCEPT_CATEGORY_SKILL_2: "conceptCategory",
    SERVICE_TYPE_SKILL: "Service Type",
    ALL: "ALL",
    EXCLUDE_25: "Exclude 25",
    EXCLUDE_65: "Exclude 65",
    EXCLUDE_85: "Exclude 85",
    GROUP_ID_65: "65",
    GROUP_ID_85: "85",
    GROUP_ID_25: "25",
    CLIENT_RODUCT_ROLE: "clientProdRole",
    ERROR_MSG: "There are  no client(s) and role(s) associated to the entered User Id. Please check",
    USER_FORM_JSON: "./assets/json/user-management/users-management/user-form.json",
    BULK_EDIT_USER_FORM: "./assets/json/user-management/users-management/bulk-edit-user-form.json",
    SKILL_HISTORY_JSON: "./assets/json/user-management/users-management/skill-history.json",
    USER_ROLE_STEPPER_JSON: "./assets/json/user-management/users-management/user-role-stepper-form.json",
    USER_FORM_CONFIGURATION_JSON: "./assets/json/security/user-form-configurations.json",
    USER_FORM_EXPERIENCE_JSON: "./assets/json/user-management/users-management/user-experience-configurations.json",
    GROUP_LIST_JSON: "./assets/json/user-management/group-management/group-list.json",
    USER_LIST_JSON: "./assets/json/user-management/users-management/users-list.json",
    USER_LIST_AUDIT_JSON: "./assets/json/user-management/users-management/users-list-audit.json",
    CLIENT_PRODUCTS_JSON: "./assets/json/user-management/users-management/client-products.json",
    USER_ID: "userId",
    PERMISSIONS: 'permissions',
    USER_NAME: "userName",
    TEAM_NAME: "TeamName",
    DESCRIPTION: "description",
    ROLE: "role",
    OPTION_NAME: "label",
    OPTION_VALUE: "value",
    OPTION_TYPE_SELECT: "select",
    GROUP_NAME: "groupName",
    CLIENT_OFFSHORE: "Offshore",
    CLIENT_ONSHORE: "Onshore",
    RESPONSE_DATA: "responseData",
    DATACONTEXT: 'dataContext',
    VALID: 'VALID',
    MARKET_SKILL: 'Market Skill',
    GROUP_NUMBER: 'Group Number',
    BLUE_CARD_ACCESS: 'Blue Card Access',
    COB_SKILL: 'Skill',
    COB_SKILL_ID: 'cobSkill',
    COB_SUB_SKILL: 'Subskill',
    COB_SUB_SKILL_ID: 'cobSubSkill',
    CLAIM_SYSTEM: 'Claim System',
    CLAIM_SYSTEM_1: 'claimSystem',
    FUNDING_TYPE: 'Funding Type',
    LOB_SKILL: 'Lob Skill',
    GROUP_CONTROLS: 'groupControls',
    CLIENT_ID: 'clientId',
    CLIENT_NAME: 'clientName',
    PROD_ID: 'prodId',
    PROD_NAME: 'prodName',
    PRODUCT_NAME: 'productName',
    ROLE_ID: 'RoleId',
    ROLE_NAME: 'RoleName',
    ROLE_ID_CAMEL_CASE: 'roleId',
    ROLE_NAME_CAMEL_CASE: 'roleName',
    CARELON_ADMINISTRATOR: 'Carelon Administrator',
    CLIENT_ADMINISTRATOR: 'Client Administrator',
    PORTAL_ADMINISTRATOR: 'Portal Administrator',
    SIU_ADMIN: 'SIU Admin',
    ACTIVE: 'Active',
    INACTIVE: 'Inactive',
    DEACTIVATE: 'DeActivate',
    VIEW_USER: 'view-user',
    EDIT_USER: 'edit-user',
    EDIT_USER_MANAGEMENT: 'edit-user-management',
    EDIT_USER_HEADER: 'Edit User',
    PREVIEW_USER: 'Preview User',
    APPROVE_USER: 'Approve User',
    PREVIEW: 'Preview',
    SUBMIT: 'Submit',
    APPROVED: 'Approved',
    AWAITING_APPROVAL: 'Awaiting Approval',
    REQUEST_NOT_APPROVED: 'Request Not Approved',
    USER_REJECTED: 'User Rejected',
    USER_REJECTION_MSG: "'s user account has been Rejected",
    REMINDER_DATE: 'reminderDate',
    SELECTED_VALUE: 'selectedVal',
    ENABLE_COMMENTS: 'Enabled the user',
    DISABLE_COMMENTS: 'Disabled the user',
    ENABLE_USER: 'enableuser',
    DISABLE_USER: 'disableuser',
    ERR_MSG_SOA: 'Error while updating user status using SOA Endpoints',
    SUCCESS_USER_MSG: 'User details Successfully Saved',
    COORDINATION_OF_BENEFITS: 'Coordination Of Benefits',
    COORDINATION_OF_BENEFITS_ID: 31,
    CLAIM_ANOMALY_DETECTION: 'Data Mining Solution',
    CLAIM_ANOMALY_DETECTION_ID: 11,
    ANTHEM_CLIENT: 'Anthem',
    ANTHEM_CLIENT_ID: 59,
    CAD_PROD_ID: 11,
    USER_APPROVED: 'User Approved',
    USER_APPROVED_MSG: "'s user account has been Approved",
    USER_APPROVED_MSG_SUCCESS: 'User account has been Approved',
    CONCEPT_STATE: "Concept State",
    MEMBER_BRAND_Label: "Member Brand",
    SERVICE_PROVIDER_REGION_Label: "Service Provider Region",
    UAT: "UAT",
    PRODUCTION: "Production",
    CONCEPT_STATE_ID: "conceptStateSelect",
    MAX_CLAIM_ID: "maxClaimAssignment",
    MAX_CLAIM_ASSIGNMENT: "Max Claim Assignment",
    MAX_CLAIM_COUNT: "Max Claim Count",
    CSBD: "CSBD",
    GBD: "GBD",
    BUSINESS_DIVISION: "Business Division",
    BUS_DIV_NAME: "busDivision",
    BUS_DIV_ID: "busDivisionId",
    ADD_USER_SVG_PATH: "./assets/images/icons/AddUser.svg",
    SEARCH_SVG_PATH: "./assets/images/icons/Search.svg",
    BULK_ADDITIONAL_USER_SEARCH: "./assets/json/user-management/users-management/bulk-additional-user-search.json",
    SUPER_ADMINISTRATOR: 'Super Administrator',
    CONTRACT_ADMINISTRATOR: 'Contract Administrator',
    MEMBER_BRAND: "memberBrand",
    SERVICE_PROVIDER_REGION: "serviceProviderRegion"
}

export const nullValueProduct = {
    updateDataset: [{ id: "roleName", dataset: [] }],
    when: null
}

export const emptyProduct = {
    updateSelectedValue: [{ id: "roleName", value: null }],
    when: null
}
export const tableColumnConfig: any = {

    "switches": {
        "enableSorting": false,
        "enablePagination": false,
        "editable": false,
        "enableFiltering": false
    },
    "colDefs": [
        {
            "name": "Client",
            "field": "clientName",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Role",
            "field": "roleName",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        }
    ]
};