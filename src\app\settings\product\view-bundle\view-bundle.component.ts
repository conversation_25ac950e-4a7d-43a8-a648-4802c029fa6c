import {
  Component,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { ProductApiService } from 'src/app/_services/product-api.service';
import { Router, ActivatedRoute } from '@angular/router';
import { UtilitiesService } from 'src/app/_services/utilities.service';
import { environment } from 'src/environments/environment';
import { AuthService } from 'src/app/_services/authentication.services';
@Component({
  selector: 'app-view-bundle',
  templateUrl: './view-bundle.component.html',
  styleUrls: ['./view-bundle.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class ViewBundleComponent implements OnInit {
  public generalDetailsJson: any[];
  public product: any[];
  public bundle: any[];
  public prodId: any;
  public prodIdBundles: any;
  public bundleId: any;
  public dataForDynamicForm: any = [];
  public productName: any;
  public breadcrumbDataset: any;
  constructor(
    private router: Router,
    private productApiService: ProductApiService,
    private dateService: UtilitiesService,
    private route: ActivatedRoute,
    public authService: AuthService
  ) {
    this.bundleId = Number(this.router.url.slice(this.router.url.lastIndexOf('/') + 1));
    this.prodIdBundles = Number(this.router.url.split('/').slice(-2)[0]);
  }

  ngOnInit(): void {
    this.productApiService.getProductDetails().subscribe((data) => {
      let matchedProduct = data.find((x) => x.productId == this.prodIdBundles)
      this.productName = matchedProduct?.productName;
      this.breadcrumbDataset = [{ label: 'Home', url: '/' }, { label: `${this.productName}`, url: 'settings/product' }, { label: 'Bundle list', url: `settings/product/bundle/${this.prodIdBundles}` }, { label: `View Bundle` }];
    })
    this.productApiService.getparticularBundleDetails(this.bundleId)
      .subscribe((apidata) => {
        if (apidata) {
          apidata.concepts.forEach(element => {
            this.dataForDynamicForm.push(element.cncptId);
          });
          this.generalDetailsJson = [
            {
              "type": "group",
              "name": "General 1",
              "label": "",
              "column": "2",
              "groupControls": [
                {
                  "label": "Bundle Name",
                  "type": "text",
                  "name": "bundle_name",
                  "column": "1",
                  "disabled": true,
                  "value": apidata?.bundleName
                },
                {
                  "label": "Description",
                  "type": "textarea",
                  "name": "bundle_desc",
                  "column": "1",
                  "disabled": true,
                  "value": apidata?.bundleDesc,
                  "maxLimit": "200"
                }
              ]
            },
            {
              "type": "group",
              "name": "General 2",
              "label": "",
              "column": "2",
              "groupControls": [
                {
                  "label": "Created By",
                  "type": "text",
                  "name": "created_by",
                  "column": "2",
                  "disabled": true,
                  "value": apidata?.createdUserId
                },
                {
                  "label": "Modified By",
                  "type": "text",
                  "name": "modified_by",
                  "column": "2",
                  "disabled": true,
                  "value": apidata?.lastModifiedUserId
                },
                {
                  "label": "Effective Start Date",
                  "type": "date",
                  "name": "start_date",
                  "column": "2",
                  "disabled": true,
                  "pickerType": "single",
                  "value": this.dateService.getDbgDateFormat(apidata?.effStartDate),
                  "dateFormat": 'MM-DD-YYYY'
                },
                {
                  "label": "Effective End Date",
                  "type": "date",
                  "name": "end_date",
                  "column": "2",
                  "disabled": true,
                  "pickerType": "single",
                  "value": this.dateService.getDbgDateFormat(apidata?.effEndDate),
                  "dateFormat": 'MM-DD-YYYY'
                },
                {
                  label: 'Add to a Product',
                  group: '',
                  type: 'text',
                  name: 'product_id',
                  column: '2',
                  groupColumn: '2',
                  disabled: true,
                  value: this.productName ? this.productName : ""
                },
                {
                  optionName: 'name',
                  optionValue: 'name',
                  label: 'Add a Concept',
                  group: '',
                  type: 'select',
                  multiple: true,
                  closeOnSelect: true,
                  name: 'concept_name',
                  column: '2',
                  groupColumn: '2',
                  disabled: true,
                  selectedVal: this.dataForDynamicForm,
                  placeholder: "Select Concept Name"
                }
              ]
            }
          ];
        }
      }, err => {
      });

  }

  /**
    * breadcrumSelection Funtion
    */
  breadcrumSelection(event) {
    if (event.selected.label == 'Home') {
      this.authService.onHomeClick();
    }
    this.router.navigate([`${event.selected.url}`]);
  }

  backToPreviousPage() {
    window.history.back()
  }

  ngAfterViewInit(): void {
    const collection = document.querySelectorAll(
      'marketplace-dynamic-form button'
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].remove();
    }
  }
}
