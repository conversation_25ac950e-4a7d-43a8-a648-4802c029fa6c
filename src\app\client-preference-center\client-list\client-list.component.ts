import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { ToastService } from 'src/app/_services/toast.service';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { AuthService } from 'src/app/_services/authentication.services';

@Component({
  selector: 'app-client-list',
  templateUrl: './client-list.component.html',
  styleUrls: ['./client-list.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class ClientListComponent implements OnInit {
  public headerText = "Client Setup";
  public isPriviousRedirectPage = true;
  public ruleDashbordTableRowhg: number = 45;
  public ruleDashbordTableHeaderhg: number;
  public totalEntries: number;
  public dataURL: any;
  public tableRedraw: any;
  turnedOn: boolean = false;
  isEnabled: boolean = true;
  clientSiteFormJSON: any;
  clientDbSchemaVal: any;
  defaultSelectedDbSchema: any;
  hitRate: any = "";
  hitRateFormJSON: any;
  validationOfHitRate: boolean;
  placeholderHitRate: any = "80";
  correctHitRate: boolean = true;
  hitRateError: any = "";
  backButton: string = `<i class="fa fa-arrow-right"></i>`;
  public clientOverlayData = [];
  showClientEditOverlay: boolean = false;
  public columnConfigForclientTableUrl: string = "./assets/json/client-preference/client-list.json"
  public clientListColumnConfig: any;
  public clientDbAndSchemaData = [{ dbName: "D01_DPDF_ANTM", dbSchemaName: "ANTM_ALLPHI" }, { dbName: "D01_DPDF_DBG", dbSchemaName: "DBG_ALLPHI" }];
  dropdownLabel: string = "Client DB Schema :";
  clientDbSchema: any = [{ name: "QA", id: 1 }, { name: "Elevance", id: 2 }];
  customExport: any = {
    enabled: true,
    fileName: 'customfile.xls'
  };
  breadcrumbDataset = [{ label: 'Home', url: '/' }, { label: 'Client Setup' }];
 public kebabOptions: any = [
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View Products`,
    id: 'viewProduct'
  },
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View Fee Schedule`,
    id: 'viewFeeSetup'
  },
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View Data Exchange`,
    id: 'viewDataExchange'
  },
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View File Exchange`,
    id: 'viewFileExchange'
  },
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View Sample Validation Percentage`,
    id: 'viewSampleValidationPercentage'
  },
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.2475 4.13527C14.3108 4.026 14.2958 3.88362 14.2021 3.79L12.2104 1.80078C12.098 1.68854 11.9157 1.68949 11.8045 1.80288L3.29748 10.478L3.1828 10.6102C3.07664 10.749 2.99653 10.9065 2.94695 11.0751L2.12229 13.8761L4.92496 13.0528C5.14963 12.9868 5.35461 12.8664 5.52186 12.7025L14.2001 4.1962L14.2475 4.13527ZM6.72185 13.9268L6.55919 14.0753C6.22418 14.3605 5.83235 14.5728 5.40867 14.6974L1.09901 15.9649C0.798101 16.0535 0.472866 15.9705 0.251072 15.7487C0.0292778 15.527 -0.0536594 15.2017 0.0348549 14.9008L1.30232 10.5914L1.37189 10.3823C1.52539 9.96971 1.76416 9.59316 2.07349 9.27773L10.5805 0.602618L10.7228 0.470713C11.4612 -0.150374 12.5447 -0.153581 13.2869 0.464641L13.4218 0.587841L15.4135 2.57706C16.2009 3.36351 16.1949 4.64144 15.4001 5.42045L6.72185 13.9268ZM15.1429 14.2855C15.6163 14.2855 16 14.6693 16 15.1426C16 15.5766 15.6776 15.9352 15.2592 15.992L15.1429 15.9998H8.85716C8.38378 15.9998 8.00002 15.616 8.00002 15.1426C8.00002 14.7087 8.32248 14.3501 8.74085 14.2933L8.85716 14.2855H15.1429Z" fill="black"/>
</svg> Edit Client Data`,
    id: 'EditClientData'
  }
];

  public kebabOptions_Readonly: any = [
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View Products`,
    id: 'viewProduct'
  },
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View Fee Schedule`,
    id: 'viewFeeSetup'
  },
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View Data Exchange`,
    id: 'viewDataExchange'
  },
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View File Exchange`,
    id: 'viewFileExchange'
  },
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" style="vertical-align:middle;margin-right:8px;"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8C10.5 6.61929 9.38071 5.5 8 5.5ZM8 7C8.55228 7 9 7.44772 9 8C9 8.55228 8.55228 9 8 9C7.44772 9 7 8.55228 7 8C7 7.44772 7.44772 7 8 7Z" fill="black"/><path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C4.22173 2 1 5.41124 1 8C1 10.5888 4.22173 14 8 14C11.7783 14 15 10.5888 15 8C15 5.41124 11.7783 2 8 2ZM8 3.5C10.9301 3.5 13.5 6.22111 13.5 8C13.5 9.77889 10.9301 12.5 8 12.5C5.06994 12.5 2.5 9.77889 2.5 8C2.5 6.22111 5.06994 3.5 8 3.5Z" fill="black"/></svg> View Sample Validation Percentage`,
    id: 'viewSampleValidationPercentage'
  },
  {
    label: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.5H2.75C2.61193 2.5 2.5 2.61193 2.5 2.75V13.25C2.5 13.3881 2.61193 13.5 2.75 13.5H7.25C7.66421 13.5 8 13.8358 8 14.25C8 14.6642 7.66421 15 7.25 15H2.75C1.7835 15 1 14.2165 1 13.25V2.75C1 1.7835 1.7835 1 2.75 1H8.25C10.8734 1 13 3.12665 13 5.75V7H8.75C7.7835 7 7 6.2165 7 5.25V2.5ZM8.5 2.50947V5.25C8.5 5.38807 8.6119 5.5 8.75 5.5H11.4905C11.3691 3.9044 10.0956 2.63085 8.5 2.50947Z" fill="black"/>
<path d="M12.7432 9.64823C12.6935 9.28215 12.3797 9 12 9C11.5858 9 11.25 9.33579 11.25 9.75V11.25H9.75L9.64823 11.2568C9.28215 11.3065 9 11.6203 9 12C9 12.4142 9.33579 12.75 9.75 12.75H11.25V14.25L11.2568 14.3518C11.3065 14.7178 11.6203 15 12 15C12.4142 15 12.75 14.6642 12.75 14.25V12.75H14.25L14.3518 12.7432C14.7178 12.6935 15 12.3797 15 12C15 11.5858 14.6642 11.25 14.25 11.25H12.75V9.75L12.7432 9.64823Z" fill="black"/>
</svg>   Edit Client Data`,
    id: 'EditClientData'
  }
];

isReadOnly: boolean = false;
clientTableRender: boolean =  false;
  constructor(private router: Router, private clientApiService: ClientApiService, private alertService: ToastService,
    private authService: AuthService) { }

  ngOnInit(): void {
    this.isReadOnly = !this.authService.isWriteOnly;
    this.isReadOnly ? this.kebabOptions = this.kebabOptions_Readonly : this.kebabOptions
    this.clientApiService.getTableColumnforClientsTable(this.columnConfigForclientTableUrl).subscribe((columndata) => {
      columndata.colDefs.forEach(e => {
        e.field == "status" ? e.customFormatter = this.customFormatterStatus : "";
        e.field == "products" ? e.customFormatter = this.customFormatterProducts : "";
        e.field == "bundles" ? e.customFormatter = this.customFormatterBundles : "";
        e.field == "feeSchedule" ? e.customFormatter = this.customFormatterFeeSchedules : "";
      });
      this.clientListColumnConfig = columndata;
    });

    this.clientApiService.getAllClientsInPreferenceCenter().subscribe((apidata) => {
      if (apidata) {
        this.dataURL = apidata;
        this.totalEntries = apidata.length;
      }
    }, err => {
      this.dataURL = [];
      this.totalEntries = 0;
    });
  }
  /**
   * 
   * @param event Function to set value for client DB schema
   */
  valueSelectionForClientSchema(event) {
    event == "" ? this.isEnabled = false : this.isEnabled = true;
    let selectedVal = event.name;
    if (selectedVal == "Elevance") {
      this.clientDbSchemaVal = this.clientDbAndSchemaData[0];
    } else {
      this.clientDbSchemaVal = this.clientDbAndSchemaData[1];
    }
  }

  /**
   * Function to change the edit client site
   * @param event 
   */
  editClientSiteInOverlay(event: any) {
    this.turnedOn = event.current.active;
  }


  /*
  * Method to deploy data from table row to client overlay side pop-over
  */
  moveToClientFeeSetup(event: any) {
    this.clientOverlayData = [];
    let clientSiteFromTable = event.currentRow.clientSite;
    if (clientSiteFromTable == "Offshore" || clientSiteFromTable == "offshore") {
      this.turnedOn = true;
    } else {
      this.turnedOn = false;
    }
    this.clientSiteFormJSON = [{
      "label": "",
      "text": "Offshore",
      "type": "switch",
      "name": "active",
      "column": "2",
      "preLabel": "Onshore",
      "alignment": "horizontal",
      "value": this.turnedOn
    }];
    this.clientOverlayData.push(event);

    this.clientApiService.getClientTargetHitRate(this.clientOverlayData[0].currentRow.clientId).subscribe((data) => {
      if (data) {
        this.placeholderHitRate = String(data.targetHitRate);
        if (data.targetHitRate == undefined) {
          this.hitRateFormJSON = [{
            "type": "text",
            "name": "targetHitRate",
            "customTags": true,
            "id": "targetHitRate",
            "placeholder": "80"
          }];
        } else {
          this.hitRateFormJSON = [{
            "type": "text",
            "name": "targetHitRate",
            "customTags": true,
            "id": "targetHitRate",
            "placeholder": String(data.targetHitRate)
          }];
        }
      }
    }, err => {
      this.placeholderHitRate = "80";
      this.hitRateFormJSON = [{
        "type": "text",
        "name": "targetHitRate",
        "customTags": true,
        "id": "targetHitRate",
        "placeholder": "80"
      }];
    })

    if (event['text'] == "Edit Client Data") {
      event['currentRow']?.dbSchemaName == "ANTM_ALLPHI" ? this.defaultSelectedDbSchema = 2 : this.defaultSelectedDbSchema = 1;
      this.showClientEditOverlay = true;
    } else {
      let currentRow = event['currentRow'];
      let selectedOption = event['text'];
      this.clientApiService.setSelectedTabOption(selectedOption);
      this.clientApiService.selectedProductName = "";
      this.router.navigate([`clients/product/${currentRow['clientId']}/${currentRow['clientName']}`]);
    }
  }
  /**
   * Function to change the edit client site on click of submit
   * @param event 
   */
  changeClientSite() {
    let modifiedClientData = {
      "clientId": this.clientOverlayData[0].currentRow.clientId,
      "clientSite": this.turnedOn ? "Offshore" : "Onshore",
      "clientDbSchema": this.clientDbSchemaVal,
      "lastModifiedUserId": "AL05354"
    }
    this.clientApiService.editOnshoreOfshoreFields(modifiedClientData)
      .subscribe((data) => {
        if (data.responseCode == 200) {
          this.showClientEditOverlay = false;
          this.alertService.setSuccessNotification({
            notificationHeader: "Client Site Edited Successfully",
            notificationBody: `Client id: ${this.clientOverlayData[0].currentRow.clientId}`,
            notificationDuration: 2000,
          });
          setTimeout(() => window.location.reload(), 2000);
        }
      }, err => {
        this.showClientEditOverlay = true;
        this.alertService.setErrorNotification({
          notificationHeader: "Some Error Occurred",
          notificationBody: "",
        });
      })

    this.clientApiService.editClientTargetHitRate(this.clientOverlayData[0].currentRow.clientId, this.hitRate)
      .subscribe((data) => {
        if (data.responseCode == 200) {
          console.log(data);
          this.showClientEditOverlay = false;
          this.alertService.setSuccessNotification({
            notificationHeader: "Client Target Hit Rate Edited Successfully",
            notificationBody: `Target Hit Rate: ${this.hitRate}`,
            notificationDuration: 2000,
          });
          setTimeout(() => window.location.reload(), 2000);
        }
      }, err => {
        this.showClientEditOverlay = true;
        this.alertService.setErrorNotification({
          notificationHeader: "Some Error Occurred",
          notificationBody: "",
        });
      })

  }

  /**
    * @function breadcrumbSelection - routes to respective url
    * @param event default event
    */
  breadcrumSelection(event) {

    this.router.navigate([`${event.selected.url}`]);
  }
  /**
   * @function onCloseScheduler - on close of ClientEditOverlay popup
   */
  onCloseOverlay() {
    this.showClientEditOverlay = false;
  }
  /**
  * customFormatterStatus funtion for button in Rule table
  * @param event 
  */
  customFormatterStatus(event) {
    let btn;
    switch (event.value) {
      case "Active":
        btn = "<button type='button' title='Active' class='btn btn rule-dashboard btn-active btn-wrap-text'>Active</button>";
        break;
      case "Inactive":
        btn = "<button type='button' title='Inactive' class='btn btn rule-dashboard btn-inactive btn-wrap-text'>Inactive</button>";
        break;
    }
    return btn;
  }
  /**
  * customFormatterFeeSchedules function for Rule Table Action
  * @param event 
  */
  customFormatterFeeSchedules(event) {
    if (event.value > 1) { return event.value; }
    else { return event.value; }
  }

  /**
  * customFormatterProducts function for Rule Table Action
  * @param event 
  */
  customFormatterProducts(event) {
    if (event.value > 1) { return event.value; }
    else { return event.value; }
  }

  /**
  * customFormatterBundles function for Rule Table Action
  * @param event 
  */
  customFormatterBundles(event) {
    if (event.value > 1) { return event.value; }
    else { return event.value; }
  }

  /**
    * AddNewRulefun Funtion
    */
  AddNewBundlesfun(): void {
   this.router.navigate([`clients/product/add-bundle`]);
  }

  onHitRateValChange(event) {
    this.validateTargetHitRate(event.current.targetHitRate);
    console.log(this.hitRate);
  }

  validateTargetHitRate(hitRate: any) {
    this.validationOfHitRate = false;
    let allowedCharacters = /^(\d)*(\.)?([0-9]{1})?$/;
    if (hitRate < 0) {
      this.isEnabled = false;
      this.hitRate = "";
      this.correctHitRate = false;
      this.hitRateError = "Target Hit Rate cannot be below 0";
      return;
    }
    if (hitRate > 100) {
      this.isEnabled = false;
      this.hitRate = "";
      this.correctHitRate = false;
      this.hitRateError = "Target Hit Rate cannot exceed 100";
      return;
    }
    if (allowedCharacters.test(hitRate)) {
      this.validationOfHitRate = true;
      this.isEnabled = true;
      this.hitRate = hitRate;
      this.correctHitRate = true;
      this.hitRateError = "";
    } else {
      if (/[a-z]/i.test(hitRate)) {
        this.isEnabled = false;
        this.correctHitRate = false;
        this.hitRateError = "Target Hit Rate cannot contain letters";
      } else {
        this.isEnabled = false;
        this.correctHitRate = false;
        this.hitRateError = "Target Hit Rate can have at most one decimal place";
      }
    }
  }
}
