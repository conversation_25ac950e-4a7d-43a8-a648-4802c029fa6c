import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';
import { NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { CreateComponent } from './create.component';
import { RulesApiService } from '../_services/rules-api.service';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { ProductApiService } from '../../_services/product-api.service';
import { ToastService } from '../../_services/toast.service';
import { UtilitiesService } from '../../_services/utilities.service';
import { UserManagementApiService } from '../../users/_services/user-management-api.service';
import { AuthService } from '../../_services/authentication.services';
import { CookieService } from 'ngx-cookie-service';
import { BusinessDivisionService } from '../../_services/business-division.service';

describe('CreateComponent', () => {
  let component: CreateComponent;
  let fixture: ComponentFixture<CreateComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockRulesApiService: jasmine.SpyObj<RulesApiService>;
  let mockClientApiService: jasmine.SpyObj<ClientApiService>;
  let mockProductApiService: jasmine.SpyObj<ProductApiService>;
  let mockToastService: jasmine.SpyObj<ToastService>;
  let mockUtilitiesService: jasmine.SpyObj<UtilitiesService>;
  let mockUserManagementApiService: jasmine.SpyObj<UserManagementApiService>;
  let mockAuthService: any;
  let mockCookieService: jasmine.SpyObj<CookieService>;
  let mockBusinessDivisionService: jasmine.SpyObj<BusinessDivisionService>;

  const mockClientsResponse = {
    status: { code: 200 },
    result: [
      { clientId: 1, clientName: 'Test Client 1' },
      { clientId: 2, clientName: 'Test Client 2' }
    ]
  };

  const mockConceptsResponse = {
    status: { code: 200 },
    result: [
      { conceptId: 1, conceptName: 'Test Concept 1' },
      { conceptId: 2, conceptName: 'Test Concept 2' }
    ]
  };

  const mockInventoryStatusResponse = [
    { name: 'Active', value: 'active' },
    { name: 'Inactive', value: 'inactive' }
  ];

  // Standardized master data response for all tests
  const mockMasterDataResponse = {
    status: { code: 200 },
    result: {
      fields: {
        rule_type: [
          { 'Exclusion': { rule_sub_type: ['Test Sub Type'] } },
          { 'Inclusion': { rule_sub_type: ['Test Sub Type 2'] } }
        ],
        letter_type: ['Test Letter Type'],
        calculation_fields: ['Test Field'],
        lookup_dates: ['30 days'],
        lagging_period: ['7 days'],
        query_fields: [
          {
            field_type: 'dropdown',
            value: 'test_field',
            name: 'Test Field',
            type: 'string',
            options: [{ id: 1, name: 'Option 1' }]
          }
        ]
      },
      clients: [{ clientId: 1, clientName: 'Test Client 1' }],
      concepts: [{ conceptId: 1, conceptName: 'Test Concept 1' }],
      products: []
    }
  };

  const mockJsonFileResponse = {
    sqlStructure: [
      { name: 'group1', groupControls: [] },
      {
        name: 'group2',
        groupControls: [
          { name: 'PRODUCT', visible: true },
          { name: 'CONCEPT_ID', visible: true, options: [] }
        ]
      }
    ],
    customSQL: {}
  };

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    routerSpy.url = '/rules/create';
    const rulesApiServiceSpy = jasmine.createSpyObj('RulesApiService', [
      'createEditRule', 'getInventoryStatusData', 'getAssetsJson', 'addFilesToRules',
      'uploadFileAndQBCriteria', 'getFileDetailsOfRules', 'getColumnConfigJsonDuplicate', 'getMasterData'
    ]);

    // Setup critical mock responses BEFORE component creation
    rulesApiServiceSpy.getInventoryStatusData.and.returnValue(of(mockInventoryStatusResponse));
    rulesApiServiceSpy.getAllViewEditRuleAPIs = jasmine.createSpy('getAllViewEditRuleAPIs').and.returnValue(of([
      { status: { code: 200 }, result: { fields: { rule_type: [], query_fields: [] } } },
      { status: { code: 200 }, result: { metadata: { rules: [{}] } } }
    ]));
    rulesApiServiceSpy.getAssetsJson = jasmine.createSpy('getAssetsJson').and.returnValue(of({
      sqlStructure: [
        { name: 'group1', groupControls: [{ name: 'test', visible: true }] },
        { name: 'group2', groupControls: [
          { name: 'PRODUCT', visible: true },
          { name: 'CONCEPT_ID', visible: true, options: [] }
        ]}
      ],
      customSQL: {}
    }));
    rulesApiServiceSpy.getColumnConfigJsonDuplicate.and.returnValue(of({
      switches: { enableSorting: true },
      colDefs: []
    }));
    rulesApiServiceSpy.getMasterData.and.returnValue(of(mockMasterDataResponse));
    rulesApiServiceSpy.createEditRule.and.returnValue(of({
      status: { code: 200 },
      result: { id: 123, message: 'Rule created successfully' },
      rule_id: 123,
      duplicates_present: false
    }));

    const clientApiServiceSpy = jasmine.createSpyObj('ClientApiService', ['getAllClientsInPreferenceCenter']);
    clientApiServiceSpy.getAllClientsInPreferenceCenter.and.returnValue(of([
      { clientId: 1, clientName: 'Test Client 1' },
      { clientId: 2, clientName: 'Test Client 2' }
    ]));

    const productApiServiceSpy = jasmine.createSpyObj('ProductApiService', ['getProductConceptsId']);
    productApiServiceSpy.getProductConceptsId.and.returnValue(of({
      executionConceptAnalyticResponse: [
        {
          clientId: 1,
          exConceptReferenceNumber: 'CONCEPT-001',
          conceptName: 'Test Concept 1'
        },
        {
          clientId: 2,
          exConceptReferenceNumber: 'CONCEPT-002',
          conceptName: 'Test Concept 2'
        }
      ]
    }));

    const toastServiceSpy = jasmine.createSpyObj('ToastService', ['setSuccessNotification', 'setErrorNotification']);
    const utilitiesServiceSpy = jasmine.createSpyObj('UtilitiesService', [
      'getECPDateFormat', 'getFormattedDate', 'formatDate', 'getDbgDateFormat'
    ]);
    utilitiesServiceSpy.getECPDateFormat.and.returnValue('2023-01-01T00:00:00Z');
    utilitiesServiceSpy.formatDate.and.returnValue('2023-01-01');
    utilitiesServiceSpy.getDbgDateFormat.and.returnValue('2023-01-01 00:00:00');
    utilitiesServiceSpy.getFormattedDate.and.returnValue('2023-01-01');

    const userManagementApiServiceSpy = jasmine.createSpyObj('UserManagementApiService', ['getUserData']);
    userManagementApiServiceSpy.getUserData.and.returnValue(of({ status: { code: 200 }, result: {} }));

    const authServiceSpy = { isWriteOnly: false };
    const cookieServiceSpy = jasmine.createSpyObj('CookieService', ['get']);
    cookieServiceSpy.get.and.returnValue('mock_cookie_value');

    const businessDivisionServiceSpy = jasmine.createSpyObj('BusinessDivisionService', ['getBusinessDivisions']);
    businessDivisionServiceSpy.getBusinessDivisions.and.returnValue(of({ status: { code: 200 }, result: [] }));

    await TestBed.configureTestingModule({
      declarations: [CreateComponent],
      imports: [HttpClientTestingModule],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: RulesApiService, useValue: rulesApiServiceSpy },
        { provide: ClientApiService, useValue: clientApiServiceSpy },
        { provide: ProductApiService, useValue: productApiServiceSpy },
        { provide: ToastService, useValue: toastServiceSpy },
        { provide: UtilitiesService, useValue: utilitiesServiceSpy },
        { provide: UserManagementApiService, useValue: userManagementApiServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: CookieService, useValue: cookieServiceSpy },
        { provide: BusinessDivisionService, useValue: businessDivisionServiceSpy }
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    // Mock sessionStorage
    spyOn(sessionStorage, 'getItem').and.callFake((key: string) => {
      const mockData: { [key: string]: string } = {
        'USER_ID': 'test_user',
        'clientId': '85',
        'clientName': 'Test Client'
      };
      return mockData[key] || null;
    });

    fixture = TestBed.createComponent(CreateComponent);
    component = fixture.componentInstance;

    // Initialize component properties to prevent undefined errors
    component.postUploadDataJson = {
      commentsInUpload: 'Test comment'
    };
    component.fileUploadJSON = '';
    component.showMaxLimitMsg = false;
    component.querySpecificationJson = [
      { name: 'group1', groupControls: [] },
      {
        name: 'group2',
        groupControls: [
          { name: 'PRODUCT', visible: true },
          { name: 'CONCEPT_ID', options: [] }
        ]
      }
    ];
    component.ruleLevelSelectionJson = [
      { name: 'group1', groupControls: [] },
      {
        name: 'group2',
        groupControls: [
          { name: 'PRODUCT', visible: true },
          { name: 'CONCEPT_ID', options: [] }
        ]
      }
    ];
    component.createFormData = {
      rule_type: 'Exclusion',
      start_date: '2023-01-01',
      end_date: '2023-12-31'
    };

    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockRulesApiService = TestBed.inject(RulesApiService) as jasmine.SpyObj<RulesApiService>;
    mockClientApiService = TestBed.inject(ClientApiService) as jasmine.SpyObj<ClientApiService>;
    mockProductApiService = TestBed.inject(ProductApiService) as jasmine.SpyObj<ProductApiService>;
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    mockUtilitiesService = TestBed.inject(UtilitiesService) as jasmine.SpyObj<UtilitiesService>;
    mockUserManagementApiService = TestBed.inject(UserManagementApiService) as jasmine.SpyObj<UserManagementApiService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockCookieService = TestBed.inject(CookieService) as jasmine.SpyObj<CookieService>;
    mockBusinessDivisionService = TestBed.inject(BusinessDivisionService) as jasmine.SpyObj<BusinessDivisionService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with correct default values', () => {
      expect(component.headerText).toBe('Create New Rule');
      expect(component.showLoader).toBe(false);
      expect(component.isDisabled).toBe(true);
      expect(component.retroApply).toBe(false);
      expect(component.bypassApply).toBe(false);
      expect(component.headerLevel).toBe(false);
    });

    it('should set breadcrumb dataset correctly', () => {
      expect(component.breadcrumbDataset).toEqual([
        { label: 'Home', url: '/' },
        { label: 'Rules engine', url: '/rules' },
        { label: 'Create new rule' }
      ]);
    });

    it('should initialize query builder configuration', () => {
      expect(component.qbConfig).toBeDefined();
      expect(component.qbConfig.fields).toBeDefined();
      expect(component.qbConfig.validations).toBeDefined();
    });
  });

  describe('ngOnInit', () => {
    it('should call ngOnInit without errors', () => {
      expect(() => component.ngOnInit()).not.toThrow();
    });
  });

  describe('Form Validation', () => {
    it('should have isDisabled property', () => {
      expect(typeof component.isDisabled).toBe('boolean');
    });
  });

  describe('File Upload Functionality', () => {
    it('should validate file upload form correctly', () => {
      component.fileUploadJSON = 'test-file.csv';
      component.postUploadDataJson = { commentsInUpload: 'Test comment' };
      component.showMaxLimitMsg = false;

      component.checkValidationForUploadFile();

      expect(component.isDisabled).toBe(false);
    });

    it('should keep upload disabled when file is missing', () => {
      component.fileUploadJSON = '';
      component.postUploadDataJson = { commentsInUpload: 'Test comment' };

      component.checkValidationForUploadFile();

      expect(component.isDisabled).toBe(true);
    });

    it('should handle file upload modal opening', () => {
      component.uploadFileInCreateRule();

      expect(component.createUploadOpenPopup).toBe(true);
      expect(component.isFileReady).toBe(true);
      expect(component.isTextReady).toBe(true);
      expect(component.fileUploadPopup).toBe('block');
    });
  });

  describe('Toggle Methods', () => {
    it('should set retro apply toggle', () => {
      const mockEvent = { toggle: true };

      component.setRetro(mockEvent);

      expect(component.retroApply).toBe(true);
    });

    it('should set bypass apply toggle', () => {
      const mockEvent = { toggle: true };

      component.setBypass(mockEvent);

      expect(component.bypassApply).toBe(true);
    });

    it('should set header level toggle', () => {
      const mockEvent = { toggle: true };

      component.setLevel(mockEvent);

      expect(component.headerLevel).toBe(true);
    });
  });

  describe('Modal and Popup Methods', () => {
    it('should close all popups', () => {
      component.createErrorOpenPopup = true;
      component.createOpenPopup = true;
      component.displayStyle = 'block';
      component.popupDisplayStyle = 'block';

      component.closePopup();

      expect(component.createErrorOpenPopup).toBe(false);
      expect(component.createOpenPopup).toBe(false);
      expect(component.displayStyle).toBe('none');
      expect(component.popupDisplayStyle).toBe('none');
    });
  });


  describe('Rule Creation', () => {
    it('should have createRule method', () => {
      expect(typeof component.createRule).toBe('function');
    });

    it('should call createEditRule and handle success', () => {
      // Simulate the form data as expected by createRule
      component.createFormData = { rule_name: 'Test Rule' };
      component.createRule();
      expect(mockRulesApiService.createEditRule).toHaveBeenCalled();
    });

    it('should handle createEditRule error', () => {
      mockRulesApiService.createEditRule.and.returnValue(throwError(() => new Error('API Error')));
      component.createFormData = { rule_name: 'Test Rule' };
      expect(() => component.createRule()).not.toThrow();
    });
  });

  describe('Error and Edge Case Handling', () => {
    it('should handle null/undefined fileUploadJSON in checkValidationForUploadFile', () => {
      component.fileUploadJSON = undefined;
      component.postUploadDataJson = { commentsInUpload: 'Test comment' };
      component.showMaxLimitMsg = false; // Ensure this is set correctly
      component.checkValidationForUploadFile();
      expect(component.isDisabled).toBe(true);
      component.fileUploadJSON = null;
      component.checkValidationForUploadFile();
      expect(component.isDisabled).toBe(true);
    });

    it('should handle missing comments in checkValidationForUploadFile', () => {
      component.fileUploadJSON = 'test-file.csv';
      component.postUploadDataJson = { commentsInUpload: '' };
      component.checkValidationForUploadFile();
      expect(component.isDisabled).toBe(true);
    });

    it('should handle undefined postUploadDataJson in checkValidationForUploadFile', () => {
      component.fileUploadJSON = 'test-file.csv';
      component.postUploadDataJson = undefined;
      component.checkValidationForUploadFile();
      expect(component.isDisabled).toBe(true);
    });

    it('should handle error in uploadFileInCreateRule', () => {
      component.createUploadOpenPopup = false;
      component.isFileReady = false;
      component.isTextReady = false;
      component.fileUploadPopup = '';
      expect(() => component.uploadFileInCreateRule()).not.toThrow();
      expect(component.createUploadOpenPopup).toBe(true);
    });

    it('should handle closePopup when popups are already closed', () => {
      component.createErrorOpenPopup = false;
      component.createOpenPopup = false;
      component.displayStyle = 'none';
      component.popupDisplayStyle = 'none';
      component.closePopup();
      expect(component.createErrorOpenPopup).toBe(false);
      expect(component.createOpenPopup).toBe(false);
    });

    it('should handle navigation error in cancelCreate', () => {
      mockRouter.navigate.and.throwError('Navigation error');
      expect(() => component.cancelCreate()).toThrow();
    });

    it('should handle missing rule_type in createFormData', () => {
      component.createFormData = {};
      expect(() => component.createRule()).not.toThrow();
    });
    it('should handle undefined array access in querySpecificationJson', () => {
      component.querySpecificationJson = [];
      expect(() => {
        if (component.querySpecificationJson[1]) {
          // simulate access
          component.querySpecificationJson[1].groupControls = [];
        }
      }).not.toThrow();
    });
    it('should handle clientData.map not a function', () => {
      component.clientData = undefined;
      expect(() => {
        if (component.clientData && Array.isArray(component.clientData)) {
          component.clientData.map(x => x);
        }
      }).not.toThrow();
      component.clientData = {};
      expect(() => {
        if (component.clientData && Array.isArray(component.clientData)) {
          component.clientData.map(x => x);
        }
      }).not.toThrow();
    });
  });

  describe('Navigation Methods', () => {
    it('should handle cancel create', () => {
      component.cancelCreate();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules']);
    });
  });

  describe('Utility Methods', () => {
    it('should check if value is defined', () => {
      expect(component.isDefined('test')).toBe(true);
      expect(component.isDefined(undefined)).toBe(false);
      expect(component.isDefined(null)).toBe(false);
    });

    it('should handle cancel create action', () => {
      component.cancelCreate();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/rules']);
    });
  });

  describe('Component Properties and Configuration', () => {
    it('should have correct file details section JSON', () => {
      expect(component.fileDetailsSectionJson).toBeDefined();
      expect(Array.isArray(component.fileDetailsSectionJson)).toBe(true);
    });

    it('should have correct general details JSON configuration', () => {
      expect(component.generalDetailsJson).toBeDefined();
      expect(Array.isArray(component.generalDetailsJson)).toBe(true);
    });
  });

  describe('getAllJsonFilesData', () => {
    it('should load and map all JSON, client, and concept data successfully', () => {
      // Arrange
      const sqlStructure = [
        {},
        { groupControls: [ { name: 'PRODUCT', visible: true }, { name: 'CONCEPT_ID', options: [] } ] }
      ];
      const customSQL = [];
      const mockAssetsJson = { sqlStructure, customSQL };
      const mockClients = [ { clientId: 1, clientName: 'Test Client' } ];
      const mockConcepts = {
        executionConceptAnalyticResponse: [
          { clientId: 1, exConceptReferenceNumber: 'CONCEPT-001' },
          { clientId: 2, exConceptReferenceNumber: 'CONCEPT-002' }
        ]
      };
      component.selectedProfileClientId = 1;
      mockRulesApiService.getAssetsJson.and.returnValue(of(mockAssetsJson));
      mockClientApiService.getAllClientsInPreferenceCenter.and.returnValue(of(mockClients));
      mockProductApiService.getProductConceptsId.and.returnValue(of(mockConcepts));

      // Act
      component.getAllJsonFilesData();

      // Assert
      expect(component.querySpecificationJson).toBe(sqlStructure);
      expect(component.customSqlJson).toBe(customSQL);
      expect(component.ruleLevelSelectionJson).toBe(sqlStructure);
      expect(component.clientData).toEqual([{ value: 1, name: 'Test Client' }]);
      expect(component.conceptData).toEqual([{ id: 'CONCEPT-001', name: 'CONCEPT-001' }]);
      expect(component.showQuerySpec).toBeTrue();
      expect(component.showLoader).toBeFalse();
    });

    it('should handle error in forkJoin and set clientData to []', () => {
      mockRulesApiService.getAssetsJson.and.returnValue(of({ sqlStructure: [], customSQL: [] }));
      mockClientApiService.getAllClientsInPreferenceCenter.and.returnValue(throwError(() => new Error('API error')));
      mockProductApiService.getProductConceptsId.and.returnValue(of({ executionConceptAnalyticResponse: [] }));
      component.selectedProfileClientId = 1;
      component.getAllJsonFilesData();
      expect(component.clientData).toEqual([]);
      expect(component.showLoader).toBeFalse();
    });

    it('should not throw if querySpecificationJson[1] or groupControls is undefined', () => {
      mockRulesApiService.getAssetsJson.and.returnValue(of({ sqlStructure: [{}], customSQL: [] }));
      component.selectedProfileClientId = 1;
      expect(() => component.getAllJsonFilesData()).not.toThrow();
    });
  });

  beforeEach(() => {
    // Defensive: Always initialize arrays and options for all groupControls
    component.querySpecificationJson = [
      { id: 'sqlType', value: 'qb', groupControls: [{ name: 'test', visible: true, options: [] }] },
      {
        id: 'queryBuilder',
        groupControls: [
          { name: 'PRODUCT', visible: true, options: [] },
          { name: 'CONCEPT_ID', visible: true, options: [] },
          { name: 'rulesLevel', visible: true, selectedVal: '', options: [] }
        ]
      }
    ];
    component.ruleLevelSelectionJson = [
      { id: 'sqlType', value: 'qb', groupControls: [{ name: 'test', visible: true, options: [] }] },
      {
        id: 'queryBuilder',
        groupControls: [
          { name: 'PRODUCT', visible: true, options: [] },
          { name: 'CONCEPT_ID', visible: true, options: [] },
          { name: 'rulesLevel', visible: true, selectedVal: '', options: [] }
        ]
      }
    ];
    component.customSqlJson = [{ id: 'customSql', value: '', groupControls: [], options: [] }];
    component.clientData = [
      { clientId: 1, clientName: 'Test Client 1' },
      { clientId: 2, clientName: 'Test Client 2' }
    ];
    // Defensive: Add stub for refineMasterData if called
    if (!component.refineMasterData) {
      component.refineMasterData = () => {};
    }
  });

  it('should not throw if groupControls or options are undefined in querySpecificationJson or ruleLevelSelectionJson', () => {
    component.querySpecificationJson = [
      { id: 'sqlType', value: 'qb' },
      { id: 'queryBuilder' }
    ];
    component.ruleLevelSelectionJson = [
      { id: 'sqlType', value: 'qb' },
      { id: 'queryBuilder' }
    ];
    expect(() => {
      // Defensive: Initialize if missing
      if (!component.querySpecificationJson[0].groupControls) component.querySpecificationJson[0].groupControls = [];
      if (!component.querySpecificationJson[1].groupControls) component.querySpecificationJson[1].groupControls = [];
      if (!component.ruleLevelSelectionJson[0].groupControls) component.ruleLevelSelectionJson[0].groupControls = [];
      if (!component.ruleLevelSelectionJson[1].groupControls) component.ruleLevelSelectionJson[1].groupControls = [];
      component.querySpecificationJson[1].groupControls.push({ name: 'PRODUCT', visible: true });
      component.ruleLevelSelectionJson[1].groupControls.push({ name: 'CONCEPT_ID', visible: true });
      if (!component.querySpecificationJson[1].groupControls[0].options) component.querySpecificationJson[1].groupControls[0].options = [];
      if (!component.ruleLevelSelectionJson[1].groupControls[0].options) component.ruleLevelSelectionJson[1].groupControls[0].options = [];
      // Set properties
      component.querySpecificationJson[1].groupControls[0].visible = false;
      component.ruleLevelSelectionJson[1].groupControls[0].options = [{ id: 1, name: 'Test' }];
    }).not.toThrow();
  });

  describe('CreateComponent Edge Cases', () => {
    it('should validate form with missing required fields', () => {
      // Setup form with missing fields
      component.createFormData = {
        rule_type: '', // Missing rule_type
        start_date: '2023-01-01',
        end_date: '2023-12-31'
      };
      component.createRule();
      expect(component.isDisabled).toBe(true);
    });

    it('should handle form submission with invalid data', () => {
      // Setup invalid form data
      component.createFormData = {
        rule_type: 'Exclusion',
        start_date: 'invalid-date', // Invalid date format
        end_date: '2023-12-31'
      };
      component.createRule();
      expect(component.isDisabled).toBe(true);
    });

    it('should handle file upload with unsupported file type', () => {
      // Simulate unsupported file type upload
      component.fileUploadJSON = 'test-file.exe'; // .exe is not supported
      component.postUploadDataJson = { commentsInUpload: 'Test comment' };
      component.checkValidationForUploadFile();
      expect(component.isDisabled).toBe(true);
    });

    it('should handle file upload error', () => {
      // Simulate file upload error
      mockRulesApiService.addFilesToRules.and.returnValue(throwError(() => new Error('File upload error')));
      component.uploadFileInCreateRule();
      expect(component.createUploadOpenPopup).toBe(true);
      expect(component.isFileReady).toBe(false);
      expect(component.isTextReady).toBe(false);
    });

    it('should handle error during form submission', () => {
      // Simulate service error on submit
      mockRulesApiService.createEditRule.and.returnValue(throwError(() => new Error('Submission error')));
      component.createFormData = { rule_name: 'Test Rule' };
      component.createRule();
      expect(component.showLoader).toBe(false);
    });
  });
});
