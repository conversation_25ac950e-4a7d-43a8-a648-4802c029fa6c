# PI Portal


## Repo, Branch & SPOCs

1. *Repository URL*: `https://<domain-id>@bitbucket.elevancehealth.com/scm/piplatform/pi-pf-portal-ui.git`
2. *Active Branch*: `develop`
3. *Marketplace Library Packages Location:*  `https://<domain-id>>@bitbucket.elevancehealth.com/scm/piplatform/pi-pf-components-ui.git`
4. *Marketplace Components:* `https://va33dwviss508.devad.wellpoint.com:90`


## Developer instructions for Code commit, push, PR, merge and deployments
1. *Branch:* Always create a sub branch from active branch with the feature/story number 
`Eg: feature/<Dev-DomainID>/PITP-1234`
2. *Commit:* All the commits should have the feature/story number for that commit including detailed overview message of what that commit is for 
`Eg: git commit -m "PITP-1234 Code Chande details"`
3. *Before raising PR, make sure*
    * Make sure coding best practices are followed before committing the changes to feature branch.
    * Always Squash multiple commits of a story to single commit.
    * All PRs should be raised from local branch to `active branch`. 
    * PR should include details of what was done. (Screenshots are optional).
    * latest code updated to local branch from active branch. 
    * Remove all merge conflicts.
    * Raise PR to SIT only after the DEV region is tested with the changes.
4. *Story*: Make sure to add a detailed comment under the story including screenshots once the story is complete.


## Clone the repo

```shell
git clone https://<domain-id>@bitbucket.elevancehealth.com/scm/piplatform/pi-pf-portal-ui.git

git checkout <branch-name>

cd pi-pf-portal-ui/
```

## Install npm packages

Install the `npm` packages described in the `package.json` and verify that it works:
The package also includes marketplace dependent modules.

```shell
npm install
npm start
```

The `npm start` command builds (compiles TypeScript and copies assets) the application into `dist/`, watches for changes to the source files, and runs `server` on port `4200`.

## npm scripts

These are the most useful commands defined in `package.json`:

* `npm start` - runs the TypeScript compiler, asset copier, and a server at the same time, all three in "watch mode".
* `npm run build` - runs the TypeScript compiler and asset copier once.

These are the test-related scripts:

* `npm test` - builds the application and runs Intern tests (both unit and functional) one time.

## For Local Testing

1) Keep the call back url as "callBackUrl: 'http://localhost:4200/login/callback'" in environment.ts file
2) Enable below portion and comment the For Deployment portion in call-back component
            //For Local Testing
            this.jwtService.verifyToken(tokenDetails[AUTH_CONFIG.APP_TOKEN]).then((appToken) => {
                this.cookie.set(APP_CONSTANTS.USER_NAME, appToken[AUTH_CONFIG.SUB]);
                this.cookie.set(APP_CONSTANTS.PORTAL_ACCESS, APP_CONSTANTS.TRUE);
                this.router.navigate(['']);
            });
3) After running the application, it will be redirected to Dev environment call back url, replace it with localhost call back url 'http://localhost:4200/login/callback' in the        address bar

### PITP Module Structure
```
Application and PITP module structure

src
 ├── app
 │   ├── _constants                           # Constants across module
 |   ├── _helpers                             # Helper methods
 |   ├── _models                              # Models (types and interfaces if any)
 |   ├── _services                            # All Services
 |   ├── *project modules*                    # All Modules/Components created for the project
 |   └── ...
 └── ...

PITP components structure:
...
├── component folder name                  
|   ├── component.html         
|   ├── component.sass     
|   ├── component.ts
|   └── component.spec.ts
└── ...
 ```


## Good practices & Optimizations
Following are good practices followed in scheduler module:

* `Lazy Loading`: Module Content-management and scheduler are lazy loaded
* `Files`: Lines of code for each file should not exceed 500
* `Functions`: Lines of code for each function should not exceed 75
* `Comments`: All functions have appropriate comments with function name, description,  parameter descriptions and return value descriptions
* Using `Angular life cycle hooks (onInit & onDestroy)` in all scheduler components
* Intact variables are declared with const instead of any type
* Using  `strict types` instead of "any"
* All complex logics are broken down into multiple, manageable smaller components, dedicating each one to an `atomic task`.
* `Memory leaks` from observables have been prevented by `unsubscribing observables`
* Converting CSS to SASS and effective usage of `mixins in SASS` for more optimization
* Extensive use of `*ngIf` instead of SASS
* `Trackby` is used in `*ngFor` which will help angular create/remove only changed attributes
* Usage of `mergemap and forkjoins` for nested & parallel observables
* Error Handling: Proper `catch() throwError()` and error messages have been included

## Jasmine testing

The behavior driven development testing is done using jasmine testing framework with different functions like `describe`, `it`, `beforeEach`, and `expect` for different modules.