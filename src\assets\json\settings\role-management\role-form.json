[{"type": "text", "name": "<PERSON><PERSON><PERSON>", "label": "Role Name", "column": 1, "id": "<PERSON><PERSON><PERSON>", "disabled": false, "placeholder": "Enter Role Name", "value": ""}, {"type": "textarea", "name": "description", "label": "Role Description", "column": 1, "id": "description", "disabled": false, "placeholder": "Enter description", "value": ""}, {"type": "text", "name": "clientId", "label": "Client", "column": "3", "id": "clientId", "disabled": false, "value": ""}, {"type": "text", "name": "productId", "label": "Product Name", "column": "4", "id": "productId", "disabled": false, "value": ""}, {"type": "text", "name": "businessDivision", "label": "Business Division", "column": "4", "id": "businessDivision", "disabled": false, "placeholder": "select Business Division", "value": "", "visible": false}, {"id": "reminderDate", "label": "Reminder Date", "type": "date", "name": "reminderDate", "column": "4", "disabled": false, "placeholder": "Please select <PERSON><PERSON><PERSON> Date", "pickerType": "single", "value": ""}, {"options": [{"name": "Active", "value": true}, {"name": "Inactive", "value": false}], "id": "status", "optionName": "name", "optionValue": "value", "label": "Role Status", "type": "radio", "name": "status", "column": "4", "disabled": true, "required": true, "value": "", "customClass": "form-radio-button"}, {"text": "Offshore", "label": "Client Site", "name": "clientSite", "type": "switch", "column": "3", "preLabel": "Onshore", "disabled": false, "value": ""}, {"options": [{"name": "Internal", "value": "internal"}, {"name": "External", "value": "external"}], "optionName": "name", "optionValue": "value", "label": "Select Team", "type": "radio", "name": "teamType", "column": "4", "disabled": false, "customClass": "form-radio-button", "value": ""}]