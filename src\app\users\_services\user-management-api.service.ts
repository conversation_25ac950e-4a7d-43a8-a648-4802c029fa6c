import { Injectable } from '@angular/core';
import { EMPTY, Observable, throwError } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { CookieService } from 'ngx-cookie-service';
import { Validators } from '@angular/forms';
import { environment } from '../../../environments/environment';
import { ROUTING_LABELS } from '../../_constants/menu.constant';
import { constants } from '../users-constants';

@Injectable({
  providedIn: 'root'
})
export class UserManagementApiService {

  constructor(private http: HttpClient, private cookieService: CookieService) { }

  validationOfName: boolean;

  /**
   * Method to get all columnConfigs for product table
   * @returns 
   */
  getTableColumn(url): Observable<any> {
    return this.http.get(url);
  }
  //api/v1
  /**
   * Method to get list of all screens
   * @returns 
   */
  getListOfScreensForAddRole(): Observable<any> {
    return this.http.get<any>(`${environment.authService}/api/v1/roles/getScreenList`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }
  /**
   * Method to validate user input using Regex
   */
  checkManagerNameValidation(managerName: any) {
    this.validationOfName = false;
    let allowedCharacters = /[^a-zA-Z0-9\(\)\ ]/;
    if (allowedCharacters.test(managerName)) {
      this.validationOfName = true;
    }
  }
  /**
   * Method to validate user dependent Fields
   */
  handleUserDependFieldsVisibility(form, userStatus) {
    let deactivationStatus = form.userFormJSON.find(x => x.id == 'deactivationStatus'),
      deactivationReason = form.userFormJSON.find(x => x.id == 'deactivationReason'),
      deStatusField = form.userFormRef.form.get('deactivationStatus'),
      deReasonField = form.userFormRef.form.get('deactivationReason');
    if (userStatus === constants.INACTIVE) {
      deactivationStatus.visible = true;
      deactivationStatus.required = true;
      deactivationReason.visible = true;
      deactivationReason.required = true;
      deStatusField.setValidators([Validators.required]);
      deStatusField.updateValueAndValidity();
      deReasonField.setValidators([Validators.required]);
      deReasonField.updateValueAndValidity();
      deactivationStatus.value = 'Temporary';
      deStatusField.setValue('Temporary');
    } else {
      deactivationStatus.visible = false;
      deactivationStatus.required = false;
      deactivationReason.visible = false;
      deactivationReason.required = false;
      deStatusField.clearValidators();
      deReasonField.clearValidators();
      deStatusField.updateValueAndValidity();
      deReasonField.updateValueAndValidity();
    }
  }


  /**
   * Method to validate user dependent Fields for Bulk Edit
   */
  handleUserDependFieldsVisibilityBulkEdit(form, userStatus) {
    let deactivationStatus = form.userFormJSON.find(x => x.id == 'deactivationStatus'),
      deactivationReason = form.userFormJSON.find(x => x.id == 'deactivationReason'),
      deStatusField = form.userFormRef.form.get('deactivationStatus'),
      deReasonField = form.userFormRef.form.get('deactivationReason');
    if (userStatus === constants.INACTIVE) {
      deactivationStatus.visible = true;
      deactivationStatus.required = true;
      deactivationReason.visible = true;
      deactivationReason.required = true;
      deStatusField.setValidators([Validators.required]);
      deStatusField.updateValueAndValidity();
      deReasonField.setValidators([Validators.required]);
      deReasonField.updateValueAndValidity();
      deactivationStatus.value = '';
      deStatusField.setValue('');
    } else {
      deactivationStatus.visible = false;
      deactivationStatus.required = false;
      deactivationReason.visible = false;
      deactivationReason.required = false;
      deStatusField.clearValidators();
      deReasonField.clearValidators();
      deStatusField.updateValueAndValidity();
      deReasonField.updateValueAndValidity();
    }
  }

  /**
   * Method to get particular Role for Edit Role and View Role page
   * @returns 
   */
  getIndividualRole(clientId: number, roleId: number, prodId: number): Observable<any> {
    return this.http.get<any>(`${environment.authService}/api/v1/roles/getRoleAndScreenData?clientId=${clientId}&roleId=${roleId}&prodId=${prodId}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method to get all the roles
   * @returns 
   */
  getRolesList(): Observable<any> {
    //return this.http.get('assets/test-data/get-roles-screen-list.json')

    /**Merge */
    return this.http.get<any>(`${environment.authService}/api/v1/roles/getRolesList`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
  * Method to user form json
  * @returns 
  */
  getUserFormJson(url): Observable<any> {
    return this.http.get<any>(url).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
   * Method to get any json's from the assets
   * @returns 
   */
  getAssetsJson(url): Observable<any> {
    return this.http.get<any>(url).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
  * Method to get list of users
  * @returns 
  */
  getUsersList(): Observable<any> {
    return this.http.get<any>(`${environment.authService}/api/v1/roles/getUserList`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
  * Method to get list of all the Managers
  * @returns 
  */
  getMangersList(): Observable<any> {
    return this.http.get<any>(`${environment.authorizationUrl}api/dbg-authorization/user/getManagerList`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
  * Method to get user details
  * @returns 
  */
  getUserDetails(userId): Observable<any> {
    return this.http.get<any>(`${environment.authService}/api/v1/roles/viewUser/${userId}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }
  /**
   * Method for rejecting user approval
   * @returns 
   */
  rejectUserApproval(userDetailsObj): Observable<any> {
    return this.http.post<any>(`${environment.authorizationUrl}/api/dbg-authorization/user/updateStatus`, userDetailsObj).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
  * Method to save user
  * @returns 
  */
  createUpdateUser(user): Observable<any> {
    return this.http.post<any>(`${environment.authService}/api/v1/roles/assignRole`, user).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }
  /**
* Method to get the roles and the screen access for the user
* @returns 
*/
  getuserRolesList(): Observable<any> {
    // return this.http.get<any>(`${environment.authorizationUrl}/api/dbg-authorization/role/getRoleScreenList`).pipe(
    //   catchError(err => {
    //     return throwError(err);
    //   }));
    return EMPTY;
  }

  /**
   * Method to get the master data for the user screen
   * @returns 
   */
  getUserMasterData(): Observable<any> {
    return this.http.get<any>(`${environment.authorizationUrl}/api/dbg-authorization/masterConfig/getSkills`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }


  /**
   * Method to get the skill data based on logged in user
   * @returns 
   */
  getSkillsByUserId(): Observable<any> {
    let userId = (this.cookieService.get(ROUTING_LABELS.USER_ID)).toUpperCase();
    return this.http.get<any>(`${environment.authorizationUrl}/api/dbg-authorization/user/getAllSkillsByUserId/${userId}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }

  /**
     * @function loadingSkillValuesForProduct Loading Skill Values based on client and product
     * @param key 
     * @param currentForm 
     * @param masterData 
     * @param client obj
     * @param product obj
     */
  loadingSkillValuesForProduct(key: any, currentUserSkillsFormData: any, masterData: any, client: any, product: any, value: any): any {
    let values = currentUserSkillsFormData[key];

    let skillDetail = masterData.skills.find(x => x.skillName.toLowerCase() === key.toLowerCase() && x.clientId === client.id && x.prodId === product.id);
    let allValue = skillDetail?.values.find(x => x.value == constants.ALL)?.id;
    if (allValue && currentUserSkillsFormData[key].includes(allValue)) {
      values = [allValue];
    }
    if (key == constants.MAX_CLAIM_COUNT && skillDetail?.values[0]?.id) { //// Max Claim count will be sent only when it is configured in DB
      values = [skillDetail?.values[0].id]
    }
    return { "skillType": key, "values": values, "prodId": product?.id, "prodName": product?.name, "clientId": client?.id, "clientName": client?.name, "skillValue": value };
  }

  /**
   * Adding All Option to Skills
   */
  addingAllOption(newSkillJSON: any, currentUserSkillsFormData: any): void {

    let skillDetails = newSkillJSON[0];
    skillDetails.groupControls?.forEach(skill => {
      let allOption = skill.options?.find(x => x.label == constants.ALL);
      if (allOption) {
        skill.options.splice(skill.options.findIndex(x => x.label == constants.ALL), 1);
        skill.options.unshift(allOption);
      }
      currentUserSkillsFormData[skill.name] = allOption?.value ? [allOption.value] : [];
      skill.selectedVal = allOption?.value ? [allOption.value] : [];
    });
  }

  //C2P
  // /**
  // * Gets bulk User Edit flow User Search details
  // * @param - searchUserPayload
  // * @returns search results
  // */
  // getBulkSearchUserList(searchUserPayload: ISearchUserFields): Observable<ISearchUsersForBulkAddition[]> {
  //   return this.http.post<ISearchUsersForBulkAddition[]>(`${environment.authorizationUrl}/api/dbg-authorization/user/getAddUserList`, searchUserPayload).pipe(
  //     catchError(err => {
  //       return throwError(err);
  //     }));
  // }

  /**
   * Method to get the master data for the user screen
   * @returns 
   */
  getClientMasterData(clientId: number): Observable<any> {
    return this.http.get<any>(`${environment.authorizationUrl}/api/dbg-authorization/masterConfig/getSkills/${clientId}`).pipe(
      catchError(err => {
        return throwError(err);
      }));
  }


  //C2P
  // /* Get all the access related to Menu and Sub menu for COB application only */
  // getPermissionListing(): Observable<any> {
  //   return this.http.get<any>(`${environment.cobDomainUrl}/api/dbg-cob/feature-access-info?status=true`).pipe(
  //     catchError(err => {
  //       return throwError(err);
  //     }));
  // }

  /* Main Menu and Sub menu access based on client specific to COB application only */
  //C2P
  // cobScreenAccess(data): Promise<any> {
  //   return new Promise((resolve, reject) => {
  //     let product = ROUTING_LABELS.COB;
  //     let indexOfCOB = data.screenAccess_Cards.findIndex(list => list.label === product);
  //     if (indexOfCOB > -1) {
  //       this.getPermissionListing().subscribe({
  //         next: (permissionList) => {
  //           try {
  //             let permissionArray = []
  //             permissionList.map((item, i) => {
  //               let indexOfMenu = data.screenAccess_Cards[indexOfCOB].subMenu.findIndex(e => e.label === item.parentScreenName);
  //               if (indexOfMenu > -1) {
  //                 permissionArray.push({
  //                   label: item.parentScreenName,
  //                   id: item.parentScreenId,
  //                   permission: "create,read",
  //                   subMenu: item.subScreen
  //                 })
  //               }
  //               if ((permissionList.length - 1) === i) {
  //                 data.screenAccess_Cards[indexOfCOB].subMenu = permissionArray;
  //                 data.screenAccess_Cards[indexOfCOB].subMenu.sort((a, b) => a.id - b.id);
  //                 sessionStorage.setItem("cob_screen_access", JSON.stringify(data.screenAccess_Cards[indexOfCOB]));
  //                 resolve(data);
  //               }
  //             })
  //           } catch (error) {
  //             data = data.screenAccess_Cards.splice(indexOfCOB, 1);
  //             reject(data);
  //           }
  //         },
  //         error: (err) => {
  //           console.error('Error fetching permissions:', err);
  //           data = data.screenAccess_Cards.splice(indexOfCOB, 1);
  //           reject(data);
  //         }
  //       });
  //     } else {
  //       resolve(data);
  //     }
  //   });
  // }
}
