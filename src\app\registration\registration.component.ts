import { Component, ViewEncapsulation, Input, SimpleChanges, NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, HostListener } from '@angular/core';
import { default as registration } from 'src/assets/json/registration/registration-form.json'
import { cloneDeep } from 'lodash';
import { RegistrationConstants } from './constants';
import { ToastService } from '../_services/toast.service';
import { AuthService } from 'src/app/_services/authentication.services';
import { ExternalSOAService } from 'src/app/_services/external-soa.service';
import { IExternalUserSearch, ISearchUserFilter } from 'src/app/_models/external/external-user-search';
import { INewUserInfo, IExternalRegistration } from '../_models/external/external-registration';
import { EXTERNALUSER } from 'src/app/_models/external-user-constants';
import { externalAuthenticationConstants } from 'src/app/_helpers/helpers.constants';
import { IExternalAddUser } from 'src/app/_models/external/external-add-user';
import { ClientApiService } from '../_services/client-preference-api.service';
import { Router } from '@angular/router';
import { MPUIButtonModule } from 'marketplace-button';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { CommonModule } from '@angular/common';
import { MPUISelectModule } from 'marketplace-select';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MPUIInputModule } from 'marketplace-input';
import { MPUIRadioButtonModule } from 'marketplace-radio-button';
import {MPUIDatePickerModule} from 'marketplace-date-picker';
import {MPUISwitchModule} from 'marketplace-switch';
import {MPUICheckboxModule} from 'marketplace-checkbox';
import {MPUITextareaModule} from 'marketplace-textarea';
import {MPUITimePickerModule} from 'marketplace-time-picker'
import { NgSelectModule } from '@ng-select/ng-select';
import { MPUINotificationModule } from 'marketplace-notification';
import { RouterModule } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import {AUTH_CONFIG } from '../_constants/app.constants';
@Component({
  selector: 'app-registation',
  templateUrl: './registration.component.html',
  styleUrls: ['./registration.component.sass'],
  encapsulation: ViewEncapsulation.None, 
  schemas:[CUSTOM_ELEMENTS_SCHEMA] ,
  standalone:true,
  imports:[
    CommonModule,
    FormsModule,    
    MPUIDatePickerModule,
    MPUITextareaModule,    
    MPUIButtonModule,    
    MPUIInputModule,
    MPUISelectModule,
    MPUICheckboxModule,
    MPUIRadioButtonModule,
   // MPUISwitchModule,
    //MPUITimePickerModule,
    MPUINotificationModule,
    MPUIDynamicFormModule,
    RouterModule
  ]  
})
export class RegistrationComponent {

  userSearchPayload: IExternalUserSearch;
  isExternalUserRegistration: boolean = false;
  newUserPaylod: IExternalRegistration;
  registrationDetailsJson: any;
  isRegistrationJSONReady: boolean = false;
  btnText: string = `<span class='btn-text'><i class="btn-account fa fa-solid fa-user-plus"></i> Create account</span>`;
  createAccountSVG: string = `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M12 9.78223C13.6569 9.78223 15 8.43908 15 6.78223C15 5.12537 13.6569 3.78223 12 3.78223C10.3431 3.78223 9 5.12537 9 6.78223C9 8.43908 10.3431 9.78223 12 9.78223ZM3 19.7822C3 16.4569 6.40414 13.7822 12 13.7822C17.5959 13.7822 21 16.4569 21 19.7822C21 21.2793 20.2424 22.053 18.356 22.4363L17.9766 22.5057C17.8454 22.5273 17.7094 22.5473 17.5685 22.5659L17.131 22.6174C17.0556 22.6253 16.979 22.6329 16.9011 22.6401L16.4183 22.6796L15.9047 22.7118L15.3597 22.7372L14.7825 22.7564L13.8553 22.7745L13.1953 22.7804L11.1467 22.7816L10.144 22.7745L9.51771 22.7638L8.92435 22.7476L8.36337 22.7254L7.83416 22.6966L7.33614 22.6608L6.86869 22.6174L6.43123 22.5659C6.29034 22.5473 6.15435 22.5273 6.02316 22.5057L5.64387 22.4363C3.75756 22.053 3 21.2793 3 19.7822Z" fill="#E4E4E4"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2.78223C9.79086 2.78223 8 4.57309 8 6.78223C8 8.99137 9.79086 10.7822 12 10.7822C14.2091 10.7822 16 8.99137 16 6.78223C16 4.57309 14.2091 2.78223 12 2.78223ZM12 4.28223C13.3807 4.28223 14.5 5.40151 14.5 6.78223C14.5 8.16294 13.3807 9.28223 12 9.28223C10.6193 9.28223 9.5 8.16294 9.5 6.78223C9.5 5.40151 10.6193 4.28223 12 4.28223ZM12 13.7822C6.40414 13.7822 3 16.4569 3 19.7822C3 21.2793 3.75756 22.053 5.64387 22.4363L6.02316 22.5057C6.15435 22.5273 6.29034 22.5473 6.43123 22.5659L6.86869 22.6174L7.33614 22.6608L7.83416 22.6966L8.36337 22.7254L8.92435 22.7476L9.51771 22.7638L10.144 22.7745L11.1467 22.7816L13.1953 22.7804L13.8553 22.7745L14.7825 22.7564L15.3597 22.7372L15.9047 22.7118L16.4183 22.6796L16.9011 22.6401C16.979 22.6329 17.0556 22.6253 17.131 22.6174L17.5685 22.5659C17.7094 22.5473 17.8454 22.5273 17.9766 22.5057L18.356 22.4363C20.2424 22.053 21 21.2793 21 19.7822C21 16.4569 17.5959 13.7822 12 13.7822ZM12 15.2822C16.836 15.2822 19.5 17.3754 19.5 19.7822C19.5 20.3336 19.0669 20.7059 18.0192 20.9416L17.718 21.0025C17.6653 21.0121 17.6112 21.0215 17.5558 21.0305L17.2074 21.0817L16.8265 21.1265L16.4122 21.1653L15.7265 21.2124L15.2253 21.2367L14.6879 21.2556L14.1135 21.2693L13.1806 21.2804L10.8187 21.2804L9.88593 21.2693L9.31157 21.2556L8.77423 21.2367L8.27309 21.2124L7.80734 21.1824L7.37615 21.1466L6.97872 21.1049C6.91525 21.0974 6.85315 21.0896 6.7924 21.0817L6.44406 21.0305C6.38866 21.0215 6.33459 21.0121 6.28182 21.0025L5.98072 20.9416C5.9331 20.9309 5.88675 20.9199 5.84165 20.9086L5.58596 20.8374C4.82301 20.601 4.5 20.2584 4.5 19.7822C4.5 17.3754 7.16404 15.2822 12 15.2822Z" fill="#231E33"/>
</svg>
<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M8.74315 3.43046C8.69349 3.06438 8.3797 2.78223 8 2.78223C7.58579 2.78223 7.25 3.11801 7.25 3.53223V8.03223H2.75L2.64823 8.03907C2.28215 8.08874 2 8.40253 2 8.78223C2 9.19644 2.33579 9.53223 2.75 9.53223H7.25V14.0322L7.25685 14.134C7.30651 14.5001 7.6203 14.7822 8 14.7822C8.41421 14.7822 8.75 14.4464 8.75 14.0322V9.53223H13.25L13.3518 9.52538C13.7178 9.47572 14 9.16192 14 8.78223C14 8.36801 13.6642 8.03223 13.25 8.03223H8.75V3.53223L8.74315 3.43046Z" fill="#231E33"/>
</svg>`;
  isFormSubmitted: boolean = false;
  isReadOnly: boolean = false;
  showLoader: boolean = false;
  registrationFormData: any;
  loggedInUserId: string;
  clientData: any;
  contactUsUrl: string = externalAuthenticationConstants.CONTACT_US_URL;
  constructor(public alertService: ToastService, private authService: AuthService, private router: Router,
    private soaService: ExternalSOAService, private clientApiService: ClientApiService, private cookieService: CookieService) {
  }


  ngOnInit() {
     // Retrieve the user ID from the cookies set in the callback component
     this.loggedInUserId = this.cookieService.get(AUTH_CONFIG.SUB);
    if (this.router.url == '/signin/register') {
      this.isExternalUserRegistration = true;
    }
    this.isReadOnly = !this.authService.isWriteOnly;
    this.registrationDetailsJson = cloneDeep(registration);
    setTimeout(() => {
      const field = document.getElementById(RegistrationConstants.PASSWORD_FIELD);
      if (field) {
        field.setAttribute('title', RegistrationConstants.PASSWORD_TOOL_TIP)
      }
    }, 0);
  }


  /**
   * Method fires on value change of form
   * @param event 
   */
  onChange(event: any) {
    this.isFormSubmitted = false
    let pwd = event.current.personal.password;
    let userID = event.current.personal.userId;
    userID ? this.checkUserIdValidations(userID) : '';
    pwd ? this.checkPasswordValidations(pwd, userID) : '';

    if (event.current.personal.userId != event.previous.personal.userId) {
      this.searchUser(event.current.personal.userId);
    }

    this.registrationFormData = event.current;
  }

  /**
   * Method to check password validations
   * @param pwd 
   * @param userId 
   */
  checkPasswordValidations(pwd: string, userID: string) {
    let allowedCharacters = /^[a-zA-Z0-9!@#\$%\^\*\)\(._-]+$/;
    let regexMinMax = /^[A-Za-z0-9_!#$%^*9()+,.?;:}{|@\]]{8,20}$/
    this.registrationDetailsJson[0].groupControls[1].customErrMsg = '';
    if (allowedCharacters.test(pwd)) {
      if (pwd == userID) {
        this.isFormSubmitted = true
        this.registrationDetailsJson[0].groupControls[1].customErrMsg = "UserId and Password cannot be same";
      }
      else if (userID && pwd.indexOf(userID.substring(0, 3)) > -1) {
        this.isFormSubmitted = true
        this.registrationDetailsJson[0].groupControls[1].customErrMsg = RegistrationConstants.PWD_USR_CONTAINS_ERRMSG
      }

      else if (pwd.match(/(.)\1{2,}/)) {
        this.isFormSubmitted = true
        this.registrationDetailsJson[0].groupControls[1].customErrMsg = RegistrationConstants.PWD_CONSECUTIVE_ERRMSG
      }
      else if (!regexMinMax.test(pwd)) {
        this.isFormSubmitted = true
        this.registrationDetailsJson[0].groupControls[1].customErrMsg = "Password should be min 8 and max 20 with valid characters";
      }
    }
    else {
      this.isFormSubmitted = true
      this.registrationDetailsJson[0].groupControls[1].customErrMsg = RegistrationConstants.INVALID_CHAR
    }
  }

  /**
   * Method to check user id validations
   * @param userID 
   */

  checkUserIdValidations(userID: any) {
    let allowedCharacters = /^[a-zA-Z0-9!@\$\^\_-]+$/;
    let regexMinMax = /^[A-Za-z0-9_@]{6,20}$/
    this.registrationDetailsJson[0].groupControls[0].customErrMsg = '';
    if (allowedCharacters.test(userID)) {

      if (userID && !isNaN(userID.substring(0, 1))) {
        this.isFormSubmitted = true
        this.registrationDetailsJson[0].groupControls[0].customErrMsg = RegistrationConstants.USERID_NUMBER_ERRMSG
      }

      else if (userID && isNaN(userID.substring(0, 2)) && !isNaN(userID.substring(2, userID.length - 1))) {
        this.isFormSubmitted = true
        this.registrationDetailsJson[0].groupControls[0].customErrMsg = RegistrationConstants.USERID_ELEVANCE_ERRMSG
      }
      else if (!regexMinMax.test(userID)) {
        this.isFormSubmitted = true
        this.registrationDetailsJson[0].groupControls[0].customErrMsg = RegistrationConstants.USERID_MIMMAX_ERRMSG
      }
    }
    else {
      this.isFormSubmitted = true
      this.registrationDetailsJson[0].groupControls[0].customErrMsg = RegistrationConstants.INVALID_CHAR
    }
  }

  /**
   * To remove highlighting from fieds which passed validation
  */
  resetValidFields() {
    const collection = document.querySelectorAll(
      `marketplace-dynamic-form input.form-control.ng-valid ,
      marketplace-dynamic-form marketplace-input.ng-invalid,
    marketplace-dynamic-form .ng-select.ng-select-single.ng-valid .ng-select-container,
    marketplace-dynamic-form .ng-select.ng-select-multiple.ng-valid .ng-select-container,
    marketplace-textarea.ng-valid .textarea-holder textarea,
    marketplace-date-picker.ng-valid input.ng2-flatpickr-input.flatpickr-input`
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].classList.remove('redBorder');
    }

  }

  /**
   * Method to show the validations in red color
   */
  showInvalid() {
    this.resetValidFields();
    const invalidCollection = document.querySelectorAll(
      `marketplace-dynamic-form input.form-control.ng-invalid ,
      marketplace-dynamic-form marketplace-input.ng-invalid,
      marketplace-dynamic-form .ng-select.ng-select-single.ng-invalid .ng-select-container,
      marketplace-dynamic-form .ng-select.ng-select-multiple.ng-invalid .ng-select-container,
      marketplace-textarea.ng-invalid .textarea-holder textarea,
      marketplace-date-picker.ng-invalid input.ng2-flatpickr-input.flatpickr-input`
    );
    for (let i = 0; i < invalidCollection.length; i++) {
      //invalidCollection[i].classList.add('redBorder');
    }

    if (invalidCollection.length == 0)
      this.registerUser();
  }

  /**
   * Method to register the user
   */
  register() {
    if (this.isReadOnly) return
    this.isFormSubmitted = true;
    this.showInvalid();

  }

  /**
   * clearNotification function call to alertservice
   */
  clearNotification(): void {
    this.alertService.clearNotification();
  }

  /**
   * Search New User
   */
  searchUser(userId: string): void {
    this.showLoader = true;
    this.isReadOnly = true;

    this.soaService.searchExtUser(userId).subscribe((data: any) => {
      if (data.exceptions) {
        this.isReadOnly = false;
        this.showLoader = false;
      }
      else {
        this.isReadOnly = true;
        this.showLoader = false;
        this.alertService.setErrorNotification({
          notificationHeader: externalAuthenticationConstants.ERROR,
          notificationBody: externalAuthenticationConstants.USER_EXIST_ERRMSG
        })
      }
    }, (error: any) => {
      this.isReadOnly = false;
      this.showLoader = false;
    })

  }

  /**
   * Register User
   */
  registerUser(): void {
    this.showLoader = true;
    let newUserInfo: INewUserInfo = {
      username: this.registrationFormData.personal.userId,
      password: this.registrationFormData.personal.password,
      firstName: this.registrationFormData.personal.firstname,
      lastName: this.registrationFormData.personal.lastname,
      emailAddress: this.registrationFormData.personal.email,
      dataOfBirth: "",
      secretQuestionAnswers: [],
      repositoryEnum: EXTERNALUSER.REPOSITORYENUM,
      userRoleEnum: EXTERNALUSER.APPLICATION,
      memberOf: []
    }

    let externalUser: IExternalAddUser = {
      userId: newUserInfo.username,
      userName: newUserInfo.firstName + " " + newUserInfo.lastName,
      createdUserId: this.loggedInUserId,
      phoneNumber: this.registrationFormData.personal.phone,
      emailId: this.registrationFormData.personal.email,
      clientCode: this.registrationFormData.role.organizationId,
      clientDomainId: this.registrationFormData.clientDomain.clientDomainId,
      businessJustification: this.registrationFormData.busJustification.businessJustification,
      phiRequired: this.registrationFormData.phiAccess.isPhiRequeried === "Yes" ? true : false,
      isSelfRegistered: this.isExternalUserRegistration,
    }

    this.newUserPaylod = {
      newUserInfo: newUserInfo,
      internalUserInfo: externalUser,
      newUser: true
    };


    this.soaService.createNewUser(this.registrationFormData.personal.userId, this.newUserPaylod)
      .subscribe((data: any) => {
        if (data.responseCode === 400 && data.responseName === 'BAD_REQUEST') {
          this.alertService.setErrorNotification({
            notificationHeader: RegistrationConstants.ERROR,
            notificationBody: data.responseData
          });
          this.showLoader = false;
        } else {
          this.alertService.setSuccessNotification({
            notificationHeader: RegistrationConstants.SUCCESS,
            notificationBody: RegistrationConstants.SUCCESS_MESG,
          });
          this.showLoader = false;

          if (this.isExternalUserRegistration) {
            this.router.navigate(['/signin/register/sucess']);
          } else {
            setTimeout(() => { window.location.reload(); }, 2000);
          }
        }


      }, (error: any) => {
        let notificationHeaderMsg: string;
        let notificationBodyMsg: string;

        if (error.includes('exceptions')) { //// SOA exception added to message at API end and is sent as string JSON
          if (JSON.parse(error)?.exceptions[0]?.code == RegistrationConstants.SOA_INVALID_PASSWORD) {
            notificationHeaderMsg = RegistrationConstants.SOA_INVALID_PASSWORD_NOTIFICATION_HRD;
            notificationBodyMsg = RegistrationConstants.SOA_INVALID_PASSWORD_NOTIFICATION_BDY;
          }
          else {
            notificationHeaderMsg = JSON.parse(error)?.exceptions[0]?.code;
            notificationBodyMsg = JSON.parse(error)?.exceptions[0]?.message;
          }
        }
        else {
          notificationHeaderMsg = RegistrationConstants.ERROR;
          notificationBodyMsg = RegistrationConstants.CREATE_USER_ERRMSG;
        }

        this.alertService.setErrorNotification({
          notificationHeader: notificationHeaderMsg,
          notificationBody: notificationBodyMsg
        });
        this.showLoader = false;
      })
  }
}