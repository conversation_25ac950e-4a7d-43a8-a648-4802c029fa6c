import { Component, OnInit, ElementRef, ViewEncapsulation } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { forkJoin } from 'rxjs';
import { environment } from 'src/environments/environment';
import { UserManagementApiService } from 'src/app/users/_services/user-management-api.service';
import { ClientApiService } from 'src/app/_services/client-preference-api.service';
import { UtilitiesService } from 'src/app/_services/utilities.service';
import { ToastService } from 'src/app/_services/toast.service';
import { ROLES_CONSTANTS } from '../constants/roles-screen-constants';
import { ROUTING_LABELS } from 'src/app/_constants/menu.constant';
import { AuthService } from 'src/app/_services/authentication.services';
import { CookieService } from 'ngx-cookie-service';

const CLIENT_OFFSHORE = 'Offshore';
const CLIENT_ONSHORE = 'Onshore';

@Component({
  selector: 'app-edit-role',
  templateUrl: './edit-role.component.html',
  styleUrls: ['./edit-role.component.sass'],
  encapsulation: ViewEncapsulation.None
})
export class EditRoleComponent implements OnInit {

  isEditRoleButtonEnabled: boolean = false
  editedClientSite: any;
  editedReminderDate: any;
  editedTeamType: any;
  editRoleFormJSON: any;
  clientNameJson: any;
  productNameJson: any;
  inventoryNameJson: any;
  businessDivisionJson: any;
  clientSiteJson: any;
  clientNames: any;
  isclientNameJsonReady: boolean = false;
  isproductNameJsonReady: boolean = false;
  isinventoryNameJsonReady: boolean = false;
  isBusinessDivisionJsonReady: boolean = false;
  inventoryOptions: any = [];
  permissionsDS: any;
  permissionsColumnConfig: any;
  accessScreensData: any;
  selectedBusinessDivision: any;
  isTableReady: boolean
  isSkillsLoaded: boolean
  isStandard: boolean = true
  roleNameandDescription: any = {};
  inventoryTypeResponse: any = {};
  clientSiteChangeResponse: any = {};
  clientNameResponse: any = {};
  productNameResponse: any = {};
  _formData: any;
  createErrorOpenPopup: any = false;
  breadcrumbDataset: any = [
    {
      label: 'Home',
      url: '/'
    }, {
      label: 'Roles Management',
      url: '/settings/roles'
    }, {
      label: 'Edit Role'
    }]
  simpleConfig = [
    { name: "Step 1", description: "Edit Role" },
    { name: "Step 2", description: "Preview Role Details" }]

  isCarelonAdmin: boolean = false;

  currentRoleFormData: any = {};
  showBtns: any = false;
  editUserViewListDS: any;
  productNames: any;
  editRoleScreenData: any;
  userId: string = "";
  errorMessage: string = '';

  constructor(private router: Router,
    private clientApiService: ClientApiService,
    private userManagementSvc: UserManagementApiService, private utilityService: UtilitiesService,
    private alertService: ToastService, private dateService: UtilitiesService,
    private cookieService: CookieService, private authService: AuthService) {
    this.userId = this.cookieService.get(ROUTING_LABELS.USER_ID).toUpperCase();
  }

  /**
   * Method fires on form Value Change
   * @param event 
   */
  onRoleChange(event: any): void {
    this.currentRoleFormData.roleName = event.current.roleName;
    this.currentRoleFormData.description = event.current.description;
    this.currentRoleFormData.businessDivision = event.current.businessDivision;
    this.roleNameandDescription = event.current;
    this.selectedBusinessDivision = event.current;
  }

  /**
   * Navigate to the home page
   * @param event 
   */
  selectedLink(event: any): void {
    this.router.navigate([event.selected.url]);
  }

  /**
   * Method to set the selected Form Values
   */
  setFormData(): void {
    this.currentRoleFormData = this._formData;
    this.editedClientSite = this._formData.clientSite;
    this.editedReminderDate = this._formData.reminderDate;
    this.editedTeamType = this._formData.teamType;
    this.editRoleFormJSON.forEach(e => {
      e[ROUTING_LABELS.VALUE] = this._formData[e.name];
      e[ROUTING_LABELS.SELECTED_VALUE] = this._formData[e.name];
      e[ROUTING_LABELS.DISABLED] = true;
    })

    this.clientNameJson[0].value = localStorage.getItem('selected-client-name');
    this.productNameJson[0].value = localStorage.getItem('selected-product-name');
    this.businessDivisionJson[0].value = localStorage.getItem('selected-business-division');

    this.clientSiteJson.forEach(e => {
      if (e.name == ROUTING_LABELS.CLIENT_SITE) {
        this._formData[e.name] == ROUTING_LABELS.ONSHORE ? e[ROUTING_LABELS.VALUE] = false : e[ROUTING_LABELS.VALUE] = true;
        this._formData[e.name] == ROUTING_LABELS.ONSHORE ? e[ROUTING_LABELS.SELECTED_VALUE] = false : e[ROUTING_LABELS.SELECTED_VALUE] = true;
      } else {
        e[ROUTING_LABELS.VALUE] = this._formData[e.name];
        e[ROUTING_LABELS.SELECTED_VALUE] = this._formData[e.name];
      }
    })

  }

  ngOnInit() {
    let selectedCientId = Number(localStorage.getItem('selected-row-clientID'));
    let selectedRoleId = Number(localStorage.getItem('selected-row-RoleID'));
    let selectedProdId = Number(localStorage.getItem('selected-row-ProdID'));
    selectedCientId == 59 && selectedProdId == 11 ? this.isBusinessDivisionJsonReady = true : this.isBusinessDivisionJsonReady = false;
    this.selectedBusinessDivision = localStorage.getItem('selected-business-division') || '';
    let _fetchUser = this.userManagementSvc.getAssetsJson('./assets/json/settings/role-management/edit-Role-Form.json');
    let _fetchPage = this.userManagementSvc.getAssetsJson('./assets/json/settings/role-management/permission-role.json');
    let _getScreenList = this.userManagementSvc.getListOfScreensForAddRole();
    let _getAllClientNames = this.clientApiService.getAllClientsInPreferenceCenter();
    let _getRoleData = this.userManagementSvc.getIndividualRole(selectedCientId, selectedRoleId, selectedProdId);
    let _getAllTemplateData = this.clientApiService.getAllTemplates();
    let _allProductsData = this.clientApiService.getAllProducts();
    this.isCarelonAdmin = this.authService.isWriteOnly;
    forkJoin([_fetchUser, _fetchPage, _getRoleData, _getAllClientNames, _getScreenList, _allProductsData]).subscribe(
      ([team, pages, roleData, allClients, screenListForAddRoleTable, productData]) => {

        this.isclientNameJsonReady = false;
        this.isproductNameJsonReady = false;
        this.isinventoryNameJsonReady = false;
        this.editRoleFormJSON = team[ROLES_CONSTANTS.BASIC_ROLE_FORM];
        this.clientNameJson = team[ROLES_CONSTANTS.CLIENT_NAME_JSON];
        this.productNameJson = team[ROLES_CONSTANTS.PRODUCT_NAME_JSON];
        this.businessDivisionJson = team[ROLES_CONSTANTS.BUSINESS_DIVISION_JSON];
        this.inventoryNameJson = team[ROLES_CONSTANTS.INVENTORY_TYPE_DETAILS_JSON];
        this.clientSiteJson = team[ROLES_CONSTANTS.CLIENT_SITE_JSON];
        if (this.isCarelonAdmin) {
          this.clientSiteJson.find((x) => x.name == ROLES_CONSTANTS.TEAM_TYPE)[ROLES_CONSTANTS.DISABLED] = false;
        } else {
          this.clientSiteJson.find((x) => x.name == ROLES_CONSTANTS.TEAM_TYPE ? this.clientSiteJson.pop() : '')

        }
        roleData[ROLES_CONSTANTS.RESPONSEDATA].forEach(roleDataObj => {
          roleDataObj['reminderDate'] = this.utilityService.getDbgDateFormat(roleDataObj['reminderDate']);
        });
        this._formData = roleData[ROLES_CONSTANTS.RESPONSEDATA][0];

        this.editRoleScreenData = roleData[ROLES_CONSTANTS.RESPONSEDATA];
        this.clientNames = allClients.map(x => ({ "id": x.clientId, "name": x.clientName }));
        this.productNames = productData.map(x => ({ "id": x.prodId, "name": x.productName }));

        this.clientNames.forEach(clientNameList => {
          if (roleData[ROLES_CONSTANTS.RESPONSEDATA][0].clientId == clientNameList.id) {
            localStorage.setItem('selected-client-name', clientNameList.name);
          }
        });
        this.productNames.forEach(productNameList => {
          if (roleData[ROLES_CONSTANTS.RESPONSEDATA][0].prodId === productNameList.id) {
            localStorage.setItem('selected-product-name', productNameList.name);
            this.productNameResponse.prodId = productNameList.id;
          }
        });
        this.permissionsDS = []
        this.permissionsColumnConfig = JSON.parse(JSON.stringify(pages[ROLES_CONSTANTS.COLUMN_CONFIG]));
        this.permissionsColumnConfig.colDefs.filter(x => x.field == ROLES_CONSTANTS.CREATE).forEach(element => element.customFormatter = this.customFormatterFnCreate);
        this.permissionsColumnConfig.colDefs.filter(x => x.field == ROLES_CONSTANTS.VIEW).forEach(element => element.customFormatter = this.customFormatterFnRead);
        let navigationObjs: any = JSON.parse(JSON.stringify(screenListForAddRoleTable.responseData));

        for (let i = 0; i < navigationObjs.length; i++) {
          let matched = {};
          this.editRoleScreenData.forEach(element => {
            if (element.screenId == navigationObjs[i].masterId) {
              matched = element;
            }
          });
          this.permissionsDS.push({ "functional_page": navigationObjs[i].name, "roleScreenId": matched[ROUTING_LABELS.ROLE_SCREEN_ID], "roleId": null, "screenId": navigationObjs[i].masterId, "create": matched[ROUTING_LABELS.CREATE], "view": matched[ROUTING_LABELS.VIEW], "createUserId": navigationObjs[i].creatUserId, "createDateTime": null, "lastUpdateUserId": navigationObjs[i].lastUpdtUserId, "prodName": navigationObjs[i].prodName })
        }

        this.setFormData();
        this.isTableReady = true;
        this.isSkillsLoaded = true;
        this.isclientNameJsonReady = true;
        this.isproductNameJsonReady = true;
        this.isinventoryNameJsonReady = true;
        setTimeout(() => {
          this.showBtns = true;
        }, 500);
      },
      error => {
        this.alertService.setErrorNotification({
          notificationBody: error,
        });
      }
    )
    this.isclientNameJsonReady = true;
    this.isproductNameJsonReady = true;
  }

  /**
   * Method Fires on Selection of Client in Form
   * @param event 
   */
  _onclientNamesSelection(event: any): void {
    this.clientNameResponse = event.current;
  }

  /**
   * Method Fires on Selection of Product in Form
   * @param event 
   */

  _onproductNamesSelection(event): void {
    this.productNameResponse = event.current;
  }

  /**
    * Method Fires on Selection of Product in Form
    * @param event 
    */

  _onBusinessDivisionSelection(event): void {
    this.selectedBusinessDivision = event.current;
  }

  /**
   * Method Fires on Selection of Inventory Type Name in Form
   * @param event 
   */
  _oninventoryTypeAndSiteDetailsSelection(event: any): void {
    this.inventoryTypeResponse = event.current;
  }
  /**
  * Method Fires on Selection of Client Site in Form
  * @param event 
  */
  _onClientSiteChanged(event: any): void {
    this.isEditRoleButtonEnabled = true;
    this.editedTeamType = event.current.teamType;
    this.editedReminderDate = event.current.reminderDate;
    this.clientSiteChangeResponse = event.current.clientSite;
    event.current.clientSite == true ? this.editedClientSite = ROUTING_LABELS.OFFSHORE : this.editedClientSite = ROUTING_LABELS.ONSHORE;

  }

  /**
 * Custom Formatter method to represent cell as checkBox
 * @param event 
 */
  customFormatterFnCreate(event: any): string {
    return event?.dataContext.create ? `<input type="checkbox" class="create-check" checked="${event?.dataContext.create}">` : `<input type="checkbox" class="create-check">`;
  }

  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFnRead(event: any): string {
    return event?.dataContext.view ? `<input type="checkbox" class="read-check" checked="${event?.dataContext.view}">` : `<input type="checkbox" class="read-check">`;
  }

  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFnUpdate(event: any): string {
    return event?.dataContext.update ? `<input type="checkbox" class="update-check" checked="${event?.dataContext.update}">` : `<input type="checkbox" class="update-check">`;
  }

  /**
   * Custom Formatter method to represent cell as checkBox
   * @param event 
   */
  customFormatterFnDelete(event: any): string {
    return event?.dataContext.delete ? `<input type="checkbox" class="delete-check" checked="${event?.dataContext.delete}">` : `<input type="checkbox" class="delete-check">`;
  }

  /**
   * Method fires on cell click
   * @param e 
   */
  onCellClick(e: any): void {
    this.isEditRoleButtonEnabled = true;
    let _clickedElement: any = e.eventData.target;
    if (_clickedElement.classList.contains('create-check')) {
      e.currentRow["create"] = !e.currentRow["create"];
    }
    else if (_clickedElement.classList.contains('read-check')) {
      e.currentRow["view"] = !e.currentRow["view"];
    }
  }

  /**
  * function triggred when tab is changed in Stepper component 
  * to populate data to View list component   
  * @param event 
  */
  editRoleStepChange = (event) => {
    if (event == 1) {
      this.editUserViewListDS = [
        {
          label: "Role Name",
          value: this.currentRoleFormData[ROLES_CONSTANTS.ROLENAME]
        },
        {
          label: "Role Description",
          value: this.currentRoleFormData[ROLES_CONSTANTS.DESCRIPTION]
        },
        {
          label: "Assign ProductID",
          value: this.currentRoleFormData[ROLES_CONSTANTS.PRODID]
        },
        {
          label: "Business Division",
          value: this.currentRoleFormData[ROLES_CONSTANTS.BUSINESS_DIVISION]
        },
        {
          label: "ClientID",
          value: this.currentRoleFormData[ROLES_CONSTANTS.CLIENTID]
        },
        {
          label: "Reminder Date",
          value: this.currentRoleFormData[ROLES_CONSTANTS.REMINDERDATE]
        },
        {
          label: "Selected Team",
          value: this.currentRoleFormData['teamType']
        },
        {
          label: "Client Site",
          value: this.currentRoleFormData[ROLES_CONSTANTS.CLIENTSITE]
        },
        {
          label: "Role Status",
          value: this.currentRoleFormData['status'] == true ? "Active" : "InActive"
        },
        {
          label: "Create Screens",
          value: this.currentRoleFormData[ROLES_CONSTANTS.CREATE_SCREENS]
        },
        {
          label: "Read Screens",
          value: this.currentRoleFormData[ROLES_CONSTANTS.VIEW_SCREENS]
        }
      ]
    }
  }
  /**
  * To remove highlighting from fieds which passed validation
 */
  resetValidFields(): void {
    const collection = document.querySelectorAll(
      `marketplace-dynamic-form input.form-control.ng-valid ,
        marketplace-dynamic-form .ng-select.ng-select-single.ng-valid .ng-select-container,
        marketplace-textarea.ng-valid .textarea-holder textarea,
        marketplace-date-picker.ng-valid input.ng2-flatpickr-input.flatpickr-input`
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].classList.remove('redBorder');
    }
  }

  /**
 * Close the modal dialog
 */
  closeMandatoryFieldPopup(): void {
    this.createErrorOpenPopup = false;
  }
  /**
  * To highlight fieds which failed validation
 */
  showAllInvalidFields(): void {
    this.errorMessage = ROLES_CONSTANTS.MANDATORY_ERROR_MSG;
    this.createErrorOpenPopup = true;
    this.resetValidFields();
    const invalidCollection = document.querySelectorAll(
      `marketplace-dynamic-form input.form-control.ng-invalid ,
        marketplace-dynamic-form .ng-select.ng-select-single.ng-invalid .ng-select-container,
        marketplace-textarea.ng-invalid .textarea-holder textarea,
        marketplace-date-picker.ng-invalid input.ng2-flatpickr-input.flatpickr-input`
    );
    for (let i = 0; i < invalidCollection.length; i++) {
      invalidCollection[i].classList.add('redBorder');
    }
  }

  /**
  * Validate query builder fields and evaluate rule level
  */
  validateRole(): void {
    if (this.roleNameandDescription.roleName == "" || this.roleNameandDescription.roleName == undefined || this.roleNameandDescription.description == ""
      || this.roleNameandDescription.description == undefined || this.clientNameResponse.clientId == "" || this.clientNameResponse.clientId == undefined
      || this.productNameResponse.prodId == "" || this.productNameResponse.prodId == undefined || this.selectedBusinessDivision == undefined || this.selectedBusinessDivision == ""
      || this.clientSiteChangeResponse.reminderDate == "" || this.clientSiteChangeResponse.reminderDate == undefined) {
      this.showAllInvalidFields();
    } else {
      this.updateRole();
    }
  }

  /**
  * Close the edit form
  */
  onCancelEditRole(): void {
    this.router.navigate([`${this.breadcrumbDataset[0].url}`]);
  }

  /**
  * edit Group to list
  */
  updateRole(): void {
    if (!this.accessScreensData.dataset.find(c => c.view == true || c.create == true)) {
      this.errorMessage = ROLES_CONSTANTS.MANDATORY_SCREEN_ACCESS_ERROR_MSG;
      this.createErrorOpenPopup = true;
      return;
    }
    if (this.accessScreensData.dataset) {
      this.accessScreensData.dataset.forEach(function (v) {
        delete v.id;
        delete v.roleId;
        delete v.functional_page;
        delete v.prodName;
      });
    } else {
      this.accessScreensData.forEach(function (v) {
        delete v.id;
        delete v.roleId;
        delete v.functional_page;
        delete v.prodName;
      });
    }

    let payloadForAddingRole = {
      "roleId": this._formData.roleId,
      "roleName": this._formData.roleName,
      "description": this._formData.description,
      "clientId": this._formData.clientId,
      "prodId": this.productNameResponse.prodId,
      "clientName": localStorage.getItem('selected-client-name'),
      "prodName": localStorage.getItem('selected-product-name'),
      "businessDivision": this._formData.businessDivision,
      "inventoryId": this._formData.inventoryId,
      "status": 1,
      "clientSite": this.editedClientSite,
      "reminderDate": this.dateService.getECPDateFormat(this.editedReminderDate),
      "teamType": this.editedTeamType,
      "roleScreenModels": this.accessScreensData.dataset,
      "createDateTime": null,
      "requestType": "update",
      "createUserId": this._formData.createUserId,
      "lastUpdateUserId": this.userId
    }
    this.clientApiService.addUpdateRoleData(payloadForAddingRole).subscribe((data) => {
      if (data) {
        this.alertService.setSuccessNotification({
          notificationHeader: "Success",
          notificationBody: "Role Edited Successfully",
          notificationDuration: 2000,
        });
        window.history.back();
      }
    },
      error => {
        this.alertService.setErrorNotification({
          notificationHeader: "Warning",
          notificationBody: error,
        });
      });

  }

  /**
  * Going Back to previous page
  */
  backToPreviousPage() {
    this.router.navigate(['/settings/roles']);
  }
}