import { Component, OnInit, ViewEncapsulation, EventEmitter, Output, Input, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ClientApiService } from '../../_services/client-preference-api.service';
import { UtilitiesService } from '../../_services/utilities.service';
import { ToastService } from 'src/app/_services/toast.service';
import moment from 'moment'
import { standardFeeOptions } from '../client-preference';
import { CookieService } from 'ngx-cookie-service';

@Component({
  selector: 'app-product-bundle-fee-edit',
  templateUrl: './product-bundle-fee-edit.component.html',
  styleUrls: ['./product-bundle-fee-edit.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class ProductBundleFeeEditComponent implements OnInit {
  public feeScheduleJson: any[];
  public popupDisplayStyle: any = "none";
  public editFeeScheduleFormData: any = {};
  public editFeeScheduleForm: any = {};
  isEnabled: boolean = false;
  formObject: any;
  formStatus: any;
  feeType: any = [];
  feeMethod: any = [];
  @Input() editData: any;
  @Output() editDataEvent = new EventEmitter<string>();
  @Input() type: any;
  @ViewChild('formRef') formRef: any;
  client: any;
  showForm = false;
  callMapValuesChange: boolean = true;
  endDate:any
  standardFeeOptions:any=  standardFeeOptions;
  showRestrictPopUp: boolean=false;
  enableSubmitButtton: boolean = false;
  constructor(
    private router: Router,
    private clientApiService: ClientApiService,
    private route: ActivatedRoute,
    private alertService: ToastService,
    private dateService: UtilitiesService,
    private cookieService: CookieService
  ) {

  }
  /**
   * fetch  fee method and fee list
   * Master data for dropdown for fee type and fee Method
   */
  ngOnInit(): void {
    this.clientApiService.feeDetailsMasterData().subscribe((feeset) => {
      this.feeType = feeset.feeTypeList;
      this.feeMethod = feeset.feeCategoryList;
   
      this.tableFormConfig();
      this.showForm = true;
      setTimeout(() => {
        this.formValid(this.formRef.form,false);
      }, 100)
    }, err => {
      this.tableFormConfig();
      this.showForm = true;
    })
  }
  /**
   * prepare form for the client form and bindling the edited data to the form
   * for temination -> for view all input   will be disabled expect "End Date" 
   * for Edit-> all inputs are enabled
   */
  tableFormConfig() {
    this.feeScheduleJson = [
      {
        "type": "group",
        "name": "General 1",
        "label": "",
        "column": "2",
        "groupControls": [
{
            options: this.standardFeeOptions,
            optionName: "name",
            optionValue: "id",
            label: "Standard Fee (in %)",
            type: "numberSelect",
            multiple: false,
            closeOnSelect: true,
            name: "standardFee",
            column: "1",
            placeholder:'Choose standard fee',
            disabled: this.type == 'edit' ? false : true,
            hidden: false,
            id: "standardFee",
            required: true,
            customTags: true,
            minimum:0,
            maximum:100,
            value: Number(this.editData['standardFee'])
          },
          {
            options: this.feeMethod,
            optionName: 'feeCategoryName',
            optionValue: 'feeCategoryName',
            label: "Fee Method",
            type: "select",
            multiple: false,
            closeOnSelect: true,
            name: "feeMethod",
            placeholder:'Choose fee method',
            column: "1",
            disabled: this.type == 'edit' ? false : true,
            hidden: false,
            id: "feeMethod",
            required: true,
            selectedVal: this.editData['feeMethod']
          },
        ]
      },
      {
        "type": "group",
        "name": "General 2",
        "label": "",
        "column": "2",
        "groupControls": [
          {
            label: 'Start Date',
            type: 'date',
            name: 'startDate',
            column: '3',
            disabled: this.type == 'edit' ? false : true,
            value:this.dateService.getDbgDateFormat(this.editData['startDate']) ,
            pickerType: 'single',
            required: true,
            dateFormat: 'MM-DD-yyyy',
            id: 'startDate',
            relatedDateControls: [{
              target: 'endDate'
            }]
          },
          {
            label: 'End Date',
            type: 'date',
            name: 'endDate',
            column: '3',
            disabled: false,
            value: this.dateService.getDbgDateFormat(this.editData['endDate']),
            dateFormat: 'MM-DD-yyyy',
            required: true,
            closeOnSelect: true,
            pickerType: 'single',
            minDate: this.dateService.getFutureDate(this.editData['startDate'], 1,'MM-dd-yyyy'),
            id: 'endDate'
          },
          {
            options: this.feeType,
            optionName: 'feeTypeName',
            optionValue: 'feeTypeName',
            label: "Fee Type",
            type: "select",
            multiple: false,
            closeOnSelect: true,
            name: "feeType",
            placeholder:'Choose Fee type',
            required: true,
            column: "2",
            disabled: this.type == 'edit' ? false : true,
            hidden: false,
            id: "feeType",
            selectedVal: this.editData['feeType']
          }
        ]
      }
    ];
  }
  /**
   * navigate to back to list page
   */
  backToListPage() {
    this.editDataEvent.emit('edit');
  }
  /**
   * close the modal popup
   */
  closePopup() {
    this.popupDisplayStyle = "none";
    this.showRestrictPopUp=false;
  }
  /**
   * data creation for the edit record
   * @param event 
   */
  formValid(event, onFormChange) {
    if ( onFormChange && (!this.formObject || event.value['General 2'].endDate !==  this.formObject.endDate) ) {
      this.alertService.setInfoNotification({
        notificationHeader: ' Info ',
        notificationBody: `You have chosen the <b>end date as  ${event.value['General 2'].endDate}`,
      });
     }
    onFormChange == true ? this.isEnabled = true : this.isEnabled = false;     
    this.formObject = { ...event.value['General 1'], ...event.value['General 2'] };
    this.formStatus = event.status;
  }
  /**
   * submitting the edited record of fee setup and navigating back to the list screen
   * 
   */
  validateEditForm() {
   if(this.formStatus == 'VALID'){
    const startIsBeforeEnd=   this.dateService.checkDate(this.formObject.startDate,this.formObject.endDate);
    if (startIsBeforeEnd){
    const item = {
      "clientFeeId": this.editData['feeId'],
      "productName": this.editData['productName'],
      "productId": this.editData['productId'],
      "standardFee": Number(this.formObject.standardFee),
      "feeMethod": this.formObject.feeMethod,
      "feeMethodId": this.fetchValue(this.feeMethod, 'feeCategoryName', 'feeCategoryId', 'feeMethod'),
      "feeType": this.formObject.feeMethod,
      "feeTypeId": this.fetchValue(this.feeType, 'feeTypeName', 'feeTypeId', 'feeType'),
      "startDate": moment(this.dateService.getDbgDateFormat(this.formObject.startDate), 'MM-DD-YYYY').format('YYYY-MM-DD'),
      "endDate": moment(this.dateService.getDbgDateFormat(this.formObject.endDate), 'MM-DD-YYYY').format('YYYY-MM-DD'),
      "updatedBy": (this.cookieService.get('userId'))?.toUpperCase(),
      "updatedDate": new Date()
    }
    if(!this.editData['bundleStatus']){
      this.showRestrictPopUp=true;
      return;
    }
    this.clientApiService.editClient(item).subscribe((data) => {
     if( this.type == 'edit') {
       if(data.responseCode==200){
          this.alertService.setSuccessNotification({
        notificationHeader: 'Success',
        notificationBody: `Fee Schedule successfully edited in the list`,
      });
      this.backToListPage();
    }
      else if(data.responseCode==500){
        this.alertService.setErrorNotification({
        notificationHeader: 'Fail',
        notificationBody: 'Fee schedule already exist for requested data',
      });
    }
    } else {
      this.alertService.setSuccessNotification({
        notificationHeader: 'Success',
        notificationBody: `Fee Schedule successfully terminated from the list`,
      });
      this.backToListPage();
    }
      
     
  } 
  , (err: any) => {
      this.alertService.setErrorNotification({
        notificationHeader: 'Fail',
        notificationBody: `Data Not Saved`,
      });
    })
  } else {
    this.alertService.setErrorNotification({
      notificationHeader: 'Warning',
      notificationBody: `Please fill all the fields`,
    });
  }
   }else{
    this.alertService.setErrorNotification({
      notificationHeader: 'Warning',
      notificationBody: `Please fill all the fields`,
    });
   }
  }

  /**
    * An utility function , fetching required data from the dropdown for different field
    * Ex: selecting fee ID  fetching fee Name
    * @param arr list of elements
    * @param elemKey - key to check 
    * @param elmValue - Value to send
    * @param matchkey - Match to the key
    */
  fetchValue(arr, elemKey, elmValue, matchkey) {
    const elemItem = arr.find((elem) => {
      return elem[elemKey] == this.formObject[matchkey]
    })
    return elemItem ? elemItem[elmValue] : '';
  }

  ngAfterViewInit(): void {
    const collection = document.querySelectorAll(
      'marketplace-dynamic-form button'
    );
    for (let i = 0; i < collection.length; i++) {
      collection[i].remove();
    }
  }
 

   
}
