app-registation
    .page-header
        display: flex
        justify-content: center
        margin: 20px 10px
        font-size: 12px
        align-items: center

        .image-holder
            width: 8rem
            height: 1.5rem

        .page-text
            margin-top: 5px          

    .register-container
        display: flex
        justify-content: center

        .header-name
            display: flex
            justify-content: center
            font-family: 'elevance-semi-bold'
            margin: 2rem
            font-size: x-large
        
        .wd-30
            width: 30%

        .form-reg-container
            border: 1px solid #ccc
            margin: 1rem
            padding: 1%
            position: relative
            box-shadow: #******** 0 5px 15px
            width: 40%
        
        .redBorder
            border-color: red !important        

    .btn-footer
        display: flex
        justify-content: center 

        .btn-account
            margin: 5px
        
    .page-footer
        display: flex
        justify-content: center
        margin-bottom : 10px   

        .email-support
            margin-left: 2rem

    .btn-link
        display: flex
        justify-content: center
        text-decoration: underline
        margin: 1rem
        align-items: center
        margin-left: 2rem
    
    .spinner-border
        display: block
        position: fixed
        top: calc(50% - (58px / 2))
        right: calc(40% - (58px / 2))
        width: 5rem
        height: 5rem
    
    .backdrop
        position: fixed
        top: 11%
        left: 20%
        width: 100vw
        height: 100vh
        z-index: 999
        background-color: rgb(0, 0, 0, 0.2)
    
    marketplace-dynamic-form .grp-header-blue
        font-family: var(--font-bold)!important
    

