<div class="d-flex justify-content-center backdrop" *ngIf="isLoading">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>
<div class="body-content">
  <div class="page-header-container">
    <img src="./assets/logo.png">
    <span class="seperator"></span>
    <div class="page-header-text">Payment Integrity</div>
  </div>
  <div class="card elevated-card" *ngIf="!isSecurityQuestion">
    <div class="page-header">
      <h3>Change Password</h3>
    </div>
    <marketplace-dynamic-form [formJSON]="changePassFormJSON" [isSubmitNeeded]="false" [isSubmitted]="isFormSubmitted"
      (onValueChanges)="onChangePasswordFldsChange($event)">
    </marketplace-dynamic-form>
    <div class="red-font">{{errorMsg}}</div>
    <div class="btn-holder">
      <marketplace-button *ngIf="!isFromSignIn" [label]="'Change Password'" [name]="'primary'" [type]="'primary'"
        (onclick)="onChangePasswordClick()">
      </marketplace-button>
      <marketplace-button *ngIf="isFromSignIn" [label]="'Change Password'" [name]="'primary'" [type]="'primary'"
        (onclick)="changePasswordFromSignin()"></marketplace-button>
    </div>
    <a routerLink="/signin" *ngIf="isFromSignIn">Sign In</a>
  </div>
  <div class="card elevated-card-securityquestion" *ngIf="isSecurityQuestion">
    <div class="page-header">
      <h3>Change Password?</h3>
    </div>
    <marketplace-dynamic-form [formJSON]="securityQuestionsJSON" [isSubmitNeeded]="false"
      (onValueChanges)="onSecurityQuestionsFormChange($event)">
    </marketplace-dynamic-form>
    <div class="red-font" *ngIf="isEmailNotFound">Email not found in our system</div>
    <div class="btn-holder">
      <!--Below implementation will be changed while implementing MFA-->
      <!-- <marketplace-button [label]="'Send email >'" [name]="'primary'" [type]="'primary'"
            (onclick)="onSendEmailButtonClicked()">
        </marketplace-button> -->
      <marketplace-button [label]="'Continue'" [name]="'primary'" [type]="'primary'"
        (onclick)="continueToResetPassword()">
      </marketplace-button>
    </div>
    <a routerLink="../">Return to log in</a>
  </div>
</div>

<marketplace-popup [open]="emptyFieldsErrorPopup" [size]="'small'" (onClose)="closePopup()">
  <div mpui-modal-header>
    <div class="modal-header-custom">
      <h4 class="modal-title custom-title">Attention !</h4>
    </div>
  </div>
  <div mpui-modal-body>
    <p class="pad-35">Please fill all the mandatory fields</p>
  </div>
  <div mpui-modal-footer>
    <marketplace-button [label]="'OK'" [type]="'primary'" [name]="'primary'" (onclick)="closePopup()">
    </marketplace-button>
  </div>
</marketplace-popup>