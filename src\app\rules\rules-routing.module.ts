import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from './dashboard/dashboard.component';
import { CreateComponent } from './create/create.component';
import { EditComponent } from './edit/edit.component';
import { ViewComponent } from './view/view.component';
import { FrequentlyUsedCriteriaComponent } from './frequently-used-criteria/frequently-used-criteria.component';
import { CreateNewCriteriaComponent } from './create-new-criteria/create-new-criteria.component';
import { SetupTypeComponent } from './setup-rule-type/setup-type.component';
import { TypeDetailsComponent } from './setup-rule-type/type-details/type-details.component';
import { AuthGuard } from 'src/app/_helpers/auth.guard';

import { CopyComponent } from './copy/copy.component';

const routes: Routes = [
  { path: '', component: DashboardComponent, canActivate: [AuthGuard]},
  { path: 'create', component: CreateComponent},
  { path: 'edit/:id', component: EditComponent},
  { path: 'view/:id', component: ViewComponent},
  { path: 'create-frequently-used-criteria', component: CreateNewCriteriaComponent},
  { path: 'frequently-used-criteria', component: FrequentlyUsedCriteriaComponent},
  { path: 'rule-type', component: SetupTypeComponent},
  { path: 'rule-type/details', component: TypeDetailsComponent},
  {
    path: 'copy/:id', 
    component: CopyComponent,
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RulesRoutingModule { }