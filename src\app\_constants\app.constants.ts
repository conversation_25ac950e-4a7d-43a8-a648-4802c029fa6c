export const AUTH_CONFIG: any = {
  CODE_CHALLENGE: 'code_challenge',
  CODE: 'code',
  APP_TOKEN: 'appToken',
  SUB: 'sub',
  USER_TOKEN: 'userToken',
  CODE_VERIFIER: 'codeVerifier',
  ALG: 'RS256'
}

export const APP_CONSTANTS: any = {
  APP_TITLE: 'Payment Integrity',
  APP_LOGO: '/assets/images/logo.png',
  PORTAL_ACCESS: 'portal_access',
  USER_NAME: 'userName',
  FIRST_NAME: 'firstName',
  LAST_NAME: 'lastName',
  ERROR: 'Error',
  TRUE: 'true',
  LANDING_SCREEN_DETAILS: 'LandingScreenDetails',
  SCREEN_ACCESS_CARDS: 'screenAccessCards',
  SCREEN_ACCESS_LINKS: 'screenAccessLinks',
  IMG_SRC: `<img src='./assets/icons/landing-screen/CAD-icon.svg'>`
}

export const QUICK_LINKS: any = {
  RULES: 'Rules',
  EXTERNAL_USER_REGISTRATION: 'External User Registration',
  HELP_CENTER: 'Help Center',
  SETTINGS: 'Settings',
  CLIENTS: 'Clients',
  USERS: 'Users'
}

export const LOB_CARD_NAME: any = {
  DMS: 'Data Mining Solution',
  COB: 'Coordination of Benefits',
  SIU: 'Special Investigations Unit',
  CCA: 'Complex Clinical Audit',
  QART: 'PAD Auditor Review Tool'
}


export const RegistrationConstants: any = {
  ENTER_VALID_EMAIL: "Please Enter a Valid Email address",
  USERID_NUMBER_ERRMSG: "User ID cannot start with number",
  USERID_ELEVANCE_ERRMSG: "User ID cannot start with 2 letters followed by all numbers",
  USERID_MIMMAX_ERRMSG: "User ID should be min 6 and max 20 with valid characters",
  INVALID_CHAR: "Invalid characters",
  PWD_USR_CONTAINS_ERRMSG: "Password contains 3 characters of user ID",
  PWD_CONSECUTIVE_ERRMSG: "Consecutively character repeated more than twice",
  SUCCESS: "Success",
  SUCCESS_MESG: "Registration is Sucessfull !!",
  EXTERNAL_USER_ERRMSG: "Error occurred while adding user to CAD application",
  ERROR: "Error",
  SECURITY_ANS_INVALID_MSG: "securtiy answer is not valid",
  MAXIMUM_LIMIT_EXCEEDED_MSG: "You have exceeded the maximum number of attempts",
  UNAUTHORIZED: "Unauthorized",
  REPOSITORY_ENUM: "IAM",
  USER: "user",
  NO_USER_FOUND: "No user found.",
  NOTI_BODY_ERROR: "Some error occurred",
  WRONG_ONE_TIME_ERRMSG: "Wrong One Time Password Entered! Please enter correct information",
  OTP_INFO_MSG: "Verify your identity by entering OTP sent to your email.",
  EMAIL_ADDRESS: "emailAddress",
  PASSWORD_FIELD: "password",
  PASSWORD_TOOL_TIP: `Password cannot contain User ID or first three characters of the User ID. No character can be repeated more than twice.
  Characters allowed: ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_!#$%^*()+,.?;:]}[{|@`,
  SOA_INVALID_PASSWORD: "PASSWORDINVALID",
  SOA_INVALID_PASSWORD_NOTIFICATION_HRD: "Invalid password",
  SOA_INVALID_PASSWORD_NOTIFICATION_BDY: "Please choose allowed characters"
}