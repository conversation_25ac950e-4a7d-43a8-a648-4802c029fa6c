import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-breadcrumbs-nav',
  templateUrl: './breadcrumbs-nav.component.html',
  styleUrls: ['./breadcrumbs-nav.component.css']
})
export class BreadcrumbsNavComponent implements OnInit {
  @Input() public headerText;
  @Input() public isPriviousRedirectPage=false;
  @Input() public breadcrumbDataset:any = [];
  @Input() public client:any = "";
  @Input() public product: any = "";
  @Output() breadcrumbSelectionEvent = new EventEmitter<string>();
 
  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  breadcrumSelection(event){ 
    console.log("breadcrumb",event);
    if(event.selected.label == 'List Products'){
      this.breadcrumbSelectionEvent.emit('List Products');
    }
    else {
      this.router.navigate([`${event.selected.url}`]);
    }  
    
  }

  backToPreviousPage(){
    window.history.back()
  }
}
