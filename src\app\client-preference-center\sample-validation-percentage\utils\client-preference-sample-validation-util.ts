import * as XLSX from 'xlsx';

export const constants = {
    CLIENT_NAME: 'clientName',
    CLIENT_ID: 'clientId',
    CSBD: 'CSBD',
    GBD: 'GBD',
    ANTHEM_CLIENT_ID: 59,
    SAMPLE_VALIDATION_PERCENTAGE: 'Sample Validation Percentage',
    VALIDATION_UPDATE_MSG: 'Sample Validation Percentage for client updated',
    ERROR: 'Error',
    GET_CLIENT_PERCENTAGE_ERROR: 'Error while fetching client percentages',
    PERCENTAGE: 'percentage',
    BUSINESS_DIVISION: 'businessDiv'
}

export const sampleValidationGBDAnthemJSON = [
    {
        "id": "businessDiv",
        "label": "Business Division",
        "type": "text",
        "name": "businessDiv",
        "column": "3",
        "disabled": false,
        "required": false,
        "value": "",
    },
    {
        "id": "percentage",
        "label": "Percentage",
        "type": "number",
        "name": "percentage",
        "column": "3",
        "disabled": false,
        "required": false,
        "value": "",
        "regex": "((100([.]00?)?)|([1-9][0-9]?([.][0-9]{1,2})?)|(0[.][1-9][0-9]?))$"
    }
]

export const sampleValidationCSBDAnthemJSON = [
    {
        "id": "businessDiv",
        "label": "Business Division",
        "type": "text",
        "name": "businessDiv",
        "column": "3",
        "disabled": false,
        "required": false,
        "value": "CSBD",
    },
    {
        "id": "percentage",
        "label": "Percentage",
        "type": "number",
        "name": "percentage",
        "column": "3",
        "disabled": false,
        "required": false,
        "value": "",
        "regex": "((100([.]00?)?)|([1-9][0-9]?([.][0-9]{1,2})?)|(0[.][1-9][0-9]?))$"
    }
]

export const sampleValidationJSON = [
    {
        "id": "percentage",
        "label": "Percentage",
        "type": "number",
        "name": "percentage",
        "column": "3",
        "disabled": false,
        "required": false,
        "value": "",
        "regex": "((100([.]00?)?)|([1-9][0-9]?([.][0-9]{1,2})?)|(0[.][1-9][0-9]?))$"
    }
]

export const columnConfigs: any = {

    "switches": {
        "enableSorting": true,
        "enablePagination": true,
        "editable": false,
        "enableFiltering": true
    },
    "colDefs": [
        {
            "name": "Business Division",
            "field": "businessDiv",
            "filterType": "text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Percentage",
            "field": "pctag",
            "filterType": "text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Updated By",
            "field": "lastUpdatedUserId",
            "filterType": "text",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": ""
        },
        {
            "name": "Date",
            "field": "lastUpdatedDateTime",
            "filterType": "Calendar",
            "visible": "True",
            "editorType": "Text",
            "editorTypeRoot": "",
            "editorTypeLabel": "",
            "editorTypeValue": "",
            "dateFormat": "MM/DD/YYYY",
        }
    ]
};


/**
   * @function generateExceldata Export data into EXCEL
   * @param data data to load into excel
   * @param fileName excel file name
   */
export function generateExcelData(data: any, fileName: any) {
    const workBook = XLSX.utils.book_new(); // create a new blank book
    const workSheet = XLSX.utils.json_to_sheet(data);
    XLSX.utils.book_append_sheet(workBook, workSheet, 'data'); // add the worksheet to the book
    XLSX.writeFile(workBook, fileName); // initiate a file download in browser
}
