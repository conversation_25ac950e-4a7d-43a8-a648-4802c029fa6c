import { NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FileHomeComponent } from './file-home/file-home.component';
import { AddEditTemplateComponent } from './add-edit-template/add-edit-template.component';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ImportTemplateComponent } from './import-template/import-template.component';
import { MPUICheckboxModule } from 'marketplace-checkbox';
import { MPUITableModule } from 'marketplace-table';
import { MPUIStepperModule } from 'marketplace-stepper';
//import { MarketPlaceModule } from 'src/app/market-place.module';
import { FilebreadcumComponent } from './filebreadcum/filebreadcum.component';
import { MPUIBreadcrumbModule } from 'marketplace-breadcrumb';
import {FormatTimePipe} from '../../_pipes/format-time.pipe';
import { MPUIModalDialogModule } from 'marketplace-popup';
import { MPUIInputModule } from 'marketplace-input';
import { MPUIButtonModule } from 'marketplace-button';
import { MPUIAlertModule } from 'marketplace-alert';
import { MPUIDynamicFormModule } from 'marketplace-form';
import { PermissionGuard } from 'src/app/_helpers/permission.guard';
import { MPUISwitchModule } from 'marketplace-switch';
import { MPUIDatePickerModule } from 'marketplace-date-picker';
import { MPUINotificationModule } from 'marketplace-notification';
import { MPUIViewListModule } from 'marketplace-view-list';
import { MPUITabsModule } from 'marketplace-tabs';
import { MPUIListPickerModule } from 'marketplace-list-picker';


const routes: Routes = [
  {
    path: '',
    children: [
      { path: '', component: FileHomeComponent },
      { path: 'addEditTemplate', component: AddEditTemplateComponent ,canActivate:[PermissionGuard] },
      { path: 'importTemplate', component: ImportTemplateComponent,canActivate:[PermissionGuard] },
      { path: 'addEditTemplate/:id', component: AddEditTemplateComponent,canActivate:[PermissionGuard] },
      { path: 'addEditTemplate/view/:id', component: AddEditTemplateComponent },
      { path: '', pathMatch: 'full', redirectTo: 'file' },
      { path: '**', pathMatch: 'full', redirectTo: '' }
    ]
  }];

@NgModule({
  declarations: [
    FileHomeComponent,
    AddEditTemplateComponent,
    ImportTemplateComponent,
    FilebreadcumComponent,
    FormatTimePipe
  ],
  imports: [
    CommonModule,
    MPUIBreadcrumbModule,
    MPUIStepperModule,
    RouterModule.forChild(routes),
    FormsModule,
    MPUIModalDialogModule,
    MPUIInputModule,
    MPUITableModule,
    MPUICheckboxModule,
    MPUIButtonModule,
    MPUIAlertModule,
    MPUIDynamicFormModule,
    MPUISwitchModule,
    MPUIDatePickerModule,
    MPUIStepperModule,
    MPUINotificationModule,
    MPUICheckboxModule,
    MPUIViewListModule,
    MPUITabsModule,
    ReactiveFormsModule
  ],
  schemas:[NO_ERRORS_SCHEMA]
})

export class FileModule { }
