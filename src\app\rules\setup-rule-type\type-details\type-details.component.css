app-type-details .container,
app-type-details .container-fluid,
app-type-details .container-lg,
app-type-details .container-md,
app-type-details .container-sm,
app-type-details .container-xl {
  width: 100%;
  padding-right: 5px;
  padding-left: 5px;
  margin-right: auto;
  margin-left: auto;
}
app-type-details .ruleSubTypeDetailsContainer {
  margin-left: 2rem;
}
app-type-details .submitButton {
  margin-top: 3em;
  float: right;
  margin-bottom: 1em;
  margin-right: 1em;
}
app-type-details .card {
  border: 3px solid rgba(5, 5, 6, 0.125);
  border-radius: 0.95rem;
}
app-type-details .card .card-no-border {
  border: none !important;
  padding: 0px 25px 0px 25px;
}

 app-type-details hr.new4 {
    border: 1px solid rgb(226, 232, 236);
    margin-top: 15px;
    margin-left: 15px;
    margin-right: 15px;
  }

  app-type-details h2 { 
    display: block;
    font-size: 1.5em;
    font-weight: normal;
    color: #4863A0;
    margin-left: 8px;
  }

 
