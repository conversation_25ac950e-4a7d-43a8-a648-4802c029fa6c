import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { AUTH_CONFIG } from "../_constants/app.constants";
import { map } from 'rxjs/operators';
import CryptoJS from 'crypto-js';
import { CookieService } from "ngx-cookie-service";
import { environment } from '../../environments/environment';
import { Router } from "@angular/router";
import { RoleDTO, UserProfile } from "../_models/user-profile";
import { BehaviorSubject, Subject } from "rxjs";
import { User } from "../_models/user";
@Injectable({ providedIn: 'root' })
export class AuthService {
    private userSubject: BehaviorSubject<User>;
    public isLogin: boolean = false;
    public isLogout: boolean = false;
    public isUnAuthorized: boolean = false;
    public isDeactivated: boolean = false;
    public isTimedout: boolean = false;
    public isWriteOnly: boolean = true;
    public isInternalUser: boolean = false;
// Subject to store User Profile
    public currentUserProfile = new Subject<UserProfile>();
    private userProfile: UserProfile;
     // BehaviorSubject to store UserName
    private currentUserNameStore = new BehaviorSubject<string>("");
     // Make UserName store Observable
    public currentUserName$ = this.currentUserNameStore.asObservable();

    constructor(public http: HttpClient, public cookieService: CookieService, public router: Router) {
    }

    navigateTo(url: string): void {
        window.location.href = url;
    }

    /**
     * Single Sign On
     */
    singleSignOn() {
        let userToken = this.cookieService.get(AUTH_CONFIG.USER_TOKEN);
        let appToken = sessionStorage.getItem('appToken');

        if ((userToken === "" || !appToken) &&
            !window.location.href.includes(environment.callBackUrl) &&
            !window.location.href.includes(environment.oktaCallBackUrl)) {
            const verifyCd = this.strRandom(128);
            localStorage.setItem(AUTH_CONFIG.CODE_VERIFIER, verifyCd);
            const codeVerifierHash = CryptoJS.SHA256(verifyCd).toString(CryptoJS.enc.Base64);
            const codeChallenge = codeVerifierHash
                .replace(/=/g, '')
                .replace(/\+/g, '-')
                .replace(/\//g, '_');

            const baseUrl = environment.iamManagerAuth + '/initiatesso?' + AUTH_CONFIG.CODE_CHALLENGE + '=' + codeChallenge;
            const redirectUrl = environment.enableOkta ? baseUrl + '&internal=true' : baseUrl;
            this.navigateTo(redirectUrl);
        } else if (userToken) {
            this.isLogin = true;
        }
    }
 login(username: string, password: string) {

        return this.http.post<User>(`${environment.identityUrl}/api/v1/webusers/authenticate`, { username, password }).pipe(map(user => {
            // store user details and jwt token in local storage to keep user logged in between page refreshes
            localStorage.setItem('user', JSON.stringify(user));
            this.userSubject.next(user);
            return user;
        }));
    }


    /**
     * Get Token Details I AM Manager
     * @param authCode 
     * @param codeVerifier 
     * @returns 
     */
    getToken(authCode: string, codeVerifier: string) {
        const tokenObj = { auth_code: authCode, code_verifier: codeVerifier };
        const url = `${environment.iamManagerAuth}/token`;

        return this.http.post(url, tokenObj, {
           headers: {
                'x-appid': 'PORTAL',
                ...(environment.enableOkta && { source: 'internal' })
            },
            withCredentials: true
        }).pipe(map(tokenObject => tokenObject));
    }

    public getStoredUserProfile() {
        return this.userProfile;
    }

    /**
     * Refresh token 
     * @returns 
     */
    refreshToken() {
        let userToken = this.cookieService.get('userToken');
        let appToken = sessionStorage.getItem('appToken');

        return this.http.get(`${environment.iamManagerUser}/refresh`, {
            headers: {
                'z-generate-cad-token': 'Y',
                'x-appid': 'PORTAL',
                'userToken': `${userToken}`,
                'appToken': `${appToken}`
            }, withCredentials: true
        }).pipe(map(refreshTokenObj => {
            return refreshTokenObj;
        }));
    }

    /**
     * Logout
     * @returns 
     */
    logoutPortal() {
        let userToken = this.cookieService.get('userToken');
        let appToken = sessionStorage.getItem('appToken');

        return this.http.post(`${environment.iamManagerUser}/logout`, {}, {
            headers: {
                'x-appid': 'PORTAL',
                'userToken': `${userToken}`,
                'appToken': `${appToken}`,
                ...(environment.enableOkta && { source: 'internal' })
            }, withCredentials: true
        }).pipe(map(logoutMessage => {
            return logoutMessage;
        }));
    }

    /**
     * Generating a Random String
     * @param length 
     */
    strRandom(length: number) {
        let result = '';
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        const charactersLength = characters.length;
        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * charactersLength));
        }
        return result;
    }
// Setter to update UserName
    public setCurrentUserName(userName: string) {
        this.currentUserNameStore.next(userName);
    }
    //C2P
    /**
     * Method to check is Readonly permission for the page level and child level
     */
    checkHasWritePermission(state: any): boolean {
        this.isWriteOnly = false;
        /* let permissionObj = JSON.parse(atob(sessionStorage.getItem(ROUTING_LABELS.PERMISSIONS)));
         if (permissionObj)
             Object.keys(permissionObj).forEach((element: any) => {
                 let navigationURL = permissionObj[element].url ? permissionObj[element].url : ROUTING_LABELS.NOT_APPLICABLE; //.substring(0, permissionObj[element].url.lastIndexOf("/"))
                 if (navigationURL && state.url.indexOf(navigationURL) > -1) {
                     if (permissionObj[element]?.permission?.includes(ROUTING_LABELS.CREATE_PERMISSION)) {*/
        this.isWriteOnly = true
        /*  }
      }
  });*/

        return this.isWriteOnly;
    }
 /**
     * call user profile api to get external user's clients, products and inventory types
     * @param userId 
     */
     getExternalUserProfile(userId, expiryTime) {
        return this.http.get<any>(`${environment.authService}/api/v1/validateToken/v1`, {
            headers: {
                'loginType': environment.loginType,
                'expiryTime': expiryTime,
                'userId': userId
            }
        })
            .pipe(map(userProfile => {
                // store user details and jwt token in local storage to keep user logged in between page refreshes
                this.setUserProfile(userProfile);
                return userProfile;
            }));
    }
    // Setter to update UserName
    private setUserProfile(userProfile: UserProfile) {
        this.currentUserProfile.next(userProfile);
        this.userProfile = userProfile;
    }

    /**
     * PI Portal Authorize
     * @returns 
     */
    piAuthorize() {
        let userToken = this.cookieService.get('userToken');

        return this.http.get<any>(`${environment.iamManagerAuth}/pi-authorize`, {
            headers: {
                'z-generate-cad-token': 'Y',
                'x-appid': 'PORTAL',
                'userToken': `${userToken}`,
                ...(environment.enableOkta && { source: 'internal' }) 
            }
        })
            .pipe(map(tokenDetails => {               
                return tokenDetails;
            }));
    }

    /**
     * Clear Sessions
     */
    clearSessions() {
        this.cookieService.deleteAll('/');
        sessionStorage.clear();
        this.router.navigate(['logout']);
    }
    /**
     * On Home Click
     */
    onHomeClick() {
        sessionStorage.clear();
        this.cookieService.deleteAll('/');
        window.location.href = environment.piPortal;
    }
       
}