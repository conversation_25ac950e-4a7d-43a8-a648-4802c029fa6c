<div class="fixed-nav bg-gray">
  <div class="content-wrapper">
    <div class="container-fluid">
      <div class="card">

        <app-breadcrumbs-nav [headerText]="headerText" [isPriviousRedirectPage]="isPriviousRedirectPage"
          [breadcrumbDataset]="breadcrumbDataset">
        </app-breadcrumbs-nav>

        <hr />

        <div class="mt-10">
          <div class="card card-no-border">
            <span class="card-title">Enter below details</span>

            <marketplace-dynamic-form [formJSON]="generalDetailsJson">
            </marketplace-dynamic-form>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="fixed-nav bg-gray mar-10">
  <div class="content-wrapper">
    <div class="container-fluid">

      <div class="card">
        <div class="pd-15">
          <span class="query-builder-title">Query Builder</span>
          <hr class="qb" />
        </div>

        <marketplace-query-builder [query]="dropquery" [qbConfig]="dragdropconfig"
          (onDropquery)="dropRecentList($event)">
        </marketplace-query-builder>

        <span class="btn-span pd-25 align-right">
          <marketplace-button [label]="'Cancel'" [type]="'secondary'" [name]="'secondary'" (onclick)="cancelCreate()">
          </marketplace-button>

          <marketplace-button [label]="'Submit'" [type]="'primary'" [name]="'primary'" (onclick)="validateCreate()">
          </marketplace-button>


        </span>
      </div>


    </div>
  </div>
</div>