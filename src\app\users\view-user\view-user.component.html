<div class="breadcrumb-holder">
    <marketplace-breadcrumb [id]="'breadcrumb'" [dataset]="breadcrumbDataset" (onSelection)="selectedLink($event)">
    </marketplace-breadcrumb>
</div>
<div class="page-wrapper">
    <div class="page-header">
        <h3><a><i class="fa fa-chevron-circle-left backColor"
            aria-hidden="true" (click)="backToPreviousPage()"></i></a>
            View User
        </h3>
    </div>
    <div class="form-container">
        <marketplace-dynamic-form [formJSON]="userFormJSON" [isSubmitNeeded]="false">
        </marketplace-dynamic-form>
        <div class="col-12">
            <label class="header-blue-client">User Role</label>
            <div class="plus-hide">
            <marketplace-form-repeater *ngIf="userConfigDataset.length > 0" [(formModel)]="userConfigFormValues" [dataset]="userConfigDataset" #formRef
                        [maxItems]="maxTargetSelection" [recreate]="redrawForms">
            </marketplace-form-repeater>
            </div>
        </div>
        <!-- <div class="col-12" disabled="disabled">
            <marketplace-table 
            [id]="'sample-table-1'"
            [title]="'User Role'"
            [dataset]="tableDataJSON" 
            [columnDefinitions]="tableColumnConfig"
            [isDraggableRowsNeeded]="false"
            [isExcelExportNeeded] = "false"
            [isToggleColumnsNeeded] = "false"></marketplace-table>
        </div> -->
            <div *ngFor = "let row of filteredRecords">
                <div class="row">
                    <div class="col-2">
                        <div class="d-flex flex-column">
                            <marketplace-select [label]="'Client'" [type]="'single'" [placeholder]="'Select Client'"
                                [(ngModel)]="row.clientId" [disabled]="true" [dataset]="distinctClients"
                            >
                            </marketplace-select>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="d-flex flex-column">
                            <marketplace-select [label]="'Product'" [type]="'single'" [placeholder]="'Select Product'"
                                [(ngModel)]="row.prodId" [disabled]="true" [dataset]="distinctProducts"
                                >
                            </marketplace-select>
                        </div>
                    </div>
                </div>
            <div class="col-12">
                <marketplace-dynamic-form *ngIf="isNewSkillsReady" [formJSON]="row.skillJSON" [isSubmitNeeded]="false">
                </marketplace-dynamic-form>
            </div>
        </div>
    </div>
</div>