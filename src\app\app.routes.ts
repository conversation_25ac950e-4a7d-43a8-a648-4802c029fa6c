import { Routes } from '@angular/router';
import { AuthGuard } from './_helpers/auth.guard';
import { PermissionGuard } from './_helpers/permission.guard';
import { CallBackComponent } from './call-back/call-back.component';
import { LandingComponent } from './landing-screen/landing-screen.component';
import { LogoutComponent } from './logout/logout.component';
import { ExternalSignInComponent } from './external-signIn/external-signIn.component';
import { ForgotUseridComponent } from './external-signIn/forgot-userid/forgot-userid.component';
import { ResetPasswordComponent } from './external-signIn/forgot-password/reset-password/reset-password.component';
import { ForgotPasswordComponent } from './external-signIn/forgot-password/forgot-password.component';
import { ChangePasswordComponent } from './external-signIn/change-password/change-password.component';
import { RegistrationComponent } from './registration/registration.component';
import { RegistrationSuccessComponent } from './registration/registration-success/registration-success.component';

export const routes: Routes = [
    { path: 'login/callback', component: CallBackComponent },
   	{ path: 'login/auth/callback', component: CallBackComponent},
    { path: 'logout', component: LogoutComponent },
    { path:'users', loadChildren: () => import('./users/users.module').then((m: any) => m.UsersModule), canActivate: [PermissionGuard]},
    { path:'rules', loadChildren: () => import('./rules/rules.module').then((m: any) => m.RulesModule)},
      { path:'clients', loadChildren: () => import('./client-preference-center/clients.module').then((m: any) => m.ClientPreferenceCenterModule)},
    {
        path:'settings',
        children:[
         {
             path: 'product',
             loadChildren: () => import('./settings/product/product.module').then((m: any) => m.ProductModule),
             canActivate: [PermissionGuard]
            },
           {
             path: 'roles',
             loadChildren: () => import('./settings/roles/roles.module').then((m: any) => m.RolesModule),
             canActivate: [PermissionGuard]
           },
           {
            path: 'files',
            loadChildren: () => import('./settings/file/file.module').then((m: any) => m.FileModule),
            canActivate: [PermissionGuard]
          },
          {
            path: 'system',
            loadChildren: () => import('./settings/system/system.module').then((m: any) => m.SystemModule),
            canActivate: [PermissionGuard]
          }
        ]
    },
    { path: '', canActivate: [AuthGuard], component: LandingComponent },
    { path: 'signin/register', component: RegistrationComponent },
    { path: 'signin', component: ExternalSignInComponent },
    { path: 'signin/forgotuserid', component: ForgotUseridComponent },
    { path: 'signin/resetpassword', component: ResetPasswordComponent },
    { path: 'signin/forgotpassword', component: ForgotPasswordComponent },
    { path: 'change', component: ChangePasswordComponent },
    { path: 'signin/register/sucess', component: RegistrationSuccessComponent },
    { path: '**', redirectTo: '' }
];
