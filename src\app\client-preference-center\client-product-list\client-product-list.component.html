<div class="fixed-nav bg-gray">
  <marketplace-breadcrumb [dataset]="breadcrumbDataset" (onSelection)="breadcrumSelection($event)">
  </marketplace-breadcrumb>
  <div class="card-body card-body-padding"
    [ngClass]="{pointerEvent:selectedTab == 'Fee Schedule',dspointerEvent:selectedTab == 'Data Exchange'}">

    <marketplace-tabs class="container col-12" [selectedTabIndex]="tabSelected" (onTabSelection)="redrawTable($event)">

      <marketplace-tab [header]="'Products'">
        <marketplace-button [id]="'addProductBut'" [label]="'Add Product'" [type]="'primary'" [name]="'primary'"
          [enabled]="!isReadOnly" (onclick)="showProductModal()">
        </marketplace-button>
        <div class="title-div">
          <span class="table-title">Product Details of {{clientName}}</span>
        </div>
        <marketplace-table [id]="'view-client-product-table'" [dataset]="clientProductData"
          [rowHeight]="ruleDashbordTableRowhg" [headerRowHeight]="ruleDashbordTableHeaderhg"
          [columnDefinitions]="clientProductColConfig" [redraw]="tableRedraw" [dropdownOptions]="kebabOptions"
          [isRowSelectable]="false" [customExportConfig]="customExport"
          (onDropdownOptionsClick)="moveToSelectedTab($event)">
        </marketplace-table>
      </marketplace-tab>

      <marketplace-tab [header]="'Fee Schedule'">
        <app-product-bundle-fee-list *ngIf="selectTabheader =='Fee Schedule'"
          (feeSetupEvent)="enableNotification($event)" [navigate]="navigate" [isReadOnly]="isReadOnly">
        </app-product-bundle-fee-list>
      </marketplace-tab>

      <marketplace-tab [header]="'Data Exchange'">
        <app-client-preference-list *ngIf="selectTabheader =='Data Exchange'" [clientSelected]="clientselected"
          (activePageInfoEvent)="activePageInfoEvent($event)" [navigate]="navigate" [isReadOnly]="isReadOnly">
        </app-client-preference-list>
      </marketplace-tab>

      <marketplace-tab [header]="'File Exchange'">
        <app-acknowledgement *ngIf="selectTabheader =='File Exchange'" [clientSelected]="clientselected"
          [isReadOnly]="isReadOnly"></app-acknowledgement>
      </marketplace-tab>

      <marketplace-tab [header]="'Sample Validation Percentage'">
        <app-sample-validation-client *ngIf="selectTabheader =='Sample Validation Percentage'"
          [isReadOnly]="isReadOnly">
        </app-sample-validation-client>
      </marketplace-tab>

      <marketplace-tab [header]="'View Tenant'">
        <app-tenant-list *ngIf="selectTabheader == 'View Tenant'" (activePageInfoEvent)="activePageInfoEvent($event)"
          [isReadOnly]="isReadOnly"></app-tenant-list>
      </marketplace-tab>

    </marketplace-tabs>

  </div>



</div>

<marketplace-popup [open]="showProductInventoryEditPopUp" [size]="'xlarge'" (onClose)="onCloseOverlay()">
  <div mpui-modal-header>Add Product</div>
  <div mpui-modal-body>
    <div class="skill-category-form-box">
      <marketplace-dynamic-form *ngIf="isProductTypeFormReady" [id]="'productform'" [formJSON]="productFormJSON"
        [isSubmitNeeded]="false" (onValueChanges)="_onproductSelection($event)">
      </marketplace-dynamic-form>
      <marketplace-dynamic-form *ngIf="isInventoryTypeFormReady" [id]="'inventoryTypeform'" [formJSON]="invTypeJSON"
        [isSubmitNeeded]="false" (onValueChanges)="_onInventorySelection($event)">
      </marketplace-dynamic-form>
    </div>
    <div class="modal-footer wrapper">
      <div>
        <marketplace-button [label]="'Cancel'" [type]="'ghost'" [name]="'secondary'" (onclick)="onCloseOverlay()">
        </marketplace-button>
      </div>
      <div>
        <marketplace-button [label]="'Submit'" [type]="'primary'" [name]="'primary'" [enabled]="isFormValid"
          (onclick)="onSubmit()">
          <!-- <i class="fa fa-spinner fa-spin" *ngIf="isLoading"> </i> -->
        </marketplace-button>
      </div>
    </div>
  </div>
</marketplace-popup>

<marketplace-notification [open]="notificationOpen" [header]="notificationHeader" [bodyText]="notificationBody"
  [type]="notificationType" [duration]="notificationDuration" [position]="notificationPosition">

</marketplace-notification>