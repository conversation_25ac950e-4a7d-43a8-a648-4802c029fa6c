import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RolesComponent } from './roles.component';
import { EditRoleComponent } from './edit-role/edit-role.component';
import { ViewRoleComponent } from './view-role/view-role.component';
import { AuthGuard } from 'src/app/_helpers/auth.guard';
import { PermissionGuard } from 'src/app/_helpers/permission.guard';
const routes: Routes = [{
  path: '', 
  component: RolesComponent,
  canActivate:[AuthGuard]
},{
  path: 'edit-role', 
  component: EditRoleComponent,
  canActivate:[PermissionGuard]
},
{
  path: 'view-role',
  component: ViewRoleComponent,
  canActivate:[PermissionGuard]
}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class RolesRoutingModule { }
